package com.tzslsjy.quartz.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.net.InetAddress;
import java.time.Duration;
import java.util.UUID;

/**
 * 集群锁工具类
 * 用于在集群环境下避免重复执行预警任务
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class ClusterLockUtils {

    @Autowired(required = false)
    @Qualifier("stringRedisTemplate")
    private RedisTemplate<String, String> redisTemplate;

    private static final String LOCK_PREFIX = "alert:lock:";
    private static final String NODE_ID = generateNodeId();
    private static final Duration DEFAULT_LOCK_TIMEOUT = Duration.ofMinutes(5);

    /**
     * 尝试获取分布式锁
     *
     * @param lockKey 锁的键
     * @param timeout 锁的超时时间
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey, Duration timeout) {
        if (redisTemplate == null) {
            // 如果没有Redis，则认为是单机环境，直接返回true
            log.debug("Redis未配置，跳过集群锁检查");
            return true;
        }

        try {
            String fullLockKey = LOCK_PREFIX + lockKey;
            String lockValue = NODE_ID + ":" + System.currentTimeMillis();
            
            Boolean success = redisTemplate.opsForValue()
                    .setIfAbsent(fullLockKey, lockValue, timeout);
            
            if (Boolean.TRUE.equals(success)) {
                log.debug("成功获取集群锁: {}", lockKey);
                return true;
            } else {
                String existingValue = redisTemplate.opsForValue().get(fullLockKey);
                log.debug("获取集群锁失败，锁已被占用: {} -> {}", lockKey, existingValue);
                return false;
            }
            
        } catch (Exception e) {
            log.error("获取集群锁异常: {}, 错误: {}", lockKey, e.getMessage(), e);
            // 异常情况下返回true，避免阻塞任务执行
            return true;
        }
    }

    /**
     * 尝试获取分布式锁（使用默认超时时间）
     *
     * @param lockKey 锁的键
     * @return 是否成功获取锁
     */
    public boolean tryLock(String lockKey) {
        return tryLock(lockKey, DEFAULT_LOCK_TIMEOUT);
    }

    /**
     * 释放分布式锁
     *
     * @param lockKey 锁的键
     */
    public void releaseLock(String lockKey) {
        if (redisTemplate == null) {
            return;
        }

        try {
            String fullLockKey = LOCK_PREFIX + lockKey;
            String existingValue = redisTemplate.opsForValue().get(fullLockKey);
            
            if (existingValue != null && existingValue.startsWith(NODE_ID + ":")) {
                redisTemplate.delete(fullLockKey);
                log.debug("成功释放集群锁: {}", lockKey);
            } else {
                log.debug("集群锁不属于当前节点，跳过释放: {}", lockKey);
            }
            
        } catch (Exception e) {
            log.error("释放集群锁异常: {}, 错误: {}", lockKey, e.getMessage(), e);
        }
    }

    /**
     * 检查锁是否存在
     *
     * @param lockKey 锁的键
     * @return 锁是否存在
     */
    public boolean isLocked(String lockKey) {
        if (redisTemplate == null) {
            return false;
        }

        try {
            String fullLockKey = LOCK_PREFIX + lockKey;
            return Boolean.TRUE.equals(redisTemplate.hasKey(fullLockKey));
        } catch (Exception e) {
            log.error("检查集群锁状态异常: {}, 错误: {}", lockKey, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取锁的持有者信息
     *
     * @param lockKey 锁的键
     * @return 锁的持有者信息
     */
    public String getLockHolder(String lockKey) {
        if (redisTemplate == null) {
            return null;
        }

        try {
            String fullLockKey = LOCK_PREFIX + lockKey;
            return redisTemplate.opsForValue().get(fullLockKey);
        } catch (Exception e) {
            log.error("获取集群锁持有者信息异常: {}, 错误: {}", lockKey, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 强制释放锁（管理员操作）
     *
     * @param lockKey 锁的键
     * @return 是否成功释放
     */
    public boolean forceReleaseLock(String lockKey) {
        if (redisTemplate == null) {
            return true;
        }

        try {
            String fullLockKey = LOCK_PREFIX + lockKey;
            Boolean deleted = redisTemplate.delete(fullLockKey);
            log.info("强制释放集群锁: {} -> {}", lockKey, deleted);
            return Boolean.TRUE.equals(deleted);
        } catch (Exception e) {
            log.error("强制释放集群锁异常: {}, 错误: {}", lockKey, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成节点ID
     */
    private static String generateNodeId() {
        try {
            String hostName = InetAddress.getLocalHost().getHostName();
            String uuid = UUID.randomUUID().toString().substring(0, 8);
            return hostName + "-" + uuid;
        } catch (Exception e) {
            return "unknown-" + UUID.randomUUID().toString().substring(0, 8);
        }
    }

    /**
     * 获取当前节点ID
     */
    public static String getNodeId() {
        return NODE_ID;
    }

    /**
     * 生成预警规则锁的键
     *
     * @param ruleId 规则ID
     * @return 锁的键
     */
    public static String generateAlertRuleLockKey(Long ruleId) {
        return "rule:" + ruleId;
    }

    /**
     * 生成预警规则执行锁的键
     *
     * @param ruleId 规则ID
     * @param executeTime 执行时间（毫秒）
     * @return 锁的键
     */
    public static String generateAlertRuleExecutionLockKey(Long ruleId, long executeTime) {
        // 将执行时间按分钟对齐，确保同一分钟内的执行使用同一个锁
        long alignedTime = (executeTime / 60000) * 60000;
        return "execution:" + ruleId + ":" + alignedTime;
    }
}
