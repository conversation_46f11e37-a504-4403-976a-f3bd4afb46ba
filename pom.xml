<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

    <groupId>com.tzslsjy</groupId>
    <artifactId>sjyAlert</artifactId>
    <version>3.8.7</version>

    <name>sjyAlert</name>
    <url>http://fastbuild.run</url>
    <description>若依管理系统</description>

    <properties>
        <sjyAlert.version>3.8.7</sjyAlert.version>
        <sjy.version>3.8.7</sjy.version>
        <transmittable-thread-local.version>2.14.2</transmittable-thread-local.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <spring-framework.version>5.3.33</spring-framework.version>
        <druid.version>1.2.20</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <swagger.version>3.0.0</swagger.version>
        <kaptcha.version>2.3.3</kaptcha.version>
        <pagehelper.boot.version>1.4.7</pagehelper.boot.version>
        <fastjson.version>2.0.43</fastjson.version>
        <oshi.version>6.5.0</oshi.version>
        <commons.io.version>2.13.0</commons.io.version>
        <commons.collections.version>3.2.2</commons.collections.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>2.3</velocity.version>
        <jwt.version>0.9.1</jwt.version>
        <redisson.version>3.18.0</redisson.version>
        <redis.version>2.7.7</redis.version>
        <lombok.version>1.18.24</lombok.version>
        <mybatis-plus.version>3.5.3.1</mybatis-plus.version>
        <mybatis-plus-generator.version>3.5.3.1</mybatis-plus-generator.version>
        <springdoc.version>1.6.14</springdoc.version>
        <knife4j.version>4.0.0</knife4j.version>
        <hutool.version>5.8.11</hutool.version>
        <commons-net.version>3.8.0</commons-net.version>
        <mybatis-plus-join-boot-starter.version>1.4.3.1</mybatis-plus-join-boot-starter.version>
        <!-- 三方云服务相关 -->
        <okio.version>3.0.0</okio.version>

        <okhttp3.version>4.10.0</okhttp3.version>
        <minio.version>8.5.1</minio.version>
        <tika-core.version>2.6.0</tika-core.version>
        <jsch.version>0.1.55</jsch.version>
        <mapstruct.version>1.5.3.Final</mapstruct.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>
            <!-- Spring注解解释器 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <version>2.7.8</version>
                <optional>true</optional>
            </dependency>
            <dependency>
                <groupId>org.springframework.data</groupId>
                <artifactId>spring-data-redis</artifactId>
                <version>${redis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>transmittable-thread-local</artifactId> <!-- 解决 ThreadLocal 父子线程的传值问题 -->
                <version>${transmittable-thread-local.version}</version>
            </dependency>
            <!-- mapstruct用于类型转换 -->
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId> <!-- use mapstruct-jdk8 for Java 8 or higher -->
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-jdk8</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>


            <!-- SpringFramework的依赖配置-->
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-framework-bom</artifactId>
                <version>${spring-framework.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.github.yulichang</groupId>
                <artifactId>mybatis-plus-join-boot-starter</artifactId>
                <version>${mybatis-plus-join-boot-starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-openapi3-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.jcraft</groupId>
                <artifactId>jsch</artifactId> <!-- 解决 sftp 连接 -->
                <version>${jsch.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.tika</groupId>
                <artifactId>tika-core</artifactId> <!-- 文件类型的识别 -->
                <version>${tika-core.version}</version>
            </dependency>

            <dependency>
                <groupId>io.minio</groupId>
                <artifactId>minio</artifactId>
                <version>${minio.version}</version>
            </dependency>
            <dependency>
                <groupId>commons-net</groupId>
                <artifactId>commons-net</artifactId> <!-- 解决 ftp 连接 -->
                <version>${commons-net.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId> <!-- 代码生成器，使用它解析表结构 -->
                <version>${mybatis-plus-generator.version}</version>
            </dependency>
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
            </dependency>
            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.7.8</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>
            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis.spring.boot</groupId>
                        <artifactId>mybatis-spring-boot-starter</artifactId>
                    </exclusion>
                </exclusions>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-configuration-processor</artifactId>
                <optional>true</optional>
            </dependency>
            <!-- Swagger3依赖 -->
<!--            <dependency>-->
<!--                <groupId>io.springfox</groupId>-->
<!--                <artifactId>springfox-boot-starter</artifactId>-->
<!--                <version>${swagger.version}</version>-->
<!--                <exclusions>-->
<!--                    <exclusion>-->
<!--                        <groupId>io.swagger</groupId>-->
<!--                        <artifactId>swagger-models</artifactId>-->
<!--                    </exclusion>-->
<!--                </exclusions>-->
<!--            </dependency>-->

            <!-- io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- collections工具类 -->
            <dependency>
                <groupId>commons-collections</groupId>
                <artifactId>commons-collections</artifactId>
                <version>${commons.collections.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <!-- Token生成与解析-->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 验证码 -->
            <dependency>
                <groupId>pro.fessional</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!-- 定时任务-->
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjyAlert-quartz</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjyAlert-generator</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjy-spring-boot-start-core</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjyAlert-weixin</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjy-spring-boot-start-mq</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjy-spring-boot-start-sms</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjy-spring-boot-starter-file</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>
            <!-- 核心模块-->
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjyAlert-framework</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjy-system</artifactId>
                <version>${sjy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjy-alert-business</artifactId>
                <version>${sjy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjy-common</artifactId>
                <version>${sjy.version}</version>
            </dependency>
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjyAlert-infra</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>
            <!-- 通用工具-->
            <dependency>
                <groupId>com.tzslsjy</groupId>
                <artifactId>sjyAlert-common</artifactId>
                <version>${sjyAlert.version}</version>
            </dependency>

        </dependencies>
    </dependencyManagement>

    <modules>
        <module>sjy-alert-admin</module>
        <module>sjy-alert-framework</module>
        <module>sjy-alert-quartz</module>
        <module>sjy-alert-generator</module>
        <module>sjy-alert-common</module>
        <module>sjy-alert-infra</module>
        <module>sjy-alert-weixin</module>
        <module>sjy-alert-business</module>

    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <!--  maven仓库配置 deploy时可推送到配置的仓库中	-->
    <distributionManagement>
        <repository>
            <id>maven-releases</id>
            <name>Nexus Releases Repository Pro</name>
            <url>http://maven.slsjy.cn/repository/maven-releases/</url>
        </repository>

        <snapshotRepository>
            <id>maven-snapshots</id>
            <name>Nexus Snapshots Repository Pro</name>
            <url>http://maven.slsjy.cn/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
