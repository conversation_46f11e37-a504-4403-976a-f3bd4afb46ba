package com.tzslsjy.common.utils;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;

public class SjyAccuracyUtil {

    /**
     * 水库水位保留两位精度
     * @return
     */
    public static BigDecimal rzTwoAccuracy(BigDecimal rz){
        if (rz != null && !"".equals(rz)) {
            return NumberUtil.round(rz, 2);
        } else {
            return null;
        }
    }

    /**
     * 总库容保留两位精度
     * @return
     */
    public static BigDecimal ttcpTwoAccuracy(BigDecimal rz){
        if (rz != null && !"".equals(rz)) {
            return NumberUtil.round(rz, 2);
        } else {
            return null;
        }
    }

    /**
     * 河道水位保留两位精度
     * @return
     */
    public static BigDecimal zTwoAccuracy(BigDecimal z){
        if (z != null && !"".equals(z)) {
            return NumberUtil.round(z, 2);
        } else {
            return null;
        }
    }

    /**
     * 水位doblue类型保留两位精度
     * @return
     */
    public static Double zDoubleTwoAccuracy(Double z){
        if (z != null && !"".equals(z)) {
            return NumberUtil.round(z, 2).doubleValue();
        } else {
            return null;
        }
    }

    /**
     * 可拦雨量为负值时 返回0
     * @param nBlock
     * @return
     */
    public static BigDecimal nBlockNegativeforZreo (BigDecimal nBlock){
        if (nBlock != null && !"".equals(nBlock)) {
            if(nBlock.compareTo(BigDecimal.ZERO)<0) {
                return NumberUtil.round(BigDecimal.ZERO, 1);
            }else{
                return nBlock;
            }
        } else {
            return null;
        }
    }

}
