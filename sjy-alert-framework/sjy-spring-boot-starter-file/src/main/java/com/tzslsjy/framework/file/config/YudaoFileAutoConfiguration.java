package com.tzslsjy.framework.file.config;

import com.tzslsjy.framework.file.core.client.FileClientFactory;
import com.tzslsjy.framework.file.core.client.FileClientFactoryImpl;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 文件配置类
 *
 * <AUTHOR>
 */
@AutoConfiguration
public class YudaoFileAutoConfiguration {

    @Bean
    public FileClientFactory fileClientFactory() {
        return new FileClientFactoryImpl();
    }

}
