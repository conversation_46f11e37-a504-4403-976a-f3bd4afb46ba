package com.tzslsjy.sms.task;


import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tzslsjy.sms.domain.SendReq;
import com.tzslsjy.sms.domain.SendRes;
import com.tzslsjy.sms.domain.SjySms;
import com.tzslsjy.sms.mapper.SjySmsMapper;
import com.tzslsjy.sms.util.Md5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Iterator;
import java.util.List;

@Slf4j
@Component("smsTask")
public class BussinessTask {

    @Resource
    private SjySmsMapper sjySmsMapper;

    // 短信接口链接
    @Value("${sjy.sms.url}")
    private String url;
    @Value("${sjy.sms.reporturl}")
    private String reporturl;

    @Value("${sjy.sms.apId}")
    private String apId;
    @Value("${sjy.sms.secretKey}")
    private String secretKey;
    @Value("${sjy.sms.ecName}")
    private String ecName;  // 集团名称
    @Value("${sjy.sms.sign}")
    private String sign ; // 网关签名编码
    @Value("${sjy.sms.addSerial}")
    private String addSerial; // 拓展码 填空


    /**
     * 短信发送
     */
    @Bean
    public void SmsJob() throws IOException {
        try {
            List<SjySms> smsinfo = sjySmsMapper.selectSms();
            log.info("1、获取到待发送短信" + smsinfo.size() + "条。");
            if (smsinfo != null && smsinfo.size() > 0) {
                int i = 0;
                Iterator it = smsinfo.iterator();
                while (it.hasNext()) {
                    SjySms item = (SjySms) it.next();
                    if (!item.getSmsMobile().isEmpty()) {
                        String result = sendMsg(item.getSmsMobile().trim(), item.getSmsContent());
                        SendRes sendRes = JSON.parseObject(result, SendRes.class);
                        if (sendRes.isSuccess() && !"".equals(sendRes.getMsgGroup()) && "success".equals(sendRes.getRspcod())) {
                            String idestr = sendRes.getMsgGroup();
                            item.setSmsMsgGroup(idestr);
                            item.setSmsSended(1L);
                            item.setSmsSendedTime(DateTime.now());
                            sjySmsMapper.updateById(item);
                            ++i;
                        } else {
                            log.info("2、发送失败");
                        }
                    }
                }
                log.info("2、成功推送短信" + i + "条，推送失败" + (smsinfo.size() - i) + "条。");
            } else {
                log.info("2、未查询到待发送短信，短信发送服务结束");
            }
        } catch (Exception var1) {
            log.error(var1.getMessage());
        }
    }

    public void SmsReport() throws IOException {
        try {
            log.info("1、开始响应状态、回执状态处理");
            String receiptReport = getReport();//回执状态
            if (receiptReport != "[]") {
                JSONArray jsonArray = JSON.parseArray(receiptReport);
                for (int index = 0; index < jsonArray.size(); index++) {

                    JSONObject jsonObject = jsonArray.getJSONObject(index);
                    QueryWrapper<SjySms> sjySmsQueryWrapper = new QueryWrapper<>();
                    sjySmsQueryWrapper.eq("sms_msg_group", jsonObject.getString("msgGroup"));

                    SjySms item =new SjySms();
                    item.setSmsReceiveDate(DateUtil.parse(jsonObject.getString("receiveDate") ));
                    item.setSmsSubmitDate(DateUtil.parse(jsonObject.getString("submitDate")));
                    item.setSmsErrorCode(jsonObject.getString("errorCode"));
                    item.setSmsReportStatus(jsonObject.getString("reportStatus"));
                    item.setSmsReceipt(1L);
                    sjySmsMapper.update(item,sjySmsQueryWrapper);
                }
                log.info("2、查询"+ jsonArray.size()+"条响应状态、回执状态。");
            }
            else {
                log.info("2、未查询到响应状态、回执状态");
            }
        } catch (Exception var1) {
            log.error(var1.getMessage());
        }
    }
    /**
     * 多用户发送短信信息
     * @param mobiles 手机号逗号分隔
     * @param content 短信内容
     * @return resStr
     * <AUTHOR>
     * @date 2022/02/20 15:48
     */
    public String sendMsg(String mobiles, String content) throws IOException {

        SendReq sendReq = new SendReq();
        sendReq.setApId(apId);
        sendReq.setEcName(ecName);
        sendReq.setSecretKey(secretKey);
        sendReq.setContent(content);
        sendReq.setMobiles(mobiles);
        sendReq.setAddSerial(addSerial);
        sendReq.setSign(sign);

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(sendReq.getEcName());
        stringBuffer.append(sendReq.getApId());
        stringBuffer.append(sendReq.getSecretKey());
        stringBuffer.append(sendReq.getMobiles());
        stringBuffer.append(sendReq.getContent());
        stringBuffer.append(sendReq.getSign());
        stringBuffer.append(sendReq.getAddSerial());

        sendReq.setMac(Md5Util.MD5(stringBuffer.toString()).toLowerCase());

        String reqText = JSON.toJSONString(sendReq);

        String encode = Base64.encodeBase64String(reqText.getBytes("UTF-8"));

        //此处使用hutool工具包httpRequest工具类发送https请求
        String resStr = HttpRequest.post(url)
                .header("contentType", "utf-8")
                .body(encode)
                .execute()
                .body();

        return resStr;
    }

    //获取状态报告
    public  String getReport() throws IOException{
        SendReq sendReq = new SendReq();
        sendReq.setApId(apId);
        sendReq.setEcName(ecName);
        sendReq.setSecretKey(secretKey);

        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append(sendReq.getEcName());
        stringBuffer.append(sendReq.getApId());
        stringBuffer.append(sendReq.getSecretKey());

        sendReq.setMac(Md5Util.MD5(stringBuffer.toString()).toLowerCase());

        String reqText = JSON.toJSONString(sendReq);

        String encode = Base64.encodeBase64String(reqText.getBytes("UTF-8"));

        //此处使用hutool工具包httpRequest工具类发送https请求
        String resStr = HttpRequest.post(reporturl)
                .header("contentType", "utf-8")
                .body(encode)
                .execute()
                .body();

        return resStr;
    }
}
