package com.tzslsjy.sms.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("sjy_sms")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class SjySms implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "序号")
    @TableField("id")
    private Long id;

    @Schema(description = "短信内容，汉字70个，英文160个")
    @TableField("sms_content")
    private String smsContent;

    @Schema(description = "目标号码")
    @TableField("sms_mobile")
    private String smsMobile;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "业务短信生成时间")
    @TableField("sms_create_time")
    private Date smsCreateTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "计划发送时间")
    @TableField("sms_send_time")
    private Date smsSendTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "实际发送时间")
    @TableField("sms_sended_time")
    private Date smsSendedTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "响应报文-提交时间")
    @TableField("sms_submit_date")
    private Date smsSubmitDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "响应报文-接收时间")
    @TableField("sms_receive_date")
    private Date smsReceiveDate;

    @Schema(description = "消息批次号")
    @TableField("sms_msg_group")
    private String smsMsgGroup;

    @Schema(description = "发送失败的状态码")
    @TableField("sms_error_code")
    private String smsErrorCode;

    @Schema(description = "发送成功状态码")
    @TableField("sms_report_status")
    private String smsReportStatus;

    @Schema(description = "回执状态（成功1，失败-1，未知0）")
    @TableField("sms_receipt")
    private Long smsReceipt;

    @Schema(description = "是否发送(已发送1，未发送0)")
    @TableField("sms_sended")
    private Long smsSended;

    @Schema(description = "县市区")
    @TableField("county_code")
    private String countyCode;

    @Schema(description = "类型")
    @TableField("module_type")
    private String moduleType;

    @Schema(description = "是否删除(未删除1，已删除2，未知3)")
    @TableField("iz_del")
    private String izDel;
}
