<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.sms.mapper.SjySmsMapper">


    <select id="selectSms" resultType="com.tzslsjy.sms.domain.SjySms">
        select  * from  sjy_sms
        where  sms_create_time>= date_sub(NOW(),interval 24 hour)
          and iz_del='1' and (sms_sended=0 or sms_sended is null )
    </select>


</mapper>
