package com.tzslsjy.business.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzslsjy.business.domain.SjyStationAbnormalParamData;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 测站异常预警参数结构测试
 * 验证新的标准化参数格式和向后兼容性
 */
@SpringBootTest
public class StationAbnormalParameterTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 测试新的标准化参数格式解析
     */
    @Test
    public void testNewParameterFormat() throws Exception {
        // 新格式的参数JSON
        String newFormatJson = "{\n" +
                "    \"waterLevel\": {\n" +
                "        \"during\": \"2\",\n" +
                "        \"threshold\": \"1.5\"\n" +
                "    },\n" +
                "    \"rainfall\": {\n" +
                "        \"during\": \"1\",\n" +
                "        \"threshold\": \"20\"\n" +
                "    }\n" +
                "}";

        Map<String, Object> argMap = objectMapper.readValue(newFormatJson, new TypeReference<Map<String, Object>>() {});
        SjyStationAbnormalParamData params = parseNewFormat(argMap);

        // 验证水位参数
        assertNotNull(params.getWaterLevel());
        assertEquals(2, params.getWaterLevel().getDuring());
        assertEquals(new BigDecimal("1.5"), params.getWaterLevel().getThreshold());
        assertEquals(2, params.getWaterLevelDetectionDuringHours());
        assertEquals(new BigDecimal("1.5"), params.getWaterLevelDetectionThreshold());

        // 验证雨量参数
        assertNotNull(params.getRainfall());
        assertEquals(1, params.getRainfall().getDuring());
        assertEquals(new BigDecimal("20"), params.getRainfall().getThreshold());
        assertEquals(1, params.getRainfallDetectionDuringHours());
        assertEquals(new BigDecimal("20"), params.getRainfallDetectionThreshold());
    }

    /**
     * 测试旧格式的兼容性
     */
    @Test
    public void testOldParameterFormatCompatibility() throws Exception {
        // 旧格式的参数JSON
        String oldFormatJson = "{\n" +
                "    \"overWaterVal\": \"2.0\",\n" +
                "    \"overRainVal\": \"25.0\",\n" +
                "    \"overWaterEnable\": \"1\",\n" +
                "    \"overRainEnable\": \"1\",\n" +
                "    \"detectionInterval\": \"10\"\n" +
                "}";

        Map<String, Object> argMap = objectMapper.readValue(oldFormatJson, new TypeReference<Map<String, Object>>() {});
        SjyStationAbnormalParamData params = parseOldFormat(argMap);

        // 验证兼容性：新方法应该能够获取旧格式的值
        assertEquals(new BigDecimal("2.0"), params.getWaterLevelDetectionThreshold());
        assertEquals(new BigDecimal("25.0"), params.getRainfallDetectionThreshold());
        
        // 时间周期应该从分钟转换为小时（10分钟 -> 1小时，最小为1小时）
        assertEquals(1, params.getWaterLevelDetectionDuringHours());
        assertEquals(1, params.getRainfallDetectionDuringHours());
        
        // 检测开关应该正常工作
        assertTrue(params.isWaterLevelDetectionEnabled());
        assertTrue(params.isRainfallDetectionEnabled());
    }

    /**
     * 测试混合格式（新格式优先）
     */
    @Test
    public void testMixedParameterFormat() throws Exception {
        // 混合格式：既有新格式也有旧格式，新格式应该优先
        String mixedFormatJson = "{\n" +
                "    \"waterLevel\": {\n" +
                "        \"during\": \"3\",\n" +
                "        \"threshold\": \"2.5\"\n" +
                "    },\n" +
                "    \"overWaterVal\": \"1.0\",\n" +
                "    \"overWaterEnable\": \"1\",\n" +
                "    \"detectionInterval\": \"5\"\n" +
                "}";

        Map<String, Object> argMap = objectMapper.readValue(mixedFormatJson, new TypeReference<Map<String, Object>>() {});
        SjyStationAbnormalParamData params = parseMixedFormat(argMap);

        // 新格式应该优先于旧格式
        assertEquals(3, params.getWaterLevelDetectionDuringHours());
        assertEquals(new BigDecimal("2.5"), params.getWaterLevelDetectionThreshold());
        
        // 旧格式的值应该被保留但不被新方法使用
        assertEquals(new BigDecimal("1.0"), params.getWaterLevelThreshold());
    }

    /**
     * 解析新格式参数
     */
    private SjyStationAbnormalParamData parseNewFormat(Map<String, Object> argMap) {
        SjyStationAbnormalParamData params = new SjyStationAbnormalParamData();

        if (argMap.containsKey("waterLevel")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> waterLevelMap = (Map<String, Object>) argMap.get("waterLevel");
            SjyStationAbnormalParamData.StationAbnormalDetectionParam waterLevelParam = 
                new SjyStationAbnormalParamData.StationAbnormalDetectionParam();
            
            if (waterLevelMap.containsKey("during")) {
                waterLevelParam.setDuring(Integer.parseInt(String.valueOf(waterLevelMap.get("during"))));
            }
            if (waterLevelMap.containsKey("threshold")) {
                waterLevelParam.setThreshold(new BigDecimal(String.valueOf(waterLevelMap.get("threshold"))));
            }
            params.setWaterLevel(waterLevelParam);
        }

        if (argMap.containsKey("rainfall")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> rainfallMap = (Map<String, Object>) argMap.get("rainfall");
            SjyStationAbnormalParamData.StationAbnormalDetectionParam rainfallParam = 
                new SjyStationAbnormalParamData.StationAbnormalDetectionParam();
            
            if (rainfallMap.containsKey("during")) {
                rainfallParam.setDuring(Integer.parseInt(String.valueOf(rainfallMap.get("during"))));
            }
            if (rainfallMap.containsKey("threshold")) {
                rainfallParam.setThreshold(new BigDecimal(String.valueOf(rainfallMap.get("threshold"))));
            }
            params.setRainfall(rainfallParam);
        }

        return params;
    }

    /**
     * 解析旧格式参数
     */
    private SjyStationAbnormalParamData parseOldFormat(Map<String, Object> argMap) {
        SjyStationAbnormalParamData params = new SjyStationAbnormalParamData();

        if (argMap.containsKey("overWaterVal")) {
            params.setWaterLevelThreshold(new BigDecimal(String.valueOf(argMap.get("overWaterVal"))));
        }
        if (argMap.containsKey("overRainVal")) {
            params.setRainfallThreshold(new BigDecimal(String.valueOf(argMap.get("overRainVal"))));
        }
        if (argMap.containsKey("overWaterEnable")) {
            params.setWaterLevelDetectionEnabled("1".equals(String.valueOf(argMap.get("overWaterEnable"))));
        }
        if (argMap.containsKey("overRainEnable")) {
            params.setRainfallDetectionEnabled("1".equals(String.valueOf(argMap.get("overRainEnable"))));
        }
        if (argMap.containsKey("detectionInterval")) {
            params.setDetectionIntervalMinutes(Integer.parseInt(String.valueOf(argMap.get("detectionInterval"))));
        }

        return params;
    }

    /**
     * 解析混合格式参数
     */
    private SjyStationAbnormalParamData parseMixedFormat(Map<String, Object> argMap) {
        SjyStationAbnormalParamData params = parseOldFormat(argMap);
        
        // 新格式会覆盖旧格式
        if (argMap.containsKey("waterLevel")) {
            @SuppressWarnings("unchecked")
            Map<String, Object> waterLevelMap = (Map<String, Object>) argMap.get("waterLevel");
            SjyStationAbnormalParamData.StationAbnormalDetectionParam waterLevelParam = 
                new SjyStationAbnormalParamData.StationAbnormalDetectionParam();
            
            if (waterLevelMap.containsKey("during")) {
                waterLevelParam.setDuring(Integer.parseInt(String.valueOf(waterLevelMap.get("during"))));
            }
            if (waterLevelMap.containsKey("threshold")) {
                waterLevelParam.setThreshold(new BigDecimal(String.valueOf(waterLevelMap.get("threshold"))));
            }
            params.setWaterLevel(waterLevelParam);
        }

        return params;
    }
}
