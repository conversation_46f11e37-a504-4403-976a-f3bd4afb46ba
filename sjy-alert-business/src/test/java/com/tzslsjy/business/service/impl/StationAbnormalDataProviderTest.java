//package com.tzslsjy.business.service.impl;
//
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.BeforeEach;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.MockitoAnnotations;
//import static org.mockito.Mockito.*;
//import static org.junit.jupiter.api.Assertions.*;
//
//import com.tzslsjy.business.mapper.StRiverRMapper;
//import com.tzslsjy.business.mapper.data.StRsvrRMapper;
//import com.tzslsjy.business.mapper.data.StTideRMapper;
//import com.tzslsjy.business.mapper.data.StWasRMapper;
//import com.tzslsjy.business.mapper.StPptnRMapper;
//import com.tzslsjy.business.service.ISjyAbnormalDataService;
//import com.tzslsjy.business.vo.SjyAbnormalDataVo;
//import java.math.BigDecimal;
//
//import java.util.*;
//import java.util.Date;
//
///**
// * StationAbnormalDataProvider 测试类
// * 主要测试批量数据检查方法的性能优化
// */
//public class StationAbnormalDataProviderTest {
//
//    @Mock
//    private StRiverRMapper stRiverRMapper;
//
//    @Mock
//    private StRsvrRMapper stRsvrRMapper;
//
//    @Mock
//    private StTideRMapper stTideRMapper;
//
//    @Mock
//    private StWasRMapper stWasRMapper;
//
//    @Mock
//    private StPptnRMapper stPptnRMapper;
//
//    @Mock
//    private ISjyAbnormalDataService abnormalDataService;
//
//    @InjectMocks
//    private StationAbnormalDataProvider stationAbnormalDataProvider;
//
//    @BeforeEach
//    void setUp() {
//        MockitoAnnotations.openMocks(this);
//    }
//
//    @Test
//    void testCheckBatchHasRecentData_WithMixedResults() {
//        // 准备测试数据
//        List<String> testStcds = Arrays.asList("ST001", "ST002", "ST003", "ST004", "ST005");
//        Date timeoutThreshold = new Date(System.currentTimeMillis() - 3600000); // 1小时前
//        Date currentTime = new Date();
//
//        // 模拟各种数据源的返回结果
//        when(stRiverRMapper.selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime))
//            .thenReturn(Arrays.asList("ST001", "ST002")); // 河道数据
//
//        when(stRsvrRMapper.selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime))
//            .thenReturn(Arrays.asList("ST003")); // 水库数据
//
//        when(stTideRMapper.selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime))
//            .thenReturn(Collections.emptyList()); // 潮位数据
//
//        when(stWasRMapper.selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime))
//            .thenReturn(Collections.emptyList()); // 堰闸数据
//
//        when(stPptnRMapper.selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime))
//            .thenReturn(Arrays.asList("ST004")); // 雨量数据
//
//        // 使用反射调用私有方法进行测试
//        try {
//            java.lang.reflect.Method method = StationAbnormalDataProvider.class
//                .getDeclaredMethod("checkBatchHasRecentData", List.class, Date.class, Date.class);
//            method.setAccessible(true);
//
//            @SuppressWarnings("unchecked")
//            Map<String, Boolean> result = (Map<String, Boolean>) method.invoke(
//                stationAbnormalDataProvider, testStcds, timeoutThreshold, currentTime);
//
//            // 验证结果
//            assertNotNull(result);
//            assertEquals(5, result.size());
//
//            // ST001, ST002, ST003, ST004 应该有数据
//            assertTrue(result.get("ST001"), "ST001 should have data (river)");
//            assertTrue(result.get("ST002"), "ST002 should have data (river)");
//            assertTrue(result.get("ST003"), "ST003 should have data (reservoir)");
//            assertTrue(result.get("ST004"), "ST004 should have data (rain)");
//
//            // ST005 没有任何数据
//            assertFalse(result.get("ST005"), "ST005 should not have data");
//
//            // 验证每个mapper只被调用一次（批量查询）
//            verify(stRiverRMapper, times(1)).selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime);
//            verify(stRsvrRMapper, times(1)).selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime);
//            verify(stTideRMapper, times(1)).selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime);
//            verify(stWasRMapper, times(1)).selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime);
//            verify(stPptnRMapper, times(1)).selectExistStcdsByStcds(testStcds, timeoutThreshold, currentTime);
//
//        } catch (Exception e) {
//            fail("Test failed with exception: " + e.getMessage());
//        }
//    }
//
//    @Test
//    void testCheckBatchHasRecentData_EmptyInput() {
//        // 测试空输入
//        try {
//            java.lang.reflect.Method method = StationAbnormalDataProvider.class
//                .getDeclaredMethod("checkBatchHasRecentData", List.class, Date.class, Date.class);
//            method.setAccessible(true);
//
//            @SuppressWarnings("unchecked")
//            Map<String, Boolean> result = (Map<String, Boolean>) method.invoke(
//                stationAbnormalDataProvider, Collections.emptyList(), new Date(), new Date());
//
//            assertNotNull(result);
//            assertTrue(result.isEmpty());
//
//        } catch (Exception e) {
//            fail("Test failed with exception: " + e.getMessage());
//        }
//    }
//
//    @Test
//    void testPerformanceComparison() {
//        // 这个测试用于演示性能差异
//        // 在实际环境中，批量查询应该比逐个查询快得多
//
//        List<String> largeStcdList = new ArrayList<>();
//        for (int i = 1; i <= 1000; i++) {
//            largeStcdList.add("ST" + String.format("%03d", i));
//        }
//
//        Date timeoutThreshold = new Date(System.currentTimeMillis() - 3600000);
//        Date currentTime = new Date();
//
//        // 模拟批量查询返回部分结果
//        when(stRiverRMapper.selectExistStcdsByStcds(largeStcdList, timeoutThreshold, currentTime))
//            .thenReturn(largeStcdList.subList(0, 100));
//        when(stRsvrRMapper.selectExistStcdsByStcds(largeStcdList, timeoutThreshold, currentTime))
//            .thenReturn(largeStcdList.subList(100, 200));
//        when(stTideRMapper.selectExistStcdsByStcds(largeStcdList, timeoutThreshold, currentTime))
//            .thenReturn(Collections.emptyList());
//        when(stWasRMapper.selectExistStcdsByStcds(largeStcdList, timeoutThreshold, currentTime))
//            .thenReturn(Collections.emptyList());
//        when(stPptnRMapper.selectExistStcdsByStcds(largeStcdList, timeoutThreshold, currentTime))
//            .thenReturn(largeStcdList.subList(200, 300));
//
//        try {
//            java.lang.reflect.Method method = StationAbnormalDataProvider.class
//                .getDeclaredMethod("checkBatchHasRecentData", List.class, Date.class, Date.class);
//            method.setAccessible(true);
//
//            long startTime = System.currentTimeMillis();
//
//            @SuppressWarnings("unchecked")
//            Map<String, Boolean> result = (Map<String, Boolean>) method.invoke(
//                stationAbnormalDataProvider, largeStcdList, timeoutThreshold, currentTime);
//
//            long endTime = System.currentTimeMillis();
//
//            assertNotNull(result);
//            assertEquals(1000, result.size());
//
//            // 验证只进行了5次数据库查询（每种数据类型一次）
//            verify(stRiverRMapper, times(1)).selectExistStcdsByStcds(any(), any(), any());
//            verify(stRsvrRMapper, times(1)).selectExistStcdsByStcds(any(), any(), any());
//            verify(stTideRMapper, times(1)).selectExistStcdsByStcds(any(), any(), any());
//            verify(stWasRMapper, times(1)).selectExistStcdsByStcds(any(), any(), any());
//            verify(stPptnRMapper, times(1)).selectExistStcdsByStcds(any(), any(), any());
//
//            System.out.println("批量查询1000个测站耗时: " + (endTime - startTime) + "ms");
//            System.out.println("数据库查询次数: 5次（每种数据类型1次）");
//            System.out.println("如果使用原来的逐个查询方式，需要进行: " + (1000 * 5) + "次数据库查询");
//
//        } catch (Exception e) {
//            fail("Performance test failed with exception: " + e.getMessage());
//        }
//    }
//
//    @Test
//    void testBatchAbnormalDataQuery() {
//        // 测试批量异常数据查询的性能优化
//        List<String> testStcds = Arrays.asList("ST001", "ST002", "ST003");
//        Date startTime = new Date(System.currentTimeMillis() - 300000); // 5分钟前
//        Date currentTime = new Date();
//
//        // 模拟批量查询返回的异常数据
//        List<SjyAbnormalDataVo> mockAbnormalData = new ArrayList<>();
//
//        // 创建一些测试数据
//        SjyAbnormalDataVo vo1 = new SjyAbnormalDataVo();
//        vo1.setStcd("ST001");
//        vo1.setType("water_level");
//        vo1.setVal(new BigDecimal("10.5"));
//        vo1.setDiff(new BigDecimal("1.5")); // 超过阈值1.0
//        vo1.setTm(currentTime);
//        mockAbnormalData.add(vo1);
//
//        SjyAbnormalDataVo vo2 = new SjyAbnormalDataVo();
//        vo2.setStcd("ST002");
//        vo2.setType("rainfall");
//        vo2.setVal(new BigDecimal("20.0")); // 超过阈值15.0
//        vo2.setDiff(new BigDecimal("0.5"));
//        vo2.setTm(currentTime);
//        mockAbnormalData.add(vo2);
//
//        // 模拟批量查询
//        when(abnormalDataService.queryBatchByCondition(testStcds, "water_level", startTime, currentTime))
//            .thenReturn(Arrays.asList(vo1));
//
//        when(abnormalDataService.queryBatchByCondition(testStcds, "rainfall", startTime, currentTime))
//            .thenReturn(Arrays.asList(vo2));
//
//        // 验证批量查询只调用一次，而不是每个测站调用一次
//        verify(abnormalDataService, times(1)).queryBatchByCondition(testStcds, "water_level", startTime, currentTime);
//        verify(abnormalDataService, times(1)).queryBatchByCondition(testStcds, "rainfall", startTime, currentTime);
//
//        System.out.println("批量异常数据查询测试完成");
//        System.out.println("原来的方式需要进行: " + testStcds.size() + " 次数据库查询（每个测站一次）");
//        System.out.println("优化后只需要: 1次数据库查询（所有测站一次）");
//        System.out.println("性能提升: " + testStcds.size() + "倍");
//    }
//}
