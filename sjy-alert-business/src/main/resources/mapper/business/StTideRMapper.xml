<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.data.StTideRMapper">

    <resultMap type="com.tzslsjy.business.domain.data.StTideR" id="StTideRResult">
        <result property="stcd"    column="STCD"/>
        <result property="tm"    column="TM"/>
        <result property="tdz"    column="TDZ"/>
        <result property="airp"    column="AIRP"/>
        <result property="tdchrcd"    column="TDCHRCD"/>
        <result property="tdptn"    column="TDPTN"/>
        <result property="hltdmk"    column="HLTDMK"/>
        <result property="moditime"    column="MODITIME"/>
        <result property="edited"    column="EDITED"/>
        <result property="bFlag"    column="B_Flag"/>
    </resultMap>

    <!-- selectExistStcdsByStcds --> 

    <select id="selectExistStcdsByStcds" resultType="java.lang.String">
        select stcd from st_tide_r 
        <where>
            <if test="stcds != null">
                stcd in 
                <foreach item="stcd" collection="stcds" open="(" separator="," close=")">
                    #{stcd}
                </foreach>
            </if>
            and tm &gt;= #{lastTongTime} and tm &lt;= #{currentTime}
        </where>
    </select>
</mapper>