<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertStSituationMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyAlertStSituation" id="SjyAlertStSituationResult">
        <id     property="id"       column="id"      />
        <result property="stcd"     column="stcd"    />
        <result property="cId"      column="c_id"    />
        <result property="cType"    column="c_type"  />
        <result property="status"   column="status"  />
        <result property="startTime" column="start_time" />
        <result property="endTime"   column="end_time"   />
        <result property="createBy"   column="create_by"    />
        <result property="createTime" column="create_time"  />
        <result property="updateBy"   column="update_by"    />
        <result property="updateTime" column="update_time"  />
        <result property="remark"     column="remark"  />
    </resultMap>

    <sql id="selectSjyAlertStSituationVo">
        select id, stcd, c_id, c_type, status, start_time, end_time, create_by, create_time, update_by, update_time, remark from sjy_alert_st_situation
    </sql>

    <select id="selectSjyAlertStSituationList" parameterType="com.tzslsjy.business.domain.SjyAlertStSituation" resultMap="SjyAlertStSituationResult">
        <include refid="selectSjyAlertStSituationVo"/>
        <where>
            <if test="stcd != null  and stcd != ''"> and stcd = #{stcd}</if>
            <if test="cId != null "> and c_id = #{cId}</if>
            <if test="cType != null "> and c_type = #{cType}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectSjyAlertStSituationById" parameterType="java.lang.Integer" resultMap="SjyAlertStSituationResult">
        <include refid="selectSjyAlertStSituationVo"/>
        where id = #{id}
    </select>

    <insert id="insertSjyAlertStSituation" parameterType="com.tzslsjy.business.domain.SjyAlertStSituation" useGeneratedKeys="true" keyProperty="id">
        insert into sjy_alert_st_situation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stcd != null and stcd != ''">stcd,</if>
            <if test="cId != null">c_id,</if>
            <if test="cType != null">c_type,</if>
            <if test="status != null">status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stcd != null and stcd != ''">#{stcd},</if>
            <if test="cId != null">#{cId},</if>
            <if test="cType != null">#{cType},</if>
            <if test="status != null">#{status},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertStSituation" parameterType="com.tzslsjy.business.domain.SjyAlertStSituation">
        update sjy_alert_st_situation
        <trim prefix="SET" suffixOverrides=",">
            <if test="stcd != null and stcd != ''">stcd = #{stcd},</if>
            <if test="cId != null">c_id = #{cId},</if>
            <if test="cType != null">c_type = #{cType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSjyAlertStSituationById" parameterType="java.lang.Integer">
        delete from sjy_alert_st_situation where id = #{id}
    </delete>

    <delete id="deleteSjyAlertStSituationByIds" parameterType="java.lang.String">
        delete from sjy_alert_st_situation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByStcdAndRelation" resultMap="SjyAlertStSituationResult">
        <include refid="selectSjyAlertStSituationVo"/>
        where stcd = #{stcd} and c_id = #{cId} and c_type = #{cType}
    </select>

    <insert id="insertOrUpdate" parameterType="com.tzslsjy.business.domain.SjyAlertStSituation">
        INSERT INTO sjy_alert_st_situation (stcd, c_id, c_type, status, start_time, end_time, create_by, create_time, update_by, update_time, remark)
        VALUES (#{stcd}, #{cId}, #{cType}, #{status}, #{startTime}, #{endTime}, #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark})
        ON DUPLICATE KEY UPDATE
        status = #{status},
        start_time = #{startTime},
        end_time = #{endTime},
        update_by = #{updateBy},
        update_time = #{updateTime},
        remark = #{remark}
    </insert>

    <select id="selectActiveAlertStations" resultMap="SjyAlertStSituationResult">
        <include refid="selectSjyAlertStSituationVo"/>
        where c_id = #{cId} and c_type = #{cType} and status in (1, 2)
    </select>
</mapper>