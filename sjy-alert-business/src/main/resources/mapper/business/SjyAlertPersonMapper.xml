<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertPersonMapper">

    <resultMap type="SjyAlertPerson" id="SjyAlertPersonResult">
        <result property="personId"    column="person_id"    />
        <result property="name"    column="name"    />
        <result property="phone"    column="phone"    />
        <result property="addvcd"    column="addvcd"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>
    <!-- ResultMap for SjyAlertNode to be used in collection -->
    <resultMap type="com.tzslsjy.business.domain.SjyAlertNode" id="SjyAlertNodeSubResult">
        <id property="nodeId" column="node_node_id"/>
        <result property="nodeName" column="node_node_name"/>
        <result property="parentId" column="node_parent_id"/>
        <result property="nodeType" column="node_node_type"/>
        <result property="status" column="node_status"/>
        <result property="adcd" column="node_adcd"/>
        <result property="createBy" column="node_create_by"/>
        <result property="createTime" column="node_create_time"/>
        <result property="updateBy" column="node_update_by"/>
        <result property="updateTime" column="node_update_time"/>
        <result property="remark" column="node_remark"/>
        <result property="postName" column="node_post_name"/>
    </resultMap>

    <resultMap type="com.tzslsjy.business.domain.SjyAlertPerson" id="SjyAlertPersonWithNodesResult">
        <id property="personId" column="person_id"/>
        <result property="name" column="person_name"/>
        <result property="phone" column="person_phone"/>
        <result property="postName" column="person_position"/>
        <result property="nodeName" column="person_org_id"/>
        <result property="status" column="person_status"/>
        <result property="priority" column="person_priority"/>
        <result property="createBy" column="person_create_by"/>
        <result property="createTime" column="person_create_time"/>
        <result property="updateBy" column="person_update_by"/>
        <result property="updateTime" column="person_update_time"/>
        <result property="remark" column="person_remark"/>
        <collection property="alertNodes" resultMap="SjyAlertNodeSubResult"/>
    </resultMap>

    <sql id="selectSjyAlertPersonVo">
        select person_id, node_id, name, phone, dept_name, addvcd, post_name, status, create_by, create_time, update_by, update_time, remark from sjy_alert_person
    </sql>

    <select id="selectSjyAlertPersonList" parameterType="SjyAlertPerson" resultMap="SjyAlertPersonResult">
        <include refid="selectSjyAlertPersonVo"/>
        <where>
            <if test="nodeId != null "> and node_id = #{nodeId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="deptName != null  and deptName != ''"> and dept_name like concat('%', #{deptName}, '%')</if>
            <if test="addvcd != null  and addvcd != ''"> and addvcd = #{addvcd}</if>
            <if test="postName != null  and postName != ''"> and post_name like concat('%', #{postName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectSjyAlertPersonByPersonId" parameterType="String" resultMap="SjyAlertPersonWithNodesResult">
        SELECT
            p.person_id,
            p.name AS person_name,
            p.phone AS person_phone,
            p.status AS person_status,
            p.create_by AS person_create_by,
            p.create_time AS person_create_time,
            p.update_by AS person_update_by,
            p.update_time AS person_update_time,
            p.remark AS person_remark,

            n.node_id AS node_node_id,
            n.node_name AS node_node_name,
            n.parent_id AS node_parent_id,
            n.node_type AS node_node_type,
            n.status AS node_status,
            n.adcd AS node_adcd,
            n.create_by AS node_create_by,
            n.create_time AS node_create_time,
            n.update_by AS node_update_by,
            n.update_time AS node_update_time,
            n.remark AS node_remark,
            panr.post_id as node_post_id,
            np.post_name as node_post_name
        FROM
            sjy_alert_person p
        LEFT JOIN
            sjy_alert_person_node_relation panr ON p.person_id = panr.person_id
        LEFT JOIN
            sjy_alert_node_post np ON np.post_id = panr.post_id
        LEFT JOIN
            sjy_alert_node n ON panr.node_id = n.node_id
        WHERE
            p.person_id = #{personId}
    </select>
    <select id="selectSjyAlertPersonByIds" resultType="com.tzslsjy.business.domain.SjyAlertPerson">
        select * from sjy_alert_person
        where person_id in
        <foreach item="personId" collection="collection" open="(" separator="," close=")">
            #{personId, jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectPersonByIds" resultType="com.tzslsjy.business.domain.SjyAlertPerson">
        select * from sjy_alert_person
        where person_id in
        <foreach item="personId" collection="array" open="(" separator="," close=")">
            #{personId, jdbcType=VARCHAR}
        </foreach>
    </select>
    <select id="selectPersonByNodeIds" resultType="com.tzslsjy.business.domain.SjyAlertPerson">
        select p.*,n.node_name as nodeName,post.post_name as postName from sjy_alert_person p left join sjy_alert_person_node_relation nr on p.person_id=nr.person_id
        left join sjy_alert_node n on nr.node_id = n.node_id
        left join sjy_alert_node_post post on post.post_id = nr.post_id
        <where>
            <if test="nodeIds!=null">
                and   n.node_id in
                <foreach item="nodeId" collection="nodeIds" open="(" separator="," close=")">
                    #{nodeId}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insertSjyAlertPerson" parameterType="SjyAlertPerson">
        insert into sjy_alert_person
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personId != null and personId != ''">person_id,</if>
            <if test="nodeId != null">node_id,</if>
            <if test="name != null">name,</if>
            <if test="phone != null">phone,</if>
            <if test="deptName != null">dept_name,</if>
            <if test="addvcd != null">addvcd,</if>
            <if test="postName != null">post_name,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personId != null and personId != ''">#{personId, jdbcType=VARCHAR},</if>
            <if test="nodeId != null">#{nodeId},</if>
            <if test="name != null">#{name},</if>
            <if test="phone != null">#{phone},</if>
            <if test="deptName != null">#{deptName},</if>
            <if test="addvcd != null">#{addvcd},</if>
            <if test="postName != null">#{postName},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertPerson" parameterType="SjyAlertPerson">
        update sjy_alert_person
        <trim prefix="SET" suffixOverrides=",">
            <if test="nodeId != null">node_id = #{nodeId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="deptName != null">dept_name = #{deptName},</if>
            <if test="addvcd != null">addvcd = #{addvcd},</if>
            <if test="postName != null">post_name = #{postName},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where person_id = #{personId}
    </update>

    <delete id="deleteSjyAlertPersonByPersonId" parameterType="String">
        delete from sjy_alert_person where person_id = #{personId}
    </delete>

    <delete id="deleteSjyAlertPersonByPersonIds" parameterType="String">
        delete from sjy_alert_person where person_id in
        <foreach item="personId" collection="array" open="(" separator="," close=")">
            #{personId, jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
