<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertTemplateRelationMapper">

    <resultMap type="SjyAlertTemplateRelation" id="SjyAlertTemplateRelationResult">
        <result property="id"    column="id"    />
        <result property="templateId"    column="template_id"    />
        <result property="relationId"    column="relation_id"    />
        <result property="relationType"    column="relation_type"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <association property="sjyAlertSmsTemplate" javaType="SjyAlertSmsTemplate">
            <id property="templateId" column="sms_template_id"/>
            <result property="templateName" column="sms_template_name"/>
            <result property="templateContent" column="sms_template_content"/>
        </association>
    </resultMap>

    <sql id="selectSjyAlertTemplateRelationVo">
        select
            satr.id, satr.template_id, satr.relation_id, satr.relation_type, satr.status,
            satr.create_by, satr.create_time, satr.update_by, satr.update_time, satr.remark,
            sast.template_id as sms_template_id,
            sast.template_name as sms_template_name,
            sast.template_content as sms_template_content
        from sjy_alert_template_relation satr
        left join sjy_alert_sms_template sast on satr.template_id = sast.template_id
    </sql>

    <select id="selectSjyAlertTemplateRelationList" parameterType="SjyAlertTemplateRelation" resultMap="SjyAlertTemplateRelationResult">
        <include refid="selectSjyAlertTemplateRelationVo"/>
        <where>
            <if test="templateId != null "> and template_id = #{templateId}</if>
            <if test="relationId != null "> and relation_id = #{relationId}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectSjyAlertTemplateRelationById" parameterType="Long" resultMap="SjyAlertTemplateRelationResult">
        <include refid="selectSjyAlertTemplateRelationVo"/>
        where id = #{id}
    </select>

    <insert id="insertSjyAlertTemplateRelation" parameterType="SjyAlertTemplateRelation" useGeneratedKeys="true" keyProperty="id">
        insert into sjy_alert_template_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null">template_id,</if>
            <if test="relationId != null">relation_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateId != null">#{templateId},</if>
            <if test="relationId != null">#{relationId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertTemplateRelation" parameterType="SjyAlertTemplateRelation">
        update sjy_alert_template_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSjyAlertTemplateRelationById" parameterType="Long">
        delete from sjy_alert_template_relation where id = #{id}
    </delete>

    <delete id="deleteSjyAlertTemplateRelationByIds" parameterType="String">
        delete from sjy_alert_template_relation where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

   <insert id="batchInsert" parameterType="java.util.List">
      insert into sjy_alert_template_relation (template_id, relation_id, relation_type, status, create_by, create_time, update_by, update_time, remark)
     values
       <foreach collection="list" item="item" separator=",">
          (#{item.templateId}, #{item.relationId}, #{item.relationType}, #{item.status},
            #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark})
     </foreach>
      on duplicate key update
        template_id = values(template_id),
        relation_id = values(relation_id),
        relation_type = values(relation_type),
        status = values(status),
        create_by = values(create_by),
        create_time = values(create_time),
        update_by = values(update_by),
        update_time = values(update_time),
        remark = values(remark)
   </insert>

    <delete id="deleteByRelationIdAndType" parameterType="java.util.Map">
     delete from sjy_alert_template_relation
      where relation_id = #{relationId} and relation_type = #{relationType}
  </delete>

</mapper>
