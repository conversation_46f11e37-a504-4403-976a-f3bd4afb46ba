<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertNodePostMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyAlertNodePost" id="SjyAlertNodePostResult">
        <id property="postId" column="post_id" jdbcType="VARCHAR"/>
        <result property="nodeId" column="node_id" jdbcType="VARCHAR"/>
        <result property="postName" column="post_name" jdbcType="VARCHAR"/>
        <result property="sortNum" column="sort_num" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectSjyAlertNodePostVo">
        select post_id, node_id, post_name, sort_num, create_by, create_time, update_by, update_time, remark
        from sjy_alert_node_post
    </sql>

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO sjy_alert_node_post (post_id, node_id, post_name, sort_num, create_by, create_time, update_by, update_time, remark)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.postId, jdbcType=VARCHAR}, #{item.nodeId, jdbcType=VARCHAR}, #{item.postName, jdbcType=VARCHAR}, #{item.sortNum, jdbcType=INTEGER}, #{item.createBy, jdbcType=VARCHAR}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateBy, jdbcType=VARCHAR}, #{item.updateTime, jdbcType=TIMESTAMP}, #{item.remark, jdbcType=VARCHAR})
        </foreach>
    </insert>

    <select id="selectSjyAlertNodePostByNodeId" parameterType="java.lang.String" resultMap="SjyAlertNodePostResult">
        <include refid="selectSjyAlertNodePostVo"/>
        where node_id = #{nodeId, jdbcType=VARCHAR}
    </select>

    <delete id="deleteSjyAlertNodePostByIds" parameterType="java.util.List">
        delete from sjy_alert_node_post where post_id in
        <foreach collection="ids" item="postId" open="(" separator="," close=")">
            #{postId, jdbcType=VARCHAR}
        </foreach>
    </delete>

    <update id="updateSjyAlertNodePost" parameterType="com.tzslsjy.business.domain.SjyAlertNodePost">
        update sjy_alert_node_post
        <set>
            <if test="nodeId != null">node_id = #{nodeId, jdbcType=VARCHAR},</if>
            <if test="postName != null and postName != ''">post_name = #{postName, jdbcType=VARCHAR},</if>
            <if test="sortNum != null">sort_num = #{sortNum, jdbcType=INTEGER},</if>
            <!-- create_by 和 create_time 通常在更新时不修改 -->
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy, jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime, jdbcType=TIMESTAMP},</if>
            <if test="remark != null">remark = #{remark, jdbcType=VARCHAR},</if>
        </set>
        where post_id = #{postId, jdbcType=VARCHAR}
    </update>

</mapper>