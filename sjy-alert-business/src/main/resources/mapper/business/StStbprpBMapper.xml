<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.StStbprpBMapper">

    <resultMap type="StStbprpB" id="StStbprpBResult">
        <result property="stcd"    column="STCD"    />
        <result property="stnm"    column="stnm"    />
        <result property="rvnm"    column="RVNM"    />
        <result property="hnnm"    column="HNNM"    />
        <result property="bsnm"    column="BSNM"    />
        <result property="lgtd"    column="LGTD"    />
        <result property="lttd"    column="LTTD"    />
        <result property="stlc"    column="STLC"    />
        <result property="addvcd"    column="ADDVCD"    />
        <result property="addvcd9"    column="ADDVCD9"    />
        <result property="dtmnm"    column="DTMNM"    />
        <result property="dtmel"    column="DTMEL"    />
        <result property="dtpr"    column="DTPR"    />
        <result property="sttp"    column="STTP"    />
        <result property="item"    column="ITEM"    />
        <result property="frgrd"    column="FRGRD"    />
        <result property="esstym"    column="ESSTYM"    />

        <result property="bgfrym"    column="BGFRYM"    />
        <result property="atcunit"    column="ATCUNIT"    />
        <result property="admauth"    column="ADMAUTH"    />
        <result property="locality"    column="LOCALITY"    />
        <result property="stbk"    column="STBK"    />
        <result property="stazt"    column="STAZT"    />
        <result property="dstrvm"    column="DSTRVM"    />
        <result property="drna"    column="DRNA"    />
        <result property="phcd"    column="PHCD"    />
        <result property="usfl"    column="USFL"    />
        <result property="comments"    column="COMMENTS"    />
        <result property="moditime"    column="MODITIME"    />
        <result property="rainFlag"    column="rain_flag"    />
        <result property="reservoirFlag"    column="reservoir_flag"    />
        
        <collection property="villageStcdRels" ofType="com.tzslsjy.business.domain.SjyRiskVillageStcdRel">
        <result property="adcdNm" column="rel_adcdNm"/>
        <result property="adcd" column="rel_adcd"/>
            <result property="villageId" column="rel_village_id"/>
            <result property="stcd" column="rel_stcd"/>
            <result property="stnm" column="rel_stnm"/>
            <result property="createBy" column="rel_create_by"/>
            <result property="createTime" column="rel_create_time"/>
            <result property="updateBy" column="rel_update_by"/>
            <result property="updateTime" column="rel_update_time"/>
            <result property="remark" column="rel_remark"/>
            <result property="villageType" column="rel_village_type"/>
            <result property="isSameCa" column="rel_is_same_ca"/>
            <result property="positionType" column="rel_position_type"/>
            <result property="positionDistinct" column="rel_position_distinct"/>
        </collection>
    </resultMap>

    <sql id="selectStStbprpBVo">
        select STCD, stnm, RVNM, HNNM, BSNM, LGTD, LTTD, STLC, ADDVCD, ADDVCD9, DTMNM, DTMEL, DTPR, STTP, ITEM, FRGRD, ESSTYM, CLASS, BGFRYM, ATCUNIT, ADMAUTH, LOCALITY, STBK, STAZT, DSTRVM, DRNA, PHCD, USFL, COMMENTS, MODITIME, rain_flag, reservoir_flag from st_stbprp_b
    </sql>

    <select id="selectStStbprpBList" parameterType="StStbprpB" resultMap="StStbprpBResult">
        <include refid="selectStStbprpBVo"/>
        <where>
            <if test="stcd != null and stcd != ''">
                and STCD = #{stcd}
            </if>
            <if test="stnm != null and stnm != ''">
                and stnm like concat('%', #{stnm}, '%')
            </if>
            <if test="addvcd != null and addvcd != ''">
                and ADDVCD = #{addvcd}
            </if>
            <if test="addvcd9 != null and addvcd9 != ''">
                and ADDVCD9 like concat(#{addvcd9}, '%')
            </if>
            <if test="usfl != null and usfl != ''">
                and USFL = #{usfl}
            </if>
            <if test="sttp != null and sttp != ''">
                and STTP = #{sttp}
            </if>
            <if test="rvnm != null and rvnm != ''">
                and RVNM like concat('%', #{rvnm}, '%')
            </if>
            <if test="rainFlag != null and rainFlag != ''">
                and rain_flag = #{rainFlag}
            </if>
            <if test="reservoirFlag != null and reservoirFlag != ''">
                and reservoir_flag = #{reservoirFlag}
            </if>
        </where>
        order by STCD
    </select>

    <select id="selectStStbprpBBySTCD" parameterType="String" resultMap="StStbprpBResult">
        <include refid="selectStStbprpBVo"/>
        where STCD = #{STCD}
    </select>
    <select id="selectByAdcd" resultType="com.tzslsjy.business.domain.StStbprpB">
        select st_stbprp_b.*, comm_city.JURISDICTION as addvcd9Nm from st_stbprp_b left join comm_city on st_stbprp_b.ADDVCD9 = comm_city.JURISDICTION_NUM
        where st_stbprp_b.ADDVCD9 like concat(#{adcd}, '%')
    </select>
    <select id="getStnmStcd" resultType="com.tzslsjy.business.domain.StStbprpB">
        select STCD, stnm from st_stbprp_b where ADDVCD9 like concat(#{adcd}, '%')
        <if test="stnm != null and stnm != ''">
            and stnm like concat('%', #{stnm}, '%')
        </if>
        <if test="STCD != null and STCD != ''">
            and STCD like concat('%', #{STCD}, '%')
        </if>
    </select>
    <select id="selectStStbprpBBySTCDs" resultType="com.tzslsjy.business.domain.StStbprpB">
        select * from st_stbprp_b
        <where>
            <if test="stcds !=null and stcds.size() > 0">
                STCD in
                <foreach item="stcd" collection="stcds" open="(" separator="," close=")">
                    #{stcd}
                </foreach>
            </if>
        </where>

    </select>
    <select id="getRsInfo" resultType="com.tzslsjy.business.vo.ReservoirVo">
        select *,tb3.normz fsdlz,tb3.rsvrtp level,tb6.JURISDICTION as jurisdiction,tb8.JURISDICTION as jurisdiction9, 0
        as status from st_stbprp_b tb1
        <if test="flag == 1">right join st_stbprp_type tb2 on tb2.stcd =tb1.STCD and tb2.type="represent_z"</if>

        <if test="stType !=null">right join st_stbprp_type sttype on sttype.type=#{stType} and sttype.stcd =tb1.STCD
        </if>
        left join comm_city tb6 on tb1.ADDVCD =tb6.JURISDICTION_NUM
        left join comm_city tb8 on tb1.ADDVCD9 =tb8.JURISDICTION_NUM
        left join st_rsvrfcch_b tb3 on tb1.stcd =tb3.stcd
        left join (
        select stcd, bgmd,edmd,fsltdz,fstp,fsltdw from (
        select stcd,row_number()over(partition by stcd order by bgmd desc) rn, bgmd,edmd,fsltdz,fstp,fsltdw
        from st_rsvrfsr_b t <if test="bgmd != null and bgmd != '' ">where BGMD &lt;= #{bgmd} and EDMD >= #{bgmd}</if> )
        as ss where rn =1
        ) tb4 on tb1.stcd =tb4.stcd

        where USFL=1

        <if test="rrFlag == null  ">and sttp in ('RR','RQ')</if>
        <if test="stcd != null and stcd != ''">and tb1.STCD = #{stcd}</if>
        <if test="stcds != null and stcds.size()>0">and tb1.STCD
            <foreach collection="stcds" item="stcd" open="in (" separator="," close=")">
                #{stcd}
            </foreach>
        </if>

        <if test="stnm != null and stnm != ''">and tb1.STNM like CONCAT('%',#{stnm},'%')</if>
        <if test="stnms != null and stnms != ''">and tb1.STNM in
            <foreach collection="stnms" item="stnm" open="(" separator="," close=")">
                #{stnm}
            </foreach>
        </if>

        <if test="addvcd != null and addvcd != '' ">and tb1.ADDVCD9 like concat (#{addvcd},"%")</if>
    </select>

    <insert id="insertStStbprpB" parameterType="StStbprpB">
        insert into st_stbprp_b
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="STCD != null">STCD,</if>
            <if test="stnm != null">stnm,</if>
            <if test="RVNM != null">RVNM,</if>
            <if test="HNNM != null">HNNM,</if>
            <if test="BSNM != null">BSNM,</if>
            <if test="LGTD != null">LGTD,</if>
            <if test="LTTD != null">LTTD,</if>
            <if test="STLC != null">STLC,</if>
            <if test="ADDVCD != null">ADDVCD,</if>
            <if test="ADDVCD9 != null">ADDVCD9,</if>
            <if test="DTMNM != null">DTMNM,</if>
            <if test="DTMEL != null">DTMEL,</if>
            <if test="DTPR != null">DTPR,</if>
            <if test="STTP != null">STTP,</if>
            <if test="ITEM != null">ITEM,</if>
            <if test="FRGRD != null">FRGRD,</if>
            <if test="ESSTYM != null">ESSTYM,</if>
            <if test="CLASS != null">CLASS,</if>
            <if test="BGFRYM != null">BGFRYM,</if>
            <if test="ATCUNIT != null">ATCUNIT,</if>
            <if test="ADMAUTH != null">ADMAUTH,</if>
            <if test="LOCALITY != null">LOCALITY,</if>
            <if test="STBK != null">STBK,</if>
            <if test="STAZT != null">STAZT,</if>
            <if test="DSTRVM != null">DSTRVM,</if>
            <if test="DRNA != null">DRNA,</if>
            <if test="PHCD != null">PHCD,</if>
            <if test="USFL != null">USFL,</if>
            <if test="COMMENTS != null">COMMENTS,</if>
            <if test="MODITIME != null">MODITIME,</if>
            <if test="rainFlag != null">rain_flag,</if>
            <if test="reservoirFlag != null">reservoir_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="STCD != null">#{STCD},</if>
            <if test="stnm != null">#{stnm},</if>
            <if test="RVNM != null">#{RVNM},</if>
            <if test="HNNM != null">#{HNNM},</if>
            <if test="BSNM != null">#{BSNM},</if>
            <if test="LGTD != null">#{LGTD},</if>
            <if test="LTTD != null">#{LTTD},</if>
            <if test="STLC != null">#{STLC},</if>
            <if test="ADDVCD != null">#{ADDVCD},</if>
            <if test="ADDVCD9 != null">#{ADDVCD9},</if>
            <if test="DTMNM != null">#{DTMNM},</if>
            <if test="DTMEL != null">#{DTMEL},</if>
            <if test="DTPR != null">#{DTPR},</if>
            <if test="STTP != null">#{STTP},</if>
            <if test="ITEM != null">#{ITEM},</if>
            <if test="FRGRD != null">#{FRGRD},</if>
            <if test="ESSTYM != null">#{ESSTYM},</if>
            <if test="CLASS != null">#{CLASS},</if>
            <if test="BGFRYM != null">#{BGFRYM},</if>
            <if test="ATCUNIT != null">#{ATCUNIT},</if>
            <if test="ADMAUTH != null">#{ADMAUTH},</if>
            <if test="LOCALITY != null">#{LOCALITY},</if>
            <if test="STBK != null">#{STBK},</if>
            <if test="STAZT != null">#{STAZT},</if>
            <if test="DSTRVM != null">#{DSTRVM},</if>
            <if test="DRNA != null">#{DRNA},</if>
            <if test="PHCD != null">#{PHCD},</if>
            <if test="USFL != null">#{USFL},</if>
            <if test="COMMENTS != null">#{COMMENTS},</if>
            <if test="MODITIME != null">#{MODITIME},</if>
            <if test="rainFlag != null">#{rainFlag},</if>
            <if test="reservoirFlag != null">#{reservoirFlag},</if>
         </trim>
    </insert>

    <update id="updateStStbprpB" parameterType="StStbprpB">
        update st_stbprp_b
        <trim prefix="SET" suffixOverrides=",">
            <if test="stnm != null">stnm = #{stnm},</if>
            <if test="RVNM != null">RVNM = #{RVNM},</if>
            <if test="HNNM != null">HNNM = #{HNNM},</if>
            <if test="BSNM != null">BSNM = #{BSNM},</if>
            <if test="LGTD != null">LGTD = #{LGTD},</if>
            <if test="LTTD != null">LTTD = #{LTTD},</if>
            <if test="STLC != null">STLC = #{STLC},</if>
            <if test="ADDVCD != null">ADDVCD = #{ADDVCD},</if>
            <if test="ADDVCD9 != null">ADDVCD9 = #{ADDVCD9},</if>
            <if test="DTMNM != null">DTMNM = #{DTMNM},</if>
            <if test="DTMEL != null">DTMEL = #{DTMEL},</if>
            <if test="DTPR != null">DTPR = #{DTPR},</if>
            <if test="STTP != null">STTP = #{STTP},</if>
            <if test="ITEM != null">ITEM = #{ITEM},</if>
            <if test="FRGRD != null">FRGRD = #{FRGRD},</if>
            <if test="ESSTYM != null">ESSTYM = #{ESSTYM},</if>
            <if test="CLASS != null">CLASS = #{CLASS},</if>
            <if test="BGFRYM != null">BGFRYM = #{BGFRYM},</if>
            <if test="ATCUNIT != null">ATCUNIT = #{ATCUNIT},</if>
            <if test="ADMAUTH != null">ADMAUTH = #{ADMAUTH},</if>
            <if test="LOCALITY != null">LOCALITY = #{LOCALITY},</if>
            <if test="STBK != null">STBK = #{STBK},</if>
            <if test="STAZT != null">STAZT = #{STAZT},</if>
            <if test="DSTRVM != null">DSTRVM = #{DSTRVM},</if>
            <if test="DRNA != null">DRNA = #{DRNA},</if>
            <if test="PHCD != null">PHCD = #{PHCD},</if>
            <if test="USFL != null">USFL = #{USFL},</if>
            <if test="COMMENTS != null">COMMENTS = #{COMMENTS},</if>
            <if test="MODITIME != null">MODITIME = #{MODITIME},</if>
            <if test="rainFlag != null">rain_flag = #{rainFlag},</if>
            <if test="reservoirFlag != null">reservoir_flag = #{reservoirFlag},</if>
        </trim>
        where STCD = #{STCD}
    </update>
<!-- selectStStbprpBListWithVillage --> 

    <select id="selectStStbprpBListWithVillage" resultMap="StStbprpBResult">
        select st.*,
               rel.id as rel_id,
               rel.village_id as rel_village_id,
               rel.stcd as rel_stcd,
               rel.create_by as rel_create_by,
               rel.create_time as rel_create_time,
               rel.update_by as rel_update_by,
               rel.update_time as rel_update_time,
               rel.remark as rel_remark,
               rel.village_type as rel_village_type,
               rel.is_same_ca as rel_is_same_ca,
               rel.position_type as rel_position_type,
               rel.position_distinct as rel_position_distinct,
               rel.adcd as rel_adcd,
               IFNULL(village.village_admin, cc.JURISDICTION) as rel_adcdNm
        from st_stbprp_b st
        left join sjy_alert.sjy_risk_village_stcd_rel rel on st.STCD = rel.STCD
        left join sjy_risk_village_info village on rel.village_id = village.id
        left join comm_city cc on rel.adcd = cc.JURISDICTION_NUM
        <where>
            <if test="stcd != null and stcd != ''">
                and st.STCD = #{stcd}
            </if>
            <if test="stnm != null and stnm != ''">
                and st.stnm like concat('%', #{stnm}, '%')
            </if>
            <if test="addvcd != null and addvcd != ''">
                and st.ADDVCD = #{addvcd}
            </if>
            <if test="addvcd9 != null and addvcd9 != ''">
                and st.ADDVCD9 like concat(#{addvcd9}, '%')
            </if>
            <if test="usfl != null and usfl != ''">
                and st.USFL = #{usfl}
            </if>
        </where>
        order by st.STCD
    </select>

    <!-- 查询测站基础信息列表（用于村庄关联查询的第一步，支持分页） -->
    <select id="selectStStbprpBListForVillage" parameterType="StStbprpB" resultMap="StStbprpBResult">
        <include refid="selectStStbprpBVo"/>
        <where>
            <if test="stcd != null and stcd != ''">
                and STCD = #{stcd}
            </if>
            <if test="stnm != null and stnm != ''">
                and stnm like concat('%', #{stnm}, '%')
            </if>
            <if test="addvcd != null and addvcd != ''">
                and ADDVCD = #{addvcd}
            </if>
            <if test="addvcd9 != null and addvcd9 != ''">
                and ADDVCD9 like concat(#{addvcd9}, '%')
            </if>
            <if test="usfl != null and usfl != ''">
                and USFL = #{usfl}
            </if>
            <if test="sttp != null and sttp != ''">
                and STTP = #{sttp}
            </if>
            <if test="rvnm != null and rvnm != ''">
                and RVNM like concat('%', #{rvnm}, '%')
            </if>
            <if test="rainFlag != null and rainFlag != ''">
                and rain_flag = #{rainFlag}
            </if>
            <if test="reservoirFlag != null and reservoirFlag != ''">
                and reservoir_flag = #{reservoirFlag}
            </if>
        </where>
        order by STCD
    </select>
    <select id="getRvInfo" resultType="com.tzslsjy.business.vo.RiverVo">
        select *, 0 as status ,tb8.JURISDICTION as jurisdiction9
        <if test="addvcd!=null and addvcd=='331081'">
            ,tb2.type as stType
        </if>
        from st_stbprp_b tb1
        <!--        <if test="flag == 1">right join st_stbprp_type tb2 on tb2.stcd =tb1.STCD and tb2.type="represent_z"</if>-->
        <if test="flag != 1 and addvcd!=null and addvcd=='331081'">left join st_stbprp_type tb2 on tb2.stcd =tb1.STCD and tb2.type="represent_z_${addvcd}"</if>
        <if test="flag == 1 and addvcd!=null and addvcd!='' ">right join st_stbprp_type tb2 on tb2.stcd =tb1.STCD and tb2.type="represent_z_${addvcd}"</if>
        <if test="flag == 1 and (addvcd=='' or addvcd==null) ">right join st_stbprp_type tb2 on tb2.stcd =tb1.STCD and tb2.type="represent_z"</if>
        <if test="stType !=null">right join st_stbprp_type sttype on sttype.type=#{stType} and sttype.stcd =tb1.STCD
        </if>
        <if test="basinCode != null || basinLevel != null ">left join sjy_rl_st_basin tb4 on tb1.stcd =tb4.stcd and
            tb4.basin_level=1
        </if>
        left join st_rvfcch_b tb3 on tb1.stcd =tb3.stcd
        left join comm_city tb6 on tb1.ADDVCD =tb6.JURISDICTION_NUM
        left join comm_city tb8 on tb1.ADDVCD9 =tb8.JURISDICTION_NUM
        <!--        <if test="addvcd!=null and addvcd=='331081'">-->
        <!--            left join st_stbprp_type tb7 on tb7.stcd = tb1.stcd and tb7.type = 'represent_z_331081'-->
        <!--        </if>-->

        where tb1.USFL=1
        <choose>
            <when test="zFlag != null and zFlag == 'ZG'">
                and tb1.sttp in ('ZG')
            </when>
            <when test="zFlag != null and zFlag == 'ZZ'">
                and tb1.sttp in ('ZZ', 'ZQ')
            </when>
            <otherwise>
                <choose>
                    <when test="containFlag != null and containFlag == 1">
                        and tb1.sttp in ('ZZ', 'ZQ', 'ZG', 'DD')
                    </when>
                    <otherwise>
                        and tb1.sttp in ('ZZ', 'ZQ', 'ZG' )
                    </otherwise>
                </choose>
            </otherwise>
        </choose>

        <if test="stcd != null and stcd != ''">and tb1.STCD = #{stcd}</if>
        <if test="stcds !=null and stcds.size()>0">
            and tb1.stcd in
            <foreach collection="stcds" item="stcd" open="(" close=")" separator=",">
                #{stcd}
            </foreach>
        </if>
        <if test="stnm != null and stnm != ''">and tb1.STNM like CONCAT('%',#{stnm},'%')</if>
        <if test="lgtd != null and lgtd != '' ">and tb1.LGTD = #{lgtd}</if>
        <if test="lttd != null and lttd != '' ">and tb1.LTTD = #{lttd}</if>
        <if test="addvcd != null and addvcd != '' ">and tb1.ADDVCD9 like CONCAT( #{addvcd},"%")</if>
        <if test="basinCode != null and basinCode != '' ">and tb4.basin_code = #{basinCode}</if>
        <if test="basinLevel != null and basinLevel != '' ">and tb4.basin_level = #{basinLevel}</if>
    </select>
    <delete id="deleteStStbprpBBySTCD" parameterType="String">
        delete from st_stbprp_b where STCD = #{STCD}
    </delete>

    <delete id="deleteStStbprpBBySTCDs" parameterType="String">
        delete from st_stbprp_b where STCD in
        <foreach item="STCD" collection="array" open="(" separator="," close=")">
            #{STCD}
        </foreach>
    </delete>

    <select id="selectAllWaterStationStcds" resultType="java.lang.String">
        SELECT DISTINCT t1.stcd
        FROM st_stbprp_b t1
        INNER JOIN st_stbprp_type t2 ON t1.stcd = t2.stcd
        WHERE t1.usfl = '1' AND (t2.type = 'Z' OR t2.type = 'z')
    </select>

    <select id="selectAllRainfallStationStcds" resultType="java.lang.String">
        SELECT DISTINCT t1.stcd
        FROM st_stbprp_b t1
        INNER JOIN st_stbprp_type t2 ON t1.stcd = t2.stcd
        WHERE t1.usfl = '1' AND (t2.type = 'P' OR t2.type = 'p')
    </select>
</mapper>
