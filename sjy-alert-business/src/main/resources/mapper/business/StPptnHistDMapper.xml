<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.StPptnHistDMapper">

    <resultMap type="com.tzslsjy.business.domain.StPptnHistD" id="StPptnHistDResult">
                    <result property="stcd" column="stcd"/>
                    <result property="tm" column="tm"/>
                    <result property="drp" column="drp"/>
                    <result property="stnm" column="stnm"/>
            </resultMap>
    <insert id="insertOrUpdateBatch">
        insert into st_pptn_hist_d( tm,drp,stnm,stcd,month,day,addvcd,type
        ) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.tm},#{item.drp},#{item.stnm},#{item.stcd},#{item.month},#{item.day},#{item.addvcd},#{item.type} )
        </foreach>
        ON DUPLICATE KEY UPDATE

        `tm` = values(`tm`),
        `drp` = values(`drp`),
        `stnm` = values(`stnm`),
        `stcd` = values(`stcd`),
        `month` = values(`month`),
        `day` = values(`day`),
        `addvcd` = values(`addvcd`),
        `type` = values(`type`)
    </insert>
    <select id="selectSumByAllMonthAndDay" resultType="com.tzslsjy.business.bo.StPptnHistRespVo">
        select
        sum(drp) as samePeriodSum
        ,MIN(stcd) stcd,stnm stnm,MIN(addvcd) addvcd

        from st_pptn_hist_d
        <where>
            <if test="startMonth != null  and startDay != null">
                and (month, day) >= (#{startMonth},#{startDay})
            </if>
            <if test="endMonth != null and endDay != null">
                and (month, day) &lt;= (#{endMonth},#{endDay})
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="addvcd != null and addvcd != null">
                and addvcd = #{addvcd}
            </if>
            <if test="stcd != null and stcd != null">
                and stcd = #{stcd}
            </if>
        </where>
        group by stnm
    </select>
    <select id="selectSumByMonthAndDay" resultType="com.tzslsjy.business.bo.StPptnHistRespVo">
        select
        sum(drp) as samePeriodSum
        ,MIN(stcd) stcd,stnm stnm,MIN(addvcd) addvcd

        from st_pptn_hist_d
        <where>
        <if test="startMonth != null  and startDay != null">
           and (month, day) > (#{startMonth},#{startDay})
        </if>
        <if test="endMonth != null and endDay != null">
            and (month, day) &lt;= (#{endMonth},#{endDay})
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
            <if test="addvcd != null and addvcd != null">
                and addvcd = #{addvcd}
            </if>
            <if test="stcd != null and stcd != null">
                and stcd = #{stcd}
            </if>
        </where>
        group by stnm
    </select>
    <select id="selectByMonthAndDay" resultType="com.tzslsjy.business.bo.StPptnHistRainLineRespVo">
        select
        drp samePeriodDrp,stcd,stnm,addvcd,month,day

        from st_pptn_hist_d
        <where>
        <if test="startMonth != null  and startDay != null">
           and (month, day) >= (#{startMonth},#{startDay})
        </if>
        <if test="endMonth != null and endDay != null">
            and (month, day) &lt;= (#{endMonth},#{endDay})
        </if>
        <if test="type != null">
            and type = #{type}
        </if>
            <if test="addvcd != null and addvcd!='' ">
                and addvcd = #{addvcd}
            </if>
            <if test="stcd != null and stcd!='' ">
                and stcd = #{stcd}
            </if>
        </where>

    </select>


</mapper>
