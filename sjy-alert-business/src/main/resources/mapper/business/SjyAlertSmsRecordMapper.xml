<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertSmsRecordMapper">

    <resultMap type="SjyAlertSmsRecord" id="SjyAlertSmsRecordResult">
        <result property="alertSmsId"    column="alert_sms_id"    />
        <result property="personId"    column="person_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="alertLevel"    column="alert_level"    />
        <result property="content"    column="content"    />
        <result property="sendType"    column="send_type"    />
        <result property="smsType"    column="sms_type"    />
        <result property="moduleId"    column="module_id"    />
        <result property="alertRecordIds"    column="alert_record_ids"    />
        <result property="status"    column="status"    />
        <result property="sendTime"    column="send_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyAlertSmsRecordVo">
        select alert_sms_id, person_id, rule_id, alert_level, content, send_type, sms_type, module_id, alert_record_ids, status, send_time, create_by, create_time, update_by, update_time, remark from sjy_alert_sms_record
    </sql>

    <select id="selectSjyAlertSmsRecordList" parameterType="SjyAlertSmsRecord" resultMap="SjyAlertSmsRecordResult">
        <include refid="selectSjyAlertSmsRecordVo"/>
        <where>
            <if test="personId != null "> and person_id = #{personId}</if>
            <if test="ruleId != null  and ruleId != ''"> and rule_id = #{ruleId}</if>
            <if test="alertLevel != null "> and alert_level = #{alertLevel}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="sendType != null  and sendType != ''"> and send_type = #{sendType}</if>
            <if test="smsType != null  and smsType != ''"> and sms_type = #{smsType}</if>
            <if test="moduleId != null "> and module_id = #{moduleId}</if>
            <if test="alertRecordIds != null  and alertRecordIds != ''"> and alert_record_ids = #{alertRecordIds}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="sendTime != null "> and send_time = #{sendTime}</if>
        </where>
    </select>

    <select id="selectSjyAlertSmsRecordByAlertSmsId" parameterType="Long" resultMap="SjyAlertSmsRecordResult">
        <include refid="selectSjyAlertSmsRecordVo"/>
        where alert_sms_id = #{alertSmsId}
    </select>

    <select id="selectSjyAlertSmsRecordsByTimeRange" resultMap="SjyAlertSmsRecordResult">
        <include refid="selectSjyAlertSmsRecordVo"/>
        where create_time >= #{startTime}
        order by create_time desc
    </select>

    <insert id="insertSjyAlertSmsRecord" parameterType="SjyAlertSmsRecord" useGeneratedKeys="true" keyProperty="alertSmsId">
        insert into sjy_alert_sms_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personId != null">person_id,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="alertLevel != null">alert_level,</if>
            <if test="content != null">content,</if>
            <if test="sendType != null">send_type,</if>
            <if test="smsType != null">sms_type,</if>
            <if test="moduleId != null">module_id,</if>
            <if test="alertRecordIds != null">alert_record_ids,</if>
            <if test="status != null">status,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personId != null">#{personId},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="alertLevel != null">#{alertLevel},</if>
            <if test="content != null">#{content},</if>
            <if test="sendType != null">#{sendType},</if>
            <if test="smsType != null">#{smsType},</if>
            <if test="moduleId != null">#{moduleId},</if>
            <if test="alertRecordIds != null">#{alertRecordIds},</if>
            <if test="status != null">#{status},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertSmsRecord" parameterType="SjyAlertSmsRecord">
        update sjy_alert_sms_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="personId != null">person_id = #{personId},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="alertLevel != null">alert_level = #{alertLevel},</if>
            <if test="content != null">content = #{content},</if>
            <if test="sendType != null">send_type = #{sendType},</if>
            <if test="smsType != null">sms_type = #{smsType},</if>
            <if test="moduleId != null">module_id = #{moduleId},</if>
            <if test="alertRecordIds != null">alert_record_ids = #{alertRecordIds},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where alert_sms_id = #{alertSmsId}
    </update>

    <delete id="deleteSjyAlertSmsRecordByAlertSmsId" parameterType="Long">
        delete from sjy_alert_sms_record where alert_sms_id = #{alertSmsId}
    </delete>

    <delete id="deleteSjyAlertSmsRecordByAlertSmsIds" parameterType="String">
        delete from sjy_alert_sms_record where alert_sms_id in
        <foreach item="alertSmsId" collection="array" open="(" separator="," close=")">
            #{alertSmsId}
        </foreach>
    </delete>

    <resultMap type="com.tzslsjy.business.domain.vo.SjyAlertSmsStatsVO" id="SjyAlertSmsStatsResult">
        <result property="content" column="content"/>
        <result property="alertSmsId" column="alert_sms_id"/>
        <result property="moduleName" column="module_name"/>
        <result property="messageType" column="message_type"/>
        <result property="sendTime" column="send_time"/>
        <result property="sendType" column="send_type"/>
        <result property="successCount" column="success_count"/>
        <result property="failCount" column="fail_count"/>
        <result property="pendingCount" column="pending_count"/>
    </resultMap>

    <select id="selectSjyAlertSmsStats" parameterType="com.tzslsjy.business.domain.SjyAlertSmsStatsRequest" resultMap="SjyAlertSmsStatsResult">
        SELECT
        sr.alert_sms_id as alert_sms_id,
            sr.content AS content,
            art.type_name AS module_name,
            ar.rule_name AS message_type,
            sr.send_time AS send_time,
            sr.send_way AS sendType,
            SUM(CASE WHEN rr.status = 1 THEN 1 ELSE 0 END) AS success_count,
            SUM(CASE WHEN rr.status = 2 THEN 1 ELSE 0 END) AS fail_count,
            SUM(CASE WHEN rr.status = 0 or rr.status is null THEN 1 ELSE 0 END) AS pending_count
        FROM
            sjy_alert_sms_record sr
        JOIN
            sjy_alert_rule ar ON sr.rule_id = ar.rule_id
        JOIN
            sjy_alert_rule_type art ON ar.rule_type_id = art.rule_type_id
        LEFT JOIN
        sjy_alert_receive_record rr ON rr.sms_id = sr.alert_sms_id
        WHERE
            1 = 1
            <if test="sendStartTime != null">
                AND sr.send_time >= #{sendStartTime}
            </if>
            <if test="ruleTypeId != null">
                AND ar.rule_type_id = #{ruleTypeId}
            </if>
            <if test="sendEndTime != null">
                AND sr.send_time &lt;= #{sendEndTime}
            </if>
            <if test="sendType != null and sendType != ''">
                AND sr.send_way = #{sendType}
            </if>
            <if test="smsType != null and smsType != ''">
                AND sr.sms_type = #{smsType}
            </if>
            <if test="moduleId != null">
                AND sr.module_id = #{moduleId}
            </if>
            <if test="content != null and content != ''">
                AND sr.content LIKE CONCAT('%', #{content}, '%')
            </if>
        GROUP BY
        sr.alert_sms_id,sr.content, art.type_name, ar.rule_name, sr.send_time, sr.send_way
        order by sr.create_time desc
    </select>
    <select id="alertCount" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM sjy_alert_sms_record sr
        JOIN
        sjy_alert_rule ar ON sr.rule_id = ar.rule_id
        <where>

            <if test="ruleTypeId != null  and ruleTypeId != ''"> and ar.rule_type_id = #{ruleTypeId}</if>
            <if test="startTm != null "> and sr.send_time >= #{startTm}</if>
            <if test="endTm != null "> and sr.send_time &lt;= #{endTm}</if>
        </where>
    </select>


</mapper>
