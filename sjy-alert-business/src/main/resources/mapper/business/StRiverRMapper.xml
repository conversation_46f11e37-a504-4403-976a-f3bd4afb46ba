<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.StRiverRMapper">

    <resultMap type="StRiverR" id="StRiverRResult">
        <result property="STCD"    column="STCD"    />
        <result property="TM"    column="TM"    />
        <result property="Z"    column="Z"    />
        <result property="Q"    column="Q"    />
        <result property="XSA"    column="XSA"    />
        <result property="XSAVV"    column="XSAVV"    />
        <result property="XSMXV"    column="XSMXV"    />
        <result property="FLWCHRCD"    column="FLWCHRCD"    />
        <result property="WPTN"    column="WPTN"    />
        <result property="MSQMT"    column="MSQMT"    />
        <result property="MSAMT"    column="MSAMT"    />
        <result property="MSVMT"    column="MSVMT"    />
        <result property="MODITIME"    column="MODITIME"    />
        <result property="EDITED"    column="EDITED"    />
        <result property="bFlag"    column="B_Flag"    />
    </resultMap>

    <sql id="selectStRiverRVo">
        select STCD, TM, Z, Q, XSA, XSAVV, XSMXV, FLWCHRCD, WPTN, MSQMT, MSAMT, MSVMT, MODITIME, EDITED, B_Flag from st_river_r
    </sql>

    <select id="selectStRiverRList" parameterType="StRiverR" resultMap="StRiverRResult">
        <include refid="selectStRiverRVo"/>
        <where>
            <if test="Z != null "> and Z = #{Z}</if>
            <if test="Q != null "> and Q = #{Q}</if>
            <if test="XSA != null "> and XSA = #{XSA}</if>
            <if test="XSAVV != null "> and XSAVV = #{XSAVV}</if>
            <if test="XSMXV != null "> and XSMXV = #{XSMXV}</if>
            <if test="FLWCHRCD != null  and FLWCHRCD != ''"> and FLWCHRCD = #{FLWCHRCD}</if>
            <if test="WPTN != null  and WPTN != ''"> and WPTN = #{WPTN}</if>
            <if test="MSQMT != null  and MSQMT != ''"> and MSQMT = #{MSQMT}</if>
            <if test="MSAMT != null  and MSAMT != ''"> and MSAMT = #{MSAMT}</if>
            <if test="MSVMT != null  and MSVMT != ''"> and MSVMT = #{MSVMT}</if>
            <if test="MODITIME != null "> and MODITIME = #{MODITIME}</if>
            <if test="EDITED != null  and EDITED != ''"> and EDITED = #{EDITED}</if>
            <if test="bFlag != null  and bFlag != ''"> and B_Flag = #{bFlag}</if>
        </where>
    </select>

    <select id="selectStRiverRBySTCD" parameterType="String" resultMap="StRiverRResult">
        <include refid="selectStRiverRVo"/>
        where STCD = #{STCD}
    </select>
    <select id="selectLastestStRiverRBySTCD" resultType="com.tzslsjy.business.domain.StRiverR">
        select * from st_river_r where stcd = #{stcd} and tm>=#{startTm} and tm &lt;=#{endTm} order by TM desc limit 1
    </select>
    <select id="selectLastestValBySTCD" resultType="java.math.BigDecimal">
        select Z from st_river_r where stcd = #{stcd} order by TM desc limit 1
    </select>

    <insert id="insertStRiverR" parameterType="StRiverR">
        insert into st_river_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="STCD != null">STCD,</if>
            <if test="TM != null">TM,</if>
            <if test="Z != null">Z,</if>
            <if test="Q != null">Q,</if>
            <if test="XSA != null">XSA,</if>
            <if test="XSAVV != null">XSAVV,</if>
            <if test="XSMXV != null">XSMXV,</if>
            <if test="FLWCHRCD != null">FLWCHRCD,</if>
            <if test="WPTN != null">WPTN,</if>
            <if test="MSQMT != null">MSQMT,</if>
            <if test="MSAMT != null">MSAMT,</if>
            <if test="MSVMT != null">MSVMT,</if>
            <if test="MODITIME != null">MODITIME,</if>
            <if test="EDITED != null">EDITED,</if>
            <if test="bFlag != null">B_Flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="STCD != null">#{STCD},</if>
            <if test="TM != null">#{TM},</if>
            <if test="Z != null">#{Z},</if>
            <if test="Q != null">#{Q},</if>
            <if test="XSA != null">#{XSA},</if>
            <if test="XSAVV != null">#{XSAVV},</if>
            <if test="XSMXV != null">#{XSMXV},</if>
            <if test="FLWCHRCD != null">#{FLWCHRCD},</if>
            <if test="WPTN != null">#{WPTN},</if>
            <if test="MSQMT != null">#{MSQMT},</if>
            <if test="MSAMT != null">#{MSAMT},</if>
            <if test="MSVMT != null">#{MSVMT},</if>
            <if test="MODITIME != null">#{MODITIME},</if>
            <if test="EDITED != null">#{EDITED},</if>
            <if test="bFlag != null">#{bFlag},</if>
         </trim>
    </insert>

    <update id="updateStRiverR" parameterType="StRiverR">
        update st_river_r
        <trim prefix="SET" suffixOverrides=",">
            <if test="TM != null">TM = #{TM},</if>
            <if test="Z != null">Z = #{Z},</if>
            <if test="Q != null">Q = #{Q},</if>
            <if test="XSA != null">XSA = #{XSA},</if>
            <if test="XSAVV != null">XSAVV = #{XSAVV},</if>
            <if test="XSMXV != null">XSMXV = #{XSMXV},</if>
            <if test="FLWCHRCD != null">FLWCHRCD = #{FLWCHRCD},</if>
            <if test="WPTN != null">WPTN = #{WPTN},</if>
            <if test="MSQMT != null">MSQMT = #{MSQMT},</if>
            <if test="MSAMT != null">MSAMT = #{MSAMT},</if>
            <if test="MSVMT != null">MSVMT = #{MSVMT},</if>
            <if test="MODITIME != null">MODITIME = #{MODITIME},</if>
            <if test="EDITED != null">EDITED = #{EDITED},</if>
            <if test="bFlag != null">B_Flag = #{bFlag},</if>
        </trim>
        where STCD = #{STCD}
    </update>

    <delete id="deleteStRiverRBySTCD" parameterType="String">
        delete from st_river_r where STCD = #{STCD}
    </delete>

    <delete id="deleteStRiverRBySTCDs" parameterType="String">
        delete from st_river_r where STCD in
        <foreach item="STCD" collection="array" open="(" separator="," close=")">
            #{STCD}
        </foreach>
    </delete>

    <!-- selectExistStcdsByStcds --> 

    <select id="selectExistStcdsByStcds" resultType="java.lang.String">
        select stcd from st_river_r 
        <where>
            <if test="stcds != null">
                stcd in
                <foreach item="stcd" collection="stcds" open="(" separator="," close=")">
                    #{stcd}
                </foreach>
            </if>
            and tm &gt;= #{lastTongTime} and tm &lt;= #{currentTime}
        </where>
    </select>
</mapper>