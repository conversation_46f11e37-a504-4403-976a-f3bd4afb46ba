<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyRiskVillageStcdRelMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyRiskVillageStcdRel" id="SjyRiskVillageStcdRelResult">
                    <result property="id" column="id"/>
                    <result property="villageId" column="village_id"/>
                    <result property="stcd" column="stcd"/>
                    <result property="adcd" column="adcd"/>
                    <result property="adcdNm" column="adcdNm"/>
                    <result property="stnm" column="stnm"/>
                    <result property="createBy" column="create_by"/>
                    <result property="createTime" column="create_time"/>
                    <result property="updateBy" column="update_by"/>
                    <result property="updateTime" column="update_time"/>
                    <result property="remark" column="remark"/>
                    <result property="villageType" column="village_type"/>
                    <result property="isSameCa" column="is_same_ca"/>
                    <result property="positionType" column="position_type"/>
                    <result property="positionDistinct" column="position_distinct"/>
            </resultMap>
    <select id="selectByVillageId" resultType="com.tzslsjy.business.domain.SjyRiskVillageStcdRel">
        SELECT * from sjy_risk_village_stcd_rel WHERE village_id = #{villageId}

    </select>

    <sql id="selectSjyRiskVillageStcdRelVo">
        select id, village_id, stcd, adcd, create_by, create_time, update_by, update_time, remark, village_type, is_same_ca, position_type, position_distinct from sjy_risk_village_stcd_rel
    </sql>

    <!-- 根据测站编码列表批量查询村庄关联信息 -->
    <select id="selectBatchByStcds" resultMap="SjyRiskVillageStcdRelResult">
        select rel.id, rel.village_id, rel.stcd, rel.adcd, rel.create_by, rel.create_time,
               rel.update_by, rel.update_time, rel.remark, rel.village_type, rel.is_same_ca,
               rel.position_type, rel.position_distinct,
         IFNULL(villageA.village_natural, cc.JURISDICTION)  as adcdNm,
               st.stnm
        from sjy_risk_village_stcd_rel rel
        left join lh_water_basic_info.comm_city cc on rel.adcd = cc.JURISDICTION_NUM
        left join lh_water_basic_info.sjy_risk_village_info villageA on villageA.cadcd = rel.adcd
        left join lh_water_basic.st_stbprp_b st on rel.stcd = st.STCD
        <where>
            <if test="stcds != null and stcds.size() > 0">
                and rel.stcd in
                <foreach collection="stcds" item="stcd" open="(" separator="," close=")">
                    #{stcd}
                </foreach>
            </if>
        </where>
        order by rel.stcd, rel.id
    </select>

</mapper>