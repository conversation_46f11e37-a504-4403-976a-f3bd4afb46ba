<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper">

    <resultMap type="SjyAlertSmsTemplate" id="SjyAlertSmsTemplateResult">
        <result property="templateId"    column="template_id"    />
        <result property="ruleTypeId"    column="rule_type_id"    />
        <result property="templateName"    column="template_name"    />
        <result property="templateContent"    column="template_content"    />
        <result property="listFormat"    column="list_format"    />
        <result property="listSeparator"    column="list_separator"    />
        <result property="displayNum"    column="display_num"    />
        <result property="omitSymbol"    column="omit_symbol"    />
        <result property="status"    column="status"    />
        <result property="izMerge"    column="iz_merge"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyAlertSmsTemplateVo">
        select template_id, rule_type_id, template_name, template_content, list_format, list_separator, display_num, omit_symbol, status, iz_merge, create_by, create_time, update_by, update_time, remark from sjy_alert_sms_template
    </sql>

    <select id="selectSjyAlertSmsTemplateList" parameterType="SjyAlertSmsTemplate" resultMap="SjyAlertSmsTemplateResult">
        <include refid="selectSjyAlertSmsTemplateVo"/>
        <where>
            <if test="ruleTypeId != null "> and rule_type_id = #{ruleTypeId}</if>
            <if test="templateName != null  and templateName != ''"> and template_name like concat('%', #{templateName}, '%')</if>
            <if test="templateContent != null  and templateContent != ''"> and template_content = #{templateContent}</if>
            <if test="listFormat != null  and listFormat != ''"> and list_format = #{listFormat}</if>
            <if test="listSeparator != null  and listSeparator != ''"> and list_separator = #{listSeparator}</if>
            <if test="displayNum != null "> and display_num = #{displayNum}</if>
            <if test="omitSymbol != null  and omitSymbol != ''"> and omit_symbol = #{omitSymbol}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="izMerge != null "> and iz_merge = #{izMerge}</if>
        </where>
    </select>

    <select id="selectSjyAlertSmsTemplateByTemplateId" parameterType="Long" resultMap="SjyAlertSmsTemplateResult">
        <include refid="selectSjyAlertSmsTemplateVo"/>
        where template_id = #{templateId}
    </select>
    <select id="selectOneByTypeAndRuleId" resultType="com.tzslsjy.business.domain.SjyAlertSmsTemplate">
        select tb1.* from sjy_alert_sms_template tb1 right join sjy_alert_template_relation tb2 on tb1.template_id = tb2.template_id
        where
            <if test="types != null and types != ''">
                find_in_set(tb1.type, #{types}) and
            </if>
               tb2.relation_id = #{ruleId} and tb2.relation_type = 1
    </select>
    <select id="selectOneByRelId" resultType="com.tzslsjy.business.domain.SjyAlertSmsTemplate">
        select tb1.* from sjy_alert_sms_template tb1 right join sjy_alert_template_relation tb2 on tb1.template_id = tb2.template_id
        where

               tb2.relation_id = #{relId} and tb2.relation_type = #{relType}
    </select>

    <insert id="insertSjyAlertSmsTemplate" parameterType="SjyAlertSmsTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into sjy_alert_sms_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleTypeId != null">rule_type_id,</if>
            <if test="templateName != null">template_name,</if>
            <if test="templateContent != null">template_content,</if>
            <if test="listFormat != null">list_format,</if>
            <if test="listSeparator != null">list_separator,</if>
            <if test="displayNum != null">display_num,</if>
            <if test="omitSymbol != null">omit_symbol,</if>
            <if test="status != null">status,</if>
            <if test="izMerge != null">iz_merge,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleTypeId != null">#{ruleTypeId},</if>
            <if test="templateName != null">#{templateName},</if>
            <if test="templateContent != null">#{templateContent},</if>
            <if test="listFormat != null">#{listFormat},</if>
            <if test="listSeparator != null">#{listSeparator},</if>
            <if test="displayNum != null">#{displayNum},</if>
            <if test="omitSymbol != null">#{omitSymbol},</if>
            <if test="status != null">#{status},</if>
            <if test="izMerge != null">#{izMerge},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertSmsTemplate" parameterType="SjyAlertSmsTemplate">
        update sjy_alert_sms_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleTypeId != null">rule_type_id = #{ruleTypeId},</if>
            <if test="templateName != null">template_name = #{templateName},</if>
            <if test="templateContent != null">template_content = #{templateContent},</if>
            <if test="listFormat != null">list_format = #{listFormat},</if>
            <if test="listSeparator != null">list_separator = #{listSeparator},</if>
            <if test="displayNum != null">display_num = #{displayNum},</if>
            <if test="omitSymbol != null">omit_symbol = #{omitSymbol},</if>
            <if test="status != null">status = #{status},</if>
            <if test="izMerge != null">iz_merge = #{izMerge},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where template_id = #{templateId}
    </update>

    <delete id="deleteSjyAlertSmsTemplateByTemplateId" parameterType="Long">
        delete from sjy_alert_sms_template where template_id = #{templateId}
    </delete>

    <delete id="deleteSjyAlertSmsTemplateByTemplateIds" parameterType="String">
        delete from sjy_alert_sms_template where template_id in
        <foreach item="templateId" collection="array" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>
</mapper>
