<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyFdProneAreaMapper">
    
    <resultMap type="SjyFdProneArea" id="SjyFdProneAreaResult">
        <result property="prevCode"    column="prev_code"    />
        <result property="adminAdnm"    column="admin_adnm"    />
        <result property="naturalAdnm"    column="natural_adnm"    />
        <result property="fldarea"    column="fldarea"    />
        <result property="htcount"    column="htcount"    />
        <result property="pcount"    column="pcount"    />
        <result property="lgtd"    column="lgtd"    />
        <result property="lttd"    column="lttd"    />
        <result property="prevtpName"    column="prevtp_name"    />
        <result property="isAdded"    column="is_added"    />
        <result property="risktp"    column="risktp"    />
        <result property="adcd"    column="adcd"    />
        <result property="tongTime"    column="tong_time"    />
        <result property="townName"    column="town_name"    />
        <result property="countyName"    column="county_name"    />
        <result property="cityName"    column="city_name"    />
        <result property="drna"    column="drna"    />
        <result property="floodZ"    column="flood_z"    />
        <result property="floodQ"    column="flood_q"    />
        <result property="riverLen"    column="river_len"    />
        <result property="riverBit"    column="river_bit"    />
        <result property="roughness"    column="roughness"    />
        <result property="fiveQ"    column="five_q"    />
        <result property="tenQ"    column="ten_q"    />
        <result property="twentyQ"    column="twenty_q"    />
        <result property="fiftyQ"    column="fifty_q"    />
        <result property="hundredQ"    column="hundred_q"    />
        <result property="gcount"    column="gcount"    />
    </resultMap>

    <sql id="selectSjyFdProneAreaVo">
        select prev_code, admin_adnm, natural_adnm, fldarea, htcount, pcount, lgtd, lttd, prevtp_name, is_added, risktp, adcd, tong_time, town_name, county_name, city_name, drna, flood_z, flood_q, river_len, river_bit, roughness, five_q, ten_q, twenty_q, fifty_q, hundred_q, gcount from sjy_fd_prone_area
    </sql>

    <select id="selectSjyFdProneAreaList" parameterType="SjyFdProneArea" resultMap="SjyFdProneAreaResult">
        <include refid="selectSjyFdProneAreaVo"/>
        <where>  
            <if test="adminAdnm != null  and adminAdnm != ''"> and admin_adnm = #{adminAdnm}</if>
            <if test="naturalAdnm != null  and naturalAdnm != ''"> and natural_adnm = #{naturalAdnm}</if>
            <if test="fldarea != null "> and fldarea = #{fldarea}</if>
            <if test="htcount != null "> and htcount = #{htcount}</if>
            <if test="pcount != null "> and pcount = #{pcount}</if>
            <if test="lgtd != null "> and lgtd = #{lgtd}</if>
            <if test="lttd != null "> and lttd = #{lttd}</if>
            <if test="prevtpName != null  and prevtpName != ''"> and prevtp_name like concat('%', #{prevtpName}, '%')</if>
            <if test="isAdded != null  and isAdded != ''"> and is_added = #{isAdded}</if>
            <if test="risktp != null  and risktp != ''"> and risktp = #{risktp}</if>
            <if test="adcd != null  and adcd != ''"> and adcd = #{adcd}</if>
            <if test="tongTime != null "> and tong_time = #{tongTime}</if>
            <if test="townName != null  and townName != ''"> and town_name like concat('%', #{townName}, '%')</if>
            <if test="countyName != null  and countyName != ''"> and county_name like concat('%', #{countyName}, '%')</if>
            <if test="cityName != null  and cityName != ''"> and city_name like concat('%', #{cityName}, '%')</if>
            <if test="drna != null "> and drna = #{drna}</if>
            <if test="floodZ != null "> and flood_z = #{floodZ}</if>
            <if test="floodQ != null "> and flood_q = #{floodQ}</if>
            <if test="riverLen != null "> and river_len = #{riverLen}</if>
            <if test="riverBit != null "> and river_bit = #{riverBit}</if>
            <if test="roughness != null "> and roughness = #{roughness}</if>
            <if test="fiveQ != null "> and five_q = #{fiveQ}</if>
            <if test="tenQ != null "> and ten_q = #{tenQ}</if>
            <if test="twentyQ != null "> and twenty_q = #{twentyQ}</if>
            <if test="fiftyQ != null "> and fifty_q = #{fiftyQ}</if>
            <if test="hundredQ != null "> and hundred_q = #{hundredQ}</if>
            <if test="gcount != null "> and gcount = #{gcount}</if>
        </where>
    </select>
    
    <select id="selectSjyFdProneAreaByPrevCode" parameterType="String" resultMap="SjyFdProneAreaResult">
        <include refid="selectSjyFdProneAreaVo"/>
        where prev_code = #{prevCode}
    </select>

    <insert id="insertSjyFdProneArea" parameterType="SjyFdProneArea">
        insert into sjy_fd_prone_area
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="prevCode != null">prev_code,</if>
            <if test="adminAdnm != null">admin_adnm,</if>
            <if test="naturalAdnm != null">natural_adnm,</if>
            <if test="fldarea != null">fldarea,</if>
            <if test="htcount != null">htcount,</if>
            <if test="pcount != null">pcount,</if>
            <if test="lgtd != null">lgtd,</if>
            <if test="lttd != null">lttd,</if>
            <if test="prevtpName != null">prevtp_name,</if>
            <if test="isAdded != null">is_added,</if>
            <if test="risktp != null">risktp,</if>
            <if test="adcd != null">adcd,</if>
            <if test="tongTime != null">tong_time,</if>
            <if test="townName != null">town_name,</if>
            <if test="countyName != null">county_name,</if>
            <if test="cityName != null">city_name,</if>
            <if test="drna != null">drna,</if>
            <if test="floodZ != null">flood_z,</if>
            <if test="floodQ != null">flood_q,</if>
            <if test="riverLen != null">river_len,</if>
            <if test="riverBit != null">river_bit,</if>
            <if test="roughness != null">roughness,</if>
            <if test="fiveQ != null">five_q,</if>
            <if test="tenQ != null">ten_q,</if>
            <if test="twentyQ != null">twenty_q,</if>
            <if test="fiftyQ != null">fifty_q,</if>
            <if test="hundredQ != null">hundred_q,</if>
            <if test="gcount != null">gcount,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="prevCode != null">#{prevCode},</if>
            <if test="adminAdnm != null">#{adminAdnm},</if>
            <if test="naturalAdnm != null">#{naturalAdnm},</if>
            <if test="fldarea != null">#{fldarea},</if>
            <if test="htcount != null">#{htcount},</if>
            <if test="pcount != null">#{pcount},</if>
            <if test="lgtd != null">#{lgtd},</if>
            <if test="lttd != null">#{lttd},</if>
            <if test="prevtpName != null">#{prevtpName},</if>
            <if test="isAdded != null">#{isAdded},</if>
            <if test="risktp != null">#{risktp},</if>
            <if test="adcd != null">#{adcd},</if>
            <if test="tongTime != null">#{tongTime},</if>
            <if test="townName != null">#{townName},</if>
            <if test="countyName != null">#{countyName},</if>
            <if test="cityName != null">#{cityName},</if>
            <if test="drna != null">#{drna},</if>
            <if test="floodZ != null">#{floodZ},</if>
            <if test="floodQ != null">#{floodQ},</if>
            <if test="riverLen != null">#{riverLen},</if>
            <if test="riverBit != null">#{riverBit},</if>
            <if test="roughness != null">#{roughness},</if>
            <if test="fiveQ != null">#{fiveQ},</if>
            <if test="tenQ != null">#{tenQ},</if>
            <if test="twentyQ != null">#{twentyQ},</if>
            <if test="fiftyQ != null">#{fiftyQ},</if>
            <if test="hundredQ != null">#{hundredQ},</if>
            <if test="gcount != null">#{gcount},</if>
         </trim>
    </insert>

    <update id="updateSjyFdProneArea" parameterType="SjyFdProneArea">
        update sjy_fd_prone_area
        <trim prefix="SET" suffixOverrides=",">
            <if test="adminAdnm != null">admin_adnm = #{adminAdnm},</if>
            <if test="naturalAdnm != null">natural_adnm = #{naturalAdnm},</if>
            <if test="fldarea != null">fldarea = #{fldarea},</if>
            <if test="htcount != null">htcount = #{htcount},</if>
            <if test="pcount != null">pcount = #{pcount},</if>
            <if test="lgtd != null">lgtd = #{lgtd},</if>
            <if test="lttd != null">lttd = #{lttd},</if>
            <if test="prevtpName != null">prevtp_name = #{prevtpName},</if>
            <if test="isAdded != null">is_added = #{isAdded},</if>
            <if test="risktp != null">risktp = #{risktp},</if>
            <if test="adcd != null">adcd = #{adcd},</if>
            <if test="tongTime != null">tong_time = #{tongTime},</if>
            <if test="townName != null">town_name = #{townName},</if>
            <if test="countyName != null">county_name = #{countyName},</if>
            <if test="cityName != null">city_name = #{cityName},</if>
            <if test="drna != null">drna = #{drna},</if>
            <if test="floodZ != null">flood_z = #{floodZ},</if>
            <if test="floodQ != null">flood_q = #{floodQ},</if>
            <if test="riverLen != null">river_len = #{riverLen},</if>
            <if test="riverBit != null">river_bit = #{riverBit},</if>
            <if test="roughness != null">roughness = #{roughness},</if>
            <if test="fiveQ != null">five_q = #{fiveQ},</if>
            <if test="tenQ != null">ten_q = #{tenQ},</if>
            <if test="twentyQ != null">twenty_q = #{twentyQ},</if>
            <if test="fiftyQ != null">fifty_q = #{fiftyQ},</if>
            <if test="hundredQ != null">hundred_q = #{hundredQ},</if>
            <if test="gcount != null">gcount = #{gcount},</if>
        </trim>
        where prev_code = #{prevCode}
    </update>

    <delete id="deleteSjyFdProneAreaByPrevCode" parameterType="String">
        delete from sjy_fd_prone_area where prev_code = #{prevCode}
    </delete>

    <delete id="deleteSjyFdProneAreaByPrevCodes" parameterType="String">
        delete from sjy_fd_prone_area where prev_code in 
        <foreach item="prevCode" collection="array" open="(" separator="," close=")">
            #{prevCode}
        </foreach>
    </delete>
</mapper>
