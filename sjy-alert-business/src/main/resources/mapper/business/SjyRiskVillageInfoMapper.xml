<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyRiskVillageInfoMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyRiskVillageInfo" id="SjyRiskVillageInfoResult">
                    <result property="id" column="id"/>
                    <result property="townName" column="town_name"/>
                    <result property="adcd" column="adcd"/>
                    <result property="villageAdmin" column="village_admin"/>
                    <result property="villageNatural" column="village_natural"/>
                    <result property="cadcd" column="cadcd"/>
                    <result property="area" column="area"/>
                    <result property="houseNum" column="house_num"/>
                    <result property="popuNum" column="popu_num"/>
                    <result property="lgtd" column="lgtd"/>
                    <result property="lttd" column="lttd"/>
                    <result property="riskLevel" column="risk_level"/>
                    <result property="transPlace" column="trans_place"/>
                    <result property="urgentResource" column="urgent_resource"/>
                    <result property="warnDevice" column="warn_device"/>
                    <result property="monitorDevice" column="monitor_device"/>
                    <result property="createBy" column="create_by"/>
                    <result property="createTime" column="create_time"/>
                    <result property="updateBy" column="update_by"/>
                    <result property="updateTime" column="update_time"/>
                    <result property="izDel" column="iz_del"/>
                    <result property="remark" column="remark"/>
            </resultMap>

    <resultMap type="com.tzslsjy.business.vo.SjyRiskVillageInfoVo" id="SjyRiskVillageInfoVoResult">
        <id property="id" column="v_id"/>
        <result property="townName" column="town_name"/>
        <result property="adcd" column="adcd"/>
        <result property="villageAdmin" column="village_admin"/>
        <result property="villageNatural" column="village_natural"/>
        <result property="cadcd" column="cadcd"/>
        <result property="area" column="area"/>
        <result property="houseNum" column="house_num"/>
        <result property="popuNum" column="popu_num"/>
        <result property="lgtd" column="lgtd"/>
        <result property="lttd" column="lttd"/>
        <result property="riskLevel" column="risk_level"/>
        <result property="transPlace" column="trans_place"/>
        <result property="urgentResource" column="urgent_resource"/>
        <result property="warnDevice" column="warn_device"/>
        <result property="monitorDevice" column="monitor_device"/>
        <result property="izDel" column="iz_del"/>
        <result property="remark" column="remark"/>
        <association property="sjyPanoramaB" javaType="com.tzslsjy.business.domain.SjyPanoramaB">
            <id property="id" column="p_id"/>
            <result property="name" column="p_name"/>
            <result property="address" column="p_address"/>
            <result property="lgtd" column="p_lgtd"/>
            <result property="lttd" column="p_lttd"/>
            <result property="areacode" column="p_areacode"/>
            <result property="stcategory" column="p_stcategory"/>
            <result property="ennmcd" column="p_ennmcd"/>
            <result property="swfAttaId" column="p_swf_atta_id"/>
            <result property="appScanUrl" column="p_app_scan_url"/>
            <result property="createBy" column="p_create_by"/>
            <result property="createTime" column="p_create_time"/>
            <result property="updateBy" column="p_update_by"/>
            <result property="updateTime" column="p_update_time"/>
            <result property="izDel" column="p_iz_del"/>
            <result property="remark" column="p_remark_panorama"/> <!-- 注意这里的别名，避免和村庄的remark冲突 -->
        </association>
        <!-- SjyRiskVillageInfoVo的其他基本属性可以继续在这里映射 -->
        <collection property="sts" ofType="com.tzslsjy.business.domain.SjyRiskVillageStcdRel">
            <id property="id" column="rel_id"/>
            <result property="villageId" column="village_id"/>
            <result property="stcd" column="stcd"/>
            <result property="stnm" column="stnm"/>
            <!-- SjyRiskVillageStcdRel的其他属性 -->
        </collection>
        <collection property="sjyFolders" ofType="com.tzslsjy.business.domain.SjyFolder">
            <id property="folderId" column="f_folder_id"/>
            <result property="name" column="f_name"/>
            <result property="parentFolderId" column="f_parent_folder_id"/>
            <result property="folderType" column="f_folder_type"/>
            <result property="cType" column="f_c_type"/>
            <result property="cId" column="f_c_id"/>
            <result property="createBy" column="f_create_by"/>
            <result property="createTime" column="f_create_time"/>
            <result property="updateBy" column="f_update_by"/>
            <result property="updateTime" column="f_update_time"/>
            <result property="izDel" column="f_iz_del"/>
            <result property="remark" column="f_remark"/>
        </collection>
    </resultMap>

    <select id="selectWithId" resultType="com.tzslsjy.business.domain.SjyRiskVillageInfo">
        select  * from sjy_risk_village_info where id = #{id}
    </select>
    <select id="riskAreaStatistics" resultType="com.tzslsjy.business.domain.SjyRiskVillageInfo">
        select  * from sjy_risk_village_info
    </select>
    <select id="queryList" resultMap="SjyRiskVillageInfoVoResult">
        SELECT
            v.id as v_id, v.town_name, v.adcd, v.village_admin, v.village_natural, v.cadcd, v.area, v.house_num, v.popu_num, v.lgtd, v.lttd, v.risk_level, v.trans_place, v.urgent_resource, v.warn_device, v.monitor_device, v.iz_del, v.remark,
            v.create_by, v.create_time, v.update_by, v.update_time,
            rel.id as rel_id, rel.village_id, rel.stcd,
            f.folder_id as f_folder_id, f.name as f_name, f.parent_folder_id as f_parent_folder_id, f.folder_type as f_folder_type, f.c_type as f_c_type, f.c_id as f_c_id,
            f.create_by as f_create_by, f.create_time as f_create_time, f.update_by as f_update_by, f.update_time as f_update_time, f.iz_del as f_iz_del, f.remark as f_remark,
            p.id as p_id, p.name as p_name, p.address as p_address, p.lgtd as p_lgtd, p.lttd as p_lttd, p.areacode as p_areacode, p.stcategory as p_stcategory, p.ennmcd as p_ennmcd, p.swf_atta_id as p_swf_atta_id, p.app_scan_url as p_app_scan_url,
            p.create_by as p_create_by, p.create_time as p_create_time, p.update_by as p_update_by, p.update_time as p_update_time, p.iz_del as p_iz_del, p.remark as p_remark_panorama
        FROM
            sjy_risk_village_info v
        LEFT JOIN
            sjy_risk_village_stcd_rel rel ON v.id = rel.village_id
        LEFT JOIN
            sjy_folder f ON f.c_id = v.id AND f.c_type = '1'
        LEFT JOIN
            sjy_panorama_rel pr ON pr.rel_id = v.id AND pr.rel_type = '1'
        LEFT JOIN
            sjy_panorama_b p ON p.id = pr.panorama_id <!-- 假设sjy_panorama_b的主键是id，并且sjy_panorama_rel.panorama_id引用它 -->
        <where>
            <if test=" townName != null and  townName != ''">AND v.town_name LIKE concat('%', #{ townName}, '%')</if>
            <if test=" villageAdmin != null and  villageAdmin != ''">AND v.village_admin LIKE concat('%', #{villageAdmin}, '%')</if>
            <if test=" villageNatural != null and  villageNatural != ''">AND v.village_natural LIKE concat('%', #{villageNatural}, '%')</if>
            <if test=" riskLevel != null and  riskLevel != ''">AND v.risk_level = #{riskLevel}</if>
            <if test=" adcd != null and  adcd != ''">AND v.adcd = #{adcd}</if>
            <if test=" cadcd != null and  cadcd != ''">AND v.cadcd like concat('%', #{cadcd}, '%')</if>
            AND v.iz_del = 1
            AND (f.iz_del = 1 OR f.iz_del IS NULL) <!-- 确保只选择未删除的文件夹，或者当没有关联文件夹时也能返回村庄信息 -->
        </where>
        ORDER BY v.create_time DESC
    </select>
</mapper>