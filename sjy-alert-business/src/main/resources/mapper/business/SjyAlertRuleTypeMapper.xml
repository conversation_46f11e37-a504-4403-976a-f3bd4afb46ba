<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertRuleTypeMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyAlertRuleType" id="SjyAlertRuleTypeResult">
        <id     property="ruleTypeId"    column="rule_type_id"    />
        <result property="typeCode"    column="type_code"    />
        <result property="typeName"    column="type_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="type"        column="type"           />
        <result property="argJson"     column="arg_json"      />
        <result property="description"    column="description"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyAlertRuleTypeVo">
        select rule_type_id, type_code, type_name, parent_id, type, arg_json, description, status, create_by, create_time, update_by, update_time, remark from sjy_alert_rule_type
    </sql>

    <select id="selectSjyAlertRuleTypeList" parameterType="com.tzslsjy.business.domain.SjyAlertRuleType" resultMap="SjyAlertRuleTypeResult">
        <include refid="selectSjyAlertRuleTypeVo"/>
        <where>
            <if test="typeCode != null  and typeCode != ''"> and type_code = #{typeCode}</if>
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if> <!-- Assuming parentId can be 0 or other valid ID, not just non-empty string -->
            <if test="type != null and type != ''">
                and type in 
                <foreach item="typeItem" collection="type.split(',')" open="(" separator="," close=")">
                    #{typeItem}
                </foreach>
            </if>
            <if test="argJson != null and argJson != ''"> and arg_json = #{argJson}</if> <!-- Added condition for argJson -->
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectSjyAlertRuleTypeByRuleTypeId" parameterType="java.lang.Long" resultMap="SjyAlertRuleTypeResult">
        <include refid="selectSjyAlertRuleTypeVo"/>
        where rule_type_id = #{ruleTypeId}
    </select>

    <select id="selectSjyAlertRuleTypeByTypeCode" parameterType="java.lang.String" resultMap="SjyAlertRuleTypeResult"> <!-- Changed resultType to resultMap -->
        <include refid="selectSjyAlertRuleTypeVo"/>
        where type_code = #{typeCode}
    </select>

    <insert id="insertSjyAlertRuleType" parameterType="com.tzslsjy.business.domain.SjyAlertRuleType" useGeneratedKeys="true" keyProperty="ruleTypeId">
        insert into sjy_alert_rule_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="argJson != null">arg_json,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeCode != null and typeCode != ''">#{typeCode},</if>
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="argJson != null">#{argJson},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertRuleType" parameterType="com.tzslsjy.business.domain.SjyAlertRuleType">
        update sjy_alert_rule_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeCode != null and typeCode != ''">type_code = #{typeCode},</if>
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="argJson != null">arg_json = #{argJson},</if>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null and remark != ''">remark = #{remark},</if>
        </trim>
        where rule_type_id = #{ruleTypeId}
    </update>

    <delete id="deleteSjyAlertRuleTypeByRuleTypeId" parameterType="Long">
        delete from sjy_alert_rule_type where rule_type_id = #{ruleTypeId}
    </delete>

    <delete id="deleteSjyAlertRuleTypeByRuleTypeIds" parameterType="String">
        delete from sjy_alert_rule_type where rule_type_id in
        <foreach item="ruleTypeId" collection="array" open="(" separator="," close=")">
            #{ruleTypeId}
        </foreach>
    </delete>
</mapper>
