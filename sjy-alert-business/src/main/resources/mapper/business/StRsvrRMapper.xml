<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.data.StRsvrRMapper">

    <resultMap type="com.tzslsjy.business.domain.data.StRsvrR" id="StRsvrRResult">
        <result property="stcd"    column="STCD"/>
        <result property="tm"    column="TM"/>
        <result property="rz"    column="RZ"/>
        <result property="inq"    column="INQ"/>
        <result property="w"    column="W"/>
        <result property="blrz"    column="BLRZ"/>
        <result property="otq"    column="OTQ"/>
        <result property="rwchrcd"    column="RWCHRCD"/>
        <result property="rwptn"    column="RWPTN"/>
        <result property="inqdr"    column="INQDR"/>
        <result property="msqmt"    column="MSQMT"/>
        <result property="moditime"    column="MODITIME"/>
        <result property="edited"    column="EDITED"/>
        <result property="bFlag"    column="B_Flag"/>
    </resultMap>

    <!-- selectExistStcdsByStcds --> 

    <select id="selectExistStcdsByStcds" resultType="java.lang.String">
        select stcd from st_rsvr_r 
        <where>
            <if test="stcds != null">
                stcd in
                <foreach item="stcd" collection="stcds" open="(" separator="," close=")">
                    #{stcd}
                </foreach>
            </if>
            and tm &gt;= #{lastTongTime} and tm &lt;= #{currentTime}
        </where>
    </select>

    <select id="selectLatestByStcd" resultMap="StRsvrRResult">
        select * from st_rsvr_r
        where STCD = #{stcd} and tm>=#{startTm}
        order by TM desc
        limit 1
    </select>
    <select id="getSts" resultType="com.tzslsjy.business.vo.ReservoirVo">
        select *,tb3.normz fsdlz,tb3.rsvrtp level,tb6.JURISDICTION as jurisdiction,tb8.JURISDICTION as jurisdiction9, 0
        as status from st_stbprp_b tb1
        <if test="flag == 1">right join st_stbprp_type tb2 on tb2.stcd =tb1.STCD and tb2.type="represent_z"</if>

        <if test="stType !=null">right join st_stbprp_type sttype on sttype.type=#{stType} and sttype.stcd =tb1.STCD
        </if>
        left join comm_city tb6 on tb1.ADDVCD =tb6.JURISDICTION_NUM
        left join comm_city tb8 on tb1.ADDVCD9 =tb8.JURISDICTION_NUM
        left join st_rsvrfcch_b tb3 on tb1.stcd =tb3.stcd
        left join (
        select stcd, bgmd,edmd,fsltdz,fstp,fsltdw from (
        select stcd,row_number()over(partition by stcd order by bgmd desc) rn, bgmd,edmd,fsltdz,fstp,fsltdw
        from st_rsvrfsr_b t <if test="bgmd != null and bgmd != '' ">where BGMD &lt;= #{bgmd} and EDMD >= #{bgmd}</if> )
        as ss where rn =1
        ) tb4 on tb1.stcd =tb4.stcd

        where USFL=1

        <if test="rrFlag == null  ">and sttp in ('RR','RQ')</if>
        <if test="stcd != null and stcd != ''">and tb1.STCD = #{stcd}</if>
        <if test="stcds != null and stcds.size()>0">and tb1.STCD
            <foreach collection="stcds" item="stcd" open="in (" separator="," close=")">
                #{stcd}
            </foreach>
        </if>

        <if test="stnm != null and stnm != ''">and tb1.STNM like CONCAT('%',#{stnm},'%')</if>
        <if test="stnms != null and stnms != ''">and tb1.STNM in
            <foreach collection="stnms" item="stnm" open="(" separator="," close=")">
                #{stnm}
            </foreach>
        </if>
        <if test="lgtd != null and lgtd != '' ">and tb1.LGTD = #{lgtd}</if>
        <if test="lttd != null and lttd != '' ">and tb1.LTTD = #{lttd}</if>
        <if test="addvcd != null and addvcd != '' ">and tb1.ADDVCD9 like concat (#{addvcd},"%")</if>
    </select>
    <select id="getVal" resultType="com.tzslsjy.business.vo.ReservoirVo">
        select STCD stcd,RZ rz,TM tm,RWPTN rwptn from st_rsvr_r where tm >= #{startTm} and tm &lt;=#{endTm}
        <if test="stcd != null and stcd != ''">and STCD = #{stcd}</if>
        <if test="stcds != null and stcds.size()>0">and STCD
            <foreach collection="stcds" item="stcd" open="in (" separator="," close=")">
                #{stcd}
            </foreach>
        </if>
    </select>
    <select id="getZvarls" resultType="com.tzslsjy.business.vo.ZvarlVo">
        select * from st_zvarl_b where 1 =1
        <if test="stcd != null and stcd != ''">and STCD = #{stcd}</if>
    </select>
</mapper>