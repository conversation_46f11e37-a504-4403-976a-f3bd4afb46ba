<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyStationAbnormalConfigMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyStationAbnormalConfig" id="SjyStationAbnormalConfigResult">
        <result property="id"    column="id"    />
        <result property="stcd"    column="stcd"    />
        <result property="configType"    column="config_type"    />
        <result property="threshold"    column="threshold"    />
        <result property="unit"    column="unit"    />
        <result property="enabled"    column="enabled"    />
        <result property="globalSwitch"    column="global_switch"    />
        <result property="remark"    column="remark"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectSjyStationAbnormalConfigVo">
        select id, stcd, config_type, threshold, unit, enabled, global_switch, remark, create_by, create_time, update_by, update_time 
        from sjy_station_abnormal_config
    </sql>

    <select id="selectByStcdAndType" resultMap="SjyStationAbnormalConfigResult">
        <include refid="selectSjyStationAbnormalConfigVo"/>
        where stcd = #{stcd} and config_type = #{configType}
    </select>

    <select id="selectByConfigType" resultMap="SjyStationAbnormalConfigResult">
        <include refid="selectSjyStationAbnormalConfigVo"/>
        where config_type = #{configType}
        order by stcd
    </select>

    <select id="selectEnabledByConfigType" resultMap="SjyStationAbnormalConfigResult">
        <include refid="selectSjyStationAbnormalConfigVo"/>
        where config_type = #{configType} and enabled = 1
        order by stcd
    </select>

    <update id="batchUpdateEnabled">
        update sjy_station_abnormal_config 
        set enabled = #{enabled}, update_time = now()
        where config_type = #{configType}
        and stcd in
        <foreach item="stcd" collection="stcds" open="(" separator="," close=")">
            #{stcd}
        </foreach>
    </update>

    <update id="updateGlobalSwitch">
        update sjy_station_abnormal_config 
        set global_switch = #{globalSwitch}, update_time = now()
        where config_type = #{configType}
    </update>

    <select id="getGlobalSwitch" resultType="java.lang.Integer">
        select global_switch 
        from sjy_station_abnormal_config 
        where config_type = #{configType} 
        limit 1
    </select>

</mapper>
