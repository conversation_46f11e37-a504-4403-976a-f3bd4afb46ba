<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyFdStInfoMapper">

    <resultMap type="SjyFdStInfo" id="SjyFdStInfoResult">
        <result property="stCode"    column="st_code"    />
        <result property="equipmentCode"    column="equipment_code"    />
        <result property="monitorTp"    column="monitor_tp"    />
        <result property="lgtd"    column="lgtd"    />
        <result property="lttd"    column="lttd"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="adcd"    column="adcd"    />
        <result property="tongTime"    column="tong_time"    />
    </resultMap>

    <sql id="selectSjyFdStInfoVo">
        select st_code, equipment_code, monitor_tp, lgtd, lttd, modify_time, adcd, tong_time from sjy_fd_st_info
    </sql>

    <select id="selectSjyFdStInfoList" parameterType="SjyFdStInfo" resultMap="SjyFdStInfoResult">
        <include refid="selectSjyFdStInfoVo"/>
        <where>
            <if test="equipmentCode != null  and equipmentCode != ''"> and equipment_code = #{equipmentCode}</if>
            <if test="monitorTp != null  and monitorTp != ''"> and monitor_tp = #{monitorTp}</if>
            <if test="lgtd != null "> and lgtd = #{lgtd}</if>
            <if test="lttd != null "> and lttd = #{lttd}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="adcd != null  and adcd != ''"> and adcd = #{adcd}</if>
            <if test="tongTime != null "> and tong_time = #{tongTime}</if>
        </where>
    </select>

    <select id="selectSjyFdStInfoByStCode" parameterType="String" resultMap="SjyFdStInfoResult">
        <include refid="selectSjyFdStInfoVo"/>
        where st_code = #{stCode}
    </select>
    <select id="selectAllRainfallStationCodesByAdcd" resultType="java.lang.String">
        SELECT DISTINCT equipment_code
        FROM sjy_fd_st_info
        WHERE adcd like CONCAT(#{adcd},'%')
    </select>
    <select id="selectSjyFdStInfoByEquiID" resultType="com.tzslsjy.business.domain.SjyFdStInfo">
        SELECT *
        FROM sjy_fd_st_info
        WHERE equipment_code = #{equipmentCode} limit 1
    </select>

    <insert id="insertSjyFdStInfo" parameterType="SjyFdStInfo">
        insert into sjy_fd_st_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stCode != null">st_code,</if>
            <if test="equipmentCode != null">equipment_code,</if>
            <if test="monitorTp != null">monitor_tp,</if>
            <if test="lgtd != null">lgtd,</if>
            <if test="lttd != null">lttd,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="adcd != null and adcd != ''">adcd,</if>
            <if test="tongTime != null">tong_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stCode != null">#{stCode},</if>
            <if test="equipmentCode != null">#{equipmentCode},</if>
            <if test="monitorTp != null">#{monitorTp},</if>
            <if test="lgtd != null">#{lgtd},</if>
            <if test="lttd != null">#{lttd},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="adcd != null and adcd != ''">#{adcd},</if>
            <if test="tongTime != null">#{tongTime},</if>
         </trim>
    </insert>

    <update id="updateSjyFdStInfo" parameterType="SjyFdStInfo">
        update sjy_fd_st_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="equipmentCode != null">equipment_code = #{equipmentCode},</if>
            <if test="monitorTp != null">monitor_tp = #{monitorTp},</if>
            <if test="lgtd != null">lgtd = #{lgtd},</if>
            <if test="lttd != null">lttd = #{lttd},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="adcd != null and adcd != ''">adcd = #{adcd},</if>
            <if test="tongTime != null">tong_time = #{tongTime},</if>
        </trim>
        where st_code = #{stCode}
    </update>

    <delete id="deleteSjyFdStInfoByStCode" parameterType="String">
        delete from sjy_fd_st_info where st_code = #{stCode}
    </delete>

    <delete id="deleteSjyFdStInfoByStCodes" parameterType="String">
        delete from sjy_fd_st_info where st_code in
        <foreach item="stCode" collection="array" open="(" separator="," close=")">
            #{stCode}
        </foreach>
    </delete>
</mapper>
