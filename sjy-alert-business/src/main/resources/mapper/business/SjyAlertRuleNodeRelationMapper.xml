<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation" id="SjyAlertRuleNodeRelationResult">
        <id     property="relationId"    column="relation_id"    jdbcType="BIGINT"/>
        <result property="ruleId"    column="rule_id"    jdbcType="BIGINT"/>
        <result property="nodeId"    column="node_id"    jdbcType="VARCHAR"/>
        <result property="personId"  column="person_id"  jdbcType="VARCHAR"/>
        <result property="alertLevel"    column="alert_level"    jdbcType="BIGINT"/>
        <result property="relationType"    column="relation_type"    jdbcType="VARCHAR"/>
        <result property="argId"    column="arg_id"    jdbcType="BIGINT"/>
        <result property="contactWay"    column="contact_way"    jdbcType="VARCHAR"/>
        <result property="createBy"    column="create_by"    jdbcType="VARCHAR"/>
        <result property="createTime"    column="create_time"    jdbcType="TIMESTAMP"/>
        <result property="updateBy"    column="update_by"    jdbcType="VARCHAR"/>
        <result property="updateTime"    column="update_time"    jdbcType="TIMESTAMP"/>
        <result property="remark"    column="remark"    jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectSjyAlertRuleNodeRelationVo">
        select relation_id, rule_id, node_id, person_id, alert_level, relation_type, arg_id, contact_way, create_by, create_time, update_by, update_time, remark from sjy_alert_rule_node_relation
    </sql>

    <select id="selectSjyAlertRuleNodeRelationList" parameterType="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation" resultMap="SjyAlertRuleNodeRelationResult">
        <include refid="selectSjyAlertRuleNodeRelationVo"/>
        <where>
            <if test="ruleId != null "> and rule_id = #{ruleId, jdbcType=BIGINT}</if>
            <if test="nodeId != null and nodeId != ''"> and node_id = #{nodeId, jdbcType=VARCHAR}</if>
            <if test="personId != null and personId != ''"> and person_id = #{personId, jdbcType=VARCHAR}</if>
            <if test="alertLevel != null "> and alert_level = #{alertLevel, jdbcType=BIGINT}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType, jdbcType=VARCHAR}</if>
            <if test="argId != null "> and arg_id = #{argId, jdbcType=BIGINT}</if>
            <if test="contactWay != null  and contactWay != ''"> and contact_way = #{contactWay, jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="selectSjyAlertRuleNodeRelationByRelationId" parameterType="java.lang.Long" resultMap="SjyAlertRuleNodeRelationResult">
        <include refid="selectSjyAlertRuleNodeRelationVo"/>
        where relation_id = #{relationId, jdbcType=BIGINT}
    </select>

    <insert id="insertSjyAlertRuleNodeRelation" parameterType="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation" useGeneratedKeys="true" keyProperty="relationId">
        insert into sjy_alert_rule_node_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="nodeId != null and nodeId != ''">node_id,</if>
            <if test="personId != null and personId != ''">person_id,</if>
            <if test="alertLevel != null">alert_level,</if>
            <if test="relationType != null and relationType != ''">relation_type,</if>
            <if test="argId != null">arg_id,</if>
            <if test="contactWay != null and contactWay != ''">contact_way,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId, jdbcType=BIGINT},</if>
            <if test="nodeId != null and nodeId != ''">#{nodeId, jdbcType=VARCHAR},</if>
            <if test="personId != null and personId != ''">#{personId, jdbcType=VARCHAR},</if>
            <if test="alertLevel != null">#{alertLevel, jdbcType=BIGINT},</if>
            <if test="relationType != null and relationType != ''">#{relationType, jdbcType=VARCHAR},</if>
            <if test="argId != null">#{argId, jdbcType=BIGINT},</if>
            <if test="contactWay != null and contactWay != ''">#{contactWay, jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">#{createBy, jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime, jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy, jdbcType=VARCHAR},</if>
            <if test="updateTime != null">#{updateTime, jdbcType=TIMESTAMP},</if>
            <if test="remark != null and remark != ''">#{remark, jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="updateSjyAlertRuleNodeRelation" parameterType="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation">
        update sjy_alert_rule_node_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId, jdbcType=BIGINT},</if>
            <if test="nodeId != null and nodeId != ''">node_id = #{nodeId, jdbcType=VARCHAR},</if>
            <if test="personId != null and personId != ''">person_id = #{personId, jdbcType=VARCHAR},</if>
            <if test="alertLevel != null">alert_level = #{alertLevel, jdbcType=BIGINT},</if>
            <if test="relationType != null and relationType != ''">relation_type = #{relationType, jdbcType=VARCHAR},</if>
            <if test="argId != null">arg_id = #{argId, jdbcType=BIGINT},</if>
            <if test="contactWay != null and contactWay != ''">contact_way = #{contactWay, jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy, jdbcType=VARCHAR},</if>
            <if test="createTime != null">create_time = #{createTime, jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy, jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime, jdbcType=TIMESTAMP},</if>
            <if test="remark != null and remark != ''">remark = #{remark, jdbcType=VARCHAR},</if>
        </trim>
        where relation_id = #{relationId, jdbcType=BIGINT}
    </update>

    <delete id="deleteSjyAlertRuleNodeRelationByRelationId" parameterType="java.lang.Long">
        delete from sjy_alert_rule_node_relation where relation_id = #{relationId, jdbcType=BIGINT}
    </delete>

    <delete id="deleteSjyAlertRuleNodeRelationByRelationIds" parameterType="java.lang.String"> <!-- Assuming relationIds are passed as a comma-separated string or a List<Long> -->
        delete from sjy_alert_rule_node_relation where relation_id in
        <foreach item="relationId" collection="array" open="(" separator="," close=")">
            #{relationId, jdbcType=BIGINT} <!-- Assuming relationId in the list is Long -->
        </foreach>
    </delete>

    <insert id="batchInsert" parameterType="java.util.List">
        insert into sjy_alert_rule_node_relation (rule_id, node_id, person_id, alert_level, relation_type, arg_id, contact_way, create_by, create_time, update_by, update_time, remark)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.ruleId, jdbcType=BIGINT}, #{item.nodeId, jdbcType=VARCHAR}, #{item.personId, jdbcType=VARCHAR}, #{item.alertLevel, jdbcType=BIGINT}, #{item.relationType, jdbcType=VARCHAR}, #{item.argId, jdbcType=BIGINT}, #{item.contactWay, jdbcType=VARCHAR},
            #{item.createBy, jdbcType=VARCHAR}, #{item.createTime, jdbcType=TIMESTAMP}, #{item.updateBy, jdbcType=VARCHAR}, #{item.updateTime, jdbcType=TIMESTAMP}, #{item.remark, jdbcType=VARCHAR})
        </foreach>
    </insert>

    <delete id="deleteByRuleIdAndArgIdIsNull" parameterType="java.lang.Long">
        delete from sjy_alert_rule_node_relation
        where rule_id = #{ruleId, jdbcType=BIGINT} and arg_id IS NULL
    </delete>

    <delete id="deleteByArgId" parameterType="java.lang.Long">
        delete from sjy_alert_rule_node_relation
        where arg_id = #{argId, jdbcType=BIGINT}
    </delete>

    <select id="selectByRuleIdAndArgIdIsNull" parameterType="java.lang.Long" resultMap="SjyAlertRuleNodeRelationResult">
        <include refid="selectSjyAlertRuleNodeRelationVo"/>
        where rule_id = #{ruleId, jdbcType=BIGINT} and arg_id IS NULL
    </select>
    <select id="selectPersonByArgId" resultType="com.tzslsjy.business.domain.SjyAlertPerson">
     select p.* from sjy_alert_person p left join sjy_alert_rule_node_relation r on p.person_id = r.person_id where r.arg_id = #{argId}
    </select>

    <!-- selectNodeIdsByArgId --> 

    <select id="selectNodeIdsByArgId" resultType="java.lang.String"  >
        select CONCAT(IFNULL(relation_type, ''), '_', node_id) as node_id
        from sjy_alert_rule_node_relation
        where arg_id = #{argId}
    </select>
    <select id="selectNodeIdsByArgIdAndAdcd" resultType="java.lang.String">
        select CONCAT(IFNULL(rel.relation_type, ''), '_', rel.node_id) as node_id
        from sjy_alert_rule_node_relation rel
        left join lh_water_basic.sjy_alert_node n on rel.node_id = n.node_id
        where rel.arg_id = #{argId} and n.adcd = #{adcd}
    </select>
    <select id="selectNodeIdsByArgIdAndNodeId" resultType="java.lang.String">
        select CONCAT(IFNULL(rel.relation_type, ''), '_', rel.node_id) as node_id
        from sjy_alert_rule_node_relation rel
        left join lh_water_basic.sjy_alert_node n on rel.node_id = n.node_id
        where rel.arg_id = #{argId} and find_in_set(#{nodeId}, n.ancestors)
    </select>
</mapper>
