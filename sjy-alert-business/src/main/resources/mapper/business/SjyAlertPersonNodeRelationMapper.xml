<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertPersonNodeRelationMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyAlertPersonNodeRelation" id="SjyAlertPersonNodeRelationResult">
        <id     property="relationId"    column="relation_id" jdbcType="BIGINT"/>
        <result property="personId"    column="person_id" jdbcType="VARCHAR"/>
        <result property="nodeId"    column="node_id" jdbcType="VARCHAR"/>
        <result property="postId"      column="post_id" jdbcType="VARCHAR"/>
        <result property="postName"    column="relation_post_name" jdbcType="VARCHAR"/>
        <result property="isPrimary"    column="is_primary" jdbcType="BIGINT"/>
        <result property="status"    column="status" jdbcType="BIGINT"/>
        <result property="createBy"    column="create_by" jdbcType="VARCHAR"/>
        <result property="wayType"    column="way_type" jdbcType="VARCHAR"/>
        <result property="createTime"    column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy"    column="update_by" jdbcType="VARCHAR"/>
        <result property="updateTime"    column="update_time" jdbcType="TIMESTAMP"/>
        <result property="remark"    column="remark" jdbcType="VARCHAR"/>
        <result property="relationType"    column="relation_type" jdbcType="INTEGER"/>
        <collection property="personList" ofType="com.tzslsjy.business.domain.SjyAlertPerson">
            <id property="personId" column="p_person_id" jdbcType="VARCHAR"/> <!-- Assuming SjyAlertPerson.personId is also String -->
            <result property="name" column="p_name" jdbcType="VARCHAR"/>
            <result property="phone" column="p_phone" jdbcType="VARCHAR"/>
            <result property="status" column="p_status" jdbcType="BIGINT"/>
        </collection>
    </resultMap>

    <sql id="selectSjyAlertPersonNodeRelationVo">
        select
            sapnr.relation_id,
            sapnr.person_id,
            sapnr.node_id,
            sapnr.post_id,
            sanp.post_name as relation_post_name,
            sapnr.is_primary,
            sapnr.status,
            sapnr.create_by,
            sapnr.way_type,
            sapnr.relation_type,
            sapnr.create_time,
            sapnr.update_by,
            sapnr.update_time,
            sapnr.remark
        from sjy_alert_person_node_relation sapnr
        left join sjy_alert_node_post sanp on sapnr.post_id = sanp.post_id <!-- Changed sanp.id to sanp.post_id -->
    </sql>

    <select id="selectSjyAlertPersonNodeRelationList" parameterType="com.tzslsjy.business.domain.SjyAlertPersonNodeRelation" resultMap="SjyAlertPersonNodeRelationResult">
        <include refid="selectSjyAlertPersonNodeRelationVo"/>
        <where>
            <if test="personId != null and personId != ''"> and sapnr.person_id = #{personId, jdbcType=VARCHAR}</if>
            <if test="nodeId != null and nodeId != ''"> and sapnr.node_id = #{nodeId, jdbcType=VARCHAR}</if>
            <if test="postId != null and postId != ''"> and sapnr.post_id = #{postId, jdbcType=VARCHAR}</if>
            <if test="isPrimary != null "> and sapnr.is_primary = #{isPrimary, jdbcType=BIGINT}</if>
            <if test="status != null "> and sapnr.status = #{status, jdbcType=BIGINT}</if>
            <if test="wayType != null  and wayType != ''"> and sapnr.way_type = #{wayType, jdbcType=VARCHAR}</if>
            <if test="relationType != null "> and sapnr.relation_type = #{relationType, jdbcType=INTEGER}</if>
            <!-- 如果需要根据 postName 模糊查询，可以在这里添加，但通常不推荐直接查冗余字段 -->
            <!-- <if test="postName != null and postName != ''"> and sanp.post_name like concat('%', #{postName}, '%')</if> -->
        </where>
    </select>

    <select id="selectSjyAlertPersonNodeRelationByRelationId" parameterType="java.lang.Long" resultMap="SjyAlertPersonNodeRelationResult">
        <include refid="selectSjyAlertPersonNodeRelationVo"/>
        where sapnr.relation_id = #{relationId}
    </select>
    <select id="selectPersonByNodeIds" resultMap="SjyAlertPersonNodeRelationResult" parameterType="java.util.List">
        SELECT
            sapnr.relation_id,
            sapnr.person_id,
            sapnr.node_id,
            sapnr.post_id,
            sanp.post_name as relation_post_name,
            sapnr.is_primary,
            sapnr.status,
            sapnr.create_by,
            sapnr.way_type,
            sapnr.relation_type,
            sapnr.create_time,
            sapnr.update_by,
            sapnr.update_time,
            sapnr.remark,
            sap.person_id as p_person_id,
            sap.name as p_name,
            sap.phone as p_phone,
            sap.status as p_status
        FROM
            sjy_alert_person_node_relation sapnr
        LEFT JOIN
            sjy_alert_node_post sanp ON sapnr.post_id = sanp.post_id <!-- Changed sanp.id to sanp.post_id -->
        LEFT JOIN
            sjy_alert_person sap ON sapnr.person_id = sap.person_id <!-- Assuming sap.person_id is also VARCHAR -->
        WHERE
            sapnr.node_id IN
            <foreach item="nodeId" collection="list" open="(" separator="," close=")">
                #{nodeId, jdbcType=VARCHAR}
            </foreach>
    </select>

    <insert id="insertSjyAlertPersonNodeRelation" parameterType="com.tzslsjy.business.domain.SjyAlertPersonNodeRelation" useGeneratedKeys="true" keyProperty="relationId">
        insert into sjy_alert_person_node_relation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personId != null">person_id,</if>
            <if test="nodeId != null">node_id,</if>
            <if test="postId != null">post_id,</if>
            <if test="isPrimary != null">is_primary,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="wayType != null">way_type,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="relationType != null">relation_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personId != null and personId != ''">#{personId, jdbcType=VARCHAR},</if>
            <if test="nodeId != null and nodeId != ''">#{nodeId, jdbcType=VARCHAR},</if>
            <if test="postId != null and postId != ''">#{postId, jdbcType=VARCHAR},</if>
            <if test="isPrimary != null">#{isPrimary, jdbcType=BIGINT},</if>
            <if test="status != null">#{status, jdbcType=BIGINT},</if>
            <if test="createBy != null and createBy != ''">#{createBy, jdbcType=VARCHAR},</if>
            <if test="wayType != null and wayType != ''">#{wayType, jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime, jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy, jdbcType=VARCHAR},</if>
            <if test="updateTime != null">#{updateTime, jdbcType=TIMESTAMP},</if>
            <if test="remark != null and remark != ''">#{remark, jdbcType=VARCHAR},</if>
            <if test="relationType != null">#{relationType, jdbcType=INTEGER},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertPersonNodeRelation" parameterType="com.tzslsjy.business.domain.SjyAlertPersonNodeRelation">
        update sjy_alert_person_node_relation
        <trim prefix="SET" suffixOverrides=",">
            <if test="personId != null and personId != ''">person_id = #{personId, jdbcType=VARCHAR},</if>
            <if test="nodeId != null and nodeId != ''">node_id = #{nodeId, jdbcType=VARCHAR},</if>
            <if test="postId != null and postId != ''">post_id = #{postId, jdbcType=VARCHAR},</if>
            <if test="isPrimary != null">is_primary = #{isPrimary, jdbcType=BIGINT},</if>
            <if test="status != null">status = #{status, jdbcType=BIGINT},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy, jdbcType=VARCHAR},</if>
            <if test="wayType != null and wayType != ''">way_type = #{wayType, jdbcType=VARCHAR},</if>
            <if test="createTime != null">create_time = #{createTime, jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy, jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime, jdbcType=TIMESTAMP},</if>
            <if test="remark != null and remark != ''">remark = #{remark, jdbcType=VARCHAR},</if>
            <if test="relationType != null">relation_type = #{relationType, jdbcType=INTEGER},</if>
        </trim>
        where relation_id = #{relationId}
    </update>

    <delete id="deleteSjyAlertPersonNodeRelationByRelationId" parameterType="Long">
        delete from sjy_alert_person_node_relation where relation_id = #{relationId}
    </delete>

    <delete id="deleteSjyAlertPersonNodeRelationByRelationIds" parameterType="String">
        delete from sjy_alert_person_node_relation where relation_id in
        <foreach item="relationId" collection="array" open="(" separator="," close=")">
            #{relationId}
        </foreach>
    </delete>
</mapper>
