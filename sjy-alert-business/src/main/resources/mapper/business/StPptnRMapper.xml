<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.StPptnRMapper">

    <resultMap type="StPptnR" id="StPptnRResult">
        <result property="STCD"    column="STCD"    />
        <result property="TM"    column="TM"    />
        <result property="DRP"    column="DRP"    />
        <result property="INTV"    column="INTV"    />
        <result property="PDR"    column="PDR"    />
        <result property="DYP"    column="DYP"    />
        <result property="WTH"    column="WTH"    />
        <result property="MODITIME"    column="MODITIME"    />
        <result property="EDITED"    column="EDITED"    />
        <result property="bFlag"    column="B_Flag"    />
    </resultMap>

    <!-- 新增的结果映射用于 RainInfoHourVo -->
    <resultMap type="com.tzslsjy.business.vo.RainInfoHourVo" id="RainInfoHourVoResult">
        <result property="stcd" column="stcd"/>
        <result property="stnm" column="stnm"/>
        <result property="addvcd" column="addvcd"/>
        <result property="lgtd" column="lgtd"/>
        <result property="lttd" column="lttd"/>
        <result property="drp" column="drp"/>
        <result property="tm" column="tm"/>
        <result property="dyp" column="dyp"/>
        <result property="intv" column="intv"/>
        <result property="pdr" column="pdr"/>
        <result property="wth" column="wth"/>
    </resultMap>

    <sql id="selectStPptnRVo">
        select STCD, TM, DRP, INTV, PDR, DYP, WTH, MODITIME, EDITED, B_Flag from st_pptn_r
    </sql>

    <select id="selectStPptnRList" parameterType="StPptnR" resultMap="StPptnRResult">
        <include refid="selectStPptnRVo"/>
        <where>
            <if test="DRP != null "> and DRP = #{DRP}</if>
            <if test="INTV != null "> and INTV = #{INTV}</if>
            <if test="PDR != null "> and PDR = #{PDR}</if>
            <if test="DYP != null "> and DYP = #{DYP}</if>
            <if test="WTH != null  and WTH != ''"> and WTH = #{WTH}</if>
            <if test="MODITIME != null "> and MODITIME = #{MODITIME}</if>
            <if test="EDITED != null  and EDITED != ''"> and EDITED = #{EDITED}</if>
            <if test="bFlag != null  and bFlag != ''"> and B_Flag = #{bFlag}</if>
        </where>
    </select>

    <select id="selectStPptnRBySTCD" parameterType="String" resultMap="StPptnRResult">
        <include refid="selectStPptnRVo"/>
        where STCD = #{STCD}
    </select>
    <select id="selectOverPptnByTm" resultType="java.math.BigDecimal">
        select sum(drp) from st_pptn_r where stcd = #{stcd} and tm >= #{startTime} and tm &lt;= #{endTime}
    </select>
    <select id="selectLastPPtnByTm" resultType="StPptnR">
        select * from st_pptn_r where stcd = #{stcd} and tm>=#{startTm} and tm &lt;= #{endTm}  and drp is not null order by tm desc limit ${limitNum}
    </select>
    <select id="selectLastestTmByTm" resultType="java.util.Date">
        select max(tm) from st_pptn_r where stcd = #{stcd} and   tm &lt;= #{endTm}  and drp is not null
    </select>
    <select id="selectTotalRainfallByTimeRange" resultType="java.math.BigDecimal">
        select sum(drp) from st_pptn_r where stcd = #{stcd} and tm &gt; #{startTm} and tm &lt;= #{endTm}
    </select>

    <insert id="insertStPptnR" parameterType="StPptnR">
        insert into st_pptn_r
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="STCD != null">STCD,</if>
            <if test="TM != null">TM,</if>
            <if test="DRP != null">DRP,</if>
            <if test="INTV != null">INTV,</if>
            <if test="PDR != null">PDR,</if>
            <if test="DYP != null">DYP,</if>
            <if test="WTH != null">WTH,</if>
            <if test="MODITIME != null">MODITIME,</if>
            <if test="EDITED != null">EDITED,</if>
            <if test="bFlag != null">B_Flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="STCD != null">#{STCD},</if>
            <if test="TM != null">#{TM},</if>
            <if test="DRP != null">#{DRP},</if>
            <if test="INTV != null">#{INTV},</if>
            <if test="PDR != null">#{PDR},</if>
            <if test="DYP != null">#{DYP},</if>
            <if test="WTH != null">#{WTH},</if>
            <if test="MODITIME != null">#{MODITIME},</if>
            <if test="EDITED != null">#{EDITED},</if>
            <if test="bFlag != null">#{bFlag},</if>
         </trim>
    </insert>

    <update id="updateStPptnR" parameterType="StPptnR">
        update st_pptn_r
        <trim prefix="SET" suffixOverrides=",">
            <if test="TM != null">TM = #{TM},</if>
            <if test="DRP != null">DRP = #{DRP},</if>
            <if test="INTV != null">INTV = #{INTV},</if>
            <if test="PDR != null">PDR = #{PDR},</if>
            <if test="DYP != null">DYP = #{DYP},</if>
            <if test="WTH != null">WTH = #{WTH},</if>
            <if test="MODITIME != null">MODITIME = #{MODITIME},</if>
            <if test="EDITED != null">EDITED = #{EDITED},</if>
            <if test="bFlag != null">B_Flag = #{bFlag},</if>
        </trim>
        where STCD = #{STCD}
    </update>

    <delete id="deleteStPptnRBySTCD" parameterType="String">
        delete from st_pptn_r where STCD = #{STCD}
    </delete>

    <delete id="deleteStPptnRBySTCDs" parameterType="String">
        delete from st_pptn_r where STCD in
        <foreach item="STCD" collection="array" open="(" separator="," close=")">
            #{STCD}
        </foreach>
    </delete>

    <!-- selectExistStcdsByStcds --> 

    <select id="selectExistStcdsByStcds" resultType="java.lang.String">
        select stcd from st_pptn_r 
        <where>
            <if test="stcds != null">
                stcd in
                <foreach item="stcd" collection="stcds" open="(" separator="," close=")">
                    #{stcd}
                </foreach>
            </if>
            and tm &gt;= #{lastTongTime} and tm &lt;= #{currentTime}
        </where>
    </select>

    <!-- ====== 新增的查询方法用于 StPptnHistD 模块 ====== -->
    
    <!-- 获取测站数据（基本信息+特征值） -->
    <select id="loadPage" parameterType="com.tzslsjy.business.vo.RainInfoHourReqVo" resultMap="RainInfoHourVoResult">
        SELECT 
            s.stcd,
            s.stnm,
            s.addvcd,
            s.lgtd,
            s.lttd
        FROM st_stbprp_b s
        WHERE 1=1
        <if test="stcd != null and stcd != ''">
            AND s.stcd = #{stcd}
        </if>
        <if test="addvcd != null and addvcd != ''">
            AND s.addvcd = #{addvcd}
        </if>
        <if test="addvcd9 != null and addvcd9 != ''">
            AND s.addvcd LIKE CONCAT(#{addvcd9}, '%')
        </if>
        <if test="flag != null and flag == 1">
            AND s.flag = 1
        </if>
        ORDER BY s.stcd
    </select>

    <!-- 获取月累计雨量 -->
    <select id="getSumMonthRain" parameterType="com.tzslsjy.business.vo.RainInfoHourReqVo" resultMap="RainInfoHourVoResult">
        SELECT 
            r.stcd,
            SUM(r.drp) as drp,
            DATE(r.tm) as tm
        FROM st_pptn_r r
        WHERE 1=1
        <if test="startTm != null">
            AND r.tm >= #{startTm}
        </if>
        <if test="endTm != null">
            AND r.tm &lt;= #{endTm}
        </if>
        <if test="stcd != null and stcd != ''">
            AND r.stcd = #{stcd}
        </if>
        <if test="stcds != null and stcds.size() > 0">
            AND r.stcd IN
            <foreach collection="stcds" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY r.stcd, DATE(r.tm)
        ORDER BY r.stcd, r.tm
    </select>

    <!-- 获取剩余雨量 -->
    <select id="getSumLeftRain" parameterType="com.tzslsjy.business.vo.RainInfoHourReqVo" resultMap="RainInfoHourVoResult">
        SELECT 
            r.stcd,
            SUM(r.drp) as drp,
            r.tm
        FROM st_pptn_r_h r
        WHERE 1=1
        <if test="startTm != null">
            AND r.tm >= #{startTm}
        </if>
        <if test="endTm != null">
            AND r.tm &lt;= #{endTm}
        </if>
        <if test="stcd != null and stcd != ''">
            AND r.stcd = #{stcd}
        </if>

        GROUP BY r.stcd
        ORDER BY r.stcd
    </select>

    <!-- 获取日雨量数据 -->
    <select id="getDayRains" parameterType="com.tzslsjy.business.vo.RainInfoHourReqVo" resultMap="RainInfoHourVoResult">
        SELECT 
            r.stcd,
            r.drp,
            r.tm
        FROM st_pptn_r r
        WHERE 1=1
        <if test="startTm != null">
            AND r.tm >= #{startTm}
        </if>
        <if test="endTm != null">
            AND r.tm &lt;= #{endTm}
        </if>
        <if test="stcd != null and stcd != ''">
            AND r.stcd = #{stcd}
        </if>
        <if test="addvcd != null and addvcd != ''">
            AND r.stcd IN (
                SELECT s.stcd FROM st_stbprp_b s WHERE s.addvcd = #{addvcd}
            )
        </if>
        ORDER BY r.stcd, r.tm
    </select>

</mapper>
