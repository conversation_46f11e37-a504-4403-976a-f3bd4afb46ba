<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertRuleVariableMapper">

    <resultMap type="SjyAlertRuleVariable" id="SjyAlertRuleVariableResult">
        <result property="ruleVariableId"    column="rule_variable_id"    />
        <result property="ruleTypeId"    column="rule_type_id"    />
        <result property="variableId"    column="variable_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyAlertRuleVariableVo">
        select rule_variable_id, rule_type_id, variable_id, create_by, create_time, update_by, update_time, remark from sjy_alert_rule_variable
    </sql>

    <select id="selectSjyAlertRuleVariableList" parameterType="SjyAlertRuleVariable" resultMap="SjyAlertRuleVariableResult">
        <include refid="selectSjyAlertRuleVariableVo"/>
        <where>
            <if test="ruleTypeId != null "> and rule_type_id = #{ruleTypeId}</if>
            <if test="variableId != null "> and variable_id = #{variableId}</if>
        </where>
    </select>

    <select id="selectSjyAlertRuleVariableByRuleVariableId" parameterType="Long" resultMap="SjyAlertRuleVariableResult">
        <include refid="selectSjyAlertRuleVariableVo"/>
        where rule_variable_id = #{ruleVariableId}
    </select>

    <insert id="insertSjyAlertRuleVariable" parameterType="SjyAlertRuleVariable" useGeneratedKeys="true" keyProperty="ruleVariableId">
        insert into sjy_alert_rule_variable
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleTypeId != null">rule_type_id,</if>
            <if test="variableId != null">variable_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleTypeId != null">#{ruleTypeId},</if>
            <if test="variableId != null">#{variableId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertRuleVariable" parameterType="SjyAlertRuleVariable">
        update sjy_alert_rule_variable
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleTypeId != null">rule_type_id = #{ruleTypeId},</if>
            <if test="variableId != null">variable_id = #{variableId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where rule_variable_id = #{ruleVariableId}
    </update>

    <delete id="deleteSjyAlertRuleVariableByRuleVariableId" parameterType="Long">
        delete from sjy_alert_rule_variable where rule_variable_id = #{ruleVariableId}
    </delete>

    <delete id="deleteSjyAlertRuleVariableByRuleVariableIds" parameterType="String">
        delete from sjy_alert_rule_variable where rule_variable_id in
        <foreach item="ruleVariableId" collection="array" open="(" separator="," close=")">
            #{ruleVariableId}
        </foreach>
    </delete>
</mapper>
