<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyFolderMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyFolder" id="SjyFolderResult">
                    <result property="folderId" column="folder_id"/>
                    <result property="name" column="name"/>
                    <result property="parentFolderId" column="parent_folder_id"/>
                    <result property="cId" column="c_id"/>
                     <result property="cType" column="c_type"/>
                    <result property="folderType" column="folder_type"/>
                    <result property="createBy" column="create_by"/>
                    <result property="createTime" column="create_time"/>
                    <result property="updateBy" column="update_by"/>
                    <result property="updateTime" column="update_time"/>
                    <result property="izDel" column="iz_del"/>
                    <result property="remark" column="remark"/>
            </resultMap>
    <select id="selectByCIdAndType" resultType="com.tzslsjy.business.domain.SjyFolder">
        SELECT folder_id, name, parent_folder_id, c_id, create_by, c_type,folder_type,create_time, update_by, update_time, iz_del, remark
        FROM sjy_folder
        WHERE c_id = #{cId} AND c_type = #{cType}
    </select>

    <select id="selectFolderListByBo" parameterType="com.tzslsjy.business.bo.SjyFolderQueryBo" resultMap="SjyFolderResult">
        SELECT
            folder_id, name, parent_folder_id, c_id, c_type, folder_type,
            create_by, create_time, update_by, update_time, iz_del, remark
        FROM
            sjy_folder
        <where>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="parentFolderId != null and parentFolderId != ''">
                AND parent_folder_id = #{parentFolderId}
            </if>
            <if test="cId != null and cId != ''">
                AND c_id = #{cId}
            </if>
            <if test="cType != null and cType != ''">
                AND c_type = #{cType}
            </if>
            <if test="izDel != null">
                AND iz_del = #{izDel}
            </if>
            <!-- Add other conditions from SjyFolderQueryBo as needed -->
        </where>
    </select>

</mapper>