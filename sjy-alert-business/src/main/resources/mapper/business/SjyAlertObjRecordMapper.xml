<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertObjRecordMapper">

    <resultMap type="SjyAlertObjRecord" id="SjyAlertObjRecordResult">
        <result property="alertRecordId"    column="alert_record_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="alertObj"    column="alert_obj"    />
        <result property="alertObjType"    column="alert_obj_type"    />
        <result property="alertLevel"    column="alert_level"    />
        <result property="alertContent"    column="alert_content"    />
        <result property="status"    column="status"    />
        <result property="sendTime"    column="send_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyAlertObjRecordVo">
        select alert_record_id, rule_id, alert_obj, alert_obj_type, alert_level, alert_content, status, send_time, create_by, create_time, update_by, update_time, remark from sjy_alert_obj_record
    </sql>

    <select id="selectSjyAlertObjRecordList" parameterType="SjyAlertObjRecord" resultMap="SjyAlertObjRecordResult">
        <include refid="selectSjyAlertObjRecordVo"/>
        <where>
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="alertObj != null  and alertObj != ''"> and alert_obj = #{alertObj}</if>
            <if test="alertObjType != null  and alertObjType != ''"> and alert_obj_type = #{alertObjType}</if>
            <if test="alertLevel != null "> and alert_level = #{alertLevel}</if>
            <if test="alertContent != null  and alertContent != ''"> and alert_content = #{alertContent}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="sendTime != null "> and send_time = #{sendTime}</if>
        </where>
    </select>

    <select id="selectSjyAlertObjRecordByAlertRecordId" parameterType="Long" resultMap="SjyAlertObjRecordResult">
        <include refid="selectSjyAlertObjRecordVo"/>
        where alert_record_id = #{alertRecordId}
    </select>

    <insert id="insertSjyAlertObjRecord" parameterType="SjyAlertObjRecord" useGeneratedKeys="true" keyProperty="alertRecordId">
        insert into sjy_alert_obj_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="alertObj != null">alert_obj,</if>
            <if test="alertObjType != null">alert_obj_type,</if>
            <if test="alertLevel != null">alert_level,</if>
            <if test="alertContent != null">alert_content,</if>
            <if test="status != null">status,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId},</if>
            <if test="alertObj != null">#{alertObj},</if>
            <if test="alertObjType != null">#{alertObjType},</if>
            <if test="alertLevel != null">#{alertLevel},</if>
            <if test="alertContent != null">#{alertContent},</if>
            <if test="status != null">#{status},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertObjRecord" parameterType="SjyAlertObjRecord">
        update sjy_alert_obj_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="alertObj != null">alert_obj = #{alertObj},</if>
            <if test="alertObjType != null">alert_obj_type = #{alertObjType},</if>
            <if test="alertLevel != null">alert_level = #{alertLevel},</if>
            <if test="alertContent != null">alert_content = #{alertContent},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where alert_record_id = #{alertRecordId}
    </update>

    <delete id="deleteSjyAlertObjRecordByAlertRecordId" parameterType="Long">
        delete from sjy_alert_obj_record where alert_record_id = #{alertRecordId}
    </delete>

    <delete id="deleteSjyAlertObjRecordByAlertRecordIds" parameterType="String">
        delete from sjy_alert_obj_record where alert_record_id in
        <foreach item="alertRecordId" collection="array" open="(" separator="," close=")">
            #{alertRecordId}
        </foreach>
    </delete>
</mapper>
