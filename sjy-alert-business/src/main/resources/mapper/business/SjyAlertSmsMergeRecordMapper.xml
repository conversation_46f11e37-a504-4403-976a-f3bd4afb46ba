<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertSmsMergeRecordMapper">

    <resultMap type="SjyAlertSmsMergeRecord" id="SjyAlertSmsMergeRecordResult">
        <result property="smsMergeId"    column="sms_merge_id"    />
        <result property="personId"    column="person_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="mergedContent"    column="merged_content"    />
        <result property="alertSmsIds"    column="alert_sms_ids"    />
        <result property="status"    column="status"    />
        <result property="sendTime"    column="send_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyAlertSmsMergeRecordVo">
        select sms_merge_id, person_id, rule_id, merged_content, alert_sms_ids, status, send_time, create_by, create_time, update_by, update_time, remark from sjy_alert_sms_merge_record
    </sql>

    <select id="selectSjyAlertSmsMergeRecordList" parameterType="SjyAlertSmsMergeRecord" resultMap="SjyAlertSmsMergeRecordResult">
        <include refid="selectSjyAlertSmsMergeRecordVo"/>
        <where>
            <if test="personId != null "> and person_id = #{personId}</if>
            <if test="ruleId != null  and ruleId != ''"> and rule_id = #{ruleId}</if>
            <if test="mergedContent != null  and mergedContent != ''"> and merged_content = #{mergedContent}</if>
            <if test="alertSmsIds != null  and alertSmsIds != ''"> and alert_sms_ids = #{alertSmsIds}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="sendTime != null "> and send_time = #{sendTime}</if>
        </where>
    </select>

    <select id="selectSjyAlertSmsMergeRecordBySmsMergeId" parameterType="Long" resultMap="SjyAlertSmsMergeRecordResult">
        <include refid="selectSjyAlertSmsMergeRecordVo"/>
        where sms_merge_id = #{smsMergeId}
    </select>

    <insert id="insertSjyAlertSmsMergeRecord" parameterType="SjyAlertSmsMergeRecord" useGeneratedKeys="true" keyProperty="smsMergeId">
        insert into sjy_alert_sms_merge_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="personId != null">person_id,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="mergedContent != null">merged_content,</if>
            <if test="alertSmsIds != null">alert_sms_ids,</if>
            <if test="status != null">status,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="personId != null">#{personId},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="mergedContent != null">#{mergedContent},</if>
            <if test="alertSmsIds != null">#{alertSmsIds},</if>
            <if test="status != null">#{status},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertSmsMergeRecord" parameterType="SjyAlertSmsMergeRecord">
        update sjy_alert_sms_merge_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="personId != null">person_id = #{personId},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="mergedContent != null">merged_content = #{mergedContent},</if>
            <if test="alertSmsIds != null">alert_sms_ids = #{alertSmsIds},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where sms_merge_id = #{smsMergeId}
    </update>

    <delete id="deleteSjyAlertSmsMergeRecordBySmsMergeId" parameterType="Long">
        delete from sjy_alert_sms_merge_record where sms_merge_id = #{smsMergeId}
    </delete>

    <delete id="deleteSjyAlertSmsMergeRecordBySmsMergeIds" parameterType="String">
        delete from sjy_alert_sms_merge_record where sms_merge_id in
        <foreach item="smsMergeId" collection="array" open="(" separator="," close=")">
            #{smsMergeId}
        </foreach>
    </delete>
</mapper>
