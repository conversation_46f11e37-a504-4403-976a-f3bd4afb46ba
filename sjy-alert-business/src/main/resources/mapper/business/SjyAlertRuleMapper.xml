<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertRuleMapper">

    <!-- ResultMap for SjyAlertRuleNodeRelation directly linked to SjyAlertRule (aliased as rnr) -->
    <resultMap type="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation" id="SjyAlertRuleNodeRelation_RuleScopedResult">
        <id     property="relationId"    column="rnr_relation_id"    />
        <result property="personId"        column="rnr_person_id"        />
        <result property="ruleId"        column="rnr_rule_id"        />
        <result property="nodeId"        column="rnr_node_id"        />
        <result property="alertLevel"    column="rnr_alert_level"    />
        <result property="relationType"  column="rnr_relation_type"  />
        <result property="argId"         column="rnr_arg_id"         /> <!-- This field is in SjyAlertRuleNodeRelation as per user -->
        <result property="createBy"      column="rnr_create_by"      />
        <result property="createTime"    column="rnr_create_time"    />
        <result property="updateBy"      column="rnr_update_by"      />
        <result property="updateTime"    column="rnr_update_time"    />
        <result property="remark"        column="rnr_remark"         />
    </resultMap>

    <!-- ResultMap for SjyAlertRuleNodeRelation linked to SjyAlertRuleArg (aliased as rnra) -->
    <resultMap type="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation" id="SjyAlertRuleNodeRelation_ArgScopedResult">
        <id     property="relationId"    column="rnra_relation_id"   />
        <result property="personId"        column="rnra_person_id"        />
        <result property="ruleId"        column="rnra_rule_id"       />
        <result property="nodeId"        column="rnra_node_id"       />
        <result property="alertLevel"    column="rnra_alert_level"   />
        <result property="relationType"  column="rnra_relation_type" />
        <result property="argId"         column="rnra_arg_id"        /> <!-- This field is in SjyAlertRuleNodeRelation as per user -->
        <result property="createBy"      column="rnra_create_by"     />
        <result property="createTime"    column="rnra_create_time"   />
        <result property="updateBy"      column="rnra_update_by"     />
        <result property="updateTime"    column="rnra_update_time"   />
        <result property="remark"        column="rnra_remark"        />
    </resultMap>

    <!-- ResultMap for SjyAlertTemplateRelation directly linked to SjyAlertRule (aliased as tr) -->
    <resultMap type="com.tzslsjy.business.domain.SjyAlertTemplateRelation" id="SjyAlertTemplateRelation_RuleScopedResult">
        <id     property="id"            column="tr_id"    />
        <result property="templateId"    column="tr_template_id"    />
        <result property="relationId"    column="tr_relation_id"    />
        <result property="relationType"  column="tr_relation_type"  />
        <result property="status"        column="tr_status"         />
        <result property="createBy"      column="tr_create_by"      />
        <result property="createTime"    column="tr_create_time"    />
        <result property="updateBy"      column="tr_update_by"      />
        <result property="updateTime"    column="tr_update_time"    />
        <result property="remark"        column="tr_remark"         />
        <!-- If SjyAlertSmsTemplate is needed, add association here with aliased columns e.g. tr_sms_template_id -->
    </resultMap>

    <!-- ResultMap for SjyAlertTemplateRelation linked to SjyAlertRuleArg (aliased as tra) -->
    <resultMap type="com.tzslsjy.business.domain.SjyAlertTemplateRelation" id="SjyAlertTemplateRelation_ArgScopedResult">
        <id     property="id"            column="tra_id"    />
        <result property="templateId"    column="tra_template_id"   />
        <result property="relationId"    column="tra_relation_id"   />
        <result property="relationType"  column="tra_relation_type" />
        <result property="status"        column="tra_status"        />
        <result property="createBy"      column="tra_create_by"     />
        <result property="createTime"    column="tra_create_time"   />
        <result property="updateBy"      column="tra_update_by"     />
        <result property="updateTime"    column="tra_update_time"   />
        <result property="remark"        column="tra_remark"        />
        <!-- If SjyAlertSmsTemplate is needed, add association here with aliased columns e.g. tra_sms_template_id -->
    </resultMap>

    <!-- ResultMap for SjyAlertRuleArg, with nested collections (aliased as arg) -->
    <resultMap type="com.tzslsjy.business.domain.SjyAlertRuleArg" id="SjyAlertRuleArg_NestedResult">
        <id     property="argId"         column="arg_arg_id"    />
        <result property="relationId"    column="arg_relation_id"    />
        <result property="relationType"  column="arg_relation_type"  />
        <result property="argType"       column="arg_arg_type"       />
        <result property="argJson"       column="arg_arg_json"       />
        <result property="groupId"       column="arg_group_id"       />
        <result property="processOrder"  column="arg_process_order"  />
        <result property="mergeFlag"     column="arg_merge_flag"     />
        <result property="alertLevel"    column="arg_alert_level"    />
        <result property="status"        column="arg_status"         />
        <result property="createBy"      column="arg_create_by"      />
        <result property="createTime"    column="arg_create_time"    />
        <result property="updateBy"      column="arg_update_by"      />
        <result property="updateTime"    column="arg_update_time"    />
        <result property="remark"        column="arg_remark"         />
        <collection property="argNodeRelations"     ofType="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation"   resultMap="SjyAlertRuleNodeRelation_ArgScopedResult" />
        <collection property="argTemplateRelations" ofType="com.tzslsjy.business.domain.SjyAlertTemplateRelation" resultMap="SjyAlertTemplateRelation_ArgScopedResult" />
    </resultMap>

    <!-- Main ResultMap for SjyAlertRule (aliased as r) -->
    <resultMap type="com.tzslsjy.business.domain.SjyAlertRule" id="SjyAlertRuleResult">
        <id     property="ruleId"        column="r_rule_id"    />
        <result property="ruleName"        column="r_rule_name"    />
        <result property="ruleTypeId"      column="r_rule_type_id"    />
        <result property="ruleModel"       column="r_rule_model"    />
        <result property="templateId"      column="r_template_id"    /> <!-- This is SjyAlertRule's own templateId -->
        <result property="sendWay"         column="r_send_way"    />
        <result property="alertStcd"       column="r_alert_stcd"    />
        <result property="alertAdnm"       column="r_alert_adnm"    />
        <result property="alertLevel"      column="r_alert_level"    />
        <result property="argJson"         column="r_arg_json"    /> <!-- This is SjyAlertRule's own argJson -->
        <result property="status"          column="r_status"    />
        <result property="cronExpressions" column="r_cron_expressions"    />
        <result property="reviewMinute"   column="r_review_minute"    />
        <result property="parentId"        column="r_parent_id"    />
        <result property="createBy"        column="r_create_by"    />
        <result property="createTime"      column="r_create_time"    />
        <result property="updateBy"        column="r_update_by"    />
        <result property="updateTime"      column="r_update_time"    />
        <result property="remark"          column="r_remark"    />
        <collection property="sjyAlertRuleArgs"      ofType="com.tzslsjy.business.domain.SjyAlertRuleArg"          resultMap="SjyAlertRuleArg_SimpleResult" />
    </resultMap>

    <!-- Simplified ResultMap for SjyAlertRuleArg without nested collections -->
    <resultMap type="com.tzslsjy.business.domain.SjyAlertRuleArg" id="SjyAlertRuleArg_SimpleResult">
        <id     property="argId"         column="arg_arg_id"    />
        <result property="relationId"    column="arg_relation_id"    />
        <result property="relationType"  column="arg_relation_type"  />
        <result property="argType"       column="arg_arg_type"       />
        <result property="argJson"       column="arg_arg_json"       />
        <result property="groupId"       column="arg_group_id"       />
        <result property="processOrder"  column="arg_process_order"  />
        <result property="mergeFlag"     column="arg_merge_flag"     />
        <result property="alertLevel"    column="arg_alert_level"    />
        <result property="status"        column="arg_status"         />
        <result property="createBy"      column="arg_create_by"      />
        <result property="createTime"    column="arg_create_time"    />
        <result property="updateBy"      column="arg_update_by"      />
        <result property="updateTime"    column="arg_update_time"    />
        <result property="remark"        column="arg_remark"         />
    </resultMap>

    <sql id="selectSjyAlertRuleVo">
        SELECT
            r.rule_id           AS r_rule_id,
            r.rule_name         AS r_rule_name,
            r.rule_type_id      AS r_rule_type_id,
            r.rule_model        AS r_rule_model,
            r.send_way          AS r_send_way,
            r.alert_stcd        AS r_alert_stcd,
            r.alert_adnm        AS r_alert_adnm,
            r.alert_level       AS r_alert_level,
            r.arg_json          AS r_arg_json,
            r.status            AS r_status,
            r.cron_expressions  AS r_cron_expressions,
            r.create_by         AS r_create_by,
            r.create_time       AS r_create_time,
            r.update_by         AS r_update_by,
            r.update_time       AS r_update_time,
            r.remark            AS r_remark,
            r.review_minute     AS r_review_minute,
            r.parent_id         AS r_parent_id,

            arg.arg_id          AS arg_arg_id,
            arg.relation_id     AS arg_relation_id,
            arg.relation_type   AS arg_relation_type,
            arg.arg_type        AS arg_arg_type,
            arg.arg_json        AS arg_arg_json,
            arg.group_id        AS arg_group_id,
            arg.process_order   AS arg_process_order,
            arg.alert_level     AS arg_alert_level,
            arg.status          AS arg_status,
            arg.create_by       AS arg_create_by,
            arg.create_time     AS arg_create_time,
            arg.update_by       AS arg_update_by,
            arg.update_time     AS arg_update_time,
            arg.remark          AS arg_remark

        FROM sjy_alert_rule r
        LEFT JOIN sjy_alert_rule_arg arg ON r.rule_id = arg.relation_id AND arg.relation_type = '1'
    </sql>

    <select id="selectSjyAlertRuleList" parameterType="com.tzslsjy.business.domain.SjyAlertRule" resultMap="SjyAlertRuleResult">
        <include refid="selectSjyAlertRuleVo"/>
        <where>
            <if test="ruleName != null  and ruleName != ''"> and r.rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="ruleTypeId != null  and ruleTypeId != ''"> and r.rule_type_id = #{ruleTypeId}</if>
            <if test="ruleModel != null  and ruleModel != ''"> and r.rule_model = #{ruleModel}</if>
            <if test="templateId != null "> and r.template_id = #{templateId}</if> <!-- This refers to r.template_id -->
            <if test="sendWay != null  and sendWay != ''"> and r.send_way = #{sendWay}</if>
            <if test="alertStcd != null  and alertStcd != ''"> and r.alert_stcd = #{alertStcd}</if>
            <if test="alertAdnm != null  and alertAdnm != ''"> and r.alert_adnm = #{alertAdnm}</if>
            <if test="alertLevel != null "> and r.alert_level = #{alertLevel}</if> <!-- This refers to r.alert_level -->
            <if test="argJson != null  and argJson != ''"> and r.arg_json = #{argJson}</if> <!-- This refers to r.arg_json -->
            <if test="status != null "> and r.status = #{status}</if> <!-- This refers to r.status -->
            <if test="cronExpressions != null  and cronExpressions != ''"> and r.cron_expressions = #{cronExpressions}</if>
        </where>
    </select>

    <select id="selectSjyAlertRuleByRuleId" parameterType="java.lang.Long" resultMap="SjyAlertRuleResult">
        <include refid="selectSjyAlertRuleVo"/>
        where r.rule_id = #{ruleId}
    </select>


    <select id="selectSjyAlertRuleByRuleType" parameterType="java.lang.Long" resultMap="SjyAlertRuleResult">
        <include refid="selectSjyAlertRuleVo"/>
        where r.rule_type_id = #{ruleTypeId}
    </select>
    <select id="getFloodBaseRule" resultType="com.tzslsjy.business.domain.SjyAlertRule">
        select * from sjy_alert_rule where rule_type_id = 1 and parent_id is null
    </select>
    <select id="selectSjyAlertRuleByStcd" resultType="com.tzslsjy.business.domain.SjyAlertRule">
        select * from sjy_alert_rule where alert_stcd in

            <foreach item="stcd" collection="list" open="(" separator="," close=")">
                #{stcd}
            </foreach>
            and alert_adnm ="fyhcl"
    </select>

    <insert id="insertSjyAlertRule" parameterType="SjyAlertRule" useGeneratedKeys="true" keyProperty="ruleId">
        insert into sjy_alert_rule
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleName != null">rule_name,</if>
            <if test="ruleTypeId != null">rule_type_id,</if>
            <if test="ruleModel != null">rule_model,</if>

            <if test="sendWay != null">send_way,</if>
            <if test="alertStcd != null">alert_stcd,</if>
            <if test="alertAdnm != null">alert_adnm,</if>
            <if test="alertLevel != null">alert_level,</if>
            <if test="argJson != null">arg_json,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="reviewMinute != null">review_minute,</if>
            <if test="cronExpressions != null">cron_expressions,</if>
            <if test="parentId != null">parent_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleName != null">#{ruleName},</if>
            <if test="ruleTypeId != null">#{ruleTypeId},</if>
            <if test="ruleModel != null">#{ruleModel},</if>

            <if test="sendWay != null">#{sendWay},</if>
            <if test="alertStcd != null">#{alertStcd},</if>
            <if test="alertAdnm != null">#{alertAdnm},</if>
            <if test="alertLevel != null">#{alertLevel},</if>
            <if test="argJson != null">#{argJson},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="reviewMinute != null">#{reviewMinute},</if>
            <if test="cronExpressions != null">#{cronExpressions},</if>
            <if test="parentId != null">#{parentId},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertRule" parameterType="SjyAlertRule">
        update sjy_alert_rule
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleName != null">rule_name = #{ruleName},</if>
            <if test="ruleTypeId != null">rule_type_id = #{ruleTypeId},</if>
            <if test="ruleModel != null">rule_model = #{ruleModel},</if>

            <if test="sendWay != null">send_way = #{sendWay},</if>
            <if test="alertStcd != null">alert_stcd = #{alertStcd},</if>
            <if test="alertAdnm != null">alert_adnm = #{alertAdnm},</if>
            <if test="alertLevel != null">alert_level = #{alertLevel},</if>
            <if test="argJson != null">arg_json = #{argJson},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="reviewMinute != null">review_minute = #{reviewMinute},</if>
            <if test="cronExpressions != null">cron_expressions = #{cronExpressions},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
        </trim>
        where rule_id = #{ruleId}
    </update>

    <delete id="deleteSjyAlertRuleByRuleId" parameterType="Long">
        delete from sjy_alert_rule where rule_id = #{ruleId}
    </delete>

    <delete id="deleteSjyAlertRuleByRuleIds" parameterType="String">
        delete from sjy_alert_rule where rule_id in
        <foreach item="ruleId" collection="array" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

    <!-- 根据规则ID列表查询节点关系 -->
    <select id="selectNodeRelationsByRuleIds" resultType="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation">
        SELECT
            relation_id as relationId,
            person_id as personId,
            rule_id as ruleId,
            node_id as nodeId,
            alert_level as alertLevel,
            relation_type as relationType,
            arg_id as argId,
            create_by as createBy,
            create_time as createTime,
            update_by as updateBy,
            update_time as updateTime,
            remark as remark
        FROM sjy_alert_rule_node_relation
        WHERE rule_id IN
        <foreach item="ruleId" collection="ruleIds" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
        AND arg_id IS NULL
    </select>

    <!-- 根据参数ID列表查询节点关系 -->
    <select id="selectNodeRelationsByArgIds" resultType="com.tzslsjy.business.domain.SjyAlertRuleNodeRelation">
        SELECT
            relation_id as relationId,
            person_id as personId,
            rule_id as ruleId,
            node_id as nodeId,
            alert_level as alertLevel,
            relation_type as relationType,
            arg_id as argId,
            create_by as createBy,
            create_time as createTime,
            update_by as updateBy,
            update_time as updateTime,
            remark as remark
        FROM sjy_alert_rule_node_relation
        WHERE arg_id IN
        <foreach item="argId" collection="argIds" open="(" separator="," close=")">
            #{argId}
        </foreach>
    </select>

    <!-- 根据规则ID列表查询模板关系 -->
    <select id="selectTemplateRelationsByRuleIds" resultType="com.tzslsjy.business.domain.SjyAlertTemplateRelation">
        SELECT
            id as id,
            template_id as templateId,
            relation_id as relationId,
            relation_type as relationType,
            status as status,
            create_by as createBy,
            create_time as createTime,
            update_by as updateBy,
            update_time as updateTime,
            remark as remark
        FROM sjy_alert_template_relation
        WHERE relation_id IN
        <foreach item="ruleId" collection="ruleIds" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
        AND relation_type = '1'
    </select>

    <!-- 根据参数ID列表查询模板关系 -->
    <select id="selectTemplateRelationsByArgIds" resultType="com.tzslsjy.business.domain.SjyAlertTemplateRelation">
        SELECT
            id as id,
            template_id as templateId,
            relation_id as relationId,
            relation_type as relationType,
            status as status,
            create_by as createBy,
            create_time as createTime,
            update_by as updateBy,
            update_time as updateTime,
            remark as remark
        FROM sjy_alert_template_relation
        WHERE relation_id IN
        <foreach item="argId" collection="argIds" open="(" separator="," close=")">
            #{argId}
        </foreach>
        AND relation_type = '2'
    </select>
    <select id="selectSimpleRuleList" resultType="com.tzslsjy.business.domain.SjyAlertRule">
        select * from sjy_alert_rule
        <where>
            <if test="ruleTypeId != null  and ruleTypeId != ''"> and rule_type_id = #{ruleTypeId}</if>
            <if test="ruleName != null  and ruleName != ''"> and rule_name like concat('%', #{ruleName}, '%')</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>

            <if test="ruleModel != null  and ruleModel != ''"> and rule_model = #{ruleModel}</if>
        </where>
    </select>

    <!-- 根据测站ID列表和规则类型查询规则 -->
    <select id="selectByStationIdsAndRuleType" resultType="com.tzslsjy.business.domain.SjyAlertRule">
        SELECT * FROM sjy_alert_rule
        WHERE rule_type_id = #{ruleTypeId}
        <if test="stationIds != null and stationIds.size() > 0">
            AND alert_stcd IN
            <foreach item="stationId" collection="stationIds" open="(" separator="," close=")">
                #{stationId}
            </foreach>
        </if>
    </select>

    <!-- 根据规则类型查询所有规则 -->
    <select id="selectByRuleType" resultType="com.tzslsjy.business.domain.SjyAlertRule">
        SELECT * FROM sjy_alert_rule
        WHERE rule_type_id = #{ruleTypeId}
        ORDER BY create_time DESC
    </select>
</mapper>
