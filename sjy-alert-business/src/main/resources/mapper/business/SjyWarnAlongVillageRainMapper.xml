<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyWarnAlongVillageRainMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyWarnAlongVillageRain" id="SjyWarnAlongVillageRainResult">
                    <result property="adcd" column="adcd"/>
                    <result property="lwater" column="lwater"/>
                    <result property="ldarea" column="ldarea"/>
                    <result property="zbpv" column="zbpv"/>
                    <result property="ljpv" column="ljpv"/>
                    <result property="p" column="p"/>
                    <result property="createBy" column="create_by"/>
                    <result property="createTime" column="create_time"/>
                    <result property="updateBy" column="update_by"/>
                    <result property="updateTime" column="update_time"/>
                    <result property="izDel" column="iz_del"/>
                    <result property="remark" column="remark"/>
            </resultMap>
    <select id="selectByAdcd" resultType="com.tzslsjy.business.domain.SjyWarnAlongVillageRain">
        select * from sjy_warn_along_village_rain
        where  adcd =#{adcd}
    </select>


</mapper>