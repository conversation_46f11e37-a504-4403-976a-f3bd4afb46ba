<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyPanoramaBMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyPanoramaB" id="SjyPanoramaBResult">
                    <result property="id" column="id"/>
                    <result property="name" column="name"/>
                    <result property="address" column="address"/>
                    <result property="lgtd" column="lgtd"/>
                    <result property="lttd" column="lttd"/>
                    <result property="areacode" column="areacode"/>
                    <result property="stcategory" column="stcategory"/>
                    <result property="ennmcd" column="ennmcd"/>
                    <result property="swfAttaId" column="swf_atta_id"/>
                    <result property="appScanUrl" column="app_scan_url"/>
                    <result property="createBy" column="create_by"/>
                    <result property="createTime" column="create_time"/>
                    <result property="updateBy" column="update_by"/>
                    <result property="updateTime" column="update_time"/>
                    <result property="izDel" column="iz_del"/>
                    <result property="remark" column="remark"/>
            </resultMap>


</mapper>