<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertStSituationHisMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyAlertStSituationHis" id="SjyAlertStSituationHisResult">
        <id     property="id"       column="id"      />
        <result property="stcd"     column="stcd"    />
        <result property="cId"      column="c_id"    />
        <result property="cType"    column="c_type"  />
        <result property="status"   column="status"  />
        <result property="startTime" column="start_time" />
        <result property="endTime"   column="end_time"   />
        <result property="createBy"   column="create_by"    />
        <result property="createTime" column="create_time"  />
        <result property="updateBy"   column="update_by"    />
        <result property="updateTime" column="update_time"  />
        <result property="remark"     column="remark"  />
    </resultMap>

    <sql id="selectSjyAlertStSituationHisVo">
        select id, stcd, c_id, c_type, status, start_time, end_time, create_by, create_time, update_by, update_time, remark from sjy_alert_st_situation_his
    </sql>

    <select id="selectSjyAlertStSituationHisList" parameterType="com.tzslsjy.business.domain.SjyAlertStSituationHis" resultMap="SjyAlertStSituationHisResult">
        <include refid="selectSjyAlertStSituationHisVo"/>
        <where>
            <if test="stcd != null  and stcd != ''"> and stcd = #{stcd}</if>
            <if test="cId != null "> and c_id = #{cId}</if>
            <if test="cType != null "> and c_type = #{cType}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>

    <insert id="insertSjyAlertStSituationHis" parameterType="com.tzslsjy.business.domain.SjyAlertStSituationHis" useGeneratedKeys="true" keyProperty="id">
        insert into sjy_alert_st_situation_his
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stcd != null and stcd != ''">stcd,</if>
            <if test="cId != null">c_id,</if>
            <if test="cType != null">c_type,</if>
            <if test="status != null">status,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stcd != null and stcd != ''">#{stcd},</if>
            <if test="cId != null">#{cId},</if>
            <if test="cType != null">#{cType},</if>
            <if test="status != null">#{status},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertSjyAlertStSituationHis" parameterType="java.util.List">
        insert into sjy_alert_st_situation_his (stcd, c_id, c_type, status, start_time, end_time, create_by, create_time, update_by, update_time, remark)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.stcd}, #{item.cId}, #{item.cType}, #{item.status}, #{item.startTime}, #{item.endTime}, #{item.createBy}, #{item.createTime}, #{item.updateBy}, #{item.updateTime}, #{item.remark})
        </foreach>
    </insert>

</mapper>