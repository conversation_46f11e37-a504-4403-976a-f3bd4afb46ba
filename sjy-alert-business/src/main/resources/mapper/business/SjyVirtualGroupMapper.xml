<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyVirtualGroupMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyVirtualGroup" id="SjyVirtualGroupResult">
        <result property="id"    column="id"/>
        <result property="idList"    column="id_list"/>
        <result property="members"    column="members"/>
        <result property="name"    column="name"/>
        <result property="orderId"    column="order_id"/>
        <result property="createBy"    column="create_by"/>
        <result property="createTime"    column="create_time"/>
        <result property="updateBy"    column="update_by"/>
        <result property="updateTime"    column="update_time"/>
        <result property="izDel"    column="iz_del"/>
        <result property="remark"    column="remark"/>
    </resultMap>
    <select id="selectMembersByIds" resultType="java.lang.String">
        select  members  from sjy_virtual_group where id in
        <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>