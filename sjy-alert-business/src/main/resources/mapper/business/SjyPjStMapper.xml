<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyPjStMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyPjSt" id="SjyPjStResult">
        <result property="id"    column="id"    />
        <result property="stcd"    column="stcd"    />
        <result property="pjcd"    column="pjcd"    />
        <result property="isMian"    column="is_mian"    />
        <result property="isRain"    column="is_rain"    />
        <result property="rainWeight"    column="rain_weight"    />
        <result property="projType"    column="proj_type"    />
        <result property="orderNum"    column="order_num"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="izDel"    column="iz_del"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyPjStVo">
        select id, stcd, pjcd, is_mian, is_rain, rain_weight, proj_type, order_num, create_by, create_time, update_by, update_time, iz_del, remark from sjy_pj_st
    </sql>

    <select id="getlistWithRzAndDrp" parameterType="com.tzslsjy.business.domain.SjyPjSt" resultMap="SjyPjStResult">
        <include refid="selectSjyPjStVo"/>
        where iz_del = 1
        order by order_num
    </select>
    <select id="getList" resultType="com.tzslsjy.business.domain.SjyPjSt">
        <include refid="selectSjyPjStVo"/>
        where iz_del = 1
        <if test="stcd != null and stcd != ''"> and stcd = #{stcd}</if>
        <if test="pjcd != null and pjcd != ''"> and pjcd = #{pjcd}</if>
        <if test="isMian != null"> and is_mian = #{isMian}</if>
        <if test="isRain != null"> and is_rain = #{isRain}</if>
        <if test="projType != null and projType != ''"> and proj_type = #{projType}</if>
    </select>

</mapper>