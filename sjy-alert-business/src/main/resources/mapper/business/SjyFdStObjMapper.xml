<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyFdStObjMapper">

    <resultMap type="SjyFdStObj" id="SjyFdStObjResult">
        <result property="stCode"    column="st_code"    />
        <result property="adCode"    column="ad_code"    />
        <result property="adminAdnm"    column="admin_adnm"    />
        <result property="naturalAdnm"    column="natural_adnm"    />
        <result property="stName"    column="st_name"    />
        <result property="fromDate"    column="from_date"    />
        <result property="toDate"    column="to_date"    />
        <result property="origCode"    column="orig_code"    />
        <result property="geoCode"    column="geo_code"    />
        <result property="tongTime"    column="tong_time"    />
        <result property="op"    column="op"    />
        <result property="modifyTime"    column="modify_time"    />
        <result property="isLock"    column="is_lock"    />
        <result property="checkId"    column="check_id"    />
        <result property="isDataOfficial"    column="is_data_official"    />
        <result property="dataOfficialTime"    column="data_official_time"    />
        <result property="checkStatus"    column="check_status"    />
        <result property="checkReason"    column="check_reason"    />
        <result property="stcd"    column="stcd"    />
        <result property="adcd"    column="adcd"    />
    </resultMap>

    <sql id="selectSjyFdStObjVo">
        select st_code, ad_code, admin_adnm, natural_adnm, st_name, from_date, to_date, orig_code, geo_code, tong_time, op, modify_time, is_lock, check_id, is_data_official, data_official_time, check_status, check_reason, stcd, adcd from sjy_fd_st_obj
    </sql>

    <select id="selectSjyFdStObjList" parameterType="SjyFdStObj" resultMap="SjyFdStObjResult">
        <include refid="selectSjyFdStObjVo"/>
        <where>
            <if test="adCode != null  and adCode != ''"> and ad_code = #{adCode}</if>
            <if test="adminAdnm != null  and adminAdnm != ''"> and admin_adnm = #{adminAdnm}</if>
            <if test="naturalAdnm != null  and naturalAdnm != ''"> and natural_adnm = #{naturalAdnm}</if>
            <if test="stName != null  and stName != ''"> and st_name like concat('%', #{stName}, '%')</if>
            <if test="fromDate != null "> and from_date = #{fromDate}</if>
            <if test="toDate != null "> and to_date = #{toDate}</if>
            <if test="origCode != null  and origCode != ''"> and orig_code = #{origCode}</if>
            <if test="geoCode != null  and geoCode != ''"> and geo_code = #{geoCode}</if>
            <if test="tongTime != null "> and tong_time = #{tongTime}</if>
            <if test="op != null  and op != ''"> and op = #{op}</if>
            <if test="modifyTime != null "> and modify_time = #{modifyTime}</if>
            <if test="isLock != null  and isLock != ''"> and is_lock = #{isLock}</if>
            <if test="checkId != null  and checkId != ''"> and check_id = #{checkId}</if>
            <if test="isDataOfficial != null  and isDataOfficial != ''"> and is_data_official = #{isDataOfficial}</if>
            <if test="dataOfficialTime != null "> and data_official_time = #{dataOfficialTime}</if>
            <if test="checkStatus != null  and checkStatus != ''"> and check_status = #{checkStatus}</if>
            <if test="checkReason != null  and checkReason != ''"> and check_reason = #{checkReason}</if>
            <if test="stcd != null  and stcd != ''"> and stcd = #{stcd}</if>
            <if test="adcd != null  and adcd != ''"> and adcd = #{adcd}</if>
        </where>
    </select>

    <select id="selectSjyFdStObjByStCode" parameterType="String" resultMap="SjyFdStObjResult">
        <include refid="selectSjyFdStObjVo"/>
        where st_code = #{stCode}
    </select>
    <select id="selectVillageByStcd" resultType="com.tzslsjy.business.domain.SjyFdProneArea">
        select  tb1.* from sjy_fd_prone_area tb1 right join sjy_fd_st_obj tb2 on tb1.nature_adnm =tb2.nature_adnm where stcd = #{stcd}
    </select>
    <select id="selectVillageByStCode" resultType="com.tzslsjy.business.domain.SjyFdProneArea">
        select  tb1.* from sjy_fd_prone_area tb1 right join sjy_fd_st_obj tb2 on tb1.nature_adnm =tb2.nature_adnm where st_code = #{stCode}
    </select>
    <select id="selectSjyFdStObjByStcd" resultType="com.tzslsjy.business.domain.SjyFdStObj">
        select  tb1.* from sjy_fd_st_obj tb1 right join sjy_fd_prone_area tb2 on tb1.natural_adnm =tb2.natural_adnm where stcd = #{stcd}
    </select>
    <select id="selectOneByStcd" resultType="com.tzslsjy.business.domain.SjyFdStObj">
        select  * from sjy_fd_st_obj  where stcd = #{stcd} limit 1
    </select>

    <insert id="insertSjyFdStObj" parameterType="SjyFdStObj">
        insert into sjy_fd_st_obj
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stCode != null">st_code,</if>
            <if test="adCode != null">ad_code,</if>
            <if test="adminAdnm != null">admin_adnm,</if>
            <if test="naturalAdnm != null">natural_adnm,</if>
            <if test="stName != null">st_name,</if>
            <if test="fromDate != null">from_date,</if>
            <if test="toDate != null">to_date,</if>
            <if test="origCode != null">orig_code,</if>
            <if test="geoCode != null">geo_code,</if>
            <if test="tongTime != null">tong_time,</if>
            <if test="op != null">op,</if>
            <if test="modifyTime != null">modify_time,</if>
            <if test="isLock != null">is_lock,</if>
            <if test="checkId != null">check_id,</if>
            <if test="isDataOfficial != null">is_data_official,</if>
            <if test="dataOfficialTime != null">data_official_time,</if>
            <if test="checkStatus != null">check_status,</if>
            <if test="checkReason != null">check_reason,</if>
            <if test="stcd != null">stcd,</if>
            <if test="adcd != null and adcd != ''">adcd,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stCode != null">#{stCode},</if>
            <if test="adCode != null">#{adCode},</if>
            <if test="adminAdnm != null">#{adminAdnm},</if>
            <if test="naturalAdnm != null">#{naturalAdnm},</if>
            <if test="stName != null">#{stName},</if>
            <if test="fromDate != null">#{fromDate},</if>
            <if test="toDate != null">#{toDate},</if>
            <if test="origCode != null">#{origCode},</if>
            <if test="geoCode != null">#{geoCode},</if>
            <if test="tongTime != null">#{tongTime},</if>
            <if test="op != null">#{op},</if>
            <if test="modifyTime != null">#{modifyTime},</if>
            <if test="isLock != null">#{isLock},</if>
            <if test="checkId != null">#{checkId},</if>
            <if test="isDataOfficial != null">#{isDataOfficial},</if>
            <if test="dataOfficialTime != null">#{dataOfficialTime},</if>
            <if test="checkStatus != null">#{checkStatus},</if>
            <if test="checkReason != null">#{checkReason},</if>
            <if test="stcd != null">#{stcd},</if>
            <if test="adcd != null and adcd != ''">#{adcd},</if>
         </trim>
    </insert>

    <update id="updateSjyFdStObj" parameterType="SjyFdStObj">
        update sjy_fd_st_obj
        <trim prefix="SET" suffixOverrides=",">
            <if test="adCode != null">ad_code = #{adCode},</if>
            <if test="adminAdnm != null">admin_adnm = #{adminAdnm},</if>
            <if test="naturalAdnm != null">natural_adnm = #{naturalAdnm},</if>
            <if test="stName != null">st_name = #{stName},</if>
            <if test="fromDate != null">from_date = #{fromDate},</if>
            <if test="toDate != null">to_date = #{toDate},</if>
            <if test="origCode != null">orig_code = #{origCode},</if>
            <if test="geoCode != null">geo_code = #{geoCode},</if>
            <if test="tongTime != null">tong_time = #{tongTime},</if>
            <if test="op != null">op = #{op},</if>
            <if test="modifyTime != null">modify_time = #{modifyTime},</if>
            <if test="isLock != null">is_lock = #{isLock},</if>
            <if test="checkId != null">check_id = #{checkId},</if>
            <if test="isDataOfficial != null">is_data_official = #{isDataOfficial},</if>
            <if test="dataOfficialTime != null">data_official_time = #{dataOfficialTime},</if>
            <if test="checkStatus != null">check_status = #{checkStatus},</if>
            <if test="checkReason != null">check_reason = #{checkReason},</if>
            <if test="stcd != null">stcd = #{stcd},</if>
            <if test="adcd != null and adcd != ''">adcd = #{adcd},</if>
        </trim>
        where st_code = #{stCode}
    </update>

    <delete id="deleteSjyFdStObjByStCode" parameterType="String">
        delete from sjy_fd_st_obj where st_code = #{stCode}
    </delete>

    <delete id="deleteSjyFdStObjByStCodes" parameterType="String">
        delete from sjy_fd_st_obj where st_code in
        <foreach item="stCode" collection="array" open="(" separator="," close=")">
            #{stCode}
        </foreach>
    </delete>
</mapper>
