<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.data.StWasRMapper">

    <resultMap type="com.tzslsjy.business.domain.data.StWasR" id="StWasRResult">
        <result property="stcd"    column="STCD"/>
        <result property="tm"    column="TM"/>
        <result property="upz"    column="UPZ"/>
        <result property="dwz"    column="DWZ"/>
        <result property="tgtq"    column="TGTQ"/>
        <result property="swchrcd"    column="SWCHRCD"/>
        <result property="supwptn"    column="SUPWPTN"/>
        <result property="sdwwptn"    column="SDWWPTN"/>
        <result property="msqmt"    column="MSQMT"/>
        <result property="moditime"    column="MODITIME"/>
        <result property="edited"    column="EDITED"/>
        <result property="bFlag"    column="B_Flag"/>
    </resultMap>
    <select id="selectExistStcdsByStcds" resultType="java.lang.String">
        select stcd from st_was_r
        <where>
            <if test="stcds != null">
                stcd in
                <foreach item="stcd" collection="stcds" open="(" separator="," close=")">
                    #{stcd}
                </foreach>
            </if>
            and tm &gt;= #{lastTongTime} and tm &lt;= #{currentTime}
        </where>
    </select>

    <!-- selectExistStcdsByStcds --> 


</mapper>