<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertNodeMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyAlertNode" id="SjyAlertNodeResult">
        <id property="nodeId"    column="n_node_id"    jdbcType="VARCHAR"/>
        <result property="nodeName"    column="n_node_name"    jdbcType="VARCHAR"/>
        <result property="parentId"    column="n_parent_id"    jdbcType="VARCHAR"/>
        <result property="orderNum"    column="n_order_num"    jdbcType="INTEGER"/>
        <result property="nodeType"    column="n_node_type"    jdbcType="BIGINT"/>
        <result property="status"    column="n_status"    jdbcType="BIGINT"/>
        <result property="adcd"    column="n_adcd"    jdbcType="VARCHAR"/>
        <result property="createBy"    column="n_create_by"    jdbcType="VARCHAR"/>
        <result property="createTime"    column="n_create_time"    jdbcType="TIMESTAMP"/>
        <result property="updateBy"    column="n_update_by"    jdbcType="VARCHAR"/>
        <result property="updateTime"    column="n_update_time"    jdbcType="TIMESTAMP"/>
        <result property="remark"    column="n_remark"    jdbcType="VARCHAR"/>
        <collection property="nodePosts" ofType="com.tzslsjy.business.domain.SjyAlertNodePost" resultMap="SjyAlertNodePostSubResult"/>
    </resultMap>

    <resultMap type="com.tzslsjy.business.domain.SjyAlertNodePost" id="SjyAlertNodePostSubResult">
        <id property="postId" column="np_id" jdbcType="VARCHAR"/> <!-- Assuming postId is String -->
        <result property="nodeId" column="np_node_id" jdbcType="VARCHAR"/> <!-- Assuming nodeId is String -->
        <result property="postName" column="np_post_name" jdbcType="VARCHAR"/>
        <result property="sortNum" column="np_sort_num" jdbcType="INTEGER"/> <!-- Added sortNum -->
        <result property="remark" column="np_remark" jdbcType="VARCHAR"/>
        <result property="createBy" column="np_create_by" jdbcType="VARCHAR"/>
        <result property="createTime" column="np_create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="np_update_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="np_update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="selectSjyAlertNodeVo">
        SELECT
            n.node_id as n_node_id,
            n.node_name as n_node_name,
            n.parent_id as n_parent_id,
            n.order_num as n_order_num, -- Added order_num
            n.node_type as n_node_type,
            n.status as n_status,
            n.adcd as n_adcd,
            n.create_by as n_create_by,
            n.create_time as n_create_time,
            n.update_by as n_update_by,
            n.update_time as n_update_time,
            n.remark as n_remark,

            np.post_id as np_id,
            np.node_id as np_node_id,
            np.post_name as np_post_name,
            np.sort_num as np_sort_num, -- Added sort_num for post
            np.remark as np_remark,
            np.create_by as np_create_by,
            np.create_time as np_create_time,
            np.update_by as np_update_by,
            np.update_time as np_update_time
        FROM
            sjy_alert_node n
        LEFT JOIN
            sjy_alert_node_post np ON n.node_id = np.node_id
    </sql>

    <select id="selectSjyAlertNodeList" parameterType="com.tzslsjy.business.domain.SjyAlertNode" resultMap="SjyAlertNodeResult">
        <include refid="selectSjyAlertNodeVo"/>
        <where>
            <if test="nodeName != null  and nodeName != ''"> and n.node_name like concat('%', #{nodeName, jdbcType=VARCHAR}, '%')</if>
            <if test="parentId != null and parentId != ''"> and n.parent_id = #{parentId, jdbcType=VARCHAR}</if>
            <if test="nodeType != null "> and n.node_type = #{nodeType, jdbcType=BIGINT}</if>
            <if test="status != null "> and n.status = #{status, jdbcType=BIGINT}</if>
            <if test="adcd != null  and adcd != ''"> and n.adcd = #{adcd, jdbcType=VARCHAR}</if>
            <if test="orderNum != null "> and n.order_num = #{orderNum, jdbcType=INTEGER}</if>
        </where>
        <!-- Add order by clause if needed, e.g., ORDER BY n.parent_id, n.order_num -->
    </select>

    <select id="selectSjyAlertNodeByNodeId" parameterType="java.lang.String" resultMap="SjyAlertNodeResult">
        <include refid="selectSjyAlertNodeVo"/>
        where n.node_id = #{nodeId, jdbcType=VARCHAR}
    </select>
    <select id="selectPersonByAdnmAndArgId" resultType="com.tzslsjy.business.domain.SjyAlertPerson">
        SELECT
            p.*
        FROM
            sjy_alert_person p
        INNER JOIN sjy_alert_region_person rp ON p.person_id = rp.person_id <!-- Assuming p.person_id and rp.person_id are compatible (e.g. both VARCHAR) -->
        INNER JOIN sjy_base_region r ON rp.region_id = r.region_id
        INNER JOIN sjy_alert_arg_person ap ON p.person_id = ap.person_id <!-- Assuming ap.person_id is compatible -->
        WHERE
            r.region_code = #{adnm, jdbcType=VARCHAR}
            AND ap.arg_id = #{argId, jdbcType=BIGINT}
            AND p.status = 1
        ORDER BY
            p.person_id
    </select>
    <select id="selectNodeIdsByAncestor" resultType="java.lang.String">
        SELECT
            n.node_id
        FROM
            sjy_alert_node n
        WHERE
            find_in_set(#{ancestorId, jdbcType=VARCHAR},  n.ancestors)
          AND n.status = 1
    </select>

    <insert id="insertSjyAlertNode" parameterType="com.tzslsjy.business.domain.SjyAlertNode">
        insert into sjy_alert_node
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nodeId != null and nodeId != ''">node_id,</if>
            <if test="nodeName != null and nodeName != ''">node_name,</if>
            <if test="parentId != null and parentId != ''">parent_id,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="nodeType != null">node_type,</if>
            <if test="status != null">status,</if>
            <if test="adcd != null and adcd != ''">adcd,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nodeId != null and nodeId != ''">#{nodeId, jdbcType=VARCHAR},</if>
            <if test="nodeName != null and nodeName != ''">#{nodeName, jdbcType=VARCHAR},</if>
            <if test="parentId != null and parentId != ''">#{parentId, jdbcType=VARCHAR},</if>
            <if test="orderNum != null">#{orderNum, jdbcType=INTEGER},</if>
            <if test="nodeType != null">#{nodeType, jdbcType=BIGINT},</if>
            <if test="status != null">#{status, jdbcType=BIGINT},</if>
            <if test="adcd != null and adcd != ''">#{adcd, jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">#{createBy, jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime, jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy, jdbcType=VARCHAR},</if>
            <if test="updateTime != null">#{updateTime, jdbcType=TIMESTAMP},</if>
            <if test="remark != null and remark != ''">#{remark, jdbcType=VARCHAR},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertNode" parameterType="com.tzslsjy.business.domain.SjyAlertNode">
        update sjy_alert_node
        <trim prefix="SET" suffixOverrides=",">
            <if test="nodeName != null and nodeName != ''">node_name = #{nodeName, jdbcType=VARCHAR},</if>
            <if test="parentId != null and parentId != ''">parent_id = #{parentId, jdbcType=VARCHAR},</if>
            <if test="orderNum != null">order_num = #{orderNum, jdbcType=INTEGER},</if>
            <if test="nodeType != null">node_type = #{nodeType, jdbcType=BIGINT},</if>
            <if test="status != null">status = #{status, jdbcType=BIGINT},</if>
            <if test="adcd != null and adcd != ''">adcd = #{adcd, jdbcType=VARCHAR},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy, jdbcType=VARCHAR},</if>
            <if test="createTime != null">create_time = #{createTime, jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy, jdbcType=VARCHAR},</if>
            <if test="updateTime != null">update_time = #{updateTime, jdbcType=TIMESTAMP},</if>
            <if test="remark != null and remark != ''">remark = #{remark, jdbcType=VARCHAR},</if>
        </trim>
        where node_id = #{nodeId, jdbcType=VARCHAR}
    </update>

    <delete id="deleteSjyAlertNodeByNodeId" parameterType="java.lang.String">
        delete from sjy_alert_node where node_id = #{nodeId, jdbcType=VARCHAR}
    </delete>

    <delete id="deleteSjyAlertNodeByNodeIds" parameterType="java.lang.String"> <!-- Consider changing to java.util.List if passing a list of strings -->
        delete from sjy_alert_node where node_id in
        <foreach item="nodeId" collection="array" open="(" separator="," close=")"> <!-- If list, collection="list" -->
            #{nodeId, jdbcType=VARCHAR}
        </foreach>
    </delete>
</mapper>
