<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertVariableMapper">

    <resultMap type="SjyAlertVariable" id="SjyAlertVariableResult">
        <result property="variableId"    column="variable_id"    />
        <result property="variableCode"    column="variable_code"    />
        <result property="variableName"    column="variable_name"    />
        <result property="variableType"    column="variable_type"    />
        <result property="description"    column="description"    />
        <result property="izCustom"    column="iz_custom"    />
        <result property="izList"    column="iz_list"    />
        <result property="listFormat"    column="list_format"    />
        <result property="listSeparator"    column="list_separator"    />
        <result property="displayNum"    column="display_num"    />
        <result property="defaultValue"    column="default_value"    />
        <result property="omitSymbol"    column="omit_symbol"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyAlertVariableVo">
        select sv.variable_id, sv.variable_code, sv.variable_name, sv.variable_type, sv.description, sv.iz_custom, sv.iz_list, sv.list_format, sv.list_separator, sv.display_num, sv.default_value, sv.omit_symbol, sv.status, sv.create_by, sv.create_time, sv.update_by, sv.update_time, sv.remark from sjy_alert_variable as sv
    </sql>

    <select id="selectSjyAlertVariableList" parameterType="SjyAlertVariable" resultMap="SjyAlertVariableResult">
        <include refid="selectSjyAlertVariableVo"/>  
        left join sjy_alert_rule_variable as sr on sv.variable_id = sr.variable_id
        <where>
            <if test="ruleTypeId != null">
                and sr.rule_type_id = #{ruleTypeId}
            </if>
            <if test="variableCode != null  and variableCode != ''"> and sv.variable_code = #{variableCode}</if>
            <if test="variableName != null  and variableName != ''"> and sv.variable_name like concat('%', #{variableName}, '%')</if>
            <if test="variableType != null  and variableType != ''"> and sv.variable_type = #{variableType}</if>
            <if test="description != null  and description != ''"> and sv.description = #{description}</if>
            <if test="izCustom != null  and izCustom != ''"> and sv.iz_custom = #{izCustom}</if>
            <if test="izList != null "> and sv.iz_list = #{izList}</if>
            <if test="listFormat != null  and listFormat != ''"> and sv.list_format = #{listFormat}</if>
            <if test="listSeparator != null  and listSeparator != ''"> and sv.list_separator = #{listSeparator}</if>
            <if test="displayNum != null "> and sv.display_num = #{displayNum}</if>
            <if test="defaultValue != null  and defaultValue != ''"> and sv.default_value = #{defaultValue}</if>
            <if test="omitSymbol != null  and omitSymbol != ''"> and sv.omit_symbol = #{omitSymbol}</if>
            <if test="status != null "> and sv.status = #{status}</if>
        </where>
    </select>

    <select id="selectSjyAlertVariableByVariableId" parameterType="Long" resultMap="SjyAlertVariableResult">
        <include refid="selectSjyAlertVariableVo"/>
        where sv.variable_id = #{variableId}
    </select>

    <insert id="insertSjyAlertVariable" parameterType="SjyAlertVariable" useGeneratedKeys="true" keyProperty="variableId">
        insert into sjy_alert_variable
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="variableCode != null">variable_code,</if>
            <if test="variableName != null">variable_name,</if>
            <if test="variableType != null">variable_type,</if>
            <if test="description != null">description,</if>
            <if test="izCustom != null">iz_custom,</if>
            <if test="izList != null">iz_list,</if>
            <if test="listFormat != null">list_format,</if>
            <if test="listSeparator != null">list_separator,</if>
            <if test="displayNum != null">display_num,</if>
            <if test="defaultValue != null">default_value,</if>
            <if test="omitSymbol != null">omit_symbol,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="variableCode != null">#{variableCode},</if>
            <if test="variableName != null">#{variableName},</if>
            <if test="variableType != null">#{variableType},</if>
            <if test="description != null">#{description},</if>
            <if test="izCustom != null">#{izCustom},</if>
            <if test="izList != null">#{izList},</if>
            <if test="listFormat != null">#{listFormat},</if>
            <if test="listSeparator != null">#{listSeparator},</if>
            <if test="displayNum != null">#{displayNum},</if>
            <if test="defaultValue != null">#{defaultValue},</if>
            <if test="omitSymbol != null">#{omitSymbol},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertVariable" parameterType="SjyAlertVariable">
        update sjy_alert_variable
        <trim prefix="SET" suffixOverrides=",">
            <if test="variableCode != null">variable_code = #{variableCode},</if>
            <if test="variableName != null">variable_name = #{variableName},</if>
            <if test="variableType != null">variable_type = #{variableType},</if>
            <if test="description != null">description = #{description},</if>
            <if test="izCustom != null">iz_custom = #{izCustom},</if>
            <if test="izList != null">iz_list = #{izList},</if>
            <if test="listFormat != null">list_format = #{listFormat},</if>
            <if test="listSeparator != null">list_separator = #{listSeparator},</if>
            <if test="displayNum != null">display_num = #{displayNum},</if>
            <if test="defaultValue != null">default_value = #{defaultValue},</if>
            <if test="omitSymbol != null">omit_symbol = #{omitSymbol},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where variable_id = #{variableId}
    </update>

    <delete id="deleteSjyAlertVariableByVariableId" parameterType="Long">
        delete from sjy_alert_variable where variable_id = #{variableId}
    </delete>

    <delete id="deleteSjyAlertVariableByVariableIds" parameterType="String">
        delete from sjy_alert_variable where variable_id in
        <foreach item="variableId" collection="array" open="(" separator="," close=")">
            #{variableId}
        </foreach>
    </delete>
</mapper>
