<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAbnormalDataMapper">

    <resultMap type="com.tzslsjy.business.domain.SjyAbnormalData" id="SjyAbnormalDataResult">
        <result property="stcd"    column="stcd"    />
        <result property="val"    column="val"    />
        <result property="diff"    column="diff"    />
        <result property="type"    column="type"    />
        <result property="tm"    column="tm"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectSjyAbnormalDataVo">
        select stcd, val, diff, type, tm, create_time from sjy_abnormal_data
    </sql>

    <select id="getList" resultMap="SjyAbnormalDataResult">
        <include refid="selectSjyAbnormalDataVo"/>
        order by tm desc
    </select>

    <select id="selectByKey" resultMap="SjyAbnormalDataResult">
        <include refid="selectSjyAbnormalDataVo"/>
        where stcd = #{stcd} and tm = #{tm} and type = #{type}
    </select>

    <select id="selectByCondition" resultMap="SjyAbnormalDataResult">
        <include refid="selectSjyAbnormalDataVo"/>
        <where>
            <if test="stcd != null and stcd != ''"> and stcd like concat('%', #{stcd}, '%')</if>
            <if test="type != null and type != ''"> and type like concat('%', #{type}, '%')</if>
            <if test="val != null"> and val = #{val}</if>
            <if test="diff != null"> and diff = #{diff}</if>
            <if test="tm != null"> and tm = #{tm}</if>
            <if test="createTime != null"> and create_time = #{createTime}</if>
            <if test="startTime != null"> and tm &gt;= #{startTime}</if>
            <if test="endTime != null"> and tm &lt;= #{endTime}</if>
        </where>
        order by tm desc
    </select>

    <insert id="insert" parameterType="com.tzslsjy.business.domain.SjyAbnormalData">
        insert into sjy_abnormal_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="stcd != null">stcd,</if>
            <if test="val != null">val,</if>
            <if test="diff != null">diff,</if>
            <if test="type != null">type,</if>
            <if test="tm != null">tm,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="stcd != null">#{stcd},</if>
            <if test="val != null">#{val},</if>
            <if test="diff != null">#{diff},</if>
            <if test="type != null">#{type},</if>
            <if test="tm != null">#{tm},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <update id="updateByKey" parameterType="com.tzslsjy.business.domain.SjyAbnormalData">
        update sjy_abnormal_data
        <set>
            <if test="entity.val != null">val = #{entity.val},</if>
            <if test="entity.diff != null">diff = #{entity.diff},</if>
            <if test="entity.createTime != null">create_time = #{entity.createTime},</if>
        </set>
        where stcd = #{stcd} and tm = #{tm} and type = #{type}
    </update>

    <delete id="deleteByStcds">
        delete from sjy_abnormal_data
        where stcd in
        <foreach item="stcd" collection="stcds" open="(" separator="," close=")">
            #{stcd}
        </foreach>
    </delete>

    <delete id="deleteByCondition">
        delete from sjy_abnormal_data
        <where>
            <if test="stcd != null and stcd != ''"> and stcd = #{stcd}</if>
            <if test="type != null and type != ''"> and type = #{type}</if>
            <if test="startTime != null"> and tm &gt;= #{startTime}</if>
            <if test="endTime != null"> and tm &lt;= #{endTime}</if>
        </where>
    </delete>

    <select id="selectBatchByCondition" resultMap="SjyAbnormalDataResult">
        <include refid="selectSjyAbnormalDataVo"/>
        <where>
            <if test="stcds != null and stcds.size() > 0">
                and stcd in
                <foreach collection="stcds" item="stcd" open="(" separator="," close=")">
                    #{stcd}
                </foreach>
            </if>
            <if test="types != null and types.size() > 0">
                and type in
                <foreach collection="types" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>
            <if test="startTime != null"> and tm &gt;= #{startTime}</if>
            <if test="endTime != null"> and tm &lt;= #{endTime}</if>
        </where>
        order by stcd, tm desc
    </select>

</mapper>
