<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertReceiveRecordMapper">

    <resultMap type="SjyAlertReceiveRecord" id="SjyAlertReceiveRecordResult">
        <result property="receiveId"    column="receive_id"    />
        <result property="smsId"    column="sms_id"    />
        <result property="personId"    column="person_id"    />
        <result property="phone"    column="phone"    />
        <result property="wayType"    column="way_type"    />
        <result property="izRead"    column="iz_read"    />
        <result property="status"    column="status"    />
        <result property="sendTime"    column="send_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="orgName"    column="org_name"    />
        <result property="adcdName"    column="adcd_name"    />
        <result property="dutyName"    column="duty_name"    />
        <result property="personName"    column="person_name"    />
        <result property="postName"    column="post_name"    />
    </resultMap>

    <sql id="selectSjyAlertReceiveRecordVo">
        select r.receive_id, r.sms_id, r.person_id, r.phone, r.way_type, r.iz_read, r.status, r.send_time, r.org_name, r.adcd_name,
               r.duty_name, r.post_name, r.create_by, r.create_time, r.update_by, r.update_time, r.remark, p.name as person_name
        from sjy_alert_receive_record r
        left join sjy_alert_person p on r.person_id = p.person_id
    </sql>

    <select id="selectSjyAlertReceiveRecordList" parameterType="SjyAlertReceiveRecord" resultMap="SjyAlertReceiveRecordResult">
        <include refid="selectSjyAlertReceiveRecordVo"/>
        <where>
            <if test="smsId != null "> and r.sms_id = #{smsId}</if>
            <if test="personId != null "> and r.person_id = #{personId}</if>
            <if test="phone != null  and phone != ''"> and r.phone = #{phone}</if>
            <if test="wayType != null  and wayType != ''"> and r.way_type = #{wayType}</if>
            <if test="izRead != null "> and r.iz_read = #{izRead}</if>
            <if test="status != null "> and r.status = #{status}</if>
            <if test="sendTime != null "> and r.send_time = #{sendTime}</if>
            <if test="orgName != null  and orgName != ''"> and r.org_name = #{orgName}</if>
            <if test="adcdName != null  and adcdName != ''"> and r.adcd_name = #{adcdName}</if>
            <if test="dutyName != null  and dutyName != ''"> and r.duty_name = #{dutyName}</if>
            <if test="postName != null  and postName != ''"> and r.post_name like concat('%', #{postName}, '%')</if>
            <if test="personName != null  and personName != ''"> and p.name like concat('%', #{personName}, '%')</if>
        </where>
    </select>

    <select id="selectSjyAlertReceiveRecordByReceiveId" parameterType="Long" resultMap="SjyAlertReceiveRecordResult">
        <include refid="selectSjyAlertReceiveRecordVo"/>
        where receive_id = #{receiveId}
    </select>

    <insert id="insertSjyAlertReceiveRecord" parameterType="SjyAlertReceiveRecord" useGeneratedKeys="true" keyProperty="receiveId">
        insert into sjy_alert_receive_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="smsId != null">sms_id,</if>
            <if test="personId != null">person_id,</if>
            <if test="phone != null">phone,</if>
            <if test="wayType != null">way_type,</if>
            <if test="izRead != null">iz_read,</if>
            <if test="status != null">status,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="orgName != null">org_name,</if>
            <if test="adcdName != null">adcd_name,</if>
            <if test="dutyName != null">duty_name,</if>
            <if test="postName != null">post_name,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="smsId != null">#{smsId},</if>
            <if test="personId != null">#{personId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="wayType != null">#{wayType},</if>
            <if test="izRead != null">#{izRead},</if>
            <if test="status != null">#{status},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="adcdName != null">#{adcdName},</if>
            <if test="dutyName != null">#{dutyName},</if>
            <if test="postName != null">#{postName},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertReceiveRecord" parameterType="SjyAlertReceiveRecord">
        update sjy_alert_receive_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="smsId != null">sms_id = #{smsId},</if>
            <if test="personId != null">person_id = #{personId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="wayType != null">way_type = #{wayType},</if>
            <if test="izRead != null">iz_read = #{izRead},</if>
            <if test="status != null">status = #{status},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="adcdName != null">adcd_name = #{adcdName},</if>
            <if test="dutyName != null">duty_name = #{dutyName},</if>
            <if test="postName != null">post_name = #{postName},</if>
        </trim>
        where receive_id = #{receiveId}
    </update>

    <delete id="deleteSjyAlertReceiveRecordByReceiveId" parameterType="Long">
        delete from sjy_alert_receive_record where receive_id = #{receiveId}
    </delete>

    <delete id="deleteSjyAlertReceiveRecordByReceiveIds" parameterType="String">
        delete from sjy_alert_receive_record where receive_id in
        <foreach item="receiveId" collection="array" open="(" separator="," close=")">
            #{receiveId}
        </foreach>
    </delete>
</mapper>
