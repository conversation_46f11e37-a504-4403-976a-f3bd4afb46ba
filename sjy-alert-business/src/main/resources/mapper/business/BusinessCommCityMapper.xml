<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.comm.BusinessCommCityMapper">

        <!-- 通用查询映射结果 -->
        <resultMap id="BaseResultMap" type="com.tzslsjy.business.domain.comm.BusinessCommCity">
                    <id column="id" property="id" />
                    <result column="JURISDICTION_NUM" property="jurisdictionNum" />
                    <result column="JURISDICTION" property="jurisdiction" />
                    <result column="JURISDICTION_S" property="jurisdictionS" />
                    <result column="PID" property="pid" />
                    <result column="LGTD" property="lgtd" />
                    <result column="LTTD" property="lttd" />
                    <result column="POPUNUM" property="popunum" />
                    <result column="AREA" property="area" />
                    <result column="RC" property="rc" />
                    <result column="CLSS" property="clss" />
                    <result column="SJ_SEQ" property="sjSeq" />
        </resultMap>

        <!-- 通用查询结果列 -->
        <sql id="Base_Column_List">
        id, JURISDICTION_NUM, JURISDICTION, JURISDICTION_S, PID, LGTD, LTTD, POPUNUM, AREA, RC, CLSS, SJ_SEQ
    </sql>
    <select id="getCitys" resultType="com.tzslsjy.business.domain.comm.BusinessCommCity">
        SELECT * FROM comm_city where LENGTH(JURISDICTION_NUM)= 6
    </select>
    <select id="pageList" resultType="com.tzslsjy.business.domain.comm.BusinessCommCity">
        select * from comm_city where 1 = 1
        <if test="jurisdictionNum != null and jurisdictionNum != ''">and JURISDICTION_NUM = #{jurisdictionNum}</if>
        <if test="jurisdiction != null and jurisdiction != ''">and JURISDICTION = #{jurisdiction}</if>
        <if test="jurisdictionS != null and jurisdictionS != ''">and JURISDICTION_S = #{jurisdictionS}</if>
        <if test="pid != null and pid != ''">and PID = #{pid}</if>
        <if test="addvcd != null and addvcd != ''">and JURISDICTION_NUM like concat( #{addvcd},'%')</if>
        <if test="id != null and id != ''">and id = #{id}</if>
        <if test="pid != null and pid != '' and pid!=3310">
           order by JURISDICTION_NUM
        </if>
    </select>
    <select id="getAll" resultType="com.tzslsjy.business.domain.comm.BusinessCommCItyTree">
        select * from comm_city cc    where 1 = 1

        <if test="jurisdictionNum != null and jurisdictionNum != ''">and cc.JURISDICTION_NUM = #{jurisdictionNum}</if>
        <if test="jurisdiction != null and jurisdiction != ''">and cc.JURISDICTION = #{jurisdiction}</if>
        <if test="jurisdictionS != null and jurisdictionS != ''">and cc.JURISDICTION_S = #{jurisdictionS}</if>
        <if test="pid != null and pid != ''">and cc.PID = #{pid}</if>
        <if test="id != null and id != ''">and cc.id = #{id}</if>
    </select>
    <select id="getTableNames" resultType="java.lang.String">
        select table_name  from information_schema.tables where table_schema='water_basic' and table_name like CONCAT('%',#{name},'%')
    </select>

    <select id="getTableNamesDB1" resultType="java.lang.String">
        select table_name  from information_schema.tables where table_schema='water_basic_submeter' and table_name like CONCAT('%',#{name},'%')
    </select>
    <select id="getStreet" resultType="com.tzslsjy.business.domain.comm.BusinessCommCity">
        SELECT * FROM comm_city where LENGTH(JURISDICTION_NUM)= 9
    </select>
    <select id="getCityCounty" resultType="com.tzslsjy.business.domain.comm.BusinessCommCity">
        SELECT * FROM comm_city where LENGTH(JURISDICTION_NUM)= 4 or LENGTH(JURISDICTION_NUM)= 6
    </select>
    <select id="getNameByAddvcd" resultType="java.lang.String">
        select JURISDICTION from comm_city where JURISDICTION_NUM = #{addvcd}
    </select>
    <select id="getStreetByAddvcd" resultType="com.tzslsjy.business.domain.comm.BusinessCommCity">
        SELECT * FROM comm_city <where>
        <if test="addvcd != null and addvcd != ''"> and (PID = #{addvcd} or JURISDICTION_NUM=#{addvcd})</if>
    </where>


    </select>
    <select id="getFullNameByAdcd" resultType="java.lang.String">
          SELECT GROUP_CONCAT(JURISDICTION SEPARATOR '')  from comm_city where   JURISDICTION_NUM =LEFT(#{adcd}, 6) or JURISDICTION_NUM = #{adcd}
    </select>
    <select id="getCity" resultType="com.tzslsjy.business.domain.comm.BusinessCommCity">
        select  * from comm_city  where    ( LENGTH(JURISDICTION_NUM)=6 or LENGTH(JURISDICTION_NUM)= 4) and sj_seq!=30
    </select>
    <select id="getCity1" resultType="com.tzslsjy.business.domain.comm.BusinessCommCity">
        SELECT * FROM comm_city where LENGTH(JURISDICTION_NUM)= 6 and sj_seq!=30
    </select>
    <select id="getCcounty" resultType="com.tzslsjy.business.domain.comm.BusinessCommCity">
        select  * from comm_city  where  (LENGTH(JURISDICTION_NUM)=9 or LENGTH(JURISDICTION_NUM)=6)  and JURISDICTION_NUM like CONCAT('%',#{addvcd},'%') order by  JURISDICTION_NUM
    </select>

    <select id="getNamesByAddvcd" resultType="java.lang.String">
        select JURISDICTION from comm_city where JURISDICTION_NUM = #{addvcd}
    </select>
    <select id="getAddvcdByNameAndPid" resultType="java.lang.String">
        select JURISDICTION_NUM from comm_city where JURISDICTION = #{name} and PID = #{pid}
    </select>
    <select id="getAllWithVillage" resultType="com.tzslsjy.business.domain.comm.BusinessCommCItyTree">
        select * from comm_city cc  left join lh_water_basic_info.sjy_risk_village_info rv on cc.JURISDICTION_NUM = rv.cadcd  where 1 = 1
        <if test="jurisdictionNum != null and jurisdictionNum != ''">and cc.JURISDICTION_NUM = #{jurisdictionNum}</if>
        <if test="jurisdiction != null and jurisdiction != ''">and cc.JURISDICTION = #{jurisdiction}</if>
        <if test="jurisdictionS != null and jurisdictionS != ''">and cc.JURISDICTION_S = #{jurisdictionS}</if>
        <if test="pid != null and pid != ''">and cc.PID = #{pid}</if>
        <if test="id != null and id != ''">and cc.id = #{id}</if>
    </select>
    <select id="getVillage" resultType="com.tzslsjy.business.domain.comm.BusinessCommCItyTree">
        select village_natural as jurisdiction, cadcd as jurisdictionNum,adcd as pid from  sjy_risk_village_info where 1 = 1
        <if test="jurisdictionNum != null and jurisdictionNum != ''">and  cadcd = #{jurisdictionNum}</if>
        <if test="jurisdiction != null and jurisdiction != ''">and   village_natural  = #{jurisdiction}</if>
        <if test="jurisdictionS != null and jurisdictionS != ''">and cadcd = #{jurisdictionS}</if>
        <if test="pid != null and pid != ''">and adcd = #{pid}</if>
        <if test="id != null and id != ''">and  id = #{id}</if>
    </select>
</mapper>
