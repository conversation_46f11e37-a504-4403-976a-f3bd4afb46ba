<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tzslsjy.business.mapper.SjyAlertRuleArgMapper">

    <resultMap type="SjyAlertRuleArg" id="SjyAlertRuleArgResult">
        <result property="argId"    column="arg_id"    />
        <result property="relationId"    column="relation_id"    />
        <result property="relationType"    column="relation_type"    />
        <result property="argType"    column="arg_type"    />
        <result property="argJson"    column="arg_json"    />
        <result property="alertLevel"    column="alert_level"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSjyAlertRuleArgVo">
        select arg_id, relation_id, relation_type, arg_type, arg_json, alert_level, status, create_by, create_time, update_by, update_time, remark from sjy_alert_rule_arg
    </sql>

    <select id="selectSjyAlertRuleArgList" parameterType="SjyAlertRuleArg" resultMap="SjyAlertRuleArgResult">
        <include refid="selectSjyAlertRuleArgVo"/>
        <where>
            <if test="relationId != null "> and relation_id = #{relationId}</if>
            <if test="relationType != null  and relationType != ''"> and relation_type = #{relationType}</if>
            <if test="argType != null  and argType != ''"> and arg_type = #{argType}</if>
            <if test="argJson != null  and argJson != ''"> and arg_json = #{argJson}</if>
            <if test="alertLevel != null "> and alert_level = #{alertLevel}</if>
            <if test="status != null "> and status = #{status}</if>
        </where>
    </select>

    <select id="selectSjyAlertRuleArgByArgId" parameterType="Long" resultMap="SjyAlertRuleArgResult">
        <include refid="selectSjyAlertRuleArgVo"/>
        where arg_id = #{argId}
    </select>

    <insert id="insertSjyAlertRuleArg" parameterType="SjyAlertRuleArg" useGeneratedKeys="true" keyProperty="argId">
        insert into sjy_alert_rule_arg
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="relationId != null">relation_id,</if>
            <if test="relationType != null">relation_type,</if>
            <if test="argType != null">arg_type,</if>
            <if test="argJson != null">arg_json,</if>
            <if test="alertLevel != null">alert_level,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="relationId != null">#{relationId},</if>
            <if test="relationType != null">#{relationType},</if>
            <if test="argType != null">#{argType},</if>
            <if test="argJson != null">#{argJson},</if>
            <if test="alertLevel != null">#{alertLevel},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSjyAlertRuleArg" parameterType="SjyAlertRuleArg">
        update sjy_alert_rule_arg
        <trim prefix="SET" suffixOverrides=",">
            <if test="relationId != null">relation_id = #{relationId},</if>
            <if test="relationType != null">relation_type = #{relationType},</if>
            <if test="argType != null">arg_type = #{argType},</if>
            <if test="argJson != null">arg_json = #{argJson},</if>
            <if test="alertLevel != null">alert_level = #{alertLevel},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where arg_id = #{argId}
    </update>

    <delete id="deleteSjyAlertRuleArgByArgId" parameterType="Long">
        delete from sjy_alert_rule_arg where arg_id = #{argId}
    </delete>

    <delete id="deleteSjyAlertRuleArgByArgIds" parameterType="String">
        delete from sjy_alert_rule_arg where arg_id in
        <foreach item="argId" collection="array" open="(" separator="," close=")">
            #{argId}
        </foreach>
    </delete>

    <select id="selectArgIdsByRuleId" parameterType="java.lang.Long" resultType="java.lang.Long">
       select arg_id from sjy_alert_rule_arg
      where relation_id = #{ruleId} and relation_type = '1' <!-- Assuming relation_type '1' links arg to rule -->
    </select>

   <delete id="deleteByRuleId" parameterType="java.lang.Long">
       delete from sjy_alert_rule_arg
        where relation_id = #{ruleId} and relation_type = '1' <!-- Assuming relation_type '1' links arg to rule -->
    </delete>

 </mapper>
