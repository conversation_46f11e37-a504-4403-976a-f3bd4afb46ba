package com.tzslsjy.business.task;


import com.tzslsjy.business.service.RefactoredAlertService;
import com.tzslsjy.common.utils.spring.SpringUtils;
import com.tzslsjy.quartz.util.ClusterLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobDataMap;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import java.time.Duration;
import java.util.Date;

/**
 * 预警规则执行任务
 * 用于Quartz调度执行预警规则
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
public class AlertRuleExecutionJob implements Job {

    private static final String RULE_ID_KEY = "ruleId";
    private static final String RULE_NAME_KEY = "ruleName";

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        JobDataMap dataMap = context.getJobDetail().getJobDataMap();
        Long ruleId = dataMap.getLong(RULE_ID_KEY);
        String ruleName = dataMap.getString(RULE_NAME_KEY);

        log.info("开始执行预警规则定时任务: {} (ID: {})", ruleName, ruleId);

        // 获取集群锁工具
        ClusterLockUtils clusterLockUtils = SpringUtils.getBean(ClusterLockUtils.class);
        String lockKey = null;

        try {
            // 生成集群锁键
            Date currentTime = new Date();
            lockKey = ClusterLockUtils.generateAlertRuleExecutionLockKey(ruleId, currentTime.getTime());

            // 尝试获取集群锁，避免重复执行
            if (clusterLockUtils != null && !clusterLockUtils.tryLock(lockKey, Duration.ofMinutes(3))) {
                log.info("预警规则 {} (ID: {}) 正在其他节点执行，跳过本次执行", ruleName, ruleId);
                return;
            }

            // 获取预警服务
            RefactoredAlertService alertService = SpringUtils.getBean(RefactoredAlertService.class);

            if (alertService == null) {
                log.error("无法获取RefactoredAlertService实例");
                throw new JobExecutionException("无法获取预警服务实例");
            }

            // 执行预警规则
            boolean result = alertService.processAlert(ruleId, currentTime);

            if (result) {
                log.info("预警规则 {} (ID: {}) 执行成功", ruleName, ruleId);
            } else {
                log.warn("预警规则 {} (ID: {}) 执行失败或未触发", ruleName, ruleId);
            }

        } catch (Exception e) {
            log.error("执行预警规则 {} (ID: {}) 时发生异常: {}", ruleName, ruleId, e.getMessage(), e);

            // 包装异常以便Quartz处理
            JobExecutionException jobException = new JobExecutionException(e);
            // 设置为不重新调度，避免异常规则反复执行
            jobException.setRefireImmediately(false);
            throw jobException;
        } finally {
            // 释放集群锁
            if (clusterLockUtils != null && lockKey != null) {
                clusterLockUtils.releaseLock(lockKey);
            }
        }
    }

    /**
     * 创建JobDataMap
     *
     * @param ruleId   规则ID
     * @param ruleName 规则名称
     * @return JobDataMap
     */
    public static JobDataMap createJobDataMap(Long ruleId, String ruleName) {
        JobDataMap dataMap = new JobDataMap();
        dataMap.put(RULE_ID_KEY, ruleId);
        dataMap.put(RULE_NAME_KEY, ruleName);
        return dataMap;
    }
}
