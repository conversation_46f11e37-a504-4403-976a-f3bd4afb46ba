package com.tzslsjy.business.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

@Data
@Schema(description = "水库水情查询请求参数V1")
public class ReservoirWaterV1ReqVo {
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private Date startTm;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间")
    private Date endTm;
    @Schema(description = "行政区划码")
    @TableField("ADDVCD")
    private String addvcd;
    @Schema(description = "水库等级（1 大型 2 中型 3 小型 4 山塘  多选用逗号隔开）")
    private String level;

    private List<String> stcds;

    private List<String> levels;
    @Schema(description = "水位超限（0未 1 超汛限 2 超正常）")
    private Integer over;
    @Schema(description = "径流系数")
    private Double modulus;
    @Schema(description = "开始月日")
    @TableField("BGMD")
    private String bgmd;

    @Schema(description = "结束月日")
    @TableField("EDMD")
    private String edmd;
    private String adcdRs;
}