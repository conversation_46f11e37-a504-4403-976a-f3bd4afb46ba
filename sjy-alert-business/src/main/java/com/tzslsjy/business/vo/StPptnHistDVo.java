package com.tzslsjy.business.vo;

import com.tzslsjy.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;



/**
 * 单站多年日降雨视图对象 mall_package
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Data
@Schema(description = "单站多年日降雨视图对象")
public class StPptnHistDVo {
	private static final long serialVersionUID = 1L;



	/**  */
	@Excel(name = "")
	@Schema(description = "测站编号")
	private String stcd;
	/**  */
	@Excel(name = "" , width = 30, dateFormat = "yyyy-MM-dd")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@Schema(description = "时间")
	private Date tm;
	@Schema(description = "月份")
	private Integer month;
	@Schema(description = "日期")
	private Integer day;
	/**  */
	@Excel(name = "")
	@Schema(description = "降雨")
	private BigDecimal drp;
	/**  */
	@Excel(name = "")
	@Schema(description = "测站名称")
	private String stnm;

}