package com.tzslsjy.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 测站规则状态信息
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@Schema(description = "测站规则状态信息")
public class StationRuleStatus implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @Schema(description = "测站编码")
    private String stationId;

    /** 测站名称 */
    @Schema(description = "测站名称")
    private String stationName;

    /** 规则类型ID */
    @Schema(description = "规则类型ID")
    private String ruleTypeId;

    /** 规则类型名称 */
    @Schema(description = "规则类型名称")
    private String ruleTypeName;

    /** 规则ID */
    @Schema(description = "规则ID")
    private Long ruleId;

    /** 规则名称 */
    @Schema(description = "规则名称")
    private String ruleName;

    /** 是否存在规则 */
    @Schema(description = "是否存在规则")
    private Boolean ruleExists;

    /** 规则状态 (0-禁用, 1-启用) */
    @Schema(description = "规则状态 (0-禁用, 1-启用)")
    private Integer status;

    /** 规则状态描述 */
    @Schema(description = "规则状态描述")
    private String statusDesc;

    /** 创建时间 */
    @Schema(description = "创建时间")
    private String createTime;

    /** 更新时间 */
    @Schema(description = "更新时间")
    private String updateTime;
}
