package com.tzslsjy.business.vo;

 
import com.baomidou.mybatisplus.annotation.TableField;
import com.tzslsjy.business.domain.StStbprpB;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false) //此注解会生成equals(Object other) 和 hashCode()方法。不调用父类的属性

//库（湖） 站汛限水位表-汛限水位,库（湖） 站防洪指标表-正常水位,
public class ReservoirReqVo extends StStbprpB {
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @Schema(description = "开始时间")
    private Date startTm;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm")
    @Schema(description = "结束时间")
    private Date endTm;
    @Schema(description = "页大小")
    private Integer pageSize;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "页数")
    private Integer pageNum;
    /*库（湖） 站汛限水位表-汛限水位*/
    @Schema(description = "开始月日")
    @TableField("BGMD")
    private String bgmd;

    @Schema(description = "结束月日")
    @TableField("EDMD")
    private String edmd;

    @Schema(description = "汛限水位（m）")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    private List<String> stcds;
    private List<String> stnms;
    @Schema(description = "汛限库容（10^6m^3）")
    @TableField("FSLTDW")
    private BigDecimal fsltdw;

    @Schema(description = "汛期类别")
    @TableField("FSTP")
    private String fstp;

    /*库（湖） 站防洪指标表-正常水位*/

    @Schema(description = "水库类型")
    @TableField("RSVRTP")
    private String rsvrtp;

    @Schema(description = "坝顶高程")
    @TableField("DAMEL")
    private BigDecimal damel;

    @Schema(description = "校核洪水位")
    @TableField("CKFLZ")
    private BigDecimal ckflz;

    @Schema(description = "设计洪水位")
    @TableField("DSFLZ")
    private BigDecimal dsflz;

    @Schema(description = "正常水位")
    @TableField("NORMZ")
    private BigDecimal normz;

    @Schema(description = "死水位")
    @TableField("DDZ")
    private BigDecimal ddz;

    @Schema(description = "兴利水位")
    @TableField("ACTZ")
    private BigDecimal actz;

    @Schema(description = "总库容")
    @TableField("TTCP")
    private BigDecimal ttcp;

    @Schema(description = "兴利水位")
    @TableField("ACTCP")
    private BigDecimal actcp;



    /**水库实时水位流量表**/
    @Schema(description = "时间")
    @TableField("TM")
    private Date tm;

    @Schema(description = "库上水位")
    @TableField("RZ")
    private BigDecimal rz;

    /*库容曲线-库容计算*/
    @Schema(description = "点序号")
    @TableField("PTNO")
    private BigDecimal ptno;

    @Schema(description = "库水位(m)")
    @TableField("RZ")
    private BigDecimal rzs;

    @Schema(description = "需水量（10^6m^3）")
    @TableField("W")
    private BigDecimal w;

    /*其他*/
    @Schema(description = "实际水位，跟库容曲线相减的绝对值")
    private BigDecimal abs;

    private Integer level;

    private Integer status;

    private BigDecimal newW;
    @Schema(description = "1重点测站")
    private String flag;
    @Schema(description = "站点分类:P 含雨量数据的测站 、Z 含水位数据的测站、basin_jlj_csinfo 椒江流域的断面关联测站、")
    private String stType;

    @Schema(description = "限制类型RR，RS,缺省不限制有值则限制")

    private String rrFlag;

    @Schema(description = "流域编码")
    private String basinCode;
    @Schema(description = "流域编码")
    private String lhBasinCode;
}
