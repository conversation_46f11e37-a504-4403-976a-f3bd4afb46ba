package com.tzslsjy.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * @auther seven
 * @create 2021-04-02 11:25:57
 * @describe 水库实时水位流量表实体类
 */
@Data
@Schema(description = "StRsvrR Req对象")
public class StRsvrRReqVo implements Serializable {

    private static final long serialVersionUID = 1L;

    //    @Schema(description = "测站编码")
//    private String stcd;
    @NotNull(message = "测站编码列表不能为空")
    @Schema(description = "测站编码列表", required = true) // Corrected annotation
    private Set<String> stcds;
    @NotNull(message = "开始时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间", required = true) // Corrected annotation
    private Date startTm;
    @NotNull(message = "结束时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间", required = true) // Corrected annotation
    private Date endTm;
}