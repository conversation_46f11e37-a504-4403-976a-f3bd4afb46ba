package com.tzslsjy.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 批量创建测站异常检测规则请求VO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@Schema(description = "批量创建测站异常检测规则请求VO")
public class StationAbnormalRuleBatchCreateReqVo {

    @Schema(description = "测站ID列表", required = true, example = "[\"13123a\", \"13123b\"]")
    @NotEmpty(message = "测站ID列表不能为空")
    private List<String> stationIds;

    @Schema(description = "是否创建水位异常规则", required = true, example = "true")
    @NotNull(message = "是否创建水位异常规则不能为空")
    private Boolean createWaterLevel;

    @Schema(description = "是否创建雨量异常规则", required = true, example = "false")
    @NotNull(message = "是否创建雨量异常规则不能为空")
    private Boolean createRainfall;
}
