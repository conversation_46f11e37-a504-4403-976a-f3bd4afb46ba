package com.tzslsjy.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.List;

/**
 * 启用或禁用测站异常检测规则请求VO
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@Schema(description = "启用或禁用测站异常检测规则请求VO")
public class StationAbnormalRuleToggleReqVo {

    @Schema(description = "测站ID列表", required = true, example = "[\"13123a\", \"13123b\"]")
    @NotEmpty(message = "测站ID列表不能为空")
    private List<String> stationIds;

    @Schema(description = "规则类型ID (31-水位异常, 34-雨量异常)", required = true, example = "31")
    @NotNull(message = "规则类型ID不能为空")
    private String ruleTypeId;

    @Schema(description = "启用标志 (true-启用, false-禁用)", required = true, example = "true")
    @NotNull(message = "启用标志不能为空")
    private Boolean enabled;
}
