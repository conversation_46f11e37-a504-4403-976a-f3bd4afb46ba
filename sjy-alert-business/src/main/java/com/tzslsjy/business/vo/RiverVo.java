package com.tzslsjy.business.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.common.utils.SjyAccuracyUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false) //此注解会生成equals(Object other) 和 hashCode()方法。不调用父类的属性
/**
 * 堰闸水情表 , 河道防洪指标-警戒水位、保证水位
 */
@Schema(name = "RiverWaterVo", description = "河道堰闸水情表")
public class RiverVo extends StStbprpB {
    /*潮位水情表 tide_r*/
    @Schema(description = "时间")
    @TableField("TM")
    private Date tm;
    @Schema(description = "测站重点类型")
    private String stType;
    private String pjcd;
    @Schema(description = "水位")
    @TableField("Z")
    private BigDecimal z;
    @Schema(description = "最高水位")
    private BigDecimal highZ;
    @Schema(description = "最高水位时间")
    private Date highZTm;
    @Schema(description = "收藏标志")
    @TableField(exist = false)
    private Boolean starFlag;
    @Schema(description = "流量")
    @TableField("Q")
    private BigDecimal q;

    @Schema(description = "断面过水面积")
    @TableField("XSA")
    private BigDecimal xsa;

    @Schema(description = "断面平均流速")
    @TableField("XSAVV")
    private BigDecimal xsavv;

    @Schema(description = "断面最大流速")
    @TableField("XSMXV")
    private BigDecimal xsmxv;

    @Schema(description = "河水特征码")
    @TableField("FLWCHRCD")
    private String flwchrcd;

    @Schema(description = "水势")
    @TableField("WPTN")
    private String wptn;

    @Schema(description = "测流方法")
    @TableField("MSQMT")
    private String msqmt;

    @Schema(description = "测积方法")
    @TableField("MSAMT")
    private String msamt;
    /** 雨量值 */
    @Schema(description = "雨量值")
    private BigDecimal drp;
    @Schema(description = "测速方法")
    @TableField("MSVMT")
    private String msvmt;

    @TableField("EDITED")
    private String edited;

    @TableField("B_Flag")
    private String bFlag;

    //河道防洪指标-警戒水位、保证水位,StRvfcchB
    @Schema(description = "警戒水位（m）")
    @TableField("WRZ")
    private BigDecimal wrz;

    @Schema(description = "保证水位（m）")
    @TableField("GRZ")
    private BigDecimal grz;

    @Schema(description = "实际最高水位（m）")
    @TableField("OBHTZ")
    private BigDecimal obhtz;

    @Schema(description = "实际最高水位出现时间")
    @TableField("OBHTZTM")
    private Date obhtztm;

    @Schema(description = "历史最大流量（m）")
    @TableField(exist = false)
    private BigDecimal hisQ;

    @Schema(description = "左提高程（m）")
    @TableField(exist = false)
    private BigDecimal leftHign;

    @Schema(description = "右提高程（m）")
    @TableField(exist = false)
    private BigDecimal righHigh;

    @Schema(description = "当前站点的现在的情况，0 正常，1超警戒/超汛限，2超保证超正常 6数据缺失")
    private String status;
    @Schema(description = "行政区划")
    @TableField("JURISDICTION")
    private String jurisdiction;
    @Schema(description = "行政区划9位对应中文")
    private String jurisdiction9;
    @Schema(description = "超警戒水位值")
    private BigDecimal ovalWrzValue;
    @Schema(description = "超保证水位值")
    private BigDecimal ovalGrzValue;
    @Schema(description = "超过水位值")
    private BigDecimal overValue;
    @Schema(description = "雨站特殊标识，null&0普通")
    private String rainFlag;

    public BigDecimal getZ() {
        return SjyAccuracyUtil.zTwoAccuracy(z);
    }
    public RiverVo setZ(BigDecimal z) {
        this.z = z;
        return this;
    }
}
