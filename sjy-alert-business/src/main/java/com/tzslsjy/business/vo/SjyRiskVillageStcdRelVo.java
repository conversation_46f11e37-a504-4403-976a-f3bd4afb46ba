package com.tzslsjy.business.vo;

import com.tzslsjy.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 村庄关联测站编号视图对象 sjy_risk_village_stcd_rel
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "村庄关联测站编号视图对象")
public class SjyRiskVillageStcdRelVo {
	private static final long serialVersionUID = 1L;

	/** 主键id */
	@Schema(description = "主键id")
	@Excel(name = "主键id")
	private Long id;

	/** 村庄id */
	@Schema(description = "村庄id")
	@Excel(name = "村庄id")
	private String villageId;
	/** 测站编码 */
	@Schema(description = "测站编码")
	@Excel(name = "测站编码")
	private String stcd;
	/** 行政区划编码 */
	@Schema(description = "行政区划编码")
	@Excel(name = "行政区划编码")
	private String adcd;
	/** 备注 */
	@Excel(name = "备注")
	@Schema(description = "备注")
	private String remark;
	/** 创建人 */
	@Excel(name = "创建人")
	private String createBy;

}