package com.tzslsjy.business.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.tzslsjy.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;



/**
 * 节点职位视图对象 mall_package
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@Schema(description = "节点职位视图对象")
public class SjyAlertNodePostVo {
	private static final long serialVersionUID = 1L;

	/** 主键ID */
	@Schema(description = "主键ID")
	private Long id;

	/** 节点id */
	@Excel(name = "节点id")
	@Schema(description = "节点id")
	private Long nodeId;
	/** 职位名称 */
	@Excel(name = "职位名称")
	@Schema(description = "职位名称")
	private String postName;
	/** 备注 */
	@Excel(name = "备注")
	@Schema(description = "备注")
	private String remark;

}
