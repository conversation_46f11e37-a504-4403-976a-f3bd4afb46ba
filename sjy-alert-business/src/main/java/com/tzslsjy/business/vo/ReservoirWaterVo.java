package com.tzslsjy.business.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.common.utils.SjyAccuracyUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@Schema(description = "水情报表")
//库（湖） 站汛限水位表-汛限水位,库（湖） 站防洪指标表-正常水位,
public class ReservoirWaterVo extends StStbprpB {

    /*库（湖） 站汛限水位表-汛限水位*/
    @Schema(description = "开始月日")
    @TableField("BGMD")
    private String bgmd;

    @Schema(description = "结束月日")
    @TableField("EDMD")
    private String edmd;

    @Schema(description = "汛限水位（m）")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @Schema(description = "汛限库容（10^6m^3）")
    @TableField("FSLTDW")
    private BigDecimal fsltdw;

    @Schema(description = "汛期类别")
    @TableField("FSTP")
    private String fstp;
    @Schema(description = "超出库容的水位，单位：m，正数表示超出，负数表示未超出")
    private BigDecimal overRz;
    /*库（湖） 站防洪指标表-正常水位*/

    @Schema(description = "水库类型")
    @TableField("RSVRTP")
    private String rsvrtp;
    @Schema(description = "防洪库容")
    @TableField("FLDCP")
    private BigDecimal fldcp;
    @Schema(description = "坝顶高程")
    @TableField("DAMEL")
    private BigDecimal damel;

    @Schema(description = "校核洪水位")
    @TableField("CKFLZ")
    private BigDecimal ckflz;

    @Schema(description = "设计洪水位")
    @TableField("DSFLZ")
    private BigDecimal dsflz;

    @Schema(description = "正常水位")
    @TableField("NORMZ")
    private BigDecimal normz;
    @Schema(description = "正常库容")

    private BigDecimal norcp;
    @Schema(description = "死水位")
    @TableField("DDZ")
    private BigDecimal ddz;
    @Schema(description = "死库容")

    private BigDecimal ddcp;
    @Schema(description = "兴利水位")
    @TableField("ACTZ")
    private BigDecimal actz;

    @Schema(description = "总库容")
    @TableField("TTCP")
    private BigDecimal ttcp;

    @Schema(description = "兴利水位")
    @TableField("ACTCP")
    private BigDecimal actcp;

    /**水库实时水位流量表**/
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "时间")
    @TableField("TM")
    private Date tm;

    @Schema(description = "库上水位")
    @TableField("RZ")
    private BigDecimal rz;

    private String wptn;

    private String startWptn;

    private String endWptn;
    /*库容曲线-库容计算*/
    @Schema(description = "点序号")
    @TableField("PTNO")
    private BigDecimal ptno;

    @Schema(description = "库水位(m)")
    @TableField("RZ")
    private BigDecimal rzs;

    @Schema(description = "需水量（10^6m^3）")
    @TableField("W")
    private BigDecimal w;
    @Schema(description = "实时最高库容")
    private BigDecimal maxRz;
    @Schema(description = "实时最高水位")
    private BigDecimal maxVal;
    @Schema(description = "开始时间库容")
    private BigDecimal beginRz;

    @Schema(description = "结束时间库容")
    private BigDecimal endRz;

    @Schema(description = "汛限可拦蓄（开始时间）")
    private BigDecimal beginFlBlock;

    @Schema(description = "汛限可拦蓄（结束时间）")
    private BigDecimal endFlBlock;

    @Schema(description = "正常可拦蓄（开始时间）")
    private BigDecimal beginNBlock;

    @Schema(description = "正常可拦蓄（结束时间）")
    private BigDecimal endNBlock;

    @Schema(description = "设计可拦蓄（开始时间）")
    private BigDecimal beginDBlock;

    @Schema(description = "设计可拦蓄（结束时间）")
    private BigDecimal endDBlock;
    /*其他*/
    @Schema(description = "实际水位，跟库容曲线相减的绝对值")
    private BigDecimal abs;
    @Schema(description = "规模名")
    private String levelName;
    @Schema(description = "当前站点的现在的情况，0 正常，1超保证，2超正常，3涨，4落，5平，6无数据")
    private Integer status;

    private BigDecimal newW;

    @Schema(description = "开始时间水位")

    private BigDecimal beginVal;

    @Schema(description = "结束时间水位")

    private BigDecimal endVal;
    @Schema(description = "超讯限")

    private BigDecimal overVal;

    @Schema(description = "超正常")

    private BigDecimal overNVal;

    @Schema(description = "phone")

    private String phone;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")

    private Date startTm;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间")

    private Date endTm;
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "历史最高水位时间")

    private Date hTime;
    @Schema(description = "历史最高水位")

    private BigDecimal highVal;
    @Schema(description = "序号")
    private Integer num;
    @Schema(description = "行政区划")
    @TableField("JURISDICTION")
    private String jurisdiction;
    @Schema(description = "9位行政区划")
    private String addvcd9Nm;
    @Schema(description = "开始时间正常蓄水率")
    private BigDecimal bNRate;
    @Schema(description = "开始时间汛限蓄水率")
    private BigDecimal bFRate;
    @Schema(description = "结束时间正常蓄水率")
    private BigDecimal eNRate;
    @Schema(description = "结束时间汛限蓄水率")
    private BigDecimal eFRate;
    @Schema(description = "过去6小时的水位")
    private BigDecimal theStartRz;
    @Schema(description = "过去6小时的水位")
    private BigDecimal theEndRz;

    public BigDecimal getRz() {
        return SjyAccuracyUtil.rzTwoAccuracy(rz);
    }
    public ReservoirWaterVo setRz(BigDecimal rz) {
        this.rz = rz;
        return this;
    }

    public BigDecimal getBeginFlBlock() {
        return SjyAccuracyUtil.nBlockNegativeforZreo(beginFlBlock);
    }
    public ReservoirWaterVo setBeginFlBlock(BigDecimal beginFlBlock) {
        this.beginFlBlock = beginFlBlock;
        return this;
    }

    public BigDecimal getBeginNBlock() {
        return SjyAccuracyUtil.nBlockNegativeforZreo(beginNBlock);
    }
    public ReservoirWaterVo setBeginNBlock(BigDecimal beginNBlock) {
        this.beginNBlock = beginNBlock;
        return this;
    }

    public BigDecimal getEndNBlock() {
        return SjyAccuracyUtil.nBlockNegativeforZreo(endNBlock);
    }
    public ReservoirWaterVo setEndNBlock(BigDecimal endNBlock) {
        this.endNBlock = endNBlock;
        return this;
    }

    public BigDecimal getEndFlBlock() {
        return SjyAccuracyUtil.nBlockNegativeforZreo(endFlBlock);
    }
    public ReservoirWaterVo setEndFlBlock(BigDecimal endFlBlock) {
        this.endFlBlock = endFlBlock;
        return this;
    }
}