package com.tzslsjy.business.vo;


import com.baomidou.mybatisplus.annotation.TableField;
import com.tzslsjy.business.domain.StStbprpB;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = false) //此注解会生成equals(Object other) 和 hashCode()方法。不调用父类的属性
/**
 * 堰闸水情表 , 河道防洪指标-警戒水位、保证水位
 */

public class RiverReqVo extends StStbprpB {
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "开始时间")
    private Date startTm;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm")
    @Schema(description = "结束时间")
    private Date endTm;
    @Schema(description = "流域编号")
    private String basinCode;
    @Schema(description = "流域级别（1市级流域、2县级流域、3镇级流域）")
    private String basinLevel;
    @Schema(description = "0或缺省代表按水位排序 1代表固定的行政区划排序，再水位降序")
    private String orderFlag;
    @Schema(description = "用户id")
    private String userId;
    @Schema(description = "页大小")
    private Integer pageSize;
    @Schema(description = "页数")
    private Integer pageNum;
    @Schema(description = "测站编码列表")
    private List<String> stcds;
    @Schema(description = "行政区划码Like")
    private String addvcdLike;
    @Schema(description = "河道标志 ZZ河道 ZG地下水 其他全部")
    private String zFlag;
    /*潮位水情表 tide_r*/
    @Schema(description = "时间")
    @TableField("TM")
    private Date tm;

    @Schema(description = "水位")
    @TableField("Z")
    private BigDecimal z;

    @Schema(description = "流量")
    @TableField("Q")
    private BigDecimal q;

    @Schema(description = "断面过水面积")
    @TableField("XSA")
    private BigDecimal xsa;

    @Schema(description = "断面平均流速")
    @TableField("XSAVV")
    private BigDecimal xsavv;

    @Schema(description = "断面最大流速")
    @TableField("XSMXV")
    private BigDecimal xsmxv;

    @Schema(description = "河水特征码")
    @TableField("FLWCHRCD")
    private String flwchrcd;

    @Schema(description = "水势")
    @TableField("WPTN")
    private String wptn;

    @Schema(description = "测流方法")
    @TableField("MSQMT")
    private String msqmt;

    @Schema(description = "测积方法")
    @TableField("MSAMT")
    private String msamt;

    @Schema(description = "测速方法")
    @TableField("MSVMT")
    private String msvmt;

    @TableField("EDITED")
    private String edited;

    @TableField("B_Flag")
    private String bFlag;

    //河道防洪指标-警戒水位、保证水位,StRvfcchB
    @Schema(description = "警戒水位（m）")
    @TableField("WRZ")
    private BigDecimal wrz;

    @Schema(description = "保证水位（m）")
    @TableField("GRZ")
    private BigDecimal grz;

    @Schema(description = "实际最高水位（m）")
    @TableField("OBHTZ")
    private BigDecimal obhtz;

    @Schema(description = "实际最高水位出现时间")
    @TableField("OBHTZTM")
    private Date obhtztm;

    @Schema(description = "当前站点的现在的情况，0 正常，1超保证，2超警戒，3涨，4落，5平，6无数据")
    private String status;

    @Schema(description = "1重点测站")
    private String flag;
    @Schema(description = "站点分类:P 含雨量数据的测站 、Z 含水位数据的测站、basin_jlj_csinfo 椒江流域的断面关联测站、")
    private String stType;

    @Schema(description = "是否包含闸站 0 否 1 是 缺省否")
    private String containFlag;

    @Schema(description = "流域编号")
    private String lhBasinCode;

}
