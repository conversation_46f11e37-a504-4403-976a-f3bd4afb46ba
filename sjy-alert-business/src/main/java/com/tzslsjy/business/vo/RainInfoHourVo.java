package com.tzslsjy.business.vo;


import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.tzslsjy.business.domain.StStbprpB;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false) //此注解会生成equals(Object other) 和 hashCode()方法。不调用父类的属性
@Schema(description = "基本+极值统计")
public class RainInfoHourVo extends StStbprpB implements Serializable {

    private static final long serialVersionUID = 1L;
    @Schema(description = "id")
    @TableField(exist = false)
    private String id;
    @Schema(description = "时段降雨量（mm）")
    @TableField("DRP")
    private BigDecimal drp;
    @Schema(description = "水位（m）")
    private BigDecimal z;
    @Schema(description = "水位时间")
    private Date zTm;
    @Schema(description = "雨量站类型（PP 水文站 MM 气象站）")
    private String pType;
    @Schema(description = "隶属行业单位")
    @TableField("ATCUNIT")
    private String atcunit;
    @Schema(description = "时段长（h）")
    @TableField("INTV")
    private BigDecimal intv;

    @Schema(description = "降水历时")
    @TableField("PDR")
    private BigDecimal pdr;

    @Schema(description = "日降雨量")
    @TableField("DYP")
    private BigDecimal dyp;

    @Schema(description = "天气情况")
    @TableField("WTH")
    private String wth;
    @Schema(description = "某个时间段的雨量总和")
    private Float rainfall;

    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "时间")
//    @TableField("tm")
    @TableField("tm")
    private Date tm;

    @Schema(description = "行政区划名称")
    @TableField("JURISDICTION")
    private String jurisdiction;
    @Schema(description = "排序号")
    private String sort;
    @Schema(description = "0正常 1预警")
    private String status;

    @Schema(description = "0无 1有雨")
    private Integer isRain;
    //    @TableField(exist = false)
//    private BigDecimal wrz;
//
//   // @TableField(exist = false)
//    private BigDecimal grz;
    // @TableField(exist = false)
    private String jurisdictionNum;
    private String city;
    private String county;
    // @TableField(exist = false)
    private String area;
    // @TableField(exist = false)
    private String rc;
    @Schema(description = "收藏标志")
    private Boolean starFlag;

    private Date dayTm;
    @Schema(description = "流域编码")
    private String basinCode;

    @Schema(description = "总雨量")
    private BigDecimal sumDrp;

    @Schema(description = "雨势 4-减 5-增  6-平  0-停 ")
    private String rainTrend;

}
