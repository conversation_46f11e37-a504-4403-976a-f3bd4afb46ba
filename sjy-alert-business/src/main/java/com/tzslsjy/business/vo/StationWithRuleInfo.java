package com.tzslsjy.business.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 测站规则信息
 *
 * <AUTHOR>
 * @date 2025-07-22
 */
@Data
@Schema(description = "测站规则信息")
public class StationWithRuleInfo implements Serializable {
    
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @Schema(description = "测站编码")
    private String stationId;

    /** 测站名称 */
    @Schema(description = "测站名称")
    private String stationName;

    /** 测站地址 */
    @Schema(description = "测站地址")
    private String stationLocation;

    /** 经度 */
    @Schema(description = "经度")
    private String longitude;

    /** 纬度 */
    @Schema(description = "纬度")
    private String latitude;

    /** 测站类型 */
    @Schema(description = "测站类型")
    private String stationType;

    /** 行政区划名称 */
    @Schema(description = "行政区划名称")
    private String adminName;

    /** 规则ID */
    @Schema(description = "规则ID")
    private Long ruleId;

    /** 规则名称 */
    @Schema(description = "规则名称")
    private String ruleName;

    /** 规则类型ID */
    @Schema(description = "规则类型ID")
    private String ruleTypeId;

    /** 规则类型名称 */
    @Schema(description = "规则类型名称")
    private String ruleTypeName;

    /** 规则状态 (0-禁用, 1-启用) */
    @Schema(description = "规则状态 (0-禁用, 1-启用)")
    private Integer status;

    /** 规则状态描述 */
    @Schema(description = "规则状态描述")
    private String statusDesc;

    /** 创建时间 */
    @Schema(description = "创建时间")
    private String createTime;

    /** 更新时间 */
    @Schema(description = "更新时间")
    private String updateTime;

    /** 执行表达式 */
    @Schema(description = "执行表达式")
    private String cronExpressions;

    /** 规则参数JSON */
    @Schema(description = "规则参数JSON")
    private String argJson;
}
