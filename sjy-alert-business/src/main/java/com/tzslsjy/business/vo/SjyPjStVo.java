package com.tzslsjy.business.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import java.math.BigDecimal;

import com.tzslsjy.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.util.Date;



/**
 * 工程测站关联视图对象 mall_package
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@Schema(description = "工程测站关联视图对象")
public class SjyPjStVo {
	private static final long serialVersionUID = 1L;

	/**  */
	@Schema(description = "")
	private Long id;

	/** 测站编码 */
	@Excel(name = "测站编码")
	@Schema(description = "测站编码")
	private String stcd;
	/** 工程编码 */
	@Excel(name = "工程编码")
	@Schema(description = "工程编码")
	private String pjcd;
	/** 是否主站 */
	@Excel(name = "是否主站")
	@Schema(description = "是否主站")
	private String isMian;
	/** 是否雨量站 */
	@Excel(name = "是否雨量站")
	@Schema(description = "是否雨量站")
	private String isRain;
	/** 雨量权重 */
	@Excel(name = "雨量权重")
	@Schema(description = "雨量权重")
	private BigDecimal rainWeight;
	/** 工程类型 */
	@Excel(name = "工程类型")
	@Schema(description = "工程类型")
	private String projType;
	/** 排序 */
	@Excel(name = "排序")
	@Schema(description = "排序")
	private Long orderNum;
	/** 是否删除;是否删除:1未删除，2删除 */
	@Excel(name = "是否删除;是否删除:1未删除，2删除")
	@Schema(description = "是否删除;是否删除:1未删除，2删除")
	private Long izDel;
	/** 备注;冗余，可用于数据库层面临时操作时的标记 */
	@Excel(name = "备注;冗余，可用于数据库层面临时操作时的标记")
	@Schema(description = "备注;冗余，可用于数据库层面临时操作时的标记")
	private String remark;

}