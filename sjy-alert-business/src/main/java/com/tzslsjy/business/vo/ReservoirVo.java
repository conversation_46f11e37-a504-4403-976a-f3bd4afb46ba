package com.tzslsjy.business.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.common.utils.SjyAccuracyUtil; // Assuming this will be available from sjy-alert-common
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
// import lombok.EqualsAndHashCode; // Removed

import java.math.BigDecimal;
import java.util.Date;

@Data
// @EqualsAndHashCode(callSuper = false) //此注解会生成equals(Object other) 和 hashCode()方法。不调用父类的属性 // Removed
//库（湖） 站汛限水位表-汛限水位,库（湖） 站防洪指标表-正常水位,
@Schema(description = "库（湖） 站汛限水位表 ReservoirVo")
public class ReservoirVo extends StStbprpB {

    /*库（湖） 站汛限水位表-汛限水位*/
    @Schema(description = "开始月日")
    @TableField("BGMD")
    private String bgmd;

    private String pjcd;
    @Schema(description = "结束月日")
    @TableField("EDMD")
    private String edmd;

    @Schema(description = "行政区划名称")
    private String addvcdNm;

    @Schema(description = "汛限水位（m）")
    @TableField("FSLTDZ")
    private BigDecimal fsltdz;

    @Schema(description = "降雨量")
    private BigDecimal drp;

    @Schema(description = "汛限库容（10^6m^3）")
    @TableField("FSLTDW")
    private BigDecimal fsltdw;

    /** 可供低水位 (m) */
    @Schema(description = "可供低水位 (m)")
    private BigDecimal spllymz;

    @Schema(description = "平均日耗水量 (万m³/d) ")
    private BigDecimal avgDailyUsage;

    @Schema(description = "汛期类别")
    @TableField("FSTP")
    private String fstp;

    /*库（湖） 站防洪指标表-正常水位*/

    @Schema(description = "水库类型")
    @TableField("RSVRTP")
    private String rsvrtp;

    @Schema(description = "预报最高水位")
    @TableField(exist = false)
    private BigDecimal maxRz;

    @Schema(description = "坝顶高程")
    @TableField("DAMEL")
    private BigDecimal damel;

    @Schema(description = "校核洪水位")
    @TableField("CKFLZ")
    private BigDecimal ckflz;

    @Schema(description = "设计洪水位")
    @TableField("DSFLZ")
    private BigDecimal dsflz;

    @Schema(description = "正常水位")
    @TableField("NORMZ")
    private BigDecimal normz;

    @Schema(description = "死水位")
    @TableField("DDZ")
    private BigDecimal ddz;

    @Schema(description = "死库容")
    @TableField("DDCP")
    private BigDecimal ddcp;

    @Schema(description = "防洪高水位")
    //@TableField("DDCP")
    private BigDecimal fsdlz;

    @Schema(description = "兴利水位")
    @TableField("ACTZ")
    private BigDecimal actz;

    @Schema(description = "总库容")
    @TableField("TTCP")
    private BigDecimal ttcp;

    @Schema(description = "兴利库容")
    @TableField("ACTCP")
    private BigDecimal actcp;

    @Schema(description = "水情型式")
    @TableField("RWPTN")
    private String rwptn;

    @Schema(description = "当前库容") // Note: Field name is norRz, description is "当前库容"
    private BigDecimal norRz;

    @Schema(description = "正常库容")
    private BigDecimal norcp;

    @Schema(description = "汛限蓄水率")
    private BigDecimal xRate;

    @Schema(description = "汛限可拦蓄雨量")
    private BigDecimal xblock;

    @Schema(description = "正常可拦蓄雨量")
    private BigDecimal nblock;

    @Schema(description = "当前蓄水率")
    private BigDecimal norRate;

    @Schema(description = "收藏标志")
    private Boolean starFlag;
    /**
     * 水库实时水位流量表
     **/
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "时间")
    @TableField("TM")
    private Date tm;

    @Schema(description = "库上水位")
    @TableField("RZ")
    private BigDecimal rz;

    @Schema(description = "最高水位")
    private BigDecimal highRz;

    @Schema(description = "最高水位时间")
    private Date highRzTm;

    /*库容曲线-库容计算*/
    @Schema(description = "点序号")
    @TableField("PTNO")
    private BigDecimal ptno;

    @Schema(description = "库水位(m)")
    @TableField("RZ") // Note: This @TableField("RZ") might conflict with the other 'rz' field if 'rzs' is not the actual DB column name.
    private BigDecimal rzs;

    @Schema(description = "需水量（10^6m^3）")
    @TableField("W")
    private BigDecimal w;

    /*其他*/
    @Schema(description = "实际水位，跟库容曲线相减的绝对值")
    private BigDecimal abs;

    @Schema(description = "水库类型等级")
    private Integer level;

    @Schema(description = "当前站点的现在的情况，0 正常，1超警戒/超汛限，2超保证超正常， 6无数据")
    private Integer status;

    @Schema(description = "超出库容的水位，单位：m，正数表示超出，负数表示未超出")
    private BigDecimal overRz;

    @Schema(description = "水势")
    private String wptn;

    @Schema(description = "新W值")
    private BigDecimal newW;

    @Schema(description = "水库类型名称")
    private String levelName;

    @Schema(description = "水库等级排序")
    Long levelSort;

    @Schema(description = "排序号")
    private Integer sort;

    @Schema(description = "1重点测站")
    private String flag;

    @Schema(description = "站点分类:P 含雨量数据的测站 、Z 含水位数据的测站、basin_jlj_csinfo 椒江流域的断面关联测站、")
    private String stType;

    @Schema(description = "行政区划")
    @TableField("JURISDICTION")
    private String jurisdiction;

    @Schema(description = "行政区划9位对应中文")
    private String jurisdiction9;

    public BigDecimal getXblock() {
        return SjyAccuracyUtil.nBlockNegativeforZreo(xblock);
    }
    public ReservoirVo setXblock(BigDecimal xblock) {
        this.xblock = xblock;
        return this;
    }

    public BigDecimal getNblock() {
        return SjyAccuracyUtil.nBlockNegativeforZreo(nblock);
    }
    public ReservoirVo setNblock(BigDecimal nblock) {
        this.nblock = nblock;
        return this;
    }
}
