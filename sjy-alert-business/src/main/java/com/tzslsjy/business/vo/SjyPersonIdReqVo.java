package com.tzslsjy.business.vo;

import com.tzslsjy.business.domain.SjyAlertNode; // Updated import
import lombok.Data;
import io.swagger.v3.oas.annotations.media.Schema;

import java.util.List;

@Data
@Schema(description = "人员ID请求VO")
public class SjyPersonIdReqVo {
    @Schema(description = "成员列表 (例如逗号分隔的用户/组织ID)")
    private String members;

    @Schema(description = "预警节点列表")
    List<SjyAlertNode> alertNodes;
}