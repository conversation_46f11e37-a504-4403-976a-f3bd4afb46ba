package com.tzslsjy.business.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 单站多年日降雨添加对象 st_pptn_hist_d
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Data
@Schema(description = "单站历史降雨分析返回对象")
public class StPptnHistRespVo {
    /**  */
    @Schema(description = "测站编号")
    private String stcd;

    /**  */
    @Schema(description = "测站名称")
    private String stnm;
    /**  */
    @Schema(description = "类型（1县市区 2 测站）")
    private Integer type;
    /**  */
    @Schema(description = "行政区划")
    private String addvcd;
    @Schema(description = "累计")
    private BigDecimal sum;
    @Schema(description = "同期累计")
    private BigDecimal samePeriodSum;
    @Schema(description = "同比增速")
    private BigDecimal growthRate;
    @Schema(description = "预计未来降雨")
    private BigDecimal frDrp;
    @Schema(description = "经度")
    private BigDecimal lgtd;
    @Schema(description = "纬度")
    private BigDecimal lttd;
}
