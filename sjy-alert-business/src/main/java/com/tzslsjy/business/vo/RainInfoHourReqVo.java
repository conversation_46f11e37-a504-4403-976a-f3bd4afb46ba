package com.tzslsjy.business.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Set;

@Schema(description = "RainInfoHourReqVo")
@Data
public class RainInfoHourReqVo {
    @Schema(description = "页大小")
    private Integer pageSize;
    @Schema(description = "页数")
    private Integer pageNum;
    @Schema(description = "极值时间")
    private Integer hour;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private Date startTm;
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间")
    private Date endTm;
    @Schema(description = "雨量站类型（PP 水文站 MM 气象站）")
    private String pType;
    @Schema(description = "去除的站点类型(弃用)")
    private String nType;
    @Schema(description = "时段降雨量（mm）")
    @TableField("DRP")
    private BigDecimal drp;
    @Schema(description = "MM 气象站 SS 水文站 默认全部")
    private String stSource;
    @Schema(description = "时段长（h）")
    @TableField("INTV")
    private BigDecimal intv;

    @Schema(description = "降水历时")
    @TableField("PDR")
    private BigDecimal pdr;

    @Schema(description = "日降雨量")
    @TableField("DYP")
    private BigDecimal dyp;

    @Schema(description = "天气情况")
    @TableField("WTH")
    private String wth;
    @Schema(description = "某个时间段的雨量总和")
    private Float rainfall;

    @Schema(description = "时间")
    private Date tm;

    @Schema(description = "type")
    private String type;

    @Schema(description = "雨量大于")
    @TableField(exist = false)
    private BigDecimal val;

    @Schema(description = "雨量小于")
    @TableField(exist = false)
    private BigDecimal endVal;
    @Schema(description = "极值雨量大于")
    @TableField(exist = false)
    private BigDecimal maxVal;

    @Schema(description = "代表站类型（0所有站点 1雨量代表站）")
    @TableField(exist = false)
    private Integer flag;
    @Schema(description = "平均雨量（1所有站点 0雨量代表站）")
    @TableField(exist = false)
    private String avgFlag;
    @Schema(description = "返回列表限制数量（默认10）")
    @TableField(exist = false)
    private Integer limit;

    @Schema(description = "测站编码集合")
    @TableField(exist = false)
    private Set<String> stcds;

    @Schema(description = "全部（0）气象（1）水纹（2）")
    @TableField(exist = false)
    Integer source;
    @Schema(description = "用户id(用于查询收藏站点)")
    @TableField(exist = false)
    String userId;
    @Schema(description = "流域编码")
    private String basinCode;
    @Schema(description = "流域编码")
    private String lhBasinCode;
    
    @Schema(description = "行政区划编码9位")
    @TableField(exist = false)
    private String addvcd9;
    
    @Schema(description = "测站编号")
    private String stcd;
    
    @Schema(description = "行政区划编码")
    private String addvcd;
}