package com.tzslsjy.business.vo;


import com.tzslsjy.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


/**
 * 节点职位视图对象 mall_package
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@Schema(description = "节点职位视图对象")
public class SjyAlertStatRespVo {
	private static final long serialVersionUID = 1L;

	@Schema(description = "雨量预警")
	private Integer rainAlertNum;

	@Schema(description = " 水库预警")
	private Integer rsAlertNum;

	@Schema(description = "河道预警")
	private Integer  rvAlertNum;

	@Schema(description = "山洪预警")
	private Integer fdAlertNum;

}
