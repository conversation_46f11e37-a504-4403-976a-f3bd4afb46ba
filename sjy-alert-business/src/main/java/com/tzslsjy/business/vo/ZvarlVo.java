package com.tzslsjy.business.vo;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

//库容曲线表
@Data
@Schema(description = "库容曲线表")
public class ZvarlVo {
    @Schema(description = "测站编码")
    @TableId(value = "STCD", type = IdType.ASSIGN_UUID)
    private String stcd;
    @Schema(description = "点序号")
    @TableField("PTNO")
    private BigDecimal ptno;

    @Schema(description = "库水位(m)")
    @TableField("RZ")
    private BigDecimal rz;

    @Schema(description = "需水量（10^6m^3）")
    @TableField("W")
    private BigDecimal w;
}