package com.tzslsjy.business.config;

import com.tzslsjy.business.service.scheduler.AlertRuleSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * 预警规则调度器配置
 * 在应用启动后自动初始化所有预警规则的定时任务
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Configuration
@Slf4j
@Order(100) // 确保在其他组件初始化后执行
public class AlertRuleSchedulerConfig implements ApplicationRunner {

    @Autowired
    private AlertRuleSchedulerService alertRuleSchedulerService;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("应用启动完成，开始初始化预警规则调度器");
        
        try {
            // 延迟一段时间确保所有Bean都已完全初始化
            Thread.sleep(3000);
            
            // 初始化所有预警规则的定时任务
            alertRuleSchedulerService.initializeAllAlertRules();
            
            log.info("预警规则调度器初始化完成");
            
        } catch (Exception e) {
            log.error("预警规则调度器初始化失败", e);
            // 不抛出异常，避免影响应用启动
        }
    }
}
