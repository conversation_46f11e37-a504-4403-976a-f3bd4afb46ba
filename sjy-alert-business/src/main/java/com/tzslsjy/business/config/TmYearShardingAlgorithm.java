package com.tzslsjy.business.config;

import cn.hutool.core.date.DateUtil;
import com.google.common.collect.Range;
import org.apache.shardingsphere.sharding.api.sharding.standard.PreciseShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.RangeShardingValue;
import org.apache.shardingsphere.sharding.api.sharding.standard.StandardShardingAlgorithm;

import java.util.*;

public class TmYearShardingAlgorithm implements StandardShardingAlgorithm<Date> {
//    private static final String ALGORITHM_EXPRESSION_KEY = "algorithm-expression";
//    private static final String ALLOW_RANGE_QUERY_KEY = "allow-range-query-with-inline-sharding";
//    private String algorithmExpression;
//    private boolean allowRangeQuery;
//    private Properties props = new Properties();


    @Override
    public String doSharding(Collection availableTargetNames, PreciseShardingValue shardingValue) {
        Date value = (Date) shardingValue.getValue();
        String logicTableName = shardingValue.getLogicTableName();

        // 构造目标表名
        String targetTableName = logicTableName + "_" + DateUtil.format(value, "yyyy");

        // 检查目标表是否存在，如果不存在则使用默认表
        if (availableTargetNames.contains(targetTableName)) {
            return targetTableName;
        } else {
            // 使用默认表（逻辑表名）
            return logicTableName;
        }
    }

    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, RangeShardingValue<Date> rangeShardingValue) {
        Range<Date> range = rangeShardingValue.getValueRange();
        Date lowerEndpoint = range.lowerEndpoint();
        Date upperEndpoint = range.hasUpperBound() ? range.upperEndpoint() : DateUtil.date();

        String logicTableName = rangeShardingValue.getLogicTableName();
        Set<String> result = new HashSet<>();

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(lowerEndpoint);
        int startYear = calendar.get(Calendar.YEAR);
        calendar.setTime(upperEndpoint);
        int endYear = calendar.get(Calendar.YEAR);
        boolean useDefaultTable = true;
        for (int year = startYear; year <= endYear; year++) {
            String targetTableName = logicTableName + "_" + year;

            // 检查目标表是否存在
            if (availableTargetNames.contains(targetTableName)) {
                result.add(targetTableName);
                useDefaultTable = false;
            }
        }

        // 如果没有找到任何匹配的年份表，则使用默认表
        if (useDefaultTable) {
            result.add(logicTableName);
        }

        return result;

    }

    public Collection<String> getYears(Date start, Date end, String tableName) {
        Collection<String> collection = new ArrayList<>();
        Date startDate = DateUtil.beginOfYear(start);
        //结束时间
        Date endDate = DateUtil.beginOfYear(end);

        while (startDate.getTime() <= endDate.getTime()) {
            collection.add(tableName + "_".concat(DateUtil.format(startDate, "yyyy")));
            startDate = DateUtil.offsetMonth(startDate, 12);
        }
        //System.out.println(collection);
        return collection;
    }


/*    private Closure<?> createClosure() {
        Closure<?> result = (new InlineExpressionParser(this.algorithmExpression)).evaluateClosure().rehydrate(new Expando(), (Object)null, (Object)null);
        result.setResolveStrategy(3);
        return result;
    }*/



/*
    private boolean isAllowRangeQuery() {
        return Boolean.parseBoolean(this.props.getOrDefault("allow-range-query-with-inline-sharding", Boolean.FALSE.toString()).toString());
    }

    private String getAlgorithmExpression() {
        String expression = "max_temp_log_$->{equipment_id % 2}";
        Preconditions.checkNotNull(expression, "Inline sharding algorithm expression cannot be null.");
        return InlineExpressionParser.handlePlaceHolder(expression.trim());
    }
*/

    @Override
    public String getType() {
        return "CLASS_BASED";
    }

    @Override
    public Properties getProps() {
        return null;
    }

    @Override
    public void init(Properties properties) {
        System.out.println(String.valueOf(properties));
    }
}

