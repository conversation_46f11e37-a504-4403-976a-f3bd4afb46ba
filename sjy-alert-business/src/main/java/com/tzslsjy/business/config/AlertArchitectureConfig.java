//package com.tzslsjy.business.config;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.core.task.TaskExecutor;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//
///**
// * 预警系统架构配置类
// * 负责配置新架构的相关组件
// */
//@Configuration
//@Slf4j
//public class AlertArchitectureConfig {
//
//    /**
//     * 配置预警任务执行器
//     * 用于异步发送通知等任务
//     */
//    @Bean("alertTaskExecutor")
//    public TaskExecutor alertTaskExecutor() {
//        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
//        executor.setCorePoolSize(2);
//        executor.setMaxPoolSize(8);
//        executor.setQueueCapacity(100);
//        executor.setThreadNamePrefix("alert-task-");
//        executor.setKeepAliveSeconds(60);
//        executor.setRejectedExecutionHandler(new java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy());
//        executor.initialize();
//
//        log.info("预警任务执行器配置完成: core={}, max={}, queue={}",
//                executor.getCorePoolSize(), executor.getMaxPoolSize(), executor.getQueueCapacity());
//
//        return executor;
//    }
//}