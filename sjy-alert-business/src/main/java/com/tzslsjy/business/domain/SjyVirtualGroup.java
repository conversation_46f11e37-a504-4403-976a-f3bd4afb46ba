package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.Date;

/**
 * 虚拟分组对象 sjy_virtual_group
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_virtual_group")
@Schema(description = "虚拟分组")
public class SjyVirtualGroup implements Serializable {

private static final long serialVersionUID=1L;


    /** 主键ID (UUID) */
    @TableId(value = "id")
    @Schema(description = "主键ID (UUID)")
    private String id;

    /** ID列表 */
    @Schema(description = "ID列表")
    private String idList;

    /** 成员列表 (例如逗号分隔的用户/组织ID) */
    @Schema(description = "成员列表 (例如逗号分隔的用户/组织ID)")
    private String members;

    /** 分组名称 */
    @Schema(description = "分组名称")
    private String name;

    /** 排序ID */
    @Schema(description = "排序ID")
    private Long orderId;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建人")
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    @Schema(description = "创建时间")
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新人")
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    @Schema(description = "更新时间")
    private Date updateTime;

    /** 是否删除;是否删除:1未删除，2删除 */
    @Schema(description = "是否删除;是否删除:1未删除，2删除")
    private Long izDel;

    /** 备注;冗余，可用于数据库层面临时操作时的标记 */
    @Schema(description = "备注;冗余，可用于数据库层面临时操作时的标记")
    private String remark;

}