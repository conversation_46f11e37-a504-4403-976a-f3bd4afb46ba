package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 单站多年日降雨对象 st_pptn_hist_d
 * 
 * <AUTHOR>
 * @date 2023-05-15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("st_pptn_hist_d")
public class StPptnHistD implements Serializable {

private static final long serialVersionUID=1L;


    /** 测站编号 */
    private String stcd;

    /** 站名 */
    private String stnm;

    /** 时间 */
    private Date tm;

    /** 降水量 */
    private BigDecimal drp;

    /** 月份 */
    private Integer month;

    /** 日期 */
    private Integer day;

    /** 类型（1县市区 2 测站） */
    private Integer type;

    /** 行政区划编号 */
    private String addvcd;

}