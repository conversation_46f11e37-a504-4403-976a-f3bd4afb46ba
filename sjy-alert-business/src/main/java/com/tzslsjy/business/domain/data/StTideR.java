package com.tzslsjy.business.domain.data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema; // Updated import

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @auther seven
 * @create 2021-04-02 11:25:57
 * @describe 潮汐水情表实体类
 */
@TableName("st_tide_r")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description="潮汐水情表") // Updated annotation
public class StTideR implements Serializable {

private static final long serialVersionUID = 1L;

        @Schema(description = "测站编码") // Updated annotation
    @TableField("STCD")
        private String stcd;

        @Schema(description = "时间") // Updated annotation
    @TableField("TM")
        private Date tm;

        @Schema(description = "潮位（m）") // Updated annotation
    @TableField("TDZ")
        private BigDecimal tdz;

        @Schema(description = "气压（10^2Pa）") // Updated annotation
    @TableField("AIRP")
        private BigDecimal airp;

        @Schema(description = "潮水特征码") // Updated annotation
    @TableField("TDCHRCD")
        private String tdchrcd;

        @Schema(description = "潮势") // Updated annotation
    @TableField("TDPTN")
        private String tdptn;

        @Schema(description = "高低潮标志") // Updated annotation
    @TableField("HLTDMK")
        private String hltdmk;

    @TableField("MODITIME")
        private Date moditime;

    @TableField("EDITED")
        private String edited;

    @TableField("B_Flag")
        private String bFlag;


    public String getStcd() {
            return stcd;
            }

        public StTideR setStcd(String stcd) {
            this.stcd = stcd;
                return this;
            }

    public Date getTm() {
            return tm;
            }

        public StTideR setTm(Date tm) {
            this.tm = tm;
                return this;
            }

    public BigDecimal getTdz() {
            return tdz;
            }

        public StTideR setTdz(BigDecimal tdz) {
            this.tdz = tdz;
                return this;
            }

    public BigDecimal getAirp() {
            return airp;
            }

        public StTideR setAirp(BigDecimal airp) {
            this.airp = airp;
                return this;
            }

    public String getTdchrcd() {
            return tdchrcd;
            }

        public StTideR setTdchrcd(String tdchrcd) {
            this.tdchrcd = tdchrcd;
                return this;
            }

    public String getTdptn() {
            return tdptn;
            }

        public StTideR setTdptn(String tdptn) {
            this.tdptn = tdptn;
                return this;
            }

    public String getHltdmk() {
            return hltdmk;
            }

        public StTideR setHltdmk(String hltdmk) {
            this.hltdmk = hltdmk;
                return this;
            }

    public Date getModitime() {
            return moditime;
            }

        public StTideR setModitime(Date moditime) {
            this.moditime = moditime;
                return this;
            }

    public String getEdited() {
            return edited;
            }

        public StTideR setEdited(String edited) {
            this.edited = edited;
                return this;
            }

    public String getbFlag() {
            return bFlag;
            }

        public StTideR setbFlag(String bFlag) {
            this.bFlag = bFlag;
                return this;
            }

@Override
public String toString() {
        return "StTideR{" +
                "stcd=" + stcd +
                ", tm=" + tm +
                ", tdz=" + tdz +
                ", airp=" + airp +
                ", tdchrcd=" + tdchrcd +
                ", tdptn=" + tdptn +
                ", hltdmk=" + hltdmk +
                ", moditime=" + moditime +
                ", edited=" + edited +
                ", bFlag=" + bFlag +
        "}";
        }
        }