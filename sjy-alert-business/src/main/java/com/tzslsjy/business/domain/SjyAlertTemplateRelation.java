package com.tzslsjy.business.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * 消息模板关联对象 sjy_alert_template_relation
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Schema(description = "消息模板关联对象")
public class SjyAlertTemplateRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long id;

    /** 模板ID */
    @Schema(description = "模板ID")
    @Excel(name = "模板ID")
    @Max(value = 2147483647)
    private Long templateId;

    /** 关联ID */
    @Schema(description = "关联ID")
    @Excel(name = "关联ID")
    @Max(value = Long.MAX_VALUE)
    private Long relationId;

    /** 关联类型(1-规则模版，2-参数模板) */
    @Schema(description = "关联类型(1-规则模版，2-参数模板)")
    @Excel(name = "关联类型(1-规则模版，2-参数模板)")
    @Size(max = 255)
    private String relationType;

    /** 状态：0-禁用，1-启用 */
    @Schema(description = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    private Long status;

    /** 关联的短信模板对象 */
    @Schema(description = "关联的短信模板对象")
    private SjyAlertSmsTemplate sjyAlertSmsTemplate;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setTemplateId(Long templateId)
    {
        this.templateId = templateId;
    }

    public Long getTemplateId()
    {
        return templateId;
    }
    public void setRelationId(Long relationId)
    {
        this.relationId = relationId;
    }

    public Long getRelationId()
    {
        return relationId;
    }
    public void setRelationType(String relationType)
    {
        this.relationType = relationType;
    }

    public String getRelationType()
    {
        return relationType;
    }

    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public SjyAlertSmsTemplate getSjyAlertSmsTemplate() {
        return sjyAlertSmsTemplate;
    }

    public void setSjyAlertSmsTemplate(SjyAlertSmsTemplate sjyAlertSmsTemplate) {
        this.sjyAlertSmsTemplate = sjyAlertSmsTemplate;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("templateId", getTemplateId())
            .append("relationId", getRelationId())
            .append("relationType", getRelationType())
            .append("status", getStatus())
            .append("sjyAlertSmsTemplate", getSjyAlertSmsTemplate())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
