package com.tzslsjy.business.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * 山洪易发区信息对象 sjy_fd_prone_area
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Schema(description = "山洪易发区信息对象")
public class SjyFdProneArea extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 防治对象编码 */
    @Schema(description = "防治对象编码")
    @Size(max = 36)
    private String prevCode;

    /** 行政村 */
    @Schema(description = "行政村")
    @Excel(name = "行政村")
    @Size(max = 255)
    private String adminAdnm;

    /** 自然村 */
    @Schema(description = "自然村")
    @Excel(name = "自然村")
    @Size(max = 255)
    private String naturalAdnm;

    /** 辖区面积 */
    @Schema(description = "辖区面积")
    @Excel(name = "辖区面积")
    private BigDecimal fldarea;

    /** 总户数 */
    @Schema(description = "总户数")
    @Excel(name = "总户数")
    @Max(value = 2147483647)
    private Long htcount;

    /** 总人口 */
    @Schema(description = "总人口")
    @Excel(name = "总人口")
    @Max(value = 2147483647)
    private Long pcount;

    /** 经度 */
    @Schema(description = "经度")
    @Excel(name = "经度")
    private BigDecimal lgtd;

    /** 纬度 */
    @Schema(description = "纬度")
    @Excel(name = "纬度")
    private BigDecimal lttd;

    /** 枚举名称 */
    @Schema(description = "枚举名称")
    @Excel(name = "枚举名称")
    @Size(max = 255)
    private String prevtpName;

    /** 已有或新增 */
    @Schema(description = "已有或新增")
    @Excel(name = "已有或新增")
    @Size(max = 1)
    private String isAdded;

    /** 山洪灾害风险类型 */
    @Schema(description = "山洪灾害风险类型")
    @Excel(name = "山洪灾害风险类型")
    @Size(max = 255)
    private String risktp;

    /** 行政区划编码 */
    @Schema(description = "行政区划编码")
    @Excel(name = "行政区划编码")
    @Size(max = 50)
    private String adcd;

    /** 数据入库时间 */
    @Schema(description = "数据入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据入库时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date tongTime;

    /** 乡镇名称 */
    @Schema(description = "乡镇名称")
    @Excel(name = "乡镇名称")
    @Size(max = 50)
    private String townName;

    /** 县级名称 */
    @Schema(description = "县级名称")
    @Excel(name = "县级名称")
    @Size(max = 50)
    private String countyName;

    /** 市级名称 */
    @Schema(description = "市级名称")
    @Excel(name = "市级名称")
    @Size(max = 50)
    private String cityName;

    /** 集雨面积 */
    @Schema(description = "集雨面积")
    @Excel(name = "集雨面积")
    private BigDecimal drna;

    /** 成灾水位（*） */
    @Schema(description = "成灾水位（*）")
    @Excel(name = "成灾水位", readConverterExp = "*=")
    private BigDecimal floodZ;

    /** 成灾流量（*） */
    @Schema(description = "成灾流量（*）")
    @Excel(name = "成灾流量", readConverterExp = "*=")
    private BigDecimal floodQ;

    /** 主干河长 */
    @Schema(description = "主干河长")
    @Excel(name = "主干河长")
    private BigDecimal riverLen;

    /** 河道比降 */
    @Schema(description = "河道比降")
    @Excel(name = "河道比降")
    private BigDecimal riverBit;

    /** 糙率 */
    @Schema(description = "糙率")
    @Excel(name = "糙率")
    private BigDecimal roughness;

    /** 5年一遇设计洪峰流量 */
    @Schema(description = "5年一遇设计洪峰流量")
    @Excel(name = "5年一遇设计洪峰流量")
    private BigDecimal fiveQ;

    /** 10年一遇设计洪峰流量 */
    @Schema(description = "10年一遇设计洪峰流量")
    @Excel(name = "10年一遇设计洪峰流量")
    private BigDecimal tenQ;

    /** 20年一遇设计洪峰流量 */
    @Schema(description = "20年一遇设计洪峰流量")
    @Excel(name = "20年一遇设计洪峰流量")
    private BigDecimal twentyQ;

    /** 50年一遇设计洪峰流量 */
    @Schema(description = "50年一遇设计洪峰流量")
    @Excel(name = "50年一遇设计洪峰流量")
    private BigDecimal fiftyQ;

    /** 100年一遇设计洪峰流量 */
    @Schema(description = "100年一遇设计洪峰流量")
    @Excel(name = "100年一遇设计洪峰流量")
    private BigDecimal hundredQ;

    /** 去年防御人数 */
    @Schema(description = "去年防御人数")
    @Excel(name = "去年防御人数")
    @Max(value = 2147483647)
    private Long gcount;

    public void setPrevCode(String prevCode)
    {
        this.prevCode = prevCode;
    }

    public String getPrevCode()
    {
        return prevCode;
    }
    public void setAdminAdnm(String adminAdnm)
    {
        this.adminAdnm = adminAdnm;
    }

    public String getAdminAdnm()
    {
        return adminAdnm;
    }
    public void setNaturalAdnm(String naturalAdnm)
    {
        this.naturalAdnm = naturalAdnm;
    }

    public String getNaturalAdnm()
    {
        return naturalAdnm;
    }
    public void setFldarea(BigDecimal fldarea)
    {
        this.fldarea = fldarea;
    }

    public BigDecimal getFldarea()
    {
        return fldarea;
    }
    public void setHtcount(Long htcount)
    {
        this.htcount = htcount;
    }

    public Long getHtcount()
    {
        return htcount;
    }
    public void setPcount(Long pcount)
    {
        this.pcount = pcount;
    }

    public Long getPcount()
    {
        return pcount;
    }
    public void setLgtd(BigDecimal lgtd)
    {
        this.lgtd = lgtd;
    }

    public BigDecimal getLgtd()
    {
        return lgtd;
    }
    public void setLttd(BigDecimal lttd)
    {
        this.lttd = lttd;
    }

    public BigDecimal getLttd()
    {
        return lttd;
    }
    public void setPrevtpName(String prevtpName)
    {
        this.prevtpName = prevtpName;
    }

    public String getPrevtpName()
    {
        return prevtpName;
    }
    public void setIsAdded(String isAdded)
    {
        this.isAdded = isAdded;
    }

    public String getIsAdded()
    {
        return isAdded;
    }
    public void setRisktp(String risktp)
    {
        this.risktp = risktp;
    }

    public String getRisktp()
    {
        return risktp;
    }
    public void setAdcd(String adcd)
    {
        this.adcd = adcd;
    }

    public String getAdcd()
    {
        return adcd;
    }
    public void setTongTime(Date tongTime)
    {
        this.tongTime = tongTime;
    }

    public Date getTongTime()
    {
        return tongTime;
    }
    public void setTownName(String townName)
    {
        this.townName = townName;
    }

    public String getTownName()
    {
        return townName;
    }
    public void setCountyName(String countyName)
    {
        this.countyName = countyName;
    }

    public String getCountyName()
    {
        return countyName;
    }
    public void setCityName(String cityName)
    {
        this.cityName = cityName;
    }

    public String getCityName()
    {
        return cityName;
    }
    public void setDrna(BigDecimal drna)
    {
        this.drna = drna;
    }

    public BigDecimal getDrna()
    {
        return drna;
    }
    public void setFloodZ(BigDecimal floodZ)
    {
        this.floodZ = floodZ;
    }

    public BigDecimal getFloodZ()
    {
        return floodZ;
    }
    public void setFloodQ(BigDecimal floodQ)
    {
        this.floodQ = floodQ;
    }

    public BigDecimal getFloodQ()
    {
        return floodQ;
    }
    public void setRiverLen(BigDecimal riverLen)
    {
        this.riverLen = riverLen;
    }

    public BigDecimal getRiverLen()
    {
        return riverLen;
    }
    public void setRiverBit(BigDecimal riverBit)
    {
        this.riverBit = riverBit;
    }

    public BigDecimal getRiverBit()
    {
        return riverBit;
    }
    public void setRoughness(BigDecimal roughness)
    {
        this.roughness = roughness;
    }

    public BigDecimal getRoughness()
    {
        return roughness;
    }
    public void setFiveQ(BigDecimal fiveQ)
    {
        this.fiveQ = fiveQ;
    }

    public BigDecimal getFiveQ()
    {
        return fiveQ;
    }
    public void setTenQ(BigDecimal tenQ)
    {
        this.tenQ = tenQ;
    }

    public BigDecimal getTenQ()
    {
        return tenQ;
    }
    public void setTwentyQ(BigDecimal twentyQ)
    {
        this.twentyQ = twentyQ;
    }

    public BigDecimal getTwentyQ()
    {
        return twentyQ;
    }
    public void setFiftyQ(BigDecimal fiftyQ)
    {
        this.fiftyQ = fiftyQ;
    }

    public BigDecimal getFiftyQ()
    {
        return fiftyQ;
    }
    public void setHundredQ(BigDecimal hundredQ)
    {
        this.hundredQ = hundredQ;
    }

    public BigDecimal getHundredQ()
    {
        return hundredQ;
    }
    public void setGcount(Long gcount)
    {
        this.gcount = gcount;
    }

    public Long getGcount()
    {
        return gcount;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("prevCode", getPrevCode())
            .append("adminAdnm", getAdminAdnm())
            .append("naturalAdnm", getNaturalAdnm())
            .append("fldarea", getFldarea())
            .append("htcount", getHtcount())
            .append("pcount", getPcount())
            .append("lgtd", getLgtd())
            .append("lttd", getLttd())
            .append("prevtpName", getPrevtpName())
            .append("isAdded", getIsAdded())
            .append("risktp", getRisktp())
            .append("adcd", getAdcd())
            .append("tongTime", getTongTime())
            .append("townName", getTownName())
            .append("countyName", getCountyName())
            .append("cityName", getCityName())
            .append("drna", getDrna())
            .append("floodZ", getFloodZ())
            .append("floodQ", getFloodQ())
            .append("riverLen", getRiverLen())
            .append("riverBit", getRiverBit())
            .append("roughness", getRoughness())
            .append("fiveQ", getFiveQ())
            .append("tenQ", getTenQ())
            .append("twentyQ", getTwentyQ())
            .append("fiftyQ", getFiftyQ())
            .append("hundredQ", getHundredQ())
            .append("gcount", getGcount())
            .toString();
    }
}
