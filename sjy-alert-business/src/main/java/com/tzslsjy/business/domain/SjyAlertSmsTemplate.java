package com.tzslsjy.business.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * 短信模板对象 sjy_alert_sms_template
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@Schema(description = "短信模板对象")
public class SjyAlertSmsTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 模板ID */
    @Schema(description = "模板ID")
    @Max(value = 2147483647)
    private Long templateId;

    /** 规则类型ID */
    @Schema(description = "规则类型ID")
    @Excel(name = "规则类型ID")
    @Max(value = Long.MAX_VALUE)
    private Long ruleTypeId;

    @Schema(description = "模板类型")
    private String type;

    /** 模板名称 */
    @Schema(description = "模板名称")
    @Excel(name = "模板名称")
    @Size(max = 100)
    private String templateName;

    /** 模板内容(支持变量占位符,如${variable_id:value}) */
    @Schema(description = "模板内容(支持变量占位符,如${variable_id:value})")
    @Excel(name = "模板内容(支持变量占位符,如${variable_id:value})")
    private String templateContent;

    /** 列表格式化模板，如：{stcd}:{water_level}m */
    @Schema(description = "列表格式化模板，如：{stcd}:{water_level}m")
    @Excel(name = "列表格式化模板，如：{stcd}:{water_level}m")
    private String listFormat;

    /** 内容连接符 */
    @Schema(description = "内容连接符")
    @Excel(name = "内容连接符")
    @Size(max = 255)
    private String listSeparator;

    /** 展示条数 */
    @Schema(description = "展示条数")
    @Excel(name = "展示条数")
    @Max(value = 2147483647)
    private Long displayNum;

    /** 省略替换 */
    @Schema(description = "省略替换")
    @Excel(name = "省略替换")
    @Size(max = 255)
    private String omitSymbol;

    /** 状态(0-禁用,1-启用) */
    @Schema(description = "状态(0-禁用,1-启用)")
    @Excel(name = "状态(0-禁用,1-启用)")
    private Integer status;

    /** 支持合并状态(0-禁用,1-启用) */
    @Schema(description = "支持合并状态(0-禁用,1-启用)")
    @Excel(name = "支持合并状态(0-禁用,1-启用)")
    private Integer izMerge;

}
