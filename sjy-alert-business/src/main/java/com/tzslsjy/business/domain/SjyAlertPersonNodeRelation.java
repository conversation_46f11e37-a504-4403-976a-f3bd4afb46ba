package com.tzslsjy.business.domain;


import com.baomidou.mybatisplus.annotation.TableField;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 人员节点关联对象 sjy_alert_person_node_relation
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Schema(description = "人员节点关联对象")
public class SjyAlertPersonNodeRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关联ID */
    @Schema(description = "关联ID")
    @Max(value = 2147483647)
    private Long relationId;

    /** 人员ID */
    @Schema(description = "人员ID")
    @Excel(name = "人员ID")
    private String personId;

    /** 节点ID */
    @Schema(description = "节点ID")
    @Excel(name = "节点ID")
    // @Max(value = Long.MAX_VALUE) // No longer applicable for String
    @Size(max = 64) // Assuming a max length for String ID
    private String nodeId;

    /** 岗位ID */
    @Schema(description = "岗位ID")
    @Excel(name = "岗位ID")
    private String postId; // Changed from Long to String

    /** 岗位名称 (冗余字段，通过postId关联查询) */
    @Schema(description = "岗位名称")
    @TableField(exist = false)
    @Excel(name = "岗位名称")
    private String postName; // 这个字段不会直接映射到数据库表列，而是通过查询填充

    /** 是否主要关联：0-否，1-是 */
    @Schema(description = "是否主要关联：0-否，1-是")
    @Excel(name = "是否主要关联：0-否，1-是")
    @Max(value = 2147483647)
    private Long isPrimary;

    /** 状态：0-禁用，1-启用 */
    @Schema(description = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    @Max(value = 2147483647)
    private Long status;

    /** 渠道（1短信2浙政钉3广播4传真） */
    @Schema(description = "渠道（1短信2浙政钉3广播4传真）")
    @Excel(name = "渠道", readConverterExp = "1=短信2浙政钉3广播4传真")
    @Size(max = 90)
    private String wayType;

    /** 关联类型（1 正常 2 职位 3 职能） */
    @Schema(description = "关联类型（1 正常 2 职位 3 职能）")
    @Excel(name = "关联类型", readConverterExp = "1=正常,2=职位,3=职能")
    private Integer relationType;

    private List<SjyAlertPerson> personList;
    public void setRelationId(Long relationId)
    {
        this.relationId = relationId;
    }

    public Long getRelationId()
    {
        return relationId;
    }
    public void setPersonId(String personId)
    {
        this.personId = personId;
    }

    public String getPersonId()
    {
        return personId;
    }
    public void setNodeId(String nodeId) // Changed Long to String
    {
        this.nodeId = nodeId;
    }

    public String getNodeId() // Changed Long to String
    {
        return nodeId;
    }

    public String getPostId() { // Changed from Long to String
        return postId;
    }

    public void setPostId(String postId) { // Changed from Long to String
        this.postId = postId;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public void setIsPrimary(Long isPrimary)
    {
        this.isPrimary = isPrimary;
    }

    public Long getIsPrimary()
    {
        return isPrimary;
    }
    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }
    public void setWayType(String wayType)
    {
        this.wayType = wayType;
    }

    public String getWayType()
    {
        return wayType;
    }

    public Integer getRelationType() {
        return relationType;
    }

    public void setRelationType(Integer relationType) {
        this.relationType = relationType;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("relationId", getRelationId())
            .append("personId", getPersonId())
            .append("nodeId", getNodeId())
            .append("postId", getPostId())
            .append("postName", getPostName())
            .append("isPrimary", getIsPrimary())
            .append("status", getStatus())
            .append("wayType", getWayType())
            .append("relationType", getRelationType())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public List<SjyAlertPerson> getPersonList() {
        return personList;
    }

    public void setPersonList(List<SjyAlertPerson> personList) {
        this.personList = personList;
    }
}
