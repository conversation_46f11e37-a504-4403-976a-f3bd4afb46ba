package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 文件夹信息对象 sjy_folder
 * 
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_folder")
public class SjyFolder implements Serializable {

private static final long serialVersionUID=1L;


    /** 文件夹ID */
    @TableId(value = "folder_id")
    private String folderId;

    /** 文件夹名称 */
    private String name;

    /** 父文件夹ID，NULL表示根目录 */
    private String parentFolderId;
    /** 文件夹类型 */
    private String folderType;
    /** 所属ID类型 (1-村庄)*/
    private String cType;
    /** 所属ID */
    private String cId;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 是否删除;是否删除:1未删除，2删除 */
    private Long izDel;

    /** 备注;冗余，可用于数据库层面临时操作时的标记 */
    private String remark;

}