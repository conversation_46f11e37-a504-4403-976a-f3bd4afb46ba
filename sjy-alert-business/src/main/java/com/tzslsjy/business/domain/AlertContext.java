package com.tzslsjy.business.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.*;

/**
 * 预警上下文对象，用于在整个预警处理流程中传递数据
 */
@Data
@Schema(description = "预警上下文对象，用于在整个预警处理流程中传递数据")
public class AlertContext {
    @Schema(description = "规则类型编码")
    private String ruleType;

    @Schema(description = "具体规则对象")
    private SjyAlertRule rule;

    @Schema(description = "规则参数列表")
    private List<SjyAlertRuleArg> ruleParams;

    @Schema(description = "需要发送的预警消息")
    private List<SjyAlertSmsRecord> records;

    @Schema(description = "预警数据 - 每个参数组对应一组数据")
    private Map<String, Map<String, Object>> groupDataMap = new HashMap<>();

    @Schema(description = "原始数据，用于通用处理")
    private Map<String, Object> data;

    @Schema(description = "生成的预警记录列表 - 每个参数组可以生成一个预警")
    private Map<String, SjyAlertSmsRecord> alertMap = new HashMap<>();

    @Schema(description = "合并后的预警记录")
    private SjyAlertSmsRecord mergedAlert;

    @Schema(description = "预警接收人列表 - 可能每个参数组有不同接收人")
    private Map<String, List<SjyAlertPerson>> receiversMap = new HashMap<>();

    @Schema(description = "合并后的接收人列表")
    private List<SjyAlertPerson> mergedReceivers = new ArrayList<>();

    @Schema(description = "处理过的参数组列表")
    private List<SjyAlertRuleArg> processedParams = new ArrayList<>();

    @Schema(description = "其他上下文数据")
    private Map<String, Object> contextData = new HashMap<>();

    @Schema(description = "判断是否需要合并消息")
    private boolean needMergeMessages = false;


}
