package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 村落基本情况对象 sjy_risk_village_info
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_risk_village_info")
public class SjyRiskVillageInfo implements Serializable {

private static final long serialVersionUID=1L;


    /** 主键ID (UUID) */
    @TableId(value = "id")
    private String id;

    /** 乡镇街道 */
    private String townName;

    /** 行政村代码 */
    private String adcd;

    /** 行政村名称 */
    private String villageAdmin;

    /** 自然村名称 */
    private String villageNatural;

    /** 自然村编码 */
    private String cadcd;

    /** 自然村辖区面积(km²) */
    private BigDecimal area;

    /** 自然村内总户数 */
    private Long houseNum;

    /** 自然村内总人口 */
    private Long popuNum;

    /** 自然村中心位置经度 */
    private BigDecimal lgtd;

    /** 自然村中心位置纬度 */
    private BigDecimal lttd;

    /** 危险区等级 (例如: 高, 危, 极高) (Excel注释为*危险区等级, 但示例有空值) */
    private Long riskLevel;

    /** 转移安置点 */
    private String transPlace;

    /** 应急物资 */
    private String urgentResource;

    /** 预警设备 */
    private String warnDevice;

    /** 人工监测设施 */
    private String monitorDevice;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 是否删除;是否删除:1未删除，2删除 */
    private Long izDel;

    /** 备注;冗余，可用于数据库层面临时操作时的标记 */
    private String remark;

}