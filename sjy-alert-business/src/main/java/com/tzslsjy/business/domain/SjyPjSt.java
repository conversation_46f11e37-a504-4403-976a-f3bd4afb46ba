package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;


/**
 * 工程测站关联对象 sjy_pj_st
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_pj_st")
public class SjyPjSt implements Serializable {

private static final long serialVersionUID=1L;


    /**  */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 测站编码 */
    private String stcd;

    /** 工程编码 */
    private String pjcd;

    /** 是否主站 */
    private String isMian;

    /** 是否雨量站 */
    private String isRain;

    /** 雨量权重 */
    private BigDecimal rainWeight;

    /** 工程类型 */
    private String projType;

    /** 排序 */
    private Long orderNum;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 是否删除;是否删除:1未删除，2删除 */
    private Long izDel;

    /** 备注;冗余，可用于数据库层面临时操作时的标记 */
    private String remark;

}