package com.tzslsjy.business.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Size;

/**
 * 雨量对象 st_pptn_r
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Schema(description = "雨量对象")
public class StPptnR extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @Schema(description = "测站编码")
    @Size(max = 8)
    private String STCD;

    /** 时间 */
    @Schema(description = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date TM;

    /** 时段降水量 */
    @Schema(description = "时段降水量")
    @Excel(name = "时段降水量")
    private BigDecimal DRP;

    /** 时段长 */
    @Schema(description = "时段长")
    @Excel(name = "时段长")
    private BigDecimal INTV;

    /** 降水历时 */
    @Schema(description = "降水历时")
    @Excel(name = "降水历时")
    private BigDecimal PDR;

    /** 日降水量 */
    @Schema(description = "日降水量")
    @Excel(name = "日降水量")
    private BigDecimal DYP;

    /** 天气状况 */
    @Schema(description = "天气状况")
    @Excel(name = "天气状况")
    @Size(max = 1)
    private String WTH;

    /** 修改时间 */
    @Schema(description = "修改时间")
    @Excel(name = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date MODITIME;

    /** 编辑标志 */
    @Schema(description = "编辑标志")
    @Excel(name = "编辑标志")
    @Size(max = 1)
    private String EDITED;

    /** 标志 */
    @Schema(description = "标志")
    @Excel(name = "标志")
    @Size(max = 2)
    private String bFlag;

    public void setSTCD(String STCD)
    {
        this.STCD = STCD;
    }

    public String getSTCD()
    {
        return STCD;
    }
    public void setTM(Date TM)
    {
        this.TM = TM;
    }

    public Date getTM()
    {
        return TM;
    }
    public void setDRP(BigDecimal DRP)
    {
        this.DRP = DRP;
    }

    public BigDecimal getDRP()
    {
        return DRP;
    }
    public void setINTV(BigDecimal INTV)
    {
        this.INTV = INTV;
    }

    public BigDecimal getINTV()
    {
        return INTV;
    }
    public void setPDR(BigDecimal PDR)
    {
        this.PDR = PDR;
    }

    public BigDecimal getPDR()
    {
        return PDR;
    }
    public void setDYP(BigDecimal DYP)
    {
        this.DYP = DYP;
    }

    public BigDecimal getDYP()
    {
        return DYP;
    }
    public void setWTH(String WTH)
    {
        this.WTH = WTH;
    }

    public String getWTH()
    {
        return WTH;
    }
    public void setMODITIME(Date MODITIME)
    {
        this.MODITIME = MODITIME;
    }

    public Date getMODITIME()
    {
        return MODITIME;
    }
    public void setEDITED(String EDITED)
    {
        this.EDITED = EDITED;
    }

    public String getEDITED()
    {
        return EDITED;
    }
    public void setbFlag(String bFlag)
    {
        this.bFlag = bFlag;
    }

    public String getbFlag()
    {
        return bFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("STCD", getSTCD())
            .append("TM", getTM())
            .append("DRP", getDRP())
            .append("INTV", getINTV())
            .append("PDR", getPDR())
            .append("DYP", getDYP())
            .append("WTH", getWTH())
            .append("MODITIME", getMODITIME())
            .append("EDITED", getEDITED())
            .append("bFlag", getbFlag())
            .toString();
    }
}
