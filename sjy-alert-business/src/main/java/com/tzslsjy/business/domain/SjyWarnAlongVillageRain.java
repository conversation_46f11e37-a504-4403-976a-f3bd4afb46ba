package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 预警危险村雨量阈值对象 sjy_warn_along_village_rain
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_warn_along_village_rain")
public class SjyWarnAlongVillageRain implements Serializable {

private static final long serialVersionUID=1L;


    /** 行政区划代码 */
    @TableId(value = "adcd",type = IdType.ASSIGN_UUID)
    private String adcd;

    /** 土壤含水量 */
    private BigDecimal lwater;

    /** 时段 */
    private BigDecimal ldarea;

    /** 临界雨量准备转移指标（mm） */
    private BigDecimal zbpv;

    /** 临界雨量立即转移指标（mm） */
    private BigDecimal ljpv;

    /** 临界雨量 */
    private BigDecimal p;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 是否删除:1未删除，2删除 */
    private Long izDel;

    /** 备注 */
    private String remark;

}