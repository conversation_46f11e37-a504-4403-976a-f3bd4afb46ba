package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.util.Date;
import java.util.List;

/**
 * 预警规则对象 sjy_alert_rule
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Schema(description = "预警规则对象")
public class SjyAlertRule extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long ruleId;
    @TableField(exist = false)
    private Integer pageSize;
    @TableField(exist = false)
    private Integer pageNum;
    /** 规则名称 */
    @Schema(description = "规则名称")
    @Excel(name = "规则名称")
    @Size(max = 100)
    private String ruleName;

    /** 规则类型 */
    @Schema(description = "规则类型")
    @Excel(name = "规则类型")
    @Size(max = 50)
    private String ruleTypeId;

    /** 规则模式：1-通用，2或缺省-特定 */
    @Schema(description = "规则模式：1-通用，2或缺省-特定")
    @Excel(name = "规则模式：1-通用，2或缺省-特定")
    @Size(max = 255)
    private String ruleModel;

    /** 模板ID */
    @Schema(description = "模板ID")
    @Excel(name = "模板ID")
    @Max(value = 2147483647)
    private Long templateId;

    /** 发送方式（直接发送，审核发送） */
    @Schema(description = "发送方式（1-直接发送，2-审核发送）")
    @Excel(name = "发送方式", readConverterExp = "直接发送，审核发送")
    @Size(max = 100)
    private String sendWay;

    public Integer getReviewMinute() {
        return reviewMinute;
    }

    public void setReviewMinute(Integer reviewMinute) {
        this.reviewMinute = reviewMinute;
    }

    @Schema(description = "审核时间")
    private Integer reviewMinute;
    /** 预警测站 */
    @Schema(description = "预警测站")
    @Excel(name = "预警测站")
    @Size(max = 90)
    private String alertStcd;

    /** 预警自然村 */
    @Schema(description = "预警自然村")
    @Excel(name = "预警自然村")
    @Size(max = 90)
    private String alertAdnm;

    /** 预警级别：1-低，2-中，3-高 */
    @Schema(description = "预警级别：1-低，2-中，3-高")
    @Excel(name = "预警级别：1-低，2-中，3-高")
    private Integer alertLevel;

    /** 预警参数json */
    @Schema(description = "预警参数json")
    @Excel(name = "预警参数json")
    private String argJson;

    /** 状态：0-禁用，1-启用 */
    @Schema(description = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    private Integer status;



    /** 执行表达式（多个逗号隔开） */
    @Schema(description = "执行表达式（多个逗号隔开）")
    @Excel(name = "执行表达式", readConverterExp = "多个逗号隔开")
    @Size(max = 255)
    private String cronExpressions;

    /** 父规则ID */
    @Schema(description = "父规则ID")
    @Excel(name = "父规则ID")
    private Integer parentId;

    @Schema(description = "规则绑定的接收人列表")
    List<SjyAlertRuleNodeRelation> ruleNodeRelations;

    List<SjyAlertTemplateRelation> ruleTemplateRelations;

    List<SjyAlertRuleArg> sjyAlertRuleArgs;
    public void setRuleId(Long ruleId)
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId()
    {
        return ruleId;
    }
    public void setRuleName(String ruleName)
    {
        this.ruleName = ruleName;
    }

    public String getRuleName()
    {
        return ruleName;
    }
    public void setRuleTypeId(String ruleTypeId)
    {
        this.ruleTypeId = ruleTypeId;
    }

    public String getRuleTypeId()
    {
        return ruleTypeId;
    }
    public void setRuleModel(String ruleModel)
    {
        this.ruleModel = ruleModel;
    }

    public String getRuleModel()
    {
        return ruleModel;
    }
    public void setTemplateId(Long templateId)
    {
        this.templateId = templateId;
    }

    public Long getTemplateId()
    {
        return templateId;
    }
    public void setSendWay(String sendWay)
    {
        this.sendWay = sendWay;
    }

    public String getSendWay()
    {
        return sendWay;
    }
    public void setAlertStcd(String alertStcd)
    {
        this.alertStcd = alertStcd;
    }

    public String getAlertStcd()
    {
        return alertStcd;
    }
    public void setAlertAdnm(String alertAdnm)
    {
        this.alertAdnm = alertAdnm;
    }

    public String getAlertAdnm()
    {
        return alertAdnm;
    }
    public void setAlertLevel(Integer alertLevel)
    {
        this.alertLevel = alertLevel;
    }

    public Integer getAlertLevel()
    {
        return alertLevel;
    }
    public void setArgJson(String argJson)
    {
        this.argJson = argJson;
    }

    public String getArgJson()
    {
        return argJson;
    }
    public void setStatus(Integer status)
    {
        this.status = status;
    }

    public Integer getStatus()
    {
        return status;
    }



    public String getCronExpressions() {
        return cronExpressions;
    }

    public void setCronExpressions(String cronExpressions) {
        this.cronExpressions = cronExpressions;
    }

    public Integer getParentId() {
        return parentId;
    }

    public void setParentId(Integer parentId) {
        this.parentId = parentId;
    }

    public List<SjyAlertRuleNodeRelation> getRuleNodeRelations() {
        return ruleNodeRelations;
    }

    public void setRuleNodeRelations(List<SjyAlertRuleNodeRelation> ruleNodeRelations) {
        this.ruleNodeRelations = ruleNodeRelations;
    }

    public List<SjyAlertTemplateRelation> getRuleTemplateRelations() {
        return ruleTemplateRelations;
    }

    public void setRuleTemplateRelations(List<SjyAlertTemplateRelation> ruleTemplateRelations) {
        this.ruleTemplateRelations = ruleTemplateRelations;
    }

    public List<SjyAlertRuleArg> getSjyAlertRuleArgs() {
        return sjyAlertRuleArgs;
    }

    public void setSjyAlertRuleArgs(List<SjyAlertRuleArg> sjyAlertRuleArgs) {
        this.sjyAlertRuleArgs = sjyAlertRuleArgs;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ruleId", getRuleId())
            .append("ruleName", getRuleName())
            .append("ruleTypeId", getRuleTypeId())
            .append("ruleModel", getRuleModel())
            .append("templateId", getTemplateId())
            .append("sendWay", getSendWay())
            .append("alertStcd", getAlertStcd())
            .append("alertAdnm", getAlertAdnm())
            .append("alertLevel", getAlertLevel())
            .append("argJson", getArgJson())
            .append("status", getStatus())
            .append("cronExpressions", getCronExpressions())
            .append("parentId", getParentId())
            .append("reviewMinute", getReviewMinute())
            .append("ruleNodeRelations", getRuleNodeRelations())
            .append("ruleTemplateRelations", getRuleTemplateRelations())
            .append("sjyAlertRuleArgs", getSjyAlertRuleArgs())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }
}
