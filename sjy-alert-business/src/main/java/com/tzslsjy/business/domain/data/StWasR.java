package com.tzslsjy.business.domain.data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema; // Updated import

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @auther seven
 * @create 2021-04-02 11:25:57
 * @describe 堰闸水情表实体类
 */
@TableName("st_was_r")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description="堰闸水情表") // Updated annotation
public class StWasR implements Serializable {

private static final long serialVersionUID = 1L;

        @Schema(description = "测站编码") // Updated annotation
    @TableField("STCD")
        private String stcd;

        @Schema(description = "时间") // Updated annotation
    @TableField("TM")
        private Date tm;

        @Schema(description = "闸上水位（m）") // Updated annotation
    @TableField("UPZ")
        private BigDecimal upz;

        @Schema(description = "闸下水位（m）") // Updated annotation
    @TableField("DWZ")
        private BigDecimal dwz;

        @Schema(description = "总过闸流量（m^3/s）") // Updated annotation
    @TableField("TGTQ")
        private BigDecimal tgtq;

        @Schema(description = "闸水特征码") // Updated annotation
    @TableField("SWCHRCD")
        private String swchrcd;

        @Schema(description = "闸上水势") // Updated annotation
    @TableField("SUPWPTN")
        private String supwptn;

        @Schema(description = "闸下水势") // Updated annotation
    @TableField("SDWWPTN")
        private String sdwwptn;

        @Schema(description = "测流方法") // Updated annotation
    @TableField("MSQMT")
        private String msqmt;

    @TableField("MODITIME")
        private Date moditime;

    @TableField("EDITED")
        private String edited;

    @TableField("B_Flag")
        private String bFlag;


    public String getStcd() {
            return stcd;
            }

        public StWasR setStcd(String stcd) {
            this.stcd = stcd;
                return this;
            }

    public Date getTm() {
            return tm;
            }

        public StWasR setTm(Date tm) {
            this.tm = tm;
                return this;
            }

    public BigDecimal getUpz() {
            return upz;
            }

        public StWasR setUpz(BigDecimal upz) {
            this.upz = upz;
                return this;
            }

    public BigDecimal getDwz() {
            return dwz;
            }

        public StWasR setDwz(BigDecimal dwz) {
            this.dwz = dwz;
                return this;
            }

    public BigDecimal getTgtq() {
            return tgtq;
            }

        public StWasR setTgtq(BigDecimal tgtq) {
            this.tgtq = tgtq;
                return this;
            }

    public String getSwchrcd() {
            return swchrcd;
            }

        public StWasR setSwchrcd(String swchrcd) {
            this.swchrcd = swchrcd;
                return this;
            }

    public String getSupwptn() {
            return supwptn;
            }

        public StWasR setSupwptn(String supwptn) {
            this.supwptn = supwptn;
                return this;
            }

    public String getSdwwptn() {
            return sdwwptn;
            }

        public StWasR setSdwwptn(String sdwwptn) {
            this.sdwwptn = sdwwptn;
                return this;
            }

    public String getMsqmt() {
            return msqmt;
            }

        public StWasR setMsqmt(String msqmt) {
            this.msqmt = msqmt;
                return this;
            }

    public Date getModitime() {
            return moditime;
            }

        public StWasR setModitime(Date moditime) {
            this.moditime = moditime;
                return this;
            }

    public String getEdited() {
            return edited;
            }

        public StWasR setEdited(String edited) {
            this.edited = edited;
                return this;
            }

    public String getbFlag() {
            return bFlag;
            }

        public StWasR setbFlag(String bFlag) {
            this.bFlag = bFlag;
                return this;
            }

@Override
public String toString() {
        return "StWasR{" +
                "stcd=" + stcd +
                ", tm=" + tm +
                ", upz=" + upz +
                ", dwz=" + dwz +
                ", tgtq=" + tgtq +
                ", swchrcd=" + swchrcd +
                ", supwptn=" + supwptn +
                ", sdwwptn=" + sdwwptn +
                ", msqmt=" + msqmt +
                ", moditime=" + moditime +
                ", edited=" + edited +
                ", bFlag=" + bFlag +
        "}";
        }
        }