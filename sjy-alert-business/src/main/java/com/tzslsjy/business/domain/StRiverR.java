package com.tzslsjy.business.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Size;

/**
 * 河道水情对象 st_river_r
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public class StRiverR extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    @Size(max = 8)
    private String STCD;

    /** 时间 */

    private Date TM;

    /** 水位 */
    @Excel(name = "水位")

    private BigDecimal Z;

    /** 流量 */
    @Excel(name = "流量")

    private BigDecimal Q;

    /** 断面过水面积 */
    @Excel(name = "断面过水面积")

    private BigDecimal XSA;

    /** 断面平均流速 */
    @Excel(name = "断面平均流速")

    private BigDecimal XSAVV;

    /** 断面最大流速 */
    @Excel(name = "断面最大流速")

    private BigDecimal XSMXV;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String FLWCHRCD;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String WPTN;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String MSQMT;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String MSAMT;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String MSVMT;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")

    private Date MODITIME;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String EDITED;

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 2)
    private String bFlag;

    public void setSTCD(String STCD)
    {
        this.STCD = STCD;
    }

    public String getSTCD()
    {
        return STCD;
    }
    public void setTM(Date TM)
    {
        this.TM = TM;
    }

    public Date getTM()
    {
        return TM;
    }
    public void setZ(BigDecimal Z)
    {
        this.Z = Z;
    }

    public BigDecimal getZ()
    {
        return Z;
    }
    public void setQ(BigDecimal Q)
    {
        this.Q = Q;
    }

    public BigDecimal getQ()
    {
        return Q;
    }
    public void setXSA(BigDecimal XSA)
    {
        this.XSA = XSA;
    }

    public BigDecimal getXSA()
    {
        return XSA;
    }
    public void setXSAVV(BigDecimal XSAVV)
    {
        this.XSAVV = XSAVV;
    }

    public BigDecimal getXSAVV()
    {
        return XSAVV;
    }
    public void setXSMXV(BigDecimal XSMXV)
    {
        this.XSMXV = XSMXV;
    }

    public BigDecimal getXSMXV()
    {
        return XSMXV;
    }
    public void setFLWCHRCD(String FLWCHRCD)
    {
        this.FLWCHRCD = FLWCHRCD;
    }

    public String getFLWCHRCD()
    {
        return FLWCHRCD;
    }
    public void setWPTN(String WPTN)
    {
        this.WPTN = WPTN;
    }

    public String getWPTN()
    {
        return WPTN;
    }
    public void setMSQMT(String MSQMT)
    {
        this.MSQMT = MSQMT;
    }

    public String getMSQMT()
    {
        return MSQMT;
    }
    public void setMSAMT(String MSAMT)
    {
        this.MSAMT = MSAMT;
    }

    public String getMSAMT()
    {
        return MSAMT;
    }
    public void setMSVMT(String MSVMT)
    {
        this.MSVMT = MSVMT;
    }

    public String getMSVMT()
    {
        return MSVMT;
    }
    public void setMODITIME(Date MODITIME)
    {
        this.MODITIME = MODITIME;
    }

    public Date getMODITIME()
    {
        return MODITIME;
    }
    public void setEDITED(String EDITED)
    {
        this.EDITED = EDITED;
    }

    public String getEDITED()
    {
        return EDITED;
    }
    public void setbFlag(String bFlag)
    {
        this.bFlag = bFlag;
    }

    public String getbFlag()
    {
        return bFlag;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("STCD", getSTCD())
            .append("TM", getTM())
            .append("Z", getZ())
            .append("Q", getQ())
            .append("XSA", getXSA())
            .append("XSAVV", getXSAVV())
            .append("XSMXV", getXSMXV())
            .append("FLWCHRCD", getFLWCHRCD())
            .append("WPTN", getWPTN())
            .append("MSQMT", getMSQMT())
            .append("MSAMT", getMSAMT())
            .append("MSVMT", getMSVMT())
            .append("MODITIME", getMODITIME())
            .append("EDITED", getEDITED())
            .append("bFlag", getbFlag())
            .toString();
    }
}
