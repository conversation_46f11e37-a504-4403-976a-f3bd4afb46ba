package com.tzslsjy.business.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 雨量预警项
 * 用于存储不同时段的雨量预警数据
 */
@Data
public class SjyFloodRainWarningItem {

    /**
     * 索引序号
     */
    @JsonProperty("index")
    private Integer index;

    /**
     * 历时(分钟)
     */
    @JsonProperty("tm")
    private Integer duration;

    /**
     * 雨量阈值
     */
    @JsonProperty("val")
    private String threshold;

    /**
     * 与上次雨量差
     */
    @JsonProperty("diff")
    private String difference;
}
