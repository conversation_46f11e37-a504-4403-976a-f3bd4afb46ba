package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * 预警标签库对象 sjy_alert_variable
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@Schema(description = "预警标签库对象")
public class SjyAlertVariable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 变量ID */
    @Schema(description = "变量ID")
    @Max(value = 2147483647)
    private Long variableId;

    /** 变量代码 */
    @Schema(description = "变量代码")
    @Excel(name = "变量代码")
    @Size(max = 50)
    private String variableCode;

    /** 变量名称 */
    @Schema(description = "变量名称")
    @Excel(name = "变量名称")
    @Size(max = 100)
    private String variableName;

    /** 变量类型：string-字符串,number-数字,date-日期等 */
    @Schema(description = "变量类型：string-字符串,number-数字,date-日期等")
    @Excel(name = "变量类型：string-字符串,number-数字,date-日期等")
    @Size(max = 20)
    private String variableType;

    /** 变量描述 */
    @Schema(description = "变量描述")
    @Excel(name = "变量描述")
    @Size(max = 255)
    private String description;

    /** 是否可自定义之值：0-否，1-是 */
    @Schema(description = "是否可自定义之值：0-否，1-是")
    @Excel(name = "是否可自定义之值：0-否，1-是")
    @Size(max = 255)
    private String izCustom;

    /** 是否列表类型变量：0-否，1-是 */
    @Schema(description = "是否列表类型变量：0-否，1-是")
    @Excel(name = "是否列表类型变量：0-否，1-是")
    @Max(value = 2147483647)
    private Long izList;

    /** 列表格式化模板，如：{stcd}:{water_level}m */
    @Schema(description = "列表格式化模板，如：{stcd}:{water_level}m")
    @Excel(name = "列表格式化模板，如：{stcd}:{water_level}m")
    @Size(max = 500)
    private String listFormat;

    /** 列表项分隔符 */
    @Schema(description = "列表项分隔符")
    @Excel(name = "列表项分隔符")
    @Size(max = 10)
    private String listSeparator;

    /** 展示条数 */
    @Schema(description = "展示条数")
    @Excel(name = "展示条数")
    @Max(value = 2147483647)
    private Long displayNum;

    /** 默认值 */
    @Schema(description = "默认值")
    @Excel(name = "默认值")
    @Size(max = 255)
    private String defaultValue;

    /** 省略替换 */
    @Schema(description = "省略替换")
    @Excel(name = "省略替换")
    @Size(max = 255)
    private String omitSymbol;

    /** 状态：0-禁用，1-启用 */
    @Schema(description = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    @Max(value = 2147483647)
    private Long status;

    @Schema(description = "规则类型")
    @TableField(exist = false)
    private String ruleTypeId;

    /** 分页大小 */
    @Schema(description = "分页大小")
    @TableField(exist = false)
    private Integer pageSize;
    /** 当前页数 */
    @Schema(description = "当前页数")
    @TableField(exist = false)
    private Integer pageNum;
}
