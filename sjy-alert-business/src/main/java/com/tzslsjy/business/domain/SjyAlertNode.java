package com.tzslsjy.business.domain;


import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 节点对象 sjy_alert_node
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Schema(description = "节点对象")
public class SjyAlertNode extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    // @Max(value = 2147483647) // No longer applicable for String
    @Size(max = 64) // Assuming a max length for String ID
    private String nodeId;

    /** 节点名称 */
    @Schema(description = "节点名称")
    @Excel(name = "节点名称")
    @Size(max = 100)
    private String nodeName;

    private Integer  orderNum;
    /** 父节点ID */
    @Schema(description = "父节点ID")
    @Excel(name = "父节点ID")
    // @Max(value = Long.MAX_VALUE) // No longer applicable for String
    @Size(max = 64) // Assuming a max length for String ID
    private String parentId;

    /** 节点类型：1-组织节点，2-人员节点 */
    @Schema(description = "节点类型：1-部门节点，2-职能节点")
    @Excel(name = "节点类型：1-部门节点，2-职能节点")
    @Max(value = 2147483647)
    private Long nodeType;

    /** 状态：0-禁用，1-启用 */
    @Schema(description = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    @Max(value = 2147483647)
    private Long status;

    /** 行政区划 */
    @Schema(description = "行政区划")
    @Excel(name = "行政区划")
    @Size(max = 255)
    private String adcd;

    /** 关联的节点岗位信息 */
    @Schema(description = "关联的节点岗位信息")
    private List<SjyAlertNodePost> nodePosts;
    @Schema(description = "子节点列表")
    private List<SjyAlertNode> children;
    /** 祖级列表 */
    @Schema(description = "祖级列表")
    @Excel(name = "祖级列表")
    @Size(max = 255)
    private String ancestors;

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    /**     人员关联的节点岗位信息 */
    @Schema(description = "人员关联的节点岗位信息")
    private  String postName;
    public void setNodeId(String nodeId) // Changed Long to String
    {
        this.nodeId = nodeId;
    }

    public String getNodeId() // Changed Long to String
    {
        return nodeId;
    }
    public void setNodeName(String nodeName)
    {
        this.nodeName = nodeName;
    }

    public String getNodeName()
    {
        return nodeName;
    }
    public void setParentId(String parentId) // Changed Long to String
    {
        this.parentId = parentId;
    }

    public String getParentId() // Changed Long to String
    {
        return parentId;
    }
    public void setNodeType(Long nodeType)
    {
        this.nodeType = nodeType;
    }

    public Long getNodeType()
    {
        return nodeType;
    }
    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }

    public String getAdcd() {
        return adcd;
    }

    public void setAdcd(String adcd) {
        this.adcd = adcd;
    }

    public List<SjyAlertNodePost> getNodePosts() {
        return nodePosts;
    }

    public void setNodePosts(List<SjyAlertNodePost> nodePosts) {
        this.nodePosts = nodePosts;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("nodeId", getNodeId())
            .append("nodeName", getNodeName())
            .append("parentId", getParentId())
            .append("ancestors", getAncestors())
            .append("nodeType", getNodeType())
            .append("status", getStatus())
            .append("adcd", getAdcd())
            .append("nodePosts", getNodePosts())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public List<SjyAlertNode> getChildren() {
        return children;
    }

    public void setChildren(List<SjyAlertNode> children) {
        this.children = children;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }
}
