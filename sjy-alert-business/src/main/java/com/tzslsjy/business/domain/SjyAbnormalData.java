package com.tzslsjy.business.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 测站异常数据对象 sjy_abnormal_data
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class SjyAbnormalData implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 测站编号 */
    private String stcd;

    /** 值 */
    private BigDecimal val;

    /** 与上个时间点差值 */
    private BigDecimal diff;

    /** 类型 */
    private String type;

    /** 时间 */
    private Date tm;

    /** 创建时间 */
    private Date createTime;
}
