package com.tzslsjy.business.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Data
@Schema(description = "城市内涝水位预警参数数据")
public class SjyUrbanFloodParamData {

    @Schema(description = "规则参数ID")
    private Long argId;

    @Schema(description = "预警级别")
    private Long alertLevel;

    @Schema(description = "测站编码")
    private String stcd;

    @Schema(description = "汛限水位(WRZ)")
    private BigDecimal wrz;

    @Schema(description = "超汛限值(米)")
    private BigDecimal overWrz;

    @Schema(description = "行政区划名称")
    private String addvcdNm;

    @Schema(description = "测站名称")
    private String stnm;

    @Schema(description = "当前水位")
    private BigDecimal currentWaterLevel;

    @Schema(description = "预警时间")
    private String tm;
}