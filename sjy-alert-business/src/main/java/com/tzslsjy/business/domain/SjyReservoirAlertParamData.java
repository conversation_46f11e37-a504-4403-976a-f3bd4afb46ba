package com.tzslsjy.business.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Data
@Schema(description = "水库水位预警参数数据")
public class SjyReservoirAlertParamData {

    @Schema(description = "规则参数ID")
    private Long argId;

    @Schema(description = "预警级别")
    private Long alertLevel;

    @Schema(description = "测站编码")
    private String stcd; // 参数中可能直接指定测站

    @Schema(description = "汛限水位(WRZ)")
    private BigDecimal wrz;

    @Schema(description = "阈值")
    private Integer val;
} 