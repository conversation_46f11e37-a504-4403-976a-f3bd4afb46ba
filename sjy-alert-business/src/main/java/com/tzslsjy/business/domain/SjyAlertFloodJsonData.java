package com.tzslsjy.business.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 山洪预警数据实体类
 * 对应JSON格式的预警数据结构
 */
@Data
@Schema(description = "山洪预警数据实体类")
public class SjyAlertFloodJsonData {

    /**
     * 超警戒/汛限值
     * 负值表示临近，正值表示超过
     */
    @JsonProperty("overWaterVal")
    @Schema(description = "超警戒/汛限值, 负值表示临近，正值表示超过")
    private String overWaterValue;

    /**
     * 预警级别
     * 1: 准备转移
     * 2: 立即转移
     */
    @JsonProperty("alert_level")
    @Schema(description = "预警级别, 1: 准备转移, 2: 立即转移")
    private String alertLevel;

    /**
     * 雨量预警列表（含水量0.75Wm）
     */
    @JsonProperty("overRain75List")
    @Schema(description = "雨量预警列表（含水量0.75Wm）")
    private List<SjyFloodRainWarningItem> overRain75List;

    /**
     * 雨量预警列表（含水量0.90Wm）
     */
    @JsonProperty("overRain90List")
    @Schema(description = "雨量预警列表（含水量0.90Wm）")
    private List<SjyFloodRainWarningItem> overRain90List;
}
