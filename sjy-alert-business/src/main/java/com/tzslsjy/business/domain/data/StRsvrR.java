package com.tzslsjy.business.domain.data;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema; // Updated import

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @auther seven
 * @create 2021-04-02 11:25:57
 * @describe 水库实时水位流量表实体类
 */
@TableName("st_rsvr_r")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description="水库实时水位流量表") // Updated annotation
public class StRsvrR implements Serializable {

private static final long serialVersionUID = 1L;

        @Schema(description = "测站编码") // Updated annotation
    @TableField("STCD")
        private String stcd;

        @Schema(description = "时间") // Updated annotation
    @TableField("TM")
        private Date tm;

        @Schema(description = "库上水位") // Updated annotation
    @TableField("RZ")
        private BigDecimal rz;

        @Schema(description = "入库流量") // Updated annotation
    @TableField("INQ")
        private BigDecimal inq;

        @Schema(description = "蓄水量") // Updated annotation
    @TableField("W")
        private BigDecimal w;

        @Schema(description = "库下水位") // Updated annotation
    @TableField("BLRZ")
        private BigDecimal blrz;

        @Schema(description = "出库流量") // Updated annotation
    @TableField("OTQ")
        private BigDecimal otq;

        @Schema(description = "库水特征码") // Updated annotation
    @TableField("RWCHRCD")
        private String rwchrcd;

        @Schema(description = "库水水势") // Updated annotation
    @TableField("RWPTN")
        private String rwptn;

        @Schema(description = "入流时段长") // Updated annotation
    @TableField("INQDR")
        private BigDecimal inqdr;

        @Schema(description = "测流方法") // Updated annotation
    @TableField("MSQMT")
        private String msqmt;

    @TableField("MODITIME")
        private Date moditime;

    @TableField("EDITED")
        private String edited;

    @TableField("B_Flag")
        private String bFlag;



    public String getStcd() {
            return stcd;
            }

        public StRsvrR setStcd(String stcd) {
            this.stcd = stcd;
                return this;
            }

    public Date getTm() {
            return tm;
            }

        public StRsvrR setTm(Date tm) {
            this.tm = tm;
                return this;
            }

    public BigDecimal getRz() {
            return rz;
            }

        public StRsvrR setRz(BigDecimal rz) {
            this.rz = rz;
                return this;
            }

    public BigDecimal getInq() {
            return inq;
            }

        public StRsvrR setInq(BigDecimal inq) {
            this.inq = inq;
                return this;
            }

    public BigDecimal getW() {
            return w;
            }

        public StRsvrR setW(BigDecimal w) {
            this.w = w;
                return this;
            }

    public BigDecimal getBlrz() {
            return blrz;
            }

        public StRsvrR setBlrz(BigDecimal blrz) {
            this.blrz = blrz;
                return this;
            }

    public BigDecimal getOtq() {
            return otq;
            }

        public StRsvrR setOtq(BigDecimal otq) {
            this.otq = otq;
                return this;
            }

    public String getRwchrcd() {
            return rwchrcd;
            }

        public StRsvrR setRwchrcd(String rwchrcd) {
            this.rwchrcd = rwchrcd;
                return this;
            }

    public String getRwptn() {
            return rwptn;
            }

        public StRsvrR setRwptn(String rwptn) {
            this.rwptn = rwptn;
                return this;
            }

    public BigDecimal getInqdr() {
            return inqdr;
            }

        public StRsvrR setInqdr(BigDecimal inqdr) {
            this.inqdr = inqdr;
                return this;
            }

    public String getMsqmt() {
            return msqmt;
            }

        public StRsvrR setMsqmt(String msqmt) {
            this.msqmt = msqmt;
                return this;
            }

    public Date getModitime() {
            return moditime;
            }

        public StRsvrR setModitime(Date moditime) {
            this.moditime = moditime;
                return this;
            }

    public String getEdited() {
            return edited;
            }

        public StRsvrR setEdited(String edited) {
            this.edited = edited;
                return this;
            }

    public String getbFlag() {
            return bFlag;
            }

        public StRsvrR setbFlag(String bFlag) {
            this.bFlag = bFlag;
                return this;
            }

@Override
public String toString() {
        return "StRsvrR{" +
                "stcd=" + stcd +
                ", tm=" + tm +
                ", rz=" + rz +
                ", inq=" + inq +
                ", w=" + w +
                ", blrz=" + blrz +
                ", otq=" + otq +
                ", rwchrcd=" + rwchrcd +
                ", rwptn=" + rwptn +
                ", inqdr=" + inqdr +
                ", msqmt=" + msqmt +
                ", moditime=" + moditime +
                ", edited=" + edited +
                ", bFlag=" + bFlag +
        "}";
        }
        }