package com.tzslsjy.business.domain.comm;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema; // 更改导入

import java.io.Serializable;

/**
 * @auther seven
 * @create 2021-05-11 10:55:10
 * @describe 城乡分类代码实体类
 */
@TableName("comm_city_code")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description="城乡分类代码") // 更改注解
public class BusinessCommCityCode implements Serializable {

private static final long serialVersionUID = 1L;

        @Schema(description = "代码") // 更改注解
    @TableField("code")
        private String code;

        @Schema(description = "分类，名称") // 更改注解
    @TableField("category")
        private String category;


    public String getCode() {
            return code;
            }

        public BusinessCommCityCode setCode(String code) {
            this.code = code;
                return this;
            }

    public String getCategory() {
            return category;
            }

        public BusinessCommCityCode setCategory(String category) {
            this.category = category;
                return this;
            }

@Override
public String toString() {
        return "CommCityCode{" +
                "code=" + code +
                ", category=" + category +
        "}";
        }
        }