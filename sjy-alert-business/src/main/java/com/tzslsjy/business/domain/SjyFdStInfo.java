package com.tzslsjy.business.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Size;

/**
 * 自动监测站基本信息对象 sjy_fd_st_info
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Schema(description = "自动监测站基本信息对象")
public class SjyFdStInfo extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @Schema(description = "测站编码")
    @Size(max = 36)
    private String stCode;

    /** 设备码 */
    @Schema(description = "设备码")
    @Excel(name = "设备码")
    @Size(max = 255)
    private String equipmentCode;

    /** 监测要素(水位、雨量、水位和雨量) */
    @Schema(description = "监测要素(水位、雨量、水位和雨量)")
    @Excel(name = "监测要素(水位、雨量、水位和雨量)")
    @Size(max = 50)
    private String monitorTp;

    /** 经度 */
    @Schema(description = "经度")
    @Excel(name = "经度")
    private BigDecimal lgtd;

    /** 纬度 */
    @Schema(description = "纬度")
    @Excel(name = "纬度")
    private BigDecimal lttd;

    /** 数据更新时间 */
    @Schema(description = "数据更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    /** 行政区划编码 */
    @Schema(description = "行政区划编码")
    @Excel(name = "行政区划编码")
    @Size(max = 15)
    private String adcd;

    /** 数据入库时间 */
    @Schema(description = "数据入库时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据入库时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date tongTime;

    public void setStCode(String stCode)
    {
        this.stCode = stCode;
    }

    public String getStCode()
    {
        return stCode;
    }
    public void setEquipmentCode(String equipmentCode)
    {
        this.equipmentCode = equipmentCode;
    }

    public String getEquipmentCode()
    {
        return equipmentCode;
    }
    public void setMonitorTp(String monitorTp)
    {
        this.monitorTp = monitorTp;
    }

    public String getMonitorTp()
    {
        return monitorTp;
    }
    public void setLgtd(BigDecimal lgtd)
    {
        this.lgtd = lgtd;
    }

    public BigDecimal getLgtd()
    {
        return lgtd;
    }
    public void setLttd(BigDecimal lttd)
    {
        this.lttd = lttd;
    }

    public BigDecimal getLttd()
    {
        return lttd;
    }
    public void setModifyTime(Date modifyTime)
    {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime()
    {
        return modifyTime;
    }
    public void setAdcd(String adcd)
    {
        this.adcd = adcd;
    }

    public String getAdcd()
    {
        return adcd;
    }
    public void setTongTime(Date tongTime)
    {
        this.tongTime = tongTime;
    }

    public Date getTongTime()
    {
        return tongTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("stCode", getStCode())
            .append("equipmentCode", getEquipmentCode())
            .append("monitorTp", getMonitorTp())
            .append("lgtd", getLgtd())
            .append("lttd", getLttd())
            .append("modifyTime", getModifyTime())
            .append("adcd", getAdcd())
            .append("tongTime", getTongTime())
            .toString();
    }
}
