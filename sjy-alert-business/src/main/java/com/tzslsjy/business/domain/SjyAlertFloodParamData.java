package com.tzslsjy.business.domain;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 山洪预警参数数据实体类
 * 对应JSON格式的预警数据结构中的参数部分
 */
@Data
@Schema(description = "山洪预警参数数据实体类")
public class SjyAlertFloodParamData {

    /**
     * 超警戒/汛限值
     * 负值表示临近，正值表示超过
     */
    @Schema(description = "超警戒/汛限值, 负值表示临近，正值表示超过")
    private String overWaterValue;

    /**
     * 预警级别
     * 1: 准备转移
     * 2: 立即转移
     */
    @Schema(description = "规则参数ID")
    private Long argId;

    @Schema(description = "预警级别, 1: 准备转移, 2: 立即转移")
    private Long alertLevel;
    /**
     * 雨量预警列表
     */
    @Schema(description = "雨量预警列表")
    private List<SjyFloodRainWarningItem> overRainList;
}
