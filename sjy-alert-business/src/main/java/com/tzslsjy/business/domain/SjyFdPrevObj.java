package com.tzslsjy.business.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Size;

/**
 * 防治对象名录对象 sjy_fd_prev_obj
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Schema(description = "防治对象名录对象")
public class SjyFdPrevObj extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 防治对象编码 */
    @Schema(description = "防治对象编码")
    @Size(max = 36)
    private String prevCode;

    /** 行政区划编码 */
    @Schema(description = "行政区划编码")
    @Excel(name = "行政区划编码")
    @Size(max = 36)
    private String adCode;

    /** 行政村 */
    @Schema(description = "行政村")
    @Excel(name = "行政村")
    @Size(max = 255)
    private String adminAdnm;

    /** 自然村 */
    @Schema(description = "自然村")
    @Excel(name = "自然村")
    @Size(max = 255)
    private String naturalAdnm;

    /** 已有或新增 */
    @Schema(description = "已有或新增")
    @Excel(name = "已有或新增")
    @Size(max = 1)
    private String isAdded;

    /** 对象建立时间 */
    @Schema(description = "对象建立时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "对象建立时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date fromDate;

    /** 对象终止时间 */
    @Schema(description = "对象终止时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "对象终止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date toDate;

    /** 原始编码 */
    @Schema(description = "原始编码")
    @Excel(name = "原始编码")
    @Size(max = 255)
    private String origCode;

    /** 地理信息编码 */
    @Schema(description = "地理信息编码")
    @Excel(name = "地理信息编码")
    @Size(max = 255)
    private String geoCode;

    /** 同步时间 */
    @Schema(description = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "同步时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date tongTime;

    /** 操作标记 */
    @Schema(description = "操作标记")
    @Excel(name = "操作标记")
    @Size(max = 50)
    private String op;

    /** 数据更新时间 */
    @Schema(description = "数据更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "数据更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date modifyTime;

    /** 是否锁定 */
    @Schema(description = "是否锁定")
    @Excel(name = "是否锁定")
    @Size(max = 1)
    private String isLock;

    /** 审核记录编号 */
    @Schema(description = "审核记录编号")
    @Excel(name = "审核记录编号")
    @Size(max = 255)
    private String checkId;

    /** 是否通过审核 */
    @Schema(description = "是否通过审核")
    @Excel(name = "是否通过审核")
    @Size(max = 1)
    private String isDataOfficial;

    /** 审核通过时间 */
    @Schema(description = "审核通过时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "审核通过时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date dataOfficialTime;

    /** 审核状态 */
    @Schema(description = "审核状态")
    @Excel(name = "审核状态")
    @Size(max = 255)
    private String checkStatus;

    /** 审核原因 */
    @Schema(description = "审核原因")
    @Excel(name = "审核原因")
    @Size(max = 255)
    private String checkReason;

    /** 行政区划代码 */
    @Schema(description = "行政区划代码")
    @Excel(name = "行政区划代码")
    @Size(max = 15)
    private String adcd;

    public void setPrevCode(String prevCode)
    {
        this.prevCode = prevCode;
    }

    public String getPrevCode()
    {
        return prevCode;
    }
    public void setAdCode(String adCode)
    {
        this.adCode = adCode;
    }

    public String getAdCode()
    {
        return adCode;
    }
    public void setAdminAdnm(String adminAdnm)
    {
        this.adminAdnm = adminAdnm;
    }

    public String getAdminAdnm()
    {
        return adminAdnm;
    }
    public void setNaturalAdnm(String naturalAdnm)
    {
        this.naturalAdnm = naturalAdnm;
    }

    public String getNaturalAdnm()
    {
        return naturalAdnm;
    }
    public void setIsAdded(String isAdded)
    {
        this.isAdded = isAdded;
    }

    public String getIsAdded()
    {
        return isAdded;
    }
    public void setFromDate(Date fromDate)
    {
        this.fromDate = fromDate;
    }

    public Date getFromDate()
    {
        return fromDate;
    }
    public void setToDate(Date toDate)
    {
        this.toDate = toDate;
    }

    public Date getToDate()
    {
        return toDate;
    }
    public void setOrigCode(String origCode)
    {
        this.origCode = origCode;
    }

    public String getOrigCode()
    {
        return origCode;
    }
    public void setGeoCode(String geoCode)
    {
        this.geoCode = geoCode;
    }

    public String getGeoCode()
    {
        return geoCode;
    }
    public void setTongTime(Date tongTime)
    {
        this.tongTime = tongTime;
    }

    public Date getTongTime()
    {
        return tongTime;
    }
    public void setOp(String op)
    {
        this.op = op;
    }

    public String getOp()
    {
        return op;
    }
    public void setModifyTime(Date modifyTime)
    {
        this.modifyTime = modifyTime;
    }

    public Date getModifyTime()
    {
        return modifyTime;
    }
    public void setIsLock(String isLock)
    {
        this.isLock = isLock;
    }

    public String getIsLock()
    {
        return isLock;
    }
    public void setCheckId(String checkId)
    {
        this.checkId = checkId;
    }

    public String getCheckId()
    {
        return checkId;
    }
    public void setIsDataOfficial(String isDataOfficial)
    {
        this.isDataOfficial = isDataOfficial;
    }

    public String getIsDataOfficial()
    {
        return isDataOfficial;
    }
    public void setDataOfficialTime(Date dataOfficialTime)
    {
        this.dataOfficialTime = dataOfficialTime;
    }

    public Date getDataOfficialTime()
    {
        return dataOfficialTime;
    }
    public void setCheckStatus(String checkStatus)
    {
        this.checkStatus = checkStatus;
    }

    public String getCheckStatus()
    {
        return checkStatus;
    }
    public void setCheckReason(String checkReason)
    {
        this.checkReason = checkReason;
    }

    public String getCheckReason()
    {
        return checkReason;
    }
    public void setAdcd(String adcd)
    {
        this.adcd = adcd;
    }

    public String getAdcd()
    {
        return adcd;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("prevCode", getPrevCode())
            .append("adCode", getAdCode())
            .append("adminAdnm", getAdminAdnm())
            .append("naturalAdnm", getNaturalAdnm())
            .append("isAdded", getIsAdded())
            .append("fromDate", getFromDate())
            .append("toDate", getToDate())
            .append("origCode", getOrigCode())
            .append("geoCode", getGeoCode())
            .append("tongTime", getTongTime())
            .append("op", getOp())
            .append("modifyTime", getModifyTime())
            .append("isLock", getIsLock())
            .append("checkId", getCheckId())
            .append("isDataOfficial", getIsDataOfficial())
            .append("dataOfficialTime", getDataOfficialTime())
            .append("checkStatus", getCheckStatus())
            .append("checkReason", getCheckReason())
            .append("adcd", getAdcd())
            .toString();
    }
}
