package com.tzslsjy.business.domain;

import lombok.Data;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;
import java.util.stream.Collectors;

/**
 * 测站异常预警参数数据
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
public class SjyStationAbnormalParamData {

    /** 测站编号列表（兼容旧版本） */
    private List<String> stcds;

    /** 水位测站编号列表（兼容旧版本） */
    private List<String> waterStcds;

    /** 雨量测站编号列表（兼容旧版本） */
    private List<String> rainStcds;

    /** 超时检测测站编号列表 */
    private List<String> timeStcds;

    /** 数据接收超时阈值（分钟） */
    private Integer dataTimeoutMinutes = 60;

    /** 水位变化阈值（米）（兼容旧版本） */
    private BigDecimal waterLevelThreshold = new BigDecimal("1.0");

    /** 雨量阈值（毫米）（兼容旧版本） */
    private BigDecimal rainfallThreshold = new BigDecimal("15.0");

    /** 水位异常检测开关（兼容旧版本） */
    private Boolean waterLevelDetectionEnabled = true;

    /** 雨量异常检测开关（兼容旧版本） */
    private Boolean rainfallDetectionEnabled = true;

    /** 全局开关 */
    private Boolean globalSwitchEnabled = true;

    /** 检测时间间隔（分钟）（兼容旧版本） */
    private Integer detectionIntervalMinutes = 5;

    /** 重复发送控制时间点（小时） */
    private List<Integer> repeatSendHours = Arrays.asList(8, 12);

    // ========== 新的标准化参数结构 ==========

    /** 水位异常检测参数 */
    private StationAbnormalDetectionParam waterLevel;

    /** 雨量异常检测参数 */
    private StationAbnormalDetectionParam rainfall;

    /**
     * 标准化异常检测参数
     */
    @Data
    public static class StationAbnormalDetectionParam {
        /** 时间周期（小时），用于回溯比较的时间范围 */
        private Integer during = 1;

        /** 阈值：水位为米，雨量为毫米 */
        private BigDecimal threshold = new BigDecimal("1.0");

        public StationAbnormalDetectionParam() {}

        public StationAbnormalDetectionParam(Integer during, BigDecimal threshold) {
            this.during = during;
            this.threshold = threshold;
        }
    }

    /**
     * 获取水位检测的测站列表（兼容旧版本）
     * 优先使用 waterStcds，如果为空则使用 stcds（兼容旧版本）
     * 如果都为空，返回空列表，由调用方决定是否查询所有水位测站
     *
     * 注意：新版本中每个规则实例只针对单个测站（通过alertStcd字段），不再需要测站列表
     */
    public List<String> getWaterStcdsForDetection() {
        if (waterStcds != null && !waterStcds.isEmpty()) {
            return waterStcds;
        }
        return stcds != null ? stcds : new ArrayList<>();
    }

    /**
     * 获取雨量检测的测站列表（兼容旧版本）
     * 优先使用 rainStcds，如果为空则使用 stcds（兼容旧版本）
     * 如果都为空，返回空列表，由调用方决定是否查询所有雨量测站
     *
     * 注意：新版本中每个规则实例只针对单个测站（通过alertStcd字段），不再需要测站列表
     */
    public List<String> getRainStcdsForDetection() {
        if (rainStcds != null && !rainStcds.isEmpty()) {
            return rainStcds;
        }
        return stcds != null ? stcds : new ArrayList<>();
    }

    /**
     * 获取所有测站列表（用于超时检测）
     * 合并 waterStcds 和 rainStcds，如果都为空则使用 stcds
     */
    public List<String> getAllStcdsForDetection() {
        List<String> allStcds = new ArrayList<>();

        if ((waterStcds != null && !waterStcds.isEmpty()) ||
            (rainStcds != null && !rainStcds.isEmpty())) {
            // 使用新格式：合并水位和雨量测站
            if (waterStcds != null) {
                allStcds.addAll(waterStcds);
            }
            if (rainStcds != null) {
                allStcds.addAll(rainStcds);
            }
            // 去重
            allStcds = allStcds.stream().distinct().collect(Collectors.toList());
        }

        return allStcds;
    }

    /**
     * 获取超时检测的测站列表
     * 优先使用 timeStcds，如果为空则返回空列表
     */
    public List<String> getTimeStcdsForDetection() {
        if (timeStcds != null && !timeStcds.isEmpty()) {
            return timeStcds;
        }
        return new ArrayList<>();
    }

    // ========== 新的标准化参数获取方法 ==========

    /**
     * 获取水位异常检测的时间周期（小时）
     * 优先使用新格式，如果为空则使用旧格式的detectionIntervalMinutes转换为小时
     */
    public Integer getWaterLevelDetectionDuringHours() {
        if (waterLevel != null && waterLevel.getDuring() != null) {
            return waterLevel.getDuring();
        }
        // 兼容旧版本：将分钟转换为小时，默认5分钟转换为1小时
        return detectionIntervalMinutes != null && detectionIntervalMinutes > 0 ?
               Math.max(1, detectionIntervalMinutes / 60) : 1;
    }

    /**
     * 获取水位异常检测阈值（米）
     * 优先使用新格式，如果为空则使用旧格式
     */
    public BigDecimal getWaterLevelDetectionThreshold() {
        if (waterLevel != null && waterLevel.getThreshold() != null) {
            return waterLevel.getThreshold();
        }
        return waterLevelThreshold != null ? waterLevelThreshold : new BigDecimal("1.0");
    }

    /**
     * 获取雨量异常检测的时间周期（小时）
     * 优先使用新格式，如果为空则使用旧格式的detectionIntervalMinutes转换为小时
     */
    public Integer getRainfallDetectionDuringHours() {
        if (rainfall != null && rainfall.getDuring() != null) {
            return rainfall.getDuring();
        }
        // 兼容旧版本：将分钟转换为小时，默认5分钟转换为1小时
        return detectionIntervalMinutes != null && detectionIntervalMinutes > 0 ?
               Math.max(1, detectionIntervalMinutes / 60) : 1;
    }

    /**
     * 获取雨量异常检测阈值（毫米）
     * 优先使用新格式，如果为空则使用旧格式
     */
    public BigDecimal getRainfallDetectionThreshold() {
        if (rainfall != null && rainfall.getThreshold() != null) {
            return rainfall.getThreshold();
        }
        return rainfallThreshold != null ? rainfallThreshold : new BigDecimal("15.0");
    }

    /**
     * 检查是否启用水位异常检测
     * 新版本中通过规则的启用状态控制，这里保持兼容性
     */
    public Boolean isWaterLevelDetectionEnabled() {
        return waterLevelDetectionEnabled != null ? waterLevelDetectionEnabled : true;
    }

    /**
     * 检查是否启用雨量异常检测
     * 新版本中通过规则的启用状态控制，这里保持兼容性
     */
    public Boolean isRainfallDetectionEnabled() {
        return rainfallDetectionEnabled != null ? rainfallDetectionEnabled : true;
    }
}
