package com.tzslsjy.business.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 预警接收人对象 sjy_alert_person
 *
 * <AUTHOR>
 */
@Schema(description = "预警接收人对象")
public class SjyAlertPerson extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 人员ID */
    @Schema(description = "人员ID")
    @TableId(value = "person_id",type= IdType.ASSIGN_ID)
    private String personId;

    /** 姓名 */
    @Schema(description = "姓名")
    @Excel(name = "姓名")
    @Size(max = 100)
    private String name;

    /** 手机号 */
    @Schema(description = "手机号")
    @Excel(name = "手机号")
    @Size(max = 20)
    private String phone;
    @Schema(description = "行政区划编码")
    private String addvcd;

    /** 状态：0-禁用，1-启用 */
    @Schema(description = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    @Max(value = 2147483647)
    private Long status;
    
    /** 优先级 */
    @Schema(description = "优先级")
    @Excel(name = "优先级")
    @Max(value = 100)
    private Integer priority;
    @TableField(exist = false)
        @Schema(description = "人员所在节点名称")
    private String nodeName ;
        @TableField(exist = false)
    @Schema(description = "职务名称")
    private String postName ;
    /** 关联的节点信息 */
    @Schema(description = "关联的节点信息")
    private List<SjyAlertNode> alertNodes;

    @Schema(description = "关联的部门信息")
    private List<SjyAlertNode> deptNodes;

    @Schema(description = "关联的职能信息")
    private List<SjyAlertNode> znNodes;

    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }



    public Long getStatus() {
        return status;
    }

    public void setStatus(Long status) {
        this.status = status;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public List<SjyAlertNode> getAlertNodes() {
        return alertNodes;
    }

    public void setAlertNodes(List<SjyAlertNode> alertNodes) {
        this.alertNodes = alertNodes;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("personId", getPersonId())
            .append("name", getName())
            .append("phone", getPhone())
            .append("status", getStatus())
            .append("priority", getPriority())
            .append("alertNodes", getAlertNodes())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public List<SjyAlertNode> getDeptNodes() {
        return deptNodes;
    }

    public void setDeptNodes(List<SjyAlertNode> deptNodes) {
        this.deptNodes = deptNodes;
    }

    public List<SjyAlertNode> getZnNodes() {
        return znNodes;
    }

    public void setZnNodes(List<SjyAlertNode> znNodes) {
        this.znNodes = znNodes;
    }


    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getNodeName() {
        return nodeName;
    }

    public void setNodeName(String nodeName) {
        this.nodeName = nodeName;
    }

    public String getAddvcd() {
        return addvcd;
    }

    public void setAddvcd(String addvcd) {
        this.addvcd = addvcd;
    }
}
