package com.tzslsjy.business.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * 预警接收记录对象 sjy_alert_receive_record
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Schema(description = "预警接收记录对象")
public class SjyAlertReceiveRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long receiveId;

    /** 预警记录ID */
    @Schema(description = "预警记录ID")
    @Excel(name = "预警记录ID")
    @Max(value = Long.MAX_VALUE)
    private Long smsId;

    /** 接收人ID */
    @Schema(description = "接收人ID")
    @Excel(name = "接收人ID")
    @Size(max = 50)
    private String personId;

    /** 接收手机号 */
    @Schema(description = "接收手机号")
    @Excel(name = "接收手机号")
    @Size(max = 20)
    private String phone;

    /** 渠道（1短信2浙政钉3广播4传真） */
    @Schema(description = "渠道（1短信2浙政钉3广播4传真）")
    @Excel(name = "渠道", readConverterExp = "1=短信2浙政钉3广播4传真")
    @Size(max = 90)
    private String wayType;

    /** 是否已读 */
    @Schema(description = "是否已读")
    @Excel(name = "是否已读")
    @Max(value = 2147483647)
    private Long izRead;

    /** 状态：0-待发送，1-已发送，2-发送失败 */
    @Schema(description = "状态：0-待发送，1-已发送，2-发送失败")
    @Excel(name = "状态：0-待发送，1-已发送，2-发送失败")
    @Max(value = 2147483647)
    private Long status;

    /** 发送时间 */
    @Schema(description = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    
    private Date sendTime;

    /** 部门名称 */
    @Schema(description = "部门名称")
    @Excel(name = "部门名称")
    @Size(max = 255)
    private String orgName;

    /** 行政区划名称 */
    @Schema(description = "行政区划名称")
    @Excel(name = "行政区划名称")
    @Size(max = 255)
    private String adcdName;
    /** 职位名称 */
    @Schema(description = "职位名称")
    @Excel(name = "职位名称")
    @Size(max = 255)
    private String postName;
    /** 用户名称 */
    @Schema(description = "用户名称")
    @TableField(exist = false)
    @Excel(name = "用户名称")
    @Size(max = 255)
    private String personName;
    /** 职能名称 */
    @Schema(description = "职能名称")
    @Excel(name = "职能名称")
    @Size(max = 255)
    private String dutyName;

    public void setReceiveId(Long receiveId)
    {
        this.receiveId = receiveId;
    }

    public Long getReceiveId()
    {
        return receiveId;
    }
    public void setSmsId(Long smsId)
    {
        this.smsId = smsId;
    }

    public Long getSmsId()
    {
        return smsId;
    }
    public void setPersonId(String personId)
    {
        this.personId = personId;
    }

    public String getPersonId()
    {
        return personId;
    }
    public void setPhone(String phone)
    {
        this.phone = phone;
    }

    public String getPhone()
    {
        return phone;
    }
    public void setWayType(String wayType)
    {
        this.wayType = wayType;
    }

    public String getWayType()
    {
        return wayType;
    }
    public void setIzRead(Long izRead)
    {
        this.izRead = izRead;
    }

    public Long getIzRead()
    {
        return izRead;
    }
    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }
    public void setSendTime(Date sendTime)
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime()
    {
        return sendTime;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getAdcdName() {
        return adcdName;
    }

    public void setAdcdName(String adcdName) {
        this.adcdName = adcdName;
    }

    public String getDutyName() {
        return dutyName;
    }

    public void setDutyName(String dutyName) {
        this.dutyName = dutyName;
    }

    public String getPostName() {
        return postName;
    }

    public void setPostName(String postName) {
        this.postName = postName;
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("receiveId", getReceiveId())
            .append("smsId", getSmsId())
            .append("personId", getPersonId())
            .append("phone", getPhone())
            .append("wayType", getWayType())
            .append("izRead", getIzRead())
            .append("status", getStatus())
            .append("sendTime", getSendTime())
            .append("orgName", getOrgName())
            .append("adcdName", getAdcdName())
            .append("dutyName", getDutyName())
            .append("postName", getPostName())
            .append("personName", getPersonName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
