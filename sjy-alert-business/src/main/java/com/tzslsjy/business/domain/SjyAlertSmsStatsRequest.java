package com.tzslsjy.business.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.tzslsjy.common.annotation.Excel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
// import java.util.List; // No longer needed
// import javax.validation.constraints.NotEmpty; // No longer needed
import javax.validation.constraints.NotNull; // Retained for potential future use or if sendType becomes mandatory
import javax.validation.constraints.Size;

@Schema(description = "预警消息发送统计请求")
public class SjyAlertSmsStatsRequest {

    @Schema(description = "发送开始时间", example = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendStartTime;

    @Schema(description = "发送结束时间", example = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendEndTime;

    @Schema(description = "发送方式")
    private String sendType;

    /** 规则类型 */
    @Schema(description = "规则类型")
    @Excel(name = "规则类型")
    private String ruleTypeId;

    /** 消息类型 */
    @Schema(description = "消息类型")
    private String smsType;

    /** 所属模块ID */
    @Schema(description = "所属模块ID")
    private Long moduleId;

    private Integer pageSize;

    private Integer pageNum;
    @Schema(description = "消息内容 (支持模糊查询)")
    private String content;

    // Getters and Setters
    public Date getSendStartTime() {
        return sendStartTime;
    }

    public void setSendStartTime(Date sendStartTime) {
        this.sendStartTime = sendStartTime;
    }

    public Date getSendEndTime() {
        return sendEndTime;
    }

    public void setSendEndTime(Date sendEndTime) {
        this.sendEndTime = sendEndTime;
    }

    public String getSendType() {
        return sendType;
    }

    public void setSendType(String sendType) {
        this.sendType = sendType;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getRuleTypeId() {
        return ruleTypeId;
    }

    public void setRuleTypeId(String ruleTypeId) {
        this.ruleTypeId = ruleTypeId;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public String getSmsType() {
        return smsType;
    }

    public void setSmsType(String smsType) {
        this.smsType = smsType;
    }

    public Long getModuleId() {
        return moduleId;
    }

    public void setModuleId(Long moduleId) {
        this.moduleId = moduleId;
    }
}