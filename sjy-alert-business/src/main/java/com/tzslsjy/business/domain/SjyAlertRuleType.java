package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 规则类型对象 sjy_alert_rule_type
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Schema(description = "规则类型对象")
public class SjyAlertRuleType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 规则类型ID */
    @Schema(description = "规则类型ID")
    @Max(value = 2147483647)
    private Long ruleTypeId;

    /** 规则类型编码 */
    @Schema(description = "规则类型编码")
    @Excel(name = "规则类型编码")
    @Size(max = 50)
    private String typeCode;

    /** 规则类型名称 */
    @Schema(description = "规则类型名称")
    @Excel(name = "规则类型名称")
    @Size(max = 100)
    private String typeName;

    /** 父规则节点ID */
    @Schema(description = "父规则节点ID")
    @Excel(name = "父规则节点ID")
    private Long parentId;

    /** 类型 */
    @Schema(description = "类型")
    @Excel(name = "类型")
    @Size(max = 50)
    private String type;

    /** 参数模板 (JSON格式) */
    @Schema(description = "参数模板 (JSON格式)")
    @Excel(name = "参数模板") // 根据需要决定是否加入Excel导出
    private String argJson; // 对应数据库字段 arg_json

    /** 子节点 */
    @Schema(description = "子节点")
    @TableField(exist = false)
    private List<SjyAlertRuleType> children;

    @Schema(description = "规则节点")
    @TableField(exist = false)
    List<SjyAlertRule> rules;

    /** 类型描述 */
    @Schema(description = "类型描述")
    @Excel(name = "类型描述")
    @Size(max = 255)
    private String description;

    /** 状态：0-禁用，1-启用 */
    @Schema(description = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    @Max(value = 2147483647)
    private Long status;

    public void setRuleTypeId(Long ruleTypeId)
    {
        this.ruleTypeId = ruleTypeId;
    }

    public Long getRuleTypeId()
    {
        return ruleTypeId;
    }
    public void setTypeCode(String typeCode)
    {
        this.typeCode = typeCode;
    }

    public String getTypeCode()
    {
        return typeCode;
    }
    public void setTypeName(String typeName)
    {
        this.typeName = typeName;
    }

    public String getTypeName()
    {
        return typeName;
    }
    public void setParentId(Long parentId)
    {
        this.parentId = parentId;
    }

    public Long getParentId()
    {
        return parentId;
    }

    public void setType(String type)
    {
        this.type = type;
    }

    public String getType()
    {
        return type;
    }

    public String getArgJson() {
        return argJson;
    }

    public void setArgJson(String argJson) {
        this.argJson = argJson;
    }

    public List<SjyAlertRuleType> getChildren() {
        return children;
    }

    public void setChildren(List<SjyAlertRuleType> children) {
        this.children = children;
    }

    public List<SjyAlertRule> getRules() {
        return rules;
    }

    public void setRules(List<SjyAlertRule> rules) {
        this.rules = rules;
    }

    public void setDescription(String description)
    {
        this.description = description;
    }

    public String getDescription()
    {
        return description;
    }
    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ruleTypeId", getRuleTypeId())
            .append("typeCode", getTypeCode())
            .append("typeName", getTypeName())
            .append("parentId", getParentId())
            .append("type", getType())
            .append("argJson", getArgJson())
            .append("description", getDescription())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
