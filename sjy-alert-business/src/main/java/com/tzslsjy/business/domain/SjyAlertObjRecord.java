package com.tzslsjy.business.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * 预警对象记录对象 sjy_alert_obj_record
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Schema(description = "预警对象记录对象")
public class SjyAlertObjRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long alertRecordId;

    /** 规则ID */
    @Schema(description = "规则ID")
    @Excel(name = "规则ID")
    @Max(value = Long.MAX_VALUE)
    private Long ruleId;

    /** 预警对象 */
    @Schema(description = "预警对象")
    @Excel(name = "预警对象")
    @Size(max = 64)
    private String alertObj;

    /** 预警对象类型 */
    @Schema(description = "预警对象类型")
    @Excel(name = "预警对象类型")
    @Size(max = 64)
    private String alertObjType;

    /** 预警级别：1-低，2-中，3-高 */
    @Schema(description = "预警级别：1-低，2-中，3-高")
    @Excel(name = "预警级别：1-低，2-中，3-高")
    @Max(value = 2147483647)
    private Long alertLevel;

    /** 预警内容 */
    @Schema(description = "预警内容")
    @Excel(name = "预警内容")
    
    private String alertContent;

    /** 状态：0-待发送，1-已发送，2-发送失败 */
    @Schema(description = "状态：0-待发送，1-已发送，2-发送失败")
    @Excel(name = "状态：0-待发送，1-已发送，2-发送失败")
    @Max(value = 2147483647)
    private Long status;

    /** 发送时间 */
    @Schema(description = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    
    private Date sendTime;

    public void setAlertRecordId(Long alertRecordId)
    {
        this.alertRecordId = alertRecordId;
    }

    public Long getAlertRecordId()
    {
        return alertRecordId;
    }
    public void setRuleId(Long ruleId)
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId()
    {
        return ruleId;
    }
    public void setAlertObj(String alertObj)
    {
        this.alertObj = alertObj;
    }

    public String getAlertObj()
    {
        return alertObj;
    }
    public void setAlertObjType(String alertObjType)
    {
        this.alertObjType = alertObjType;
    }

    public String getAlertObjType()
    {
        return alertObjType;
    }
    public void setAlertLevel(Long alertLevel)
    {
        this.alertLevel = alertLevel;
    }

    public Long getAlertLevel()
    {
        return alertLevel;
    }
    public void setAlertContent(String alertContent)
    {
        this.alertContent = alertContent;
    }

    public String getAlertContent()
    {
        return alertContent;
    }
    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }
    public void setSendTime(Date sendTime)
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime()
    {
        return sendTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("alertRecordId", getAlertRecordId())
            .append("ruleId", getRuleId())
            .append("alertObj", getAlertObj())
            .append("alertObjType", getAlertObjType())
            .append("alertLevel", getAlertLevel())
            .append("alertContent", getAlertContent())
            .append("status", getStatus())
            .append("sendTime", getSendTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
