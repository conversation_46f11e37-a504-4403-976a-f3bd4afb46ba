package com.tzslsjy.business.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.math.BigDecimal;

@Data
@Schema(description = "城市内涝水位预警参数JSON对应实体")
public class SjyUrbanWaterloggingAlertJsonData {

    @Schema(description = "测站编码")
    private String stcd;

    @Schema(description = "汛限水位(WRZ)")
    private BigDecimal wrz;

    @Schema(description = "行政区划名称")
    private String addvcdNm;

    @Schema(description = "测站名称")
    private String stnm;
}
