package com.tzslsjy.business.domain.comm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @auther seven
 * @create 2021-05-11 10:55:10
 * @describe 行政区划、城市乡镇实体类
 */
@TableName("comm_city")
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "行政区划、城市乡镇")
public class BusinessCommCity implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;



    @Schema(description = "行政区划编码")
    @TableField("JURISDICTION_NUM")
    private String jurisdictionNum;

    @Schema(description = "行政区划名称")
    @TableField("JURISDICTION")
    private String jurisdiction;

    @Schema(description = "统一查询用")
    @TableField(exist = false)
    private String addvcd;

    @Schema(description = "行政区划名称_短")
    @TableField("JURISDICTION_S")
    private String jurisdictionS;

    @Schema(description = "上级行政区划编码")
    @TableField("PID")
    private String pid;

    @Schema(description = "经度")
    @TableField("LGTD")
    private String lgtd;

    @Schema(description = "纬度")
    @TableField("LTTD")
    private String lttd;

    @Schema(description = "人口数量")
    @TableField("POPUNUM")
    private Integer popunum;

    @Schema(description = "面积(km²)")
    @TableField("AREA")
    private BigDecimal area;

    @Schema(description = "径流系数")
    @TableField("RC")
    private BigDecimal rc;

    @Schema(description = "城乡分类代码")
    @TableField("CLSS")
    private String clss;

    @Schema(description = "排序")
    @TableField("SJ_SEQ")
    private Integer sjSeq;


    public Integer getId() {
        return id;
    }

    public BusinessCommCity setId(Integer id) {
        this.id = id;
        return this;
    }

    public String getJurisdictionNum() {
        return jurisdictionNum;
    }

    public BusinessCommCity setJurisdictionNum(String jurisdictionNum) {
        this.jurisdictionNum = jurisdictionNum;
        return this;
    }

    public String getJurisdiction() {
        return jurisdiction;
    }

    public BusinessCommCity setJurisdiction(String jurisdiction) {
        this.jurisdiction = jurisdiction;
        return this;
    }

    public String getJurisdictionS() {
        return jurisdictionS;
    }

    public BusinessCommCity setJurisdictionS(String jurisdictionS) {
        this.jurisdictionS = jurisdictionS;
        return this;
    }

    public String getPid() {
        return pid;
    }

    public BusinessCommCity setPid(String pid) {
        this.pid = pid;
        return this;
    }

    public String getLgtd() {
        return lgtd;
    }

    public BusinessCommCity setLgtd(String lgtd) {
        this.lgtd = lgtd;
        return this;
    }

    public String getLttd() {
        return lttd;
    }

    public BusinessCommCity setLttd(String lttd) {
        this.lttd = lttd;
        return this;
    }

    public Integer getPopunum() {
        return popunum;
    }

    public BusinessCommCity setPopunum(Integer popunum) {
        this.popunum = popunum;
        return this;
    }

    public BigDecimal getArea() {
        return area;
    }

    public BusinessCommCity setArea(BigDecimal area) {
        this.area = area;
        return this;
    }

    public BigDecimal getRc() {
        return rc;
    }

    public BusinessCommCity setRc(BigDecimal rc) {
        this.rc = rc;
        return this;
    }

    public String getClss() {
        return clss;
    }

    public BusinessCommCity setClss(String clss) {
        this.clss = clss;
        return this;
    }

    public Integer getSjSeq() {
        return sjSeq;
    }

    public BusinessCommCity setSjSeq(Integer sjSeq) {
        this.sjSeq = sjSeq;
        return this;
    }

    @Override
    public String toString() {
        return "CommCity{" +
                "id=" + id +
                ", jurisdictionNum=" + jurisdictionNum +
                ", jurisdiction=" + jurisdiction +
                ", jurisdictionS=" + jurisdictionS +
                ", pid=" + pid +
                ", lgtd=" + lgtd +
                ", lttd=" + lttd +
                ", popunum=" + popunum +
                ", area=" + area +
                ", rc=" + rc +
                ", clss=" + clss +
                ", sjSeq=" + sjSeq +
                "}";
    }

    public String getAddvcd() {
        return addvcd;
    }

    public void setAddvcd(String addvcd) {
        this.addvcd = addvcd;
    }
}