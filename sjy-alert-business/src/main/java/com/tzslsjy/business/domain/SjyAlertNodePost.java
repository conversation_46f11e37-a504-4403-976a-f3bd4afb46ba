package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;


/**
 * 节点职位对象 sjy_alert_node_post
 * 
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_alert_node_post")
public class SjyAlertNodePost implements Serializable {

private static final long serialVersionUID=1L;

 
    /** 职位ID (主键) */
    @TableId(value = "post_id", type = IdType.INPUT) // Changed from id to postId, and type to INPUT for manual assignment
    private String postId;

    /** 节点id */
    private String nodeId;

    /** 职位名称 */
    private String postName;

    /** 排序字段 */
    private Integer sortNum;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 备注 */
    private String remark;

}
