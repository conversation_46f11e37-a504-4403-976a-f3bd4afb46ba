package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 全景图对象 sjy_panorama_b
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_panorama_b")
public class SjyPanoramaB implements Serializable {

private static final long serialVersionUID=1L;


    /** 自动编号 */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 站点名称 */
    private String name;

    /** 站址 */
    private String address;

    /** 经度 */
    private BigDecimal lgtd;

    /** 纬度 */
    private BigDecimal lttd;

    /** 行政区划码 */
    private String areacode;

    /** 站点类别 */
    private String stcategory;

    /** 水利工程编码 */
    private String ennmcd;

    /** swf 视频文件 */
    private String swfAttaId;

    /** app 全景图查看地址 */
    private String appScanUrl;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 是否删除:1未删除，2删除 */
    private Long izDel;

    /** 备注 */
    private String remark;

}