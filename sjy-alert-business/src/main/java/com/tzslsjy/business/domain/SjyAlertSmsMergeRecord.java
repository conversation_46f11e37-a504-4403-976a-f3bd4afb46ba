package com.tzslsjy.business.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * 预警消息合并记录对象 sjy_alert_sms_merge_record
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Schema(description = "预警消息合并记录对象")
public class SjyAlertSmsMergeRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long smsMergeId;

    /** 接收人ID */
    @Schema(description = "接收人ID")
    @Excel(name = "接收人ID")
    @Max(value = Long.MAX_VALUE)
    private Long personId;

    /** 规则类型 */
    @Schema(description = "规则类型")
    @Excel(name = "规则类型")
    @Size(max = 50)
    private String ruleId;

    /** 合并后的内容 */
    @Schema(description = "合并后的内容")
    @Excel(name = "合并后的内容")
    
    private String mergedContent;

    /** 合并的短信ID列表 */
    @Schema(description = "合并的短信ID列表")
    @Excel(name = "合并的短信ID列表")
    @Size(max = 500)
    private String alertSmsIds;

    /** 状态：0-待发送，1-已发送，2-发送失败 */
    @Schema(description = "状态：0-待发送，1-已发送，2-发送失败")
    @Excel(name = "状态：0-待发送，1-已发送，2-发送失败")
    @Max(value = 2147483647)
    private Long status;

    /** 发送时间 */
    @Schema(description = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    
    private Date sendTime;

    public void setSmsMergeId(Long smsMergeId)
    {
        this.smsMergeId = smsMergeId;
    }

    public Long getSmsMergeId()
    {
        return smsMergeId;
    }
    public void setPersonId(Long personId)
    {
        this.personId = personId;
    }

    public Long getPersonId()
    {
        return personId;
    }
    public void setRuleId(String ruleId)
    {
        this.ruleId = ruleId;
    }

    public String getRuleId()
    {
        return ruleId;
    }
    public void setMergedContent(String mergedContent)
    {
        this.mergedContent = mergedContent;
    }

    public String getMergedContent()
    {
        return mergedContent;
    }
    public void setAlertSmsIds(String alertSmsIds)
    {
        this.alertSmsIds = alertSmsIds;
    }

    public String getAlertSmsIds()
    {
        return alertSmsIds;
    }
    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }
    public void setSendTime(Date sendTime)
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime()
    {
        return sendTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("smsMergeId", getSmsMergeId())
            .append("personId", getPersonId())
            .append("ruleId", getRuleId())
            .append("mergedContent", getMergedContent())
            .append("alertSmsIds", getAlertSmsIds())
            .append("status", getStatus())
            .append("sendTime", getSendTime())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
