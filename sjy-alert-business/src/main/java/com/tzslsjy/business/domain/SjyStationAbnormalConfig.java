package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 测站异常预警配置对象 sjy_station_abnormal_config
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_station_abnormal_config")
public class SjyStationAbnormalConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /** 测站编号 */
    private String stcd;

    /** 配置类型：1-数据接收超时，2-水位异常变化，3-雨量异常 */
    private Integer configType;

    /** 阈值 */
    private BigDecimal threshold;

    /** 单位 */
    private String unit;

    /** 是否启用：0-禁用，1-启用 */
    private Integer enabled;

    /** 全局开关：0-关闭，1-开启（仅对configType=2,3有效） */
    private Integer globalSwitch;

    /** 备注 */
    private String remark;

    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
