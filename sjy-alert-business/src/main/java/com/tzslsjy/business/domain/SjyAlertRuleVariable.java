package com.tzslsjy.business.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;

/**
 * 规则标签关联对象 sjy_alert_rule_variable
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Schema(description = "规则标签关联对象")
public class SjyAlertRuleVariable extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 关联ID */
    @Schema(description = "关联ID")
    @Max(value = 2147483647)
    private Long ruleVariableId;

    /** 规则ID */
    @Schema(description = "规则ID")
    @Excel(name = "规则ID")
    @Max(value = Long.MAX_VALUE)
    private Long ruleTypeId;

    /** 变量ID */
    @Schema(description = "变量ID")
    @Excel(name = "变量ID")
    @Max(value = Long.MAX_VALUE)
    private Long variableId;

    public void setRuleVariableId(Long ruleVariableId)
    {
        this.ruleVariableId = ruleVariableId;
    }

    public Long getRuleVariableId()
    {
        return ruleVariableId;
    }
    public void setRuleTypeId(Long ruleTypeId)
    {
        this.ruleTypeId = ruleTypeId;
    }

    public Long getRuleTypeId()
    {
        return ruleTypeId;
    }
    public void setVariableId(Long variableId)
    {
        this.variableId = variableId;
    }

    public Long getVariableId()
    {
        return variableId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("ruleVariableId", getRuleVariableId())
            .append("ruleTypeId", getRuleTypeId())
            .append("variableId", getVariableId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
