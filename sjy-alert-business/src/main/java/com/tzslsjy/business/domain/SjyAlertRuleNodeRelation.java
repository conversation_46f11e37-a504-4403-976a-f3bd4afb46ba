package com.tzslsjy.business.domain;

import com.tzslsjy.business.domain.SjyAlertPerson;
import io.swagger.v3.oas.annotations.media.Schema;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 规则接收节点关联对象 sjy_alert_rule_node_relation
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Schema(description = "规则接收节点关联对象")
public class SjyAlertRuleNodeRelation extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long relationId;

    /** 规则ID */
    @Schema(description = "规则ID")
    @Excel(name = "规则ID")
    @Max(value = Long.MAX_VALUE)
    private Long ruleId;

    public Long getArgId() {
        return argId;
    }

    public void setArgId(Long argId) {
        this.argId = argId;
    }

    @Schema(description = "参数ID")
    private Long argId;
    /** 节点ID */
    @Schema(description = "节点ID")
    @Excel(name = "节点ID")
    @Size(max = 64) // Assuming a max length for String ID
    private String nodeId; // Changed from Long to String

    /** 预警级别：1-低，2-中，3-高 */
    @Schema(description = "预警级别：1-低，2-中，3-高")
    @Excel(name = "预警级别：1-低，2-中，3-高")
    @Max(value = 2147483647)
    private Long alertLevel;

    /** 关联类型/节点前缀(g-部门节点，o-职能节点，v-变量节点，p-人员节点) */
    @Schema(description = "关联类型/节点前缀(g-部门节点，o-职能节点，v-变量节点，p-人员节点)")
    @Excel(name = "关联类型/节点前缀")
    @Size(max = 255)
    private String relationType;

    /** 联系方式 */
    @Schema(description = "联系方式")
    @Excel(name = "联系方式")
    @Size(max = 255)
    private String contactWay;

    /** 人员ID */
    @Schema(description = "人员ID")
    @Excel(name = "人员ID")
    @Size(max = 64) // Assuming a max length for String ID
    private String personId; // Changed from Long to String
    /** 人员名称 */
    @Schema(description = "人员名称")
    @Excel(name = "人员名称")

    private String personName;
    public List<SjyAlertPerson> getPersonList() {
        return personList;
    }

    public void setPersonList(List<SjyAlertPerson> personList) {
        this.personList = personList;
    }

    @Schema(description = "人员列表")
    private List<SjyAlertPerson> personList;

    public String getPersonId() { // Changed from Long to String
        return personId;
    }

    public void setPersonId(String personId) { // Changed from Long to String
        this.personId = personId;
    }

    public void setRelationId(Long relationId)
    {
        this.relationId = relationId;
    }

    public Long getRelationId()
    {
        return relationId;
    }
    public void setRuleId(Long ruleId)
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId()
    {
        return ruleId;
    }
    public void setNodeId(String nodeId) // Changed from Long to String
    {
        this.nodeId = nodeId;
    }

    public String getNodeId() // Changed from Long to String
    {
        return nodeId;
    }
    public void setAlertLevel(Long alertLevel)
    {
        this.alertLevel = alertLevel;
    }

    public Long getAlertLevel()
    {
        return alertLevel;
    }
    public void setRelationType(String relationType)
    {
        this.relationType = relationType;
    }

    public String getRelationType()
    {
        return relationType;
    }

    public String getContactWay() {
        return contactWay;
    }

    public void setContactWay(String contactWay) {
        this.contactWay = contactWay;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("relationId", getRelationId())
            .append("ruleId", getRuleId())
            .append("nodeId", getNodeId())
            .append("alertLevel", getAlertLevel())
            .append("relationType", getRelationType())
            .append("contactWay", getContactWay())
            .append("personId", getPersonId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }

    public String getPersonName() {
        return personName;
    }

    public void setPersonName(String personName) {
        this.personName = personName;
    }
}
