package com.tzslsjy.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.Serializable;
import java.util.List;
import java.util.stream.Collectors;

@Schema(description = "树形结构视图对象")
public class NodeTreeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "节点ID")
    private String id;
    @Schema(description = "节点ID")
    private String nodeId;

    @Schema(description = "人员id")
    private String personId;
    @Schema(description = "节点标签")
    private String label;

    @Schema(description = "区分人员和节点")
    private String type; // To distinguish between person and node types in the tree

    public Long getNodeType() {
        return nodeType;
    }

    public void setNodeType(Long nodeType) {
        this.nodeType = nodeType;
    }

    @Schema(description = "节点类型，区分部门树和职能树")
    private Long nodeType;

    @Schema(description = "子节点列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<NodeTreeVO> children;

    public NodeTreeVO() {
    }

    // Constructor for SjyAlertPerson
    public NodeTreeVO(com.tzslsjy.business.domain.SjyAlertPerson person,String nodeId) {
        this.id = person.getPersonId(); // person.getPersonId() is String
        this.label = person.getName();
        this.personId = person.getPersonId(); // person.getPersonId() is String
        this.nodeId = nodeId; // nodeId parameter is String
        this.type = "person"; // Mark type as person
    }

    // Constructor for SjyAlertNode
    public NodeTreeVO(com.tzslsjy.business.domain.SjyAlertNode node) {
        this.id = node.getNodeId(); // node.getNodeId() is String
        this.label = node.getNodeName();
        this.nodeType = node.getNodeType();
        this.nodeId = node.getNodeId(); // node.getNodeId() is String

        this.type = "node"; // Mark type as node
        // Note: Children will be set separately
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<NodeTreeVO> getChildren() {
        return children;
    }

    public void setChildren(List<NodeTreeVO> children) {
        this.children = children;
    }


    public String getPersonId() {
        return personId;
    }

    public void setPersonId(String personId) {
        this.personId = personId;
    }

    public String getNodeId() {
        return nodeId;
    }

    public void setNodeId(String nodeId) {
        this.nodeId = nodeId;
    }
}