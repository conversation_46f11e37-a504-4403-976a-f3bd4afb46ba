package com.tzslsjy.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 村庄关联测站编号对象 sjy_risk_village_stcd_rel
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("sjy_risk_village_stcd_rel")
public class SjyRiskVillageStcdRel implements Serializable {

private static final long serialVersionUID=1L;


    /** 主键id */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /** 村庄id */
    private String villageId;

    /** 测站编码 */
    private String stcd;

    /** 行政区划编码 */
    private String adcd;

    @TableField(exist = false)
    private String adcdNm;
    @TableField(exist = false)
    private String stnm;
    /** 创建人 */
    @TableField(fill = FieldFill.INSERT)
    private String createBy;

    /** 创建时间 */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新人 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String updateBy;

    /** 更新时间 */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 备注 */
    private String remark;

    /** 村落类型 */
    private Integer villageType;

    /** 是否在同一小流域 */
    private Integer isSameCa;

    /** 位置(上游,下游) */
    private Integer positionType;

    /** 距离 */
    private BigDecimal positionDistinct;

}