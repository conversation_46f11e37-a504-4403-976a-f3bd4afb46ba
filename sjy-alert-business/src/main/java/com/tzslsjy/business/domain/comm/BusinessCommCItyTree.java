package com.tzslsjy.business.domain.comm;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema; // 更改导入
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
@Data
@Schema(description="城市树形结果") // 更改注解
public class BusinessCommCItyTree implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "id")
    private Integer id;

    @Schema(description = "行政区划编码") // 更改注解
    @TableField("JURISDICTION_NUM")
    private String jurisdictionNum;

    @Schema(description = "行政区划名称") // 更改注解
    @TableField("JURISDICTION")
    private String jurisdiction;

    @Schema(description = "行政区划名称_短") // 更改注解
    @TableField("JURISDICTION_S")
    private String jurisdictionS;

    @Schema(description = "上级行政区划编码") // 更改注解
    @TableField("PID")
    private String pid;

    @Schema(description = "村落编码")
    private String villageId;


    @Schema(description = "经度") // 更改注解
    @TableField("LGTD")
    private String lgtd;

    @Schema(description = "纬度") // 更改注解
    @TableField("LTTD")
    private String lttd;

    @Schema(description = "人口数量") // 更改注解
    @TableField("POPUNUM")
    private Integer popunum;

    @Schema(description = "面积(km²)") // 更改注解
    @TableField("AREA")
    private BigDecimal area;

    @Schema(description = "径流系数") // 更改注解
    @TableField("RC")
    private BigDecimal rc;

    @Schema(description = "城乡分类代码") // 更改注解
    @TableField("CLSS")
    private String clss;

    @Schema(description = "排序") // 更改注解
    @TableField("SJ_SEQ")
    private Integer sjSeq;

    @Schema(description = "子节点列表")
    private List<BusinessCommCItyTree> childList;
}