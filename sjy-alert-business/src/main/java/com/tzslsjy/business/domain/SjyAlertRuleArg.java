package com.tzslsjy.business.domain;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 预警规则参数对象 sjy_alert_rule_arg
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Data
@Schema(description = "预警规则参数对象")
public class SjyAlertRuleArg extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long argId;

    /** 关联ID */
    @Schema(description = "关联ID")
    @Excel(name = "关联ID")
    @Max(value = Long.MAX_VALUE)
    private Long relationId;

    /** 关联类型（1规则类型关联 2规则实体关联） */
    @Schema(description = "关联类型（1规则类型关联 2规则实体关联）")
    @Excel(name = "关联类型", readConverterExp = "1=规则类型关联,2=规则实体关联")
    @Size(max = 255)
    private String relationType;

    /** 参数类型 */
    @Schema(description = "参数类型")
    @Excel(name = "参数类型")
    @Size(max = 255)
    private String argType;

    /** 参数内容 */
    @Schema(description = "参数内容")
    @Excel(name = "参数内容")
    private String argJson;

    /** 参数组标识 */
    @Schema(description = "参数组标识")
    @Excel(name = "参数组标识")
    @Size(max = 50)
    private String groupId;

    /** 处理顺序 */
    @Schema(description = "处理顺序")
    @Excel(name = "处理顺序")
    @Max(value = 2147483647)
    private Long processOrder = 0L;

    /** 消息合并标识：0-单独发送，1-可合并 */
    @Schema(description = "消息合并标识：0-单独发送，1-可合并")
    @Excel(name = "消息合并标识：0-单独发送，1-可合并")
    @Max(value = 2147483647)
    private Long mergeFlag = 0L;

    /** 预警级别：1-低，2-中，3-高 */
    @Schema(description = "预警级别：1-低，2-中，3-高")
    @Excel(name = "预警级别：1-低，2-中，3-高")
    @Max(value = 2147483647)
    private Long alertLevel;

    /** 状态：0-禁用，1-启用 */
    @Schema(description = "状态：0-禁用，1-启用")
    @Excel(name = "状态：0-禁用，1-启用")
    @Max(value = 2147483647)
    private Long status;

    /** 参数关联的节点信息 */
    @Schema(description = "参数关联的节点信息")
    private List<SjyAlertRuleNodeRelation> argNodeRelations;

    /** 参数关联的模板信息 */
    @Schema(description = "参数关联的模板信息")
    private List<SjyAlertTemplateRelation> argTemplateRelations;
    @Schema(description = "模板id")
    private Long templateId;

}
