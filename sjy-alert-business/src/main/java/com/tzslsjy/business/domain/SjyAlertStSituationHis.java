package com.tzslsjy.business.domain;

import com.tzslsjy.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 测站预警情况历史对象 sjy_alert_st_situation_his
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SjyAlertStSituationHis extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键 id */
    private Integer id;

    /** 测站编码 */
    private String stcd;

    /** 关联ID */
    private Integer cId;

    /** 关联类型（1 规则类型 2 规则实例） */
    private Integer cType;

    /** 状态：0-正常，1-超警戒，2-超保证 */
    private Integer status;

    /** 预警开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 预警结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}