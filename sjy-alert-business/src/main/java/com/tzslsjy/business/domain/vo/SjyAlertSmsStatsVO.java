package com.tzslsjy.business.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Max;
import java.util.Date;

@Schema(description = "预警消息发送统计响应")
public class SjyAlertSmsStatsVO {
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long alertSmsId;
    @Schema(description = "消息内容")
    private String content;

    @Schema(description = "所属模块")
    private String moduleName;

    @Schema(description = "消息类型")
    private String messageType;

    @Schema(description = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    @Schema(description = "发送方式")
    private String sendType;

    @Schema(description = "发送成功数量")
    private Long successCount;

    @Schema(description = "发送失败数量")
    private Long failCount;

    @Schema(description = "待发送数量")
    private Long pendingCount;

    // Getters and Setters

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    public String getSendType() {
        return sendType;
    }

    public void setSendType(String sendType) {
        this.sendType = sendType;
    }

    public Long getSuccessCount() {
        return successCount;
    }

    public void setSuccessCount(Long successCount) {
        this.successCount = successCount;
    }

    public Long getFailCount() {
        return failCount;
    }

    public void setFailCount(Long failCount) {
        this.failCount = failCount;
    }

    public Long getPendingCount() {
        return pendingCount;
    }

    public void setPendingCount(Long pendingCount) {
        this.pendingCount = pendingCount;
    }

    public Long getAlertSmsId() {
        return alertSmsId;
    }

    public void setAlertSmsId(Long alertSmsId) {
        this.alertSmsId = alertSmsId;
    }
}