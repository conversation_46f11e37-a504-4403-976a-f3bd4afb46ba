package com.tzslsjy.business.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;

import javax.validation.constraints.Max;
import javax.validation.constraints.Size;

/**
 * 预警消息记录对象 sjy_alert_sms_record
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Data
@Schema(description = "预警消息记录对象")
public class SjyAlertSmsRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @Schema(description = "主键ID")
    @Max(value = 2147483647)
    private Long alertSmsId;

    /** 接收人ID */
    @Schema(description = "接收人ID")
    @Excel(name = "接收人ID")
    private String personId;

    /** 规则类型 */
    @Schema(description = "规则ID")
    @Excel(name = "规则类型")
    @Size(max = 50)
    private String ruleId;

    @Schema(description = "预警级别")
    private Long  alertLevel;

    /** 合并后的内容 */
    @Schema(description = "预警内容")
    @Excel(name = "合并后的内容")
    private String content;

    @Schema(description = "发送类型")
    private String sendType;

    /** 消息类型 */
    @Schema(description = "消息类型")
    @Excel(name = "消息类型")
    @Size(max = 50)
    private String smsType;

    /** 所属模块ID */
    @Schema(description = "所属模块ID")
    @Excel(name = "所属模块ID")
    @Max(value = 2147483647)
    private Long moduleId;

    /** 合并的预警记录ID列表 */
    @Schema(description = "合并的预警记录ID列表")
    @Excel(name = "合并的预警记录ID列表")
    @Size(max = 500)
    private String alertRecordIds;

    /** 状态：0-待发送，1-已发送，2-发送失败 */
    @Schema(description = "状态：0-待发送，1-已发送，2-发送失败")
    @Excel(name = "状态：0-待发送，1-已发送，2-发送失败")
    @Max(value = 2147483647)
    private Long status;

    /** 发送时间 */
    @Schema(description = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date sendTime;


}
