package com.tzslsjy.business.domain;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.tzslsjy.common.annotation.Excel;
import com.tzslsjy.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;

import javax.validation.constraints.Size;

/**
 * 测站基础信息对象 st_stbprp_b
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Data
public class StStbprpB extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 测站编码 */
    @Size(max = 8)
    private String stcd; // 这个已经是小写了，保持不变

    /** 测站名称 */
    @Excel(name = "测站名称")
    @Size(max = 255)
    private String stnm; // 这个已经是小写了，保持不变

    /** 河道名称 */
    @Excel(name = "河道名称")
    @Size(max = 30)
    private String rvnm; // RVNM -> rvnm

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 30)
    private String hnnm; // HNNM -> hnnm

    /** 流域名称 */
    @Excel(name = "流域名称")
    @Size(max = 30)
    private String bsnm; // BSNM -> bsnm

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal lgtd; // LGTD -> lgtd

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal lttd; // LTTD -> lttd

    /** 站址 */
    @Excel(name = "站址")
    @Size(max = 255)
    private String stlc; // STLC -> stlc

    /** 6位行政区划编码 */
    @Excel(name = "6位行政区划编码")
    @Size(max = 255)
    private String addvcd; // ADDVCD -> addvcd

    /** 9位行政区划编码 */
    @Excel(name = "9位行政区划编码")
    @Size(max = 255)
    private String addvcd9; // ADDVCD9 -> addvcd9

    private String addvcd9Nm;
    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 16)
    private String dtmnm; // DTMNM -> dtmnm

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal dtmel; // DTMEL -> dtmel

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal dtpr; // DTPR -> dtpr

    /** 测站类型 */
    @Excel(name = "测站类型")
    @Size(max = 2)
    private String sttp; // STTP -> sttp

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 10)
    private String item; // ITEM -> item (全小写，因为是单个词)

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String frgrd; // FRGRD -> frgrd

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 6)
    private String esstym; // ESSTYM -> esstym

    /** $column.columnComment */
    // 注意: "CLASS" 是 Java 的关键字，直接用 "class" 作为变量名是不允许的。
    // 你需要换一个名字，比如 "clazz" 或者 "categoryClass" 等。
    // 这里我暂时将其改为 "clazzValue" 作为示例。
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 10)
    private String clazzValue; // CLASS -> clazzValue (或者其他非关键字名称)

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 6)
    private String bgfrym; // BGFRYM -> bgfrym

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 20)
    private String atcunit; // ATCUNIT -> atcunit

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 20)
    private String admauth; // ADMAUTH -> admauth

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 10)
    private String locality; // LOCALITY -> locality (全小写，因为是单个词)

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String stbk; // STBK -> stbk

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Long stazt; // STAZT -> stazt

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal dstrvm; // DSTRVM -> dstrvm

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private BigDecimal drna; // DRNA -> drna

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 12)
    private String phcd; // PHCD -> phcd

    /** 启用状态 */
    @Excel(name = "启用状态")
    @Size(max = 1)
    private String usfl; // USFL -> usfl

    /** $column.columnComment */
    // 注意: "COMMENTS" 如果你希望保持复数形式，可以改为 "comments"。
    // 如果是单个评论，可能是 "commentText" 之类的。这里改为 "comments"。
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 200)
    private String comments; // COMMENTS -> comments

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    private Date moditime; // MODITIME -> moditime

    /** $column.columnComment */
    @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
    @Size(max = 1)
    private String rainFlag; // 这个已经是驼峰式了，保持不变

    /** $column.columnComment */
    @Excel(name = "水库关联标记")
    @Size(max = 1)
    private String reservoirFlag; // 这个已经是驼峰式了，保持不变

    @Schema(description = "关联村庄名称")
    private String villageName; // 这个已经是驼峰式了，保持不变

    /** 关联的村庄信息列表 */
    private List<SjyRiskVillageStcdRel> villageStcdRels; // 这个已经是驼峰式了，保持不变

}