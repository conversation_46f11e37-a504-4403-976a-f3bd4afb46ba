package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.StPptnR;
import com.tzslsjy.business.service.IStPptnRService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 雨量Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Tag(name = "雨量管理", description = "雨量管理")
@RestController
@RequestMapping("/business/st/pptn")
public class StPptnRController extends BaseController
{
    @Autowired
    private IStPptnRService stPptnRService;

    /**
     * 查询雨量列表
     */
    @Operation(summary = "查询雨量列表")
    //@PreAuthorize("@ss.hasPermi('business:stpptn:list')")
    @GetMapping("/list")
    public TableDataInfo list(StPptnR stPptnR)
    {
        startPage();
        List<StPptnR> list = stPptnRService.selectStPptnRList(stPptnR);
        return getDataTable(list);
    }

    /**
     * 导出雨量列表
     */
    @Operation(summary = "导出雨量列表")
    //@PreAuthorize("@ss.hasPermi('business:stpptn:export')")
    @Log(title = "雨量", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StPptnR stPptnR)
    {
        List<StPptnR> list = stPptnRService.selectStPptnRList(stPptnR);
        ExcelUtil<StPptnR> util = new ExcelUtil<StPptnR>(StPptnR.class);
        util.exportExcel(response, list, "雨量数据");
    }

    /**
     * 获取雨量详细信息
     */
    @Operation(summary = "获取雨量详细信息")
    @Parameter(name = "STCD", description = "雨量ID", required = true)
    //@PreAuthorize("@ss.hasPermi('business:stpptn:query')")
    @GetMapping(value = "/{STCD}")
    public AjaxResult getInfo(@PathVariable("STCD") String STCD)
    {
        return success(stPptnRService.selectStPptnRBySTCD(STCD));
    }

    /**
     * 新增雨量
     */
    @Operation(summary = "新增雨量")
    //@PreAuthorize("@ss.hasPermi('business:stpptn:add')")
    @Log(title = "雨量", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody StPptnR stPptnR)
    {
        return toAjax(stPptnRService.insertStPptnR(stPptnR));
    }

    /**
     * 修改雨量
     */
    @Operation(summary = "修改雨量")
    //@PreAuthorize("@ss.hasPermi('business:stpptn:edit')")
    @Log(title = "雨量", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody StPptnR stPptnR)
    {
        return toAjax(stPptnRService.updateStPptnR(stPptnR));
    }

    /**
     * 删除雨量
     */
    @Operation(summary = "删除雨量")
    @Parameter(name = "STCDs", description = "雨量ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('business:stpptn:remove')")
    @Log(title = "雨量", businessType = BusinessType.DELETE)
	@DeleteMapping("/{STCDs}")
    public AjaxResult remove(@PathVariable String[] STCDs)
    {
        return toAjax(stPptnRService.deleteStPptnRBySTCDs(STCDs));
    }
}
