package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.tzslsjy.business.domain.SjyAlertRuleType;
import com.tzslsjy.business.service.ISjyAlertRuleTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;

import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 规则类型Controller
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Tag(name = "规则类型管理", description = "规则类型管理")
@RestController
@RequestMapping("/business/alert/type")
public class SjyAlertRuleTypeController extends BaseController
{
    @Autowired
    private ISjyAlertRuleTypeService sjyAlertRuleTypeService;

    /**
     * 查询规则类型列表
     */
    @Operation(summary = "查询规则类型列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:type:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertRuleType sjyAlertRuleType)
    {
        startPage();
        List<SjyAlertRuleType> list = sjyAlertRuleTypeService.selectSjyAlertRuleTypeList(sjyAlertRuleType);
        return getDataTable(list);
    }
    /**
     * 获取规则类型树形列表
     */
    @Operation(summary = "获取规则类型树形列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:type:list')") // 您可以根据需要调整权限
    @GetMapping("/tree")
    public AjaxResult<List<SjyAlertRuleType>> tree(@ParameterObject SjyAlertRuleType sjyAlertRuleType)
    {
        List<SjyAlertRuleType> list = sjyAlertRuleTypeService.selectSjyAlertRuleTypeTree(sjyAlertRuleType);
        return success(list);
    }
    /**
     * 导出规则类型列表
     */
    @Operation(summary = "导出规则类型列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:type:export')")
    @Log(title = "规则类型", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertRuleType sjyAlertRuleType)
    {
        List<SjyAlertRuleType> list = sjyAlertRuleTypeService.selectSjyAlertRuleTypeList(sjyAlertRuleType);
        ExcelUtil<SjyAlertRuleType> util = new ExcelUtil<SjyAlertRuleType>(SjyAlertRuleType.class);
        util.exportExcel(response, list, "规则类型数据");
    }

    /**
     * 获取规则类型详细信息
     */
    @Operation(summary = "获取规则类型详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:type:query')")
    @GetMapping(value = "/{ruleTypeId}")
    public AjaxResult<SjyAlertRuleType> getInfo(@Parameter(name = "ruleTypeId", description = "规则类型ID", required = true) @PathVariable("ruleTypeId") Long ruleTypeId)
    {
        return success(sjyAlertRuleTypeService.selectSjyAlertRuleTypeByRuleTypeId(ruleTypeId));
    }

    /**
     * 新增规则类型
     */
    @Operation(summary = "新增规则类型")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:type:add')")
    @Log(title = "规则类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "规则类型信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRuleType.class)))
                          @Valid @RequestBody SjyAlertRuleType sjyAlertRuleType)
    {
        return toAjax(sjyAlertRuleTypeService.insertSjyAlertRuleType(sjyAlertRuleType));
    }

    /**
     * 修改规则类型
     */
    @Operation(summary = "修改规则类型")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:type:edit')")
    @Log(title = "规则类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Integer> edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "规则类型信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRuleType.class)))
                           @Valid @RequestBody SjyAlertRuleType sjyAlertRuleType)
    {
        return toAjax(sjyAlertRuleTypeService.updateSjyAlertRuleType(sjyAlertRuleType));
    }

    /**
     * 删除规则类型
     */
    @Operation(summary = "删除规则类型")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:type:remove')")
    @Log(title = "规则类型", businessType = BusinessType.DELETE)
 @DeleteMapping("/{ruleTypeIds}")
    public AjaxResult<Integer> remove(@Parameter(name = "ruleTypeIds", description = "待删除规则类型ID数组", required = true) @PathVariable Long[] ruleTypeIds)
    {
        return toAjax(sjyAlertRuleTypeService.deleteSjyAlertRuleTypeByRuleTypeIds(ruleTypeIds));
    }


}
