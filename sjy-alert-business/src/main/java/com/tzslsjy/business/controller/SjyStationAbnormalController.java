package com.tzslsjy.business.controller;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzslsjy.business.bo.SjyAbnormalDataAddBo;
import com.tzslsjy.business.domain.SjyAbnormalData;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.domain.SjyStationAbnormalConfig;
import com.tzslsjy.business.service.ISjyAbnormalDataService;
import com.tzslsjy.business.service.ISjyAlertRuleArgService;
import com.tzslsjy.business.service.ISjyStationAbnormalConfigService;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测站异常预警管理Controller
 *
 * 注意：测站异常预警系统已重构为独立的水位异常和雨量异常预警系统：
 * - 水位异常预警使用新的规则类型54
 * - 雨量异常预警使用新的规则类型55
 * - 超时监测功能已被移除
 *
 * 此控制器保留以维持向后兼容性。
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Tag(name = "测站异常预警管理", description = "测站异常预警控制器")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/station/abnormal")
public class SjyStationAbnormalController extends BaseController {

    private final ISjyAbnormalDataService abnormalDataService;
    private final ISjyStationAbnormalConfigService configService;
    private final ISjyAlertRuleArgService alertRuleArgService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 确认异常数据并入库
     */
    @Operation(summary = "确认异常数据并入库")
    @Log(title = "异常数据确认", businessType = BusinessType.INSERT)
    @PostMapping("/confirm")
    public AjaxResult confirmAbnormalData(@RequestBody Map<String, Object> params) {
        try {
            String stcd = (String) params.get("stcd");
            String type = (String) params.get("type");
            BigDecimal val = new BigDecimal(params.get("val").toString());
            BigDecimal diff = params.get("diff") != null ? new BigDecimal(params.get("diff").toString()) : null;
            Date tm = new Date(); // 使用当前时间，或从参数中获取
            
            // 创建异常数据记录
            SjyAbnormalDataAddBo addBo = new SjyAbnormalDataAddBo();
            addBo.setStcd(stcd);
            addBo.setType(type);
            addBo.setVal(val);
            addBo.setDiff(diff);
            addBo.setTm(tm);
            addBo.setCreateTime(new Date());

            boolean success = abnormalDataService.insertByAddBo(addBo);
            
            if (success) {
                return AjaxResult.success("异常数据确认成功，已入库");
            } else {
                return AjaxResult.error("异常数据确认失败");
            }
        } catch (Exception e) {
            logger.error("确认异常数据失败: {}", e.getMessage(), e);
            return AjaxResult.error("确认异常数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取测站异常预警配置
     */
    @Operation(summary = "获取测站异常预警配置")
    @GetMapping("/config")
    public AjaxResult getStationConfig(@RequestParam(required = false) String stcd) {
        try {
            Map<String, Object> result = getStationAbnormalConfigFromRuleArg(stcd);
            if (result == null) {
                return AjaxResult.error("未找到测站异常预警配置");
            }
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("获取测站异常预警配置失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新测站异常预警配置
     */
    @Operation(summary = "更新测站异常预警配置")
    @Log(title = "异常预警配置", businessType = BusinessType.UPDATE)
    @PostMapping("/config/update")
    public AjaxResult updateStationConfig(@RequestBody Map<String, Object> configMap) {
        try {
            boolean success = updateStationAbnormalConfigToRuleArg(configMap);
            return success ? AjaxResult.success("配置更新成功") : AjaxResult.error("配置更新失败");
        } catch (Exception e) {
            logger.error("更新测站异常预警配置失败: {}", e.getMessage(), e);
            return AjaxResult.error("配置更新失败: " + e.getMessage());
        }
    }

    /**
     * 批量更新测站启用状态
     */
    @Operation(summary = "批量更新测站启用状态")
    @Log(title = "异常预警配置", businessType = BusinessType.UPDATE)
    @PostMapping("/config/batch-enable")
    public AjaxResult batchUpdateEnabled(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<String> stcds = (List<String>) params.get("stcds");
            Integer configType = (Integer) params.get("configType");
            Boolean enabled = (Boolean) params.get("enabled");
            
            boolean success = configService.batchUpdateEnabled(stcds, configType, enabled);
            return success ? AjaxResult.success("批量更新成功") : AjaxResult.error("批量更新失败");
        } catch (Exception e) {
            logger.error("批量更新测站启用状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新全局开关
     */
    @Operation(summary = "更新全局开关")
    @Log(title = "异常预警配置", businessType = BusinessType.UPDATE)
    @PostMapping("/config/global-switch")
    public AjaxResult updateGlobalSwitch(
            @Parameter(description = "配置类型") @RequestParam Integer configType,
            @Parameter(description = "启用状态") @RequestParam Boolean enabled) {
        try {
            boolean success = configService.updateGlobalSwitch(configType, enabled);
            return success ? AjaxResult.success("全局开关更新成功") : AjaxResult.error("全局开关更新失败");
        } catch (Exception e) {
            logger.error("更新全局开关失败: {}", e.getMessage(), e);
            return AjaxResult.error("全局开关更新失败: " + e.getMessage());
        }
    }

    /**
     * 一键启用/禁用所有检测
     */
    @Operation(summary = "一键启用/禁用所有检测")
    @Log(title = "异常预警配置", businessType = BusinessType.UPDATE)
    @PostMapping("/config/toggle-all")
    public AjaxResult toggleAllDetection(@Parameter(description = "启用状态") @RequestParam Boolean enabled) {
        try {
            boolean success = configService.toggleAllDetection(enabled);
            return success ? AjaxResult.success("一键切换成功") : AjaxResult.error("一键切换失败");
        } catch (Exception e) {
            logger.error("一键切换所有检测失败: {}", e.getMessage(), e);
            return AjaxResult.error("一键切换失败: " + e.getMessage());
        }
    }

    /**
     * 初始化默认配置
     */
    @Operation(summary = "初始化默认配置")
    @Log(title = "异常预警配置", businessType = BusinessType.INSERT)
    @PostMapping("/config/init")
    public AjaxResult initDefaultConfigs(@RequestBody List<String> stcds) {
        try {
            boolean success = configService.initDefaultConfigs(stcds);
            return success ? AjaxResult.success("默认配置初始化成功") : AjaxResult.error("默认配置初始化失败");
        } catch (Exception e) {
            logger.error("初始化默认配置失败: {}", e.getMessage(), e);
            return AjaxResult.error("初始化失败: " + e.getMessage());
        }
    }

    /**
     * 获取配置类型列表
     */
    @Operation(summary = "获取配置类型列表")
    @GetMapping("/config/types/{configType}")
    public AjaxResult getConfigsByType(@PathVariable Integer configType) {
        try {
            List<SjyStationAbnormalConfig> configs = configService.getConfigsByType(configType);
            return AjaxResult.success(configs);
        } catch (Exception e) {
            logger.error("获取配置类型列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取启用的配置列表
     */
    @Operation(summary = "获取启用的配置列表")
    @GetMapping("/config/enabled/{configType}")
    public AjaxResult getEnabledConfigsByType(@PathVariable Integer configType) {
        try {
            List<SjyStationAbnormalConfig> configs = configService.getEnabledConfigsByType(configType);
            return AjaxResult.success(configs);
        } catch (Exception e) {
            logger.error("获取启用的配置列表失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取配置失败: " + e.getMessage());
        }
    }

    /**
     * 从SjyAlertRuleArg获取测站异常配置
     */
    private Map<String, Object> getStationAbnormalConfigFromRuleArg(String stcd) {
        try {
            // 查找测站异常预警规则类型的参数配置
            SjyAlertRuleArg queryArg = new SjyAlertRuleArg();
            queryArg.setArgType("stationAbnormal");
            List<SjyAlertRuleArg> ruleArgs = alertRuleArgService.selectSjyAlertRuleArgList(queryArg);

            if (ruleArgs.isEmpty()) {
                logger.warn("未找到测站异常预警规则参数配置");
                return null;
            }

            // 解析第一个匹配的配置
            SjyAlertRuleArg ruleArg = ruleArgs.get(0);
            if (!StringUtils.hasText(ruleArg.getArgJson())) {
                logger.warn("测站异常预警规则参数配置为空");
                return null;
            }

            Map<String, Object> config = objectMapper.readValue(ruleArg.getArgJson(),
                    new TypeReference<Map<String, Object>>() {});

            // 如果指定了测站编号，检查该测站是否在配置的测站列表中
            if (stcd != null && config.containsKey("stcds")) {
                @SuppressWarnings("unchecked")
                List<String> configuredStcds = (List<String>) config.get("stcds");
                if (configuredStcds != null && !configuredStcds.contains(stcd)) {
                    logger.debug("测站 {} 不在配置的测站列表中", stcd);
                    return null;
                }
            }

            // 添加argId用于后续更新
            config.put("argId", ruleArg.getArgId());

            return config;

        } catch (Exception e) {
            logger.error("获取测站异常配置失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 更新测站异常配置到SjyAlertRuleArg
     */
    private boolean updateStationAbnormalConfigToRuleArg(Map<String, Object> configMap) {
        try {
            Long argId = null;
            if (configMap.containsKey("argId")) {
                argId = Long.valueOf(configMap.get("argId").toString());
                configMap.remove("argId"); // 移除argId，不保存到JSON中
            }

            if (argId == null) {
                // 如果没有argId，查找现有配置
                SjyAlertRuleArg queryArg = new SjyAlertRuleArg();
                queryArg.setArgType("stationAbnormal");
                List<SjyAlertRuleArg> ruleArgs = alertRuleArgService.selectSjyAlertRuleArgList(queryArg);

                if (!ruleArgs.isEmpty()) {
                    argId = ruleArgs.get(0).getArgId();
                }
            }

            if (argId == null) {
                logger.error("未找到要更新的规则参数配置");
                return false;
            }

            // 获取现有配置并更新
            SjyAlertRuleArg ruleArg = alertRuleArgService.selectSjyAlertRuleArgByArgId(argId);
            if (ruleArg == null) {
                logger.error("未找到argId为{}的规则参数配置", argId);
                return false;
            }

            // 将配置转换为JSON字符串
            String newArgJson = objectMapper.writeValueAsString(configMap);
            ruleArg.setArgJson(newArgJson);
            ruleArg.setUpdateTime(new Date());

            int result = alertRuleArgService.updateSjyAlertRuleArg(ruleArg);
            return result > 0;

        } catch (Exception e) {
            logger.error("更新测站异常配置失败: {}", e.getMessage(), e);
            return false;
        }
    }
}
