package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyFdStInfo;
import com.tzslsjy.business.service.ISjyFdStInfoService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 自动监测站基本信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Tag(name = "自动监测站基本信息管理", description = "自动监测站基本信息管理")
@RestController
@RequestMapping("/business/st/info")
public class SjyFdStInfoController extends BaseController
{
    @Autowired
    private ISjyFdStInfoService sjyFdStInfoService;

    /**
     * 查询自动监测站基本信息列表
     */
    @Operation(summary = "查询自动监测站基本信息列表")
    //@PreAuthorize("@ss.hasPermi('business:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyFdStInfo sjyFdStInfo)
    {
        startPage();
        List<SjyFdStInfo> list = sjyFdStInfoService.selectSjyFdStInfoList(sjyFdStInfo);
        return getDataTable(list);
    }

    /**
     * 导出自动监测站基本信息列表
     */
    @Operation(summary = "导出自动监测站基本信息列表")
    //@PreAuthorize("@ss.hasPermi('business:info:export')")
    @Log(title = "自动监测站基本信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyFdStInfo sjyFdStInfo)
    {
        List<SjyFdStInfo> list = sjyFdStInfoService.selectSjyFdStInfoList(sjyFdStInfo);
        ExcelUtil<SjyFdStInfo> util = new ExcelUtil<SjyFdStInfo>(SjyFdStInfo.class);
        util.exportExcel(response, list, "自动监测站基本信息数据");
    }

    /**
     * 获取自动监测站基本信息详细信息
     */
    @Operation(summary = "获取自动监测站基本信息详细信息")
    @Parameter(name = "stCode", description = "自动监测站基本信息ID", required = true)
    //@PreAuthorize("@ss.hasPermi('business:info:query')")
    @GetMapping(value = "/{stCode}")
    public AjaxResult getInfo(@PathVariable("stCode") String stCode)
    {
        return success(sjyFdStInfoService.selectSjyFdStInfoByStCode(stCode));
    }

    /**
     * 新增自动监测站基本信息
     */
    @Operation(summary = "新增自动监测站基本信息")
    //@PreAuthorize("@ss.hasPermi('business:info:add')")
    @Log(title = "自动监测站基本信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyFdStInfo sjyFdStInfo)
    {
        return toAjax(sjyFdStInfoService.insertSjyFdStInfo(sjyFdStInfo));
    }

    /**
     * 修改自动监测站基本信息
     */
    @Operation(summary = "修改自动监测站基本信息")
    //@PreAuthorize("@ss.hasPermi('business:info:edit')")
    @Log(title = "自动监测站基本信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyFdStInfo sjyFdStInfo)
    {
        return toAjax(sjyFdStInfoService.updateSjyFdStInfo(sjyFdStInfo));
    }

    /**
     * 删除自动监测站基本信息
     */
    @Operation(summary = "删除自动监测站基本信息")
    @Parameter(name = "stCodes", description = "自动监测站基本信息ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('business:info:remove')")
    @Log(title = "自动监测站基本信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{stCodes}")
    public AjaxResult remove(@PathVariable String[] stCodes)
    {
        return toAjax(sjyFdStInfoService.deleteSjyFdStInfoByStCodes(stCodes));
    }
}
