package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.github.pagehelper.PageHelper;
import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.service.ISjyAlertRuleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;

import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 预警规则Controller
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Tag(name = "预警规则管理", description = "预警规则管理")
@RestController
@RequestMapping("/business/alert/rule")
public class SjyAlertRuleController extends BaseController
{
    @Autowired
    private ISjyAlertRuleService sjyAlertRuleService;

    /**
     * 查询预警规则列表
     */
    @Operation(summary = "查询预警规则列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertRule sjyAlertRule)
    {
        if (sjyAlertRule.getPageSize()!=null&&sjyAlertRule.getPageNum()!=null) {
            PageHelper.startPage(sjyAlertRule.getPageNum(), sjyAlertRule.getPageSize());
        }
        List<SjyAlertRule> list = sjyAlertRuleService.selectSjyAlertRuleList(sjyAlertRule);
        return getDataTable(list);
    }

    /**
     * 导出预警规则列表
     */
    @Operation(summary = "导出预警规则列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:export')")
    @Log(title = "预警规则", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertRule sjyAlertRule)
    {
        List<SjyAlertRule> list = sjyAlertRuleService.selectSjyAlertRuleList(sjyAlertRule);
        ExcelUtil<SjyAlertRule> util = new ExcelUtil<SjyAlertRule>(SjyAlertRule.class);
        util.exportExcel(response, list, "预警规则数据");
    }
    /**
     * 获取预警规则详细信息
     */
    @Operation(summary = "获取预警规则详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:query')")
    @GetMapping(value = "/getFloodBaseRule")
    public AjaxResult<SjyAlertRule> getFloodBaseRule( )
    {
        return success(sjyAlertRuleService.getFloodBaseRule( ));
    }
    /**
     * 获取预警规则详细信息
     */
    @Operation(summary = "获取预警规则详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:query')")
    @GetMapping(value = "/{ruleId}")
    public AjaxResult<SjyAlertRule> getInfo(@Parameter(name = "ruleId", description = "预警规则ID", required = true) @PathVariable("ruleId") Long ruleId)
    {
        return success(sjyAlertRuleService.selectSjyAlertRuleById(ruleId));
    }

    /**
     * 新增预警规则
     */
    @Operation(summary = "新增预警规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:add')")
    @Log(title = "预警规则", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警规则信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRule.class)))
                          @Valid @RequestBody SjyAlertRule sjyAlertRule)
    {
        return toAjax(sjyAlertRuleService.insertSjyAlertRule(sjyAlertRule));
    }
    /**
     * 修改预警规则
     */
    @Operation(summary = "新增或修改预警规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:edit')")
    @Log(title = "预警规则", businessType = BusinessType.UPDATE)
    @PutMapping("/insertOrUpdate")
    public AjaxResult<Integer> insertOrUpdateSjyAlertRule(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警规则信息", required = true,
            content = @Content(schema = @Schema(implementation = SjyAlertRule.class)))
                                    @Valid @RequestBody SjyAlertRule sjyAlertRule)
    {
        return toAjax(sjyAlertRuleService.insertOrUpdateSjyAlertRule(sjyAlertRule));
    }
    /**
     * 修改预警规则
     */
    @Operation(summary = "非沿河村落-新增或修改预警规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:edit')")
    @Log(title = "预警规则", businessType = BusinessType.UPDATE)
    @PutMapping("/insertOrUpdateRiver")
    public AjaxResult<Integer> insertOrUpdateRiver(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警规则信息", required = true,
            content = @Content(schema = @Schema(implementation = SjyAlertRule.class)))
                                                          @Valid @RequestBody SjyAlertRule sjyAlertRule)
    {
        return toAjax(sjyAlertRuleService.insertOrUpdateRiver(sjyAlertRule));
    }
    /**
     * 修改预警规则
     */
    @Operation(summary = "修改预警规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:edit')")
    @Log(title = "预警规则", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Integer> edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警规则信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRule.class)))
                           @Valid @RequestBody SjyAlertRule sjyAlertRule)
    {
        return toAjax(sjyAlertRuleService.updateSjyAlertRule(sjyAlertRule));
    }

    /**
     * 删除预警规则
     */
    @Operation(summary = "删除预警规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:rule:remove')")
    @Log(title = "预警规则", businessType = BusinessType.DELETE)
 @DeleteMapping("/{ruleIds}")
    public AjaxResult<Integer> remove(@Parameter(name = "ruleIds", description = "待删除预警规则ID数组", required = true) @PathVariable Long[] ruleIds)
    {
        return toAjax(sjyAlertRuleService.deleteSjyAlertRuleByRuleIds(ruleIds));
    }
}
