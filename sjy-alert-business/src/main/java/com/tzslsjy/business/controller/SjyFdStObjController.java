package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyFdStObj;
import com.tzslsjy.business.service.ISjyFdStObjService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 自动监测站名录Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Tag(name = "自动监测站名录管理", description = "自动监测站名录管理")
@RestController
@RequestMapping("/business/st/obj")
public class SjyFdStObjController extends BaseController
{
    @Autowired
    private ISjyFdStObjService sjyFdStObjService;

    /**
     * 查询自动监测站名录列表
     */
    @Operation(summary = "查询自动监测站名录列表")
    //@PreAuthorize("@ss.hasPermi('business:obj:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyFdStObj sjyFdStObj)
    {
        startPage();
        List<SjyFdStObj> list = sjyFdStObjService.selectSjyFdStObjList(sjyFdStObj);
        return getDataTable(list);
    }

    /**
     * 导出自动监测站名录列表
     */
    @Operation(summary = "导出自动监测站名录列表")
    //@PreAuthorize("@ss.hasPermi('business:obj:export')")
    @Log(title = "自动监测站名录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyFdStObj sjyFdStObj)
    {
        List<SjyFdStObj> list = sjyFdStObjService.selectSjyFdStObjList(sjyFdStObj);
        ExcelUtil<SjyFdStObj> util = new ExcelUtil<SjyFdStObj>(SjyFdStObj.class);
        util.exportExcel(response, list, "自动监测站名录数据");
    }

    /**
     * 获取自动监测站名录详细信息
     */
    @Operation(summary = "获取自动监测站名录详细信息")
    @Parameter(name = "stCode", description = "自动监测站名录ID", required = true)
    //@PreAuthorize("@ss.hasPermi('business:obj:query')")
    @GetMapping(value = "/{stCode}")
    public AjaxResult getInfo(@PathVariable("stCode") String stCode)
    {
        return success(sjyFdStObjService.selectSjyFdStObjByStCode(stCode));
    }

    /**
     * 新增自动监测站名录
     */
    @Operation(summary = "新增自动监测站名录")
    //@PreAuthorize("@ss.hasPermi('business:obj:add')")
    @Log(title = "自动监测站名录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyFdStObj sjyFdStObj)
    {
        return toAjax(sjyFdStObjService.insertSjyFdStObj(sjyFdStObj));
    }

    /**
     * 修改自动监测站名录
     */
    @Operation(summary = "修改自动监测站名录")
    //@PreAuthorize("@ss.hasPermi('business:obj:edit')")
    @Log(title = "自动监测站名录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyFdStObj sjyFdStObj)
    {
        return toAjax(sjyFdStObjService.updateSjyFdStObj(sjyFdStObj));
    }

    /**
     * 删除自动监测站名录
     */
    @Operation(summary = "删除自动监测站名录")
    @Parameter(name = "stCodes", description = "自动监测站名录ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('business:obj:remove')")
    @Log(title = "自动监测站名录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{stCodes}")
    public AjaxResult remove(@PathVariable String[] stCodes)
    {
        return toAjax(sjyFdStObjService.deleteSjyFdStObjByStCodes(stCodes));
    }
}
