package com.tzslsjy.business.controller;

import com.tzslsjy.business.domain.comm.BusinessCommCity;
import com.tzslsjy.business.domain.comm.BusinessCommCItyTree; // 确保导入路径正确
import com.tzslsjy.business.service.IStPptnRService;
import com.tzslsjy.business.service.comm.ICommCityService;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation; // 更改导入
import io.swagger.v3.oas.annotations.tags.Tag; // 更改导入
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/9 15:02
 * @project water-basic-app
 * @company tzslsd
 */

@Tag(name = "测站基础数据", description = "测站基础数据") // 更改注解
@RestController
@Slf4j
@RequestMapping("/commCit")
public class StBasicDataController extends BaseController {

    @Resource(name = "businessCommCityService")
    ICommCityService commCityService;


    @PostMapping("/getCitys")
    @Operation(summary = "获取城市") // 更改注解
    public CommonResult<List<BusinessCommCity>> pageList(BusinessCommCity vo) {
        return CommonResult.success(commCityService.pageList(vo));
    }

    @PostMapping("/getCityTree")
    @Operation(summary = "获取城市树") // 更改注解
    public CommonResult<List<BusinessCommCItyTree>> getCityTree(BusinessCommCity vo) {
        return CommonResult.success(commCityService.getTree(vo));
    }

}
