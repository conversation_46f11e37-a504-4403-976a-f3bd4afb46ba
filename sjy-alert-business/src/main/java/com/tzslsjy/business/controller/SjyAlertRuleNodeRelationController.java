package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyAlertRuleNodeRelation;
import com.tzslsjy.business.service.ISjyAlertRuleNodeRelationService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 规则接收节点关联Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Tag(name = "规则接收节点关联管理", description = "规则接收节点关联管理")
@RestController
@RequestMapping("/business/alert/rn/relation")
public class SjyAlertRuleNodeRelationController extends BaseController
{
    @Autowired
    private ISjyAlertRuleNodeRelationService sjyAlertRuleNodeRelationService;

    /**
     * 查询规则接收节点关联列表
     */
    @Operation(summary = "查询规则接收节点关联列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:relation:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation)
    {
        startPage();
        List<SjyAlertRuleNodeRelation> list = sjyAlertRuleNodeRelationService.selectSjyAlertRuleNodeRelationList(sjyAlertRuleNodeRelation);
        return getDataTable(list);
    }

    /**
     * 导出规则接收节点关联列表
     */
    @Operation(summary = "导出规则接收节点关联列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:relation:export')")
    @Log(title = "规则接收节点关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation)
    {
        List<SjyAlertRuleNodeRelation> list = sjyAlertRuleNodeRelationService.selectSjyAlertRuleNodeRelationList(sjyAlertRuleNodeRelation);
        ExcelUtil<SjyAlertRuleNodeRelation> util = new ExcelUtil<SjyAlertRuleNodeRelation>(SjyAlertRuleNodeRelation.class);
        util.exportExcel(response, list, "规则接收节点关联数据");
    }

    /**
     * 获取规则接收节点关联详细信息
     */
    @Operation(summary = "获取规则接收节点关联详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:relation:query')")
    @GetMapping(value = "/{relationId}")
    public AjaxResult<SjyAlertRuleNodeRelation> getInfo(@Parameter(name = "relationId", description = "规则接收节点关联ID", required = true) @PathVariable("relationId") Long relationId)
    {
        return success(sjyAlertRuleNodeRelationService.selectSjyAlertRuleNodeRelationByRelationId(relationId));
    }

    /**
     * 新增规则接收节点关联
     */
    @Operation(summary = "新增规则接收节点关联")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:relation:add')")
    @Log(title = "规则接收节点关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "规则接收节点关联信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRuleNodeRelation.class)))
                          @Valid @RequestBody SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation)
    {
        return toAjax(sjyAlertRuleNodeRelationService.insertSjyAlertRuleNodeRelation(sjyAlertRuleNodeRelation));
    }

    /**
     * 修改规则接收节点关联
     */
    @Operation(summary = "修改规则接收节点关联")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:relation:edit')")
    @Log(title = "规则接收节点关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Integer> edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "规则接收节点关联信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRuleNodeRelation.class)))
                           @Valid @RequestBody SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation)
    {
        return toAjax(sjyAlertRuleNodeRelationService.updateSjyAlertRuleNodeRelation(sjyAlertRuleNodeRelation));
    }

    /**
     * 删除规则接收节点关联
     */
    @Operation(summary = "删除规则接收节点关联")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:relation:remove')")
    @Log(title = "规则接收节点关联", businessType = BusinessType.DELETE)
 @DeleteMapping("/{relationIds}")
    public AjaxResult<Integer> remove(@Parameter(name = "relationIds", description = "待删除规则接收节点关联ID数组", required = true) @PathVariable Long[] relationIds)
    {
        return toAjax(sjyAlertRuleNodeRelationService.deleteSjyAlertRuleNodeRelationByRelationIds(relationIds));
    }
}
