package com.tzslsjy.business.controller;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.service.ISjyAlertRuleService;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.service.scheduler.AlertRuleSchedulerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 预警规则调度管理Controller
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Tag(name = "预警规则调度管理", description = "预警规则动态定时任务管理")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/alert/scheduler")
public class AlertRuleSchedulerController extends BaseController {

    private final AlertRuleSchedulerService alertRuleSchedulerService;
    private final ISjyAlertRuleService alertRuleService;

    /**
     * 获取所有预警规则任务状态
     */
    @Operation(summary = "获取所有预警规则任务状态")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:list')")
    @GetMapping("/status/all")
    public AjaxResult getAllJobStatus() {
        try {
            List<AlertRuleSchedulerService.AlertRuleJobStatus> statusList = 
                    alertRuleSchedulerService.getAllAlertRuleJobStatus();
            return AjaxResult.success(statusList);
        } catch (Exception e) {
            logger.error("获取所有预警规则任务状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 获取指定预警规则任务状态
     */
    @Operation(summary = "获取指定预警规则任务状态")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:query')")
    @GetMapping("/status/{ruleId}")
    public AjaxResult getJobStatus(@PathVariable Long ruleId) {
        try {
            AlertRuleSchedulerService.AlertRuleJobStatus status = 
                    alertRuleSchedulerService.getAlertRuleJobStatus(ruleId);
            return AjaxResult.success(status);
        } catch (Exception e) {
            logger.error("获取预警规则任务状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 刷新所有预警规则任务
     */
    @Operation(summary = "刷新所有预警规则任务")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:refresh')")
    @Log(title = "预警规则调度", businessType = BusinessType.UPDATE)
    @PostMapping("/refresh")
    public AjaxResult refreshAllJobs() {
        try {
            alertRuleSchedulerService.refreshAllAlertRules();
            return AjaxResult.success("刷新所有预警规则任务成功");
        } catch (Exception e) {
            logger.error("刷新所有预警规则任务失败: {}", e.getMessage(), e);
            return AjaxResult.error("刷新任务失败: " + e.getMessage());
        }
    }

    /**
     * 创建预警规则任务
     */
    @Operation(summary = "创建预警规则任务")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:add')")
    @Log(title = "预警规则调度", businessType = BusinessType.INSERT)
    @PostMapping("/create/{ruleId}")
    public AjaxResult createJob(@PathVariable Long ruleId) {
        try {
            SjyAlertRule rule = alertRuleService.selectSjyAlertRuleById(ruleId);
            if (rule == null) {
                return AjaxResult.error("预警规则不存在");
            }

            boolean success = alertRuleSchedulerService.createAlertRuleJob(rule);
            return success ? AjaxResult.success("创建任务成功") : AjaxResult.error("创建任务失败");
        } catch (Exception e) {
            logger.error("创建预警规则任务失败: {}", e.getMessage(), e);
            return AjaxResult.error("创建任务失败: " + e.getMessage());
        }
    }

    /**
     * 更新预警规则任务
     */
    @Operation(summary = "更新预警规则任务")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:edit')")
    @Log(title = "预警规则调度", businessType = BusinessType.UPDATE)
    @PostMapping("/update/{ruleId}")
    public AjaxResult updateJob(@PathVariable Long ruleId) {
        try {
            SjyAlertRule rule = alertRuleService.selectSjyAlertRuleById(ruleId);
            if (rule == null) {
                return AjaxResult.error("预警规则不存在");
            }

            boolean success = alertRuleSchedulerService.updateAlertRuleJob(rule);
            return success ? AjaxResult.success("更新任务成功") : AjaxResult.error("更新任务失败");
        } catch (Exception e) {
            logger.error("更新预警规则任务失败: {}", e.getMessage(), e);
            return AjaxResult.error("更新任务失败: " + e.getMessage());
        }
    }

    /**
     * 删除预警规则任务
     */
    @Operation(summary = "删除预警规则任务")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:remove')")
    @Log(title = "预警规则调度", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ruleId}")
    public AjaxResult deleteJob(@PathVariable Long ruleId) {
        try {
            boolean success = alertRuleSchedulerService.deleteAlertRuleJob(ruleId);
            return success ? AjaxResult.success("删除任务成功") : AjaxResult.error("删除任务失败");
        } catch (Exception e) {
            logger.error("删除预警规则任务失败: {}", e.getMessage(), e);
            return AjaxResult.error("删除任务失败: " + e.getMessage());
        }
    }

    /**
     * 暂停预警规则任务
     */
    @Operation(summary = "暂停预警规则任务")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:pause')")
    @Log(title = "预警规则调度", businessType = BusinessType.UPDATE)
    @PostMapping("/pause/{ruleId}")
    public AjaxResult pauseJob(@PathVariable Long ruleId) {
        try {
            boolean success = alertRuleSchedulerService.pauseAlertRuleJob(ruleId);
            return success ? AjaxResult.success("暂停任务成功") : AjaxResult.error("暂停任务失败");
        } catch (Exception e) {
            logger.error("暂停预警规则任务失败: {}", e.getMessage(), e);
            return AjaxResult.error("暂停任务失败: " + e.getMessage());
        }
    }

    /**
     * 恢复预警规则任务
     */
    @Operation(summary = "恢复预警规则任务")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:resume')")
    @Log(title = "预警规则调度", businessType = BusinessType.UPDATE)
    @PostMapping("/resume/{ruleId}")
    public AjaxResult resumeJob(@PathVariable Long ruleId) {
        try {
            boolean success = alertRuleSchedulerService.resumeAlertRuleJob(ruleId);
            return success ? AjaxResult.success("恢复任务成功") : AjaxResult.error("恢复任务失败");
        } catch (Exception e) {
            logger.error("恢复预警规则任务失败: {}", e.getMessage(), e);
            return AjaxResult.error("恢复任务失败: " + e.getMessage());
        }
    }

    /**
     * 立即执行预警规则任务
     */
    @Operation(summary = "立即执行预警规则任务")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:execute')")
    @Log(title = "预警规则调度", businessType = BusinessType.OTHER)
    @PostMapping("/execute/{ruleId}")
    public AjaxResult executeJobNow(@PathVariable Long ruleId) {
        try {
            boolean success = alertRuleSchedulerService.executeAlertRuleNow(ruleId);
            return success ? AjaxResult.success("立即执行任务成功") : AjaxResult.error("立即执行任务失败");
        } catch (Exception e) {
            logger.error("立即执行预警规则任务失败: {}", e.getMessage(), e);
            return AjaxResult.error("立即执行任务失败: " + e.getMessage());
        }
    }

    /**
     * 检查预警规则任务是否存在
     */
    @Operation(summary = "检查预警规则任务是否存在")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:query')")
    @GetMapping("/exists/{ruleId}")
    public AjaxResult checkJobExists(@PathVariable Long ruleId) {
        try {
            boolean exists = alertRuleSchedulerService.isAlertRuleJobExists(ruleId);
            return AjaxResult.success("检查完成", exists);
        } catch (Exception e) {
            logger.error("检查预警规则任务是否存在失败: {}", e.getMessage(), e);
            return AjaxResult.error("检查失败: " + e.getMessage());
        }
    }

    /**
     * 批量操作预警规则任务
     */
    @Operation(summary = "批量操作预警规则任务")
    @PreAuthorize("@ss.hasPermi('alert:scheduler:batch')")
    @Log(title = "预警规则调度", businessType = BusinessType.UPDATE)
    @PostMapping("/batch/{operation}")
    public AjaxResult batchOperation(
            @Parameter(description = "操作类型：pause, resume, delete, execute") @PathVariable String operation,
            @RequestBody List<Long> ruleIds) {
        try {
            int successCount = 0;
            int failCount = 0;

            for (Long ruleId : ruleIds) {
                boolean success = false;
                switch (operation.toLowerCase()) {
                    case "pause":
                        success = alertRuleSchedulerService.pauseAlertRuleJob(ruleId);
                        break;
                    case "resume":
                        success = alertRuleSchedulerService.resumeAlertRuleJob(ruleId);
                        break;
                    case "delete":
                        success = alertRuleSchedulerService.deleteAlertRuleJob(ruleId);
                        break;
                    case "execute":
                        success = alertRuleSchedulerService.executeAlertRuleNow(ruleId);
                        break;
                    default:
                        return AjaxResult.error("不支持的操作类型: " + operation);
                }

                if (success) {
                    successCount++;
                } else {
                    failCount++;
                }
            }

            return AjaxResult.success(String.format("批量%s操作完成，成功: %d, 失败: %d", 
                    operation, successCount, failCount));

        } catch (Exception e) {
            logger.error("批量操作预警规则任务失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量操作失败: " + e.getMessage());
        }
    }
}
