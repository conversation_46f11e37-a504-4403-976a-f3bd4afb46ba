package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.ISjyAlertSmsTemplateService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 短信模板Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Tag(name = "短信模板管理", description = "短信模板管理")
@RestController
@RequestMapping("/business/alert/sms/template")
public class SjyAlertSmsTemplateController extends BaseController
{
    @Autowired
    private ISjyAlertSmsTemplateService sjyAlertSmsTemplateService;

    /**
     * 查询短信模板列表
     */
    @Operation(summary = "查询短信模板列表")
    //@PreAuthorize("@ss.hasPermi('system:template:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyAlertSmsTemplate sjyAlertSmsTemplate)
    {
        startPage();
        List<SjyAlertSmsTemplate> list = sjyAlertSmsTemplateService.selectSjyAlertSmsTemplateList(sjyAlertSmsTemplate);
        return getDataTable(list);
    }

    /**
     * 导出短信模板列表
     */
    @Operation(summary = "导出短信模板列表")
    //@PreAuthorize("@ss.hasPermi('system:template:export')")
    @Log(title = "短信模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyAlertSmsTemplate sjyAlertSmsTemplate)
    {
        List<SjyAlertSmsTemplate> list = sjyAlertSmsTemplateService.selectSjyAlertSmsTemplateList(sjyAlertSmsTemplate);
        ExcelUtil<SjyAlertSmsTemplate> util = new ExcelUtil<SjyAlertSmsTemplate>(SjyAlertSmsTemplate.class);
        util.exportExcel(response, list, "短信模板数据");
    }

    /**
     * 获取短信模板详细信息
     */
    @Operation(summary = "获取短信模板详细信息")
    @Parameter(name = "templateId", description = "短信模板ID", required = true)
    //@PreAuthorize("@ss.hasPermi('system:template:query')")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId)
    {
        return success(sjyAlertSmsTemplateService.selectSjyAlertSmsTemplateByTemplateId(templateId));
    }

    /**
     * 新增短信模板
     */
    @Operation(summary = "新增短信模板")
    //@PreAuthorize("@ss.hasPermi('system:template:add')")
    @Log(title = "短信模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyAlertSmsTemplate sjyAlertSmsTemplate)
    {
        return toAjax(sjyAlertSmsTemplateService.insertSjyAlertSmsTemplate(sjyAlertSmsTemplate));
    }

    /**
     * 修改短信模板
     */
    @Operation(summary = "修改短信模板")
    //@PreAuthorize("@ss.hasPermi('system:template:edit')")
    @Log(title = "短信模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyAlertSmsTemplate sjyAlertSmsTemplate)
    {
        return toAjax(sjyAlertSmsTemplateService.updateSjyAlertSmsTemplate(sjyAlertSmsTemplate));
    }

    /**
     * 删除短信模板
     */
    @Operation(summary = "删除短信模板")
    @Parameter(name = "templateIds", description = "短信模板ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('system:template:remove')")
    @Log(title = "短信模板", businessType = BusinessType.DELETE)
	@DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds)
    {
        return toAjax(sjyAlertSmsTemplateService.deleteSjyAlertSmsTemplateByTemplateIds(templateIds));
    }
}
