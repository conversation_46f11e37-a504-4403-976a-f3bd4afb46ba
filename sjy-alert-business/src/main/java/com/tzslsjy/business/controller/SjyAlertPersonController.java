package com.tzslsjy.business.controller;

import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.service.ILhDataImportService;
import com.tzslsjy.business.service.ISjyAlertPersonService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam; // Added
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile; // Added
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;

import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 人员Controller
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Tag(name = "人员管理", description = "人员管理")
@RestController
@RequestMapping("/business/alert/person")
public class SjyAlertPersonController extends BaseController
{
    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService;



    /**
     * 查询人员列表
     */
    @Operation(summary = "查询人员列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:person:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertPerson sjyAlertPerson)
    {
        startPage();
        List<SjyAlertPerson> list = sjyAlertPersonService.selectSjyAlertPersonList(sjyAlertPerson);
        return getDataTable(list);
    }

    /**
     * 导出人员列表
     */
    @Operation(summary = "导出人员列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:person:export')")
    @Log(title = "人员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertPerson sjyAlertPerson)
    {
        List<SjyAlertPerson> list = sjyAlertPersonService.selectSjyAlertPersonList(sjyAlertPerson);
        ExcelUtil<SjyAlertPerson> util = new ExcelUtil<SjyAlertPerson>(SjyAlertPerson.class);
        util.exportExcel(response, list, "人员数据");
    }

    /**
     * 获取人员详细信息
     */
    @Operation(summary = "获取人员详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:person:query')")
    @GetMapping(value = "/{personId}")
    public AjaxResult<SjyAlertPerson> getInfo(@Parameter(name = "personId", description = "人员ID", required = true) @PathVariable("personId") String personId)
    {
        return success(sjyAlertPersonService.selectSjyAlertPersonByPersonId(personId));
    }

    /**
     * 新增人员
     */
    @Operation(summary = "新增人员")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:person:add')")
    @Log(title = "人员", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "人员信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertPerson.class)))
                          @Valid @RequestBody SjyAlertPerson sjyAlertPerson)
    {
        return toAjax(sjyAlertPersonService.insertSjyAlertPerson(sjyAlertPerson));
    }

    /**
     * 修改人员
     */
    @Operation(summary = "修改人员")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:person:edit')")
    @Log(title = "人员", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Integer> edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "人员信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertPerson.class)))
                           @Valid @RequestBody SjyAlertPerson sjyAlertPerson)
    {
        return toAjax(sjyAlertPersonService.updateSjyAlertPerson(sjyAlertPerson));
    }

    /**
     * 删除人员
     */
    @Operation(summary = "删除人员")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:person:remove')")
    @Log(title = "人员", businessType = BusinessType.DELETE)
 @DeleteMapping("/{personIds}")
    public AjaxResult<Integer> remove(@Parameter(name = "personIds", description = "待删除人员ID数组", required = true) @PathVariable String[] personIds)
    {
        return toAjax(sjyAlertPersonService.deleteSjyAlertPersonByPersonIds(personIds));
    }


}
