package com.tzslsjy.business.controller;

import java.util.List;
import java.util.Arrays;

import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tzslsjy.business.vo.SjyAlertNodePostVo;
import com.tzslsjy.business.bo.SjyAlertNodePostQueryBo;
import com.tzslsjy.business.bo.SjyAlertNodePostAddBo;
import com.tzslsjy.business.bo.SjyAlertNodePostEditBo;
import com.tzslsjy.business.service.ISjyAlertNodePostService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 节点职位Controller
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Tag(name = "节点职位管理", description = "节点职位控制器")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/business/alert/post")
public class SjyAlertNodePostController extends BaseController {

    private final ISjyAlertNodePostService iSjyAlertNodePostService;

    /**
     * 查询节点职位列表
     */
    @Operation(summary = "查询节点职位列表")
    //@PreAuthorize("@ss.hasPermi('business:alert:list')")
    @GetMapping("/list")
    public TableDataInfo<SjyAlertNodePostVo> list(SjyAlertNodePostQueryBo bo) {
        startPage();
        List<SjyAlertNodePostVo> list = iSjyAlertNodePostService.queryList(bo);
        return getDataTable(list);
    }

    /**
     * 导出节点职位列表
     */
    @Operation(summary = "导出节点职位列表")
    //@PreAuthorize("@ss.hasPermi('business:alert:export')")
    @Log(title = "节点职位", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult<SjyAlertNodePostVo> export(SjyAlertNodePostQueryBo bo) {
        List<SjyAlertNodePostVo> list = iSjyAlertNodePostService.queryList(bo);
        ExcelUtil<SjyAlertNodePostVo> util = new ExcelUtil<SjyAlertNodePostVo>(SjyAlertNodePostVo.class);
        return util.exportExcel(list, "节点职位");
    }

    /**
     * 获取节点职位详细信息
     */
    @Operation(summary = "获取节点职位详细信息")
    //@PreAuthorize("@ss.hasPermi('business:alert:query')")
    @GetMapping("/{id}")
    public AjaxResult<SjyAlertNodePostVo> getInfo(@PathVariable("id" ) Long id) {
        return AjaxResult.success(iSjyAlertNodePostService.queryById(id));
    }

    /**
     * 新增节点职位
     */
    @Operation(summary = "新增节点职位")
    //@PreAuthorize("@ss.hasPermi('business:alert:add')")
    @Log(title = "节点职位", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SjyAlertNodePostAddBo bo) {
        return toAjax(iSjyAlertNodePostService.insertByAddBo(bo) ? 1 : 0);
    }

    /**
     * 修改节点职位
     */
    @Operation(summary = "修改节点职位")
    //@PreAuthorize("@ss.hasPermi('business:alert:edit')")
    @Log(title = "节点职位", businessType = BusinessType.UPDATE)
    @PutMapping()
    public AjaxResult edit(@RequestBody SjyAlertNodePostEditBo bo) {
        return toAjax(iSjyAlertNodePostService.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除节点职位
     */
    @Operation(summary = "删除节点职位")
    //@PreAuthorize("@ss.hasPermi('business:alert:remove')")
    @Log(title = "节点职位" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(iSjyAlertNodePostService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
