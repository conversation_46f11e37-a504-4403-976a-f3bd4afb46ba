package com.tzslsjy.business.controller;

import com.tzslsjy.business.vo.StPptnHistDVo;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.business.bo.*;

import com.tzslsjy.business.service.IStPptnHistDService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Arrays;

/**
 * 单站多年日降雨Controller
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/stPptnHistD")
@Tag(name = "单站多年日降雨管理", description = "单站多年日降雨管理")
public class StPptnHistDController extends BaseController {

    private final IStPptnHistDService iStPptnHistDService;

    /**
     * 查询单站多年日降雨列表
     */
    @Operation(summary = "查询单站多年日降雨列表")
    @GetMapping("/list")
    public TableDataInfo<StPptnHistDVo> list(StPptnHistDQueryBo bo) {
        startPage();
        List<StPptnHistDVo> list = iStPptnHistDService.queryList(bo);
        return getDataTable(list);
    }

    /**
     * 单站历史降雨分析
     */
    @Operation(summary = "单站历史降雨分析")
    @PostMapping("/droughtAnaly")
    public AjaxResult<List<StPptnHistRespVo>> droughtAnaly(@RequestBody StPptnHistReqVo vo) {

        List<StPptnHistRespVo> list = iStPptnHistDService.droughtAnaly(vo);
        return success(list);
    }

    /**
     * 雨量线
     */
    @Operation(summary = "雨量线")
    @PostMapping("/rainLine")
    public AjaxResult<List<StPptnHistRainLineRespVo>> rainLine(@RequestBody StPptnHistReqVo bo) {

        List<StPptnHistRainLineRespVo> list = iStPptnHistDService.rainLine(bo);
        return success(list);
    }

    /**
     * 导出单站多年日降雨列表
     */
    @Operation(summary = "导出单站多年日降雨列表")
    @PostMapping("/export")
    public AjaxResult<StPptnHistDVo> export(StPptnHistDQueryBo bo) {
        List<StPptnHistDVo> list = iStPptnHistDService.queryList(bo);
        ExcelUtil<StPptnHistDVo> util = new ExcelUtil<StPptnHistDVo>(StPptnHistDVo.class);
        return util.exportExcel(list, "单站多年日降雨");
    }

    /**
     * 获取单站多年日降雨详细信息
     */
    @Operation(summary = "获取单站多年日降雨详细信息")
    @GetMapping("/{stcd}")
    public AjaxResult<StPptnHistDVo> getInfo(@PathVariable("stcd" ) String stcd) {
        return success(iStPptnHistDService.queryById(stcd));
    }

    /**
     * 新增单站多年日降雨
     */
    @Operation(summary = "新增单站多年日降雨")
    @PostMapping()
    public AjaxResult add(@RequestBody StPptnHistDAddBo bo) {
        return toAjax(iStPptnHistDService.insertByAddBo(bo));
    }

    /**
     * 修改单站多年日降雨
     */
    @Operation(summary = "修改单站多年日降雨")
    @PutMapping()
    public AjaxResult edit(@RequestBody StPptnHistDEditBo bo) {
        return toAjax(iStPptnHistDService.updateByEditBo(bo));
    }

    /**
     * 删除单站多年日降雨
     */
    @Operation(summary = "删除单站多年日降雨")
    @DeleteMapping("/{stcds}")
    public AjaxResult remove(@PathVariable String[] stcds) {
        return toAjax(iStPptnHistDService.deleteWithValidByIds(Arrays.asList(stcds), true));
    }
}