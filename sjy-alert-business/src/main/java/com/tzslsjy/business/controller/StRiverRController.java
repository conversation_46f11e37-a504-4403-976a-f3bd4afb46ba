package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.StRiverR;
import com.tzslsjy.business.service.IStRiverRService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 河道水情Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Tag(name = "河道水情管理", description = "河道水情管理")
@RestController
@RequestMapping("/business/st/river")
public class StRiverRController extends BaseController
{
    @Autowired
    private IStRiverRService stRiverRService;

    /**
     * 查询河道水情列表
     */
    @Operation(summary = "查询河道水情列表")
    //@PreAuthorize("@ss.hasPermi('business:river:list')")
    @GetMapping("/list")
    public TableDataInfo list(StRiverR stRiverR)
    {
        startPage();
        List<StRiverR> list = stRiverRService.selectStRiverRList(stRiverR);
        return getDataTable(list);
    }

    /**
     * 导出河道水情列表
     */
    @Operation(summary = "导出河道水情列表")
    //@PreAuthorize("@ss.hasPermi('business:river:export')")
    @Log(title = "河道水情", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StRiverR stRiverR)
    {
        List<StRiverR> list = stRiverRService.selectStRiverRList(stRiverR);
        ExcelUtil<StRiverR> util = new ExcelUtil<StRiverR>(StRiverR.class);
        util.exportExcel(response, list, "河道水情数据");
    }

    /**
     * 获取河道水情详细信息
     */
    @Operation(summary = "获取河道水情详细信息")
    @Parameter(name = "STCD", description = "河道水情ID", required = true)
    //@PreAuthorize("@ss.hasPermi('business:river:query')")
    @GetMapping(value = "/{STCD}")
    public AjaxResult getInfo(@PathVariable("STCD") String STCD)
    {
        return success(stRiverRService.selectStRiverRBySTCD(STCD));
    }

    /**
     * 新增河道水情
     */
    @Operation(summary = "新增河道水情")
    //@PreAuthorize("@ss.hasPermi('business:river:add')")
    @Log(title = "河道水情", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody StRiverR stRiverR)
    {
        return toAjax(stRiverRService.insertStRiverR(stRiverR));
    }

    /**
     * 修改河道水情
     */
    @Operation(summary = "修改河道水情")
    //@PreAuthorize("@ss.hasPermi('business:river:edit')")
    @Log(title = "河道水情", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody StRiverR stRiverR)
    {
        return toAjax(stRiverRService.updateStRiverR(stRiverR));
    }

    /**
     * 删除河道水情
     */
    @Operation(summary = "删除河道水情")
    @Parameter(name = "STCDs", description = "河道水情ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('business:river:remove')")
    @Log(title = "河道水情", businessType = BusinessType.DELETE)
	@DeleteMapping("/{STCDs}")
    public AjaxResult remove(@PathVariable String[] STCDs)
    {
        return toAjax(stRiverRService.deleteStRiverRBySTCDs(STCDs));
    }
}
