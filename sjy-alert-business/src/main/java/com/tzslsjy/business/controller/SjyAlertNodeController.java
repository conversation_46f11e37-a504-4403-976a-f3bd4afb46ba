package com.tzslsjy.business.controller;

import java.util.Date;
import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.tzslsjy.business.domain.SjyAlertNode;
import com.tzslsjy.business.service.ISjyAlertNodeService;
import com.tzslsjy.business.domain.vo.NodeTreeVO;
import com.tzslsjy.business.service.RefactoredAlertService;
import com.tzslsjy.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;

import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 节点Controller
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Tag(name = "节点管理", description = "节点管理")
@RestController
@RequestMapping("/business/alert/node")
public class SjyAlertNodeController extends BaseController
{
    @Autowired
    private ISjyAlertNodeService sjyAlertNodeService;
    @Autowired
    private RefactoredAlertService alertService;
    /**
     * 查询节点列表
     */
    @Operation(summary = "查询节点列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:node:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertNode sjyAlertNode)
    {
        startPage();
        List<SjyAlertNode> list = sjyAlertNodeService.selectSjyAlertNodeList(sjyAlertNode);
        return getDataTable(list);
    }

    @Operation(summary = "获取节点树（包含人员）")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    @GetMapping("/tree")
    public AjaxResult<List<NodeTreeVO>> getNodeTreeWithPersons(@ParameterObject SjyAlertNode sjyAlertNode,
                                             @Parameter(name = "personId", description = "人员ID (可选，用于特定高亮或勾选等业务)") Long personId) {
        List<NodeTreeVO> tree = sjyAlertNodeService.buildNodeTreeWithPersons(sjyAlertNode, personId);
        return AjaxResult.success(tree);
    }

    @Operation(summary = "测试告警规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "测试调用成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = CommonResult.class)))
    })
    @GetMapping("/test")
    public CommonResult test(@Parameter(name = "ruleId", description = "规则ID", required = true) Long ruleId)
    {
        boolean b = alertService.processAlert(ruleId, new Date());
        return CommonResult.success("测试成功");
    }

    /**
     * 导出节点列表
     */
    @Operation(summary = "导出节点列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:node:export')")
    @Log(title = "节点", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertNode sjyAlertNode)
    {
        List<SjyAlertNode> list = sjyAlertNodeService.selectSjyAlertNodeList(sjyAlertNode);
        ExcelUtil<SjyAlertNode> util = new ExcelUtil<SjyAlertNode>(SjyAlertNode.class);
        util.exportExcel(response, list, "节点数据");
    }

    /**
     * 获取节点详细信息
     */
    @Operation(summary = "获取节点详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:node:query')")
    @GetMapping(value = "/{nodeId}")
    public AjaxResult<SjyAlertNode> getInfo(@Parameter(name = "nodeId", description = "节点ID", required = true) @PathVariable("nodeId") String nodeId)
    {
        return success(sjyAlertNodeService.selectSjyAlertNodeByNodeId(nodeId));
    }

    /**
     * 新增节点
     */
    @Operation(summary = "新增节点")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:node:add')")
    @Log(title = "节点", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "节点信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertNode.class)))
                          @Valid @RequestBody SjyAlertNode sjyAlertNode)
    {
        return toAjax(sjyAlertNodeService.insertSjyAlertNode(sjyAlertNode));
    }

    /**
     * 修改节点
     */
    @Operation(summary = "修改节点")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:node:edit')")
    @Log(title = "节点", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Integer> edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "节点信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertNode.class)))
                           @Valid @RequestBody SjyAlertNode sjyAlertNode)
    {
        return toAjax(sjyAlertNodeService.updateSjyAlertNode(sjyAlertNode));
    }

    /**
     * 删除节点
     */
    @Operation(summary = "删除节点")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:node:remove')")
    @Log(title = "节点", businessType = BusinessType.DELETE)
 @DeleteMapping("/{nodeIds}")
    public AjaxResult<Integer> remove(@Parameter(name = "nodeIds", description = "待删除节点ID数组", required = true) @PathVariable String[] nodeIds)
    {
        return toAjax(sjyAlertNodeService.deleteSjyAlertNodeByNodeIds(nodeIds));
    }
}
