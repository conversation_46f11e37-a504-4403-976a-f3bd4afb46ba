package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.github.pagehelper.PageHelper;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyAlertVariable;
import com.tzslsjy.business.service.ISjyAlertVariableService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 预警标签库Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Tag(name = "预警标签库管理", description = "预警标签库管理")
@RestController
@RequestMapping("/business/alert/variable")
public class SjyAlertVariableController extends BaseController
{
    @Autowired
    private ISjyAlertVariableService sjyAlertVariableService;

    /**
     * 查询预警标签库列表
     */
    @Operation(summary = "查询预警标签库列表")
    //@PreAuthorize("@ss.hasPermi('system:variable:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyAlertVariable sjyAlertVariable)
    {
        if (sjyAlertVariable.getPageSize()!=null&&sjyAlertVariable.getPageNum()!=null) {
            PageHelper.startPage(sjyAlertVariable.getPageNum(), sjyAlertVariable.getPageSize());
        }
        List<SjyAlertVariable> list = sjyAlertVariableService.selectSjyAlertVariableList(sjyAlertVariable);
        return getDataTable(list);
    }

    /**
     * 导出预警标签库列表
     */
    @Operation(summary = "导出预警标签库列表")
    //@PreAuthorize("@ss.hasPermi('system:variable:export')")
    @Log(title = "预警标签库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyAlertVariable sjyAlertVariable)
    {
        List<SjyAlertVariable> list = sjyAlertVariableService.selectSjyAlertVariableList(sjyAlertVariable);
        ExcelUtil<SjyAlertVariable> util = new ExcelUtil<SjyAlertVariable>(SjyAlertVariable.class);
        util.exportExcel(response, list, "预警标签库数据");
    }

    /**
     * 获取预警标签库详细信息
     */
    @Operation(summary = "获取预警标签库详细信息")
    @Parameter(name = "variableId", description = "预警标签库ID", required = true)
    //@PreAuthorize("@ss.hasPermi('system:variable:query')")
    @GetMapping(value = "/{variableId}")
    public AjaxResult getInfo(@PathVariable("variableId") Long variableId)
    {
        return success(sjyAlertVariableService.selectSjyAlertVariableByVariableId(variableId));
    }

    /**
     * 新增预警标签库
     */
    @Operation(summary = "新增预警标签库")
    //@PreAuthorize("@ss.hasPermi('system:variable:add')")
    @Log(title = "预警标签库", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyAlertVariable sjyAlertVariable)
    {
        return toAjax(sjyAlertVariableService.insertSjyAlertVariable(sjyAlertVariable));
    }

    /**
     * 修改预警标签库
     */
    @Operation(summary = "修改预警标签库")
    //@PreAuthorize("@ss.hasPermi('system:variable:edit')")
    @Log(title = "预警标签库", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyAlertVariable sjyAlertVariable)
    {
        return toAjax(sjyAlertVariableService.updateSjyAlertVariable(sjyAlertVariable));
    }

    /**
     * 删除预警标签库
     */
    @Operation(summary = "删除预警标签库")
    @Parameter(name = "variableIds", description = "预警标签库ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('system:variable:remove')")
    @Log(title = "预警标签库", businessType = BusinessType.DELETE)
	@DeleteMapping("/{variableIds}")
    public AjaxResult remove(@PathVariable Long[] variableIds)
    {
        return toAjax(sjyAlertVariableService.deleteSjyAlertVariableByVariableIds(variableIds));
    }
}
