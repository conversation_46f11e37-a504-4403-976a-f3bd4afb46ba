package com.tzslsjy.business.controller;

import com.tzslsjy.business.service.StationAbnormalRuleService;
import com.tzslsjy.business.vo.StationAbnormalRuleBatchCreateReqVo;
import com.tzslsjy.business.vo.StationAbnormalRuleCreateReqVo;
import com.tzslsjy.business.vo.StationAbnormalRuleDeleteReqVo;
import com.tzslsjy.business.vo.StationAbnormalRuleToggleReqVo;
import com.tzslsjy.business.vo.StationRuleStatus;
import com.tzslsjy.business.vo.StationWithRuleInfo;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.enums.BusinessType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

/**
 * 测站异常规则管理Controller
 * 提供测站异常规则的创建和启用功能
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Tag(name = "测站异常规则管理", description = "测站异常规则创建和启用控制器")
@RestController
@RequestMapping("/station/abnormal/rule")
@Slf4j
@Validated
public class StationAbnormalRuleController extends BaseController {

    @Autowired
    private StationAbnormalRuleService stationAbnormalRuleService;

    /**
     * 根据测站ID列表和规则类型创建异常检测规则
     */
    @Operation(summary = "创建测站异常检测规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    @Log(title = "测站异常规则", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createStationAbnormalRules(@Valid @RequestBody StationAbnormalRuleCreateReqVo reqVo) {

        try {
            log.info("开始创建测站异常检测规则，测站数量: {}, 规则类型: {}",
                    reqVo.getStationIds().size(), reqVo.getRuleTypeId());

            int createdCount = stationAbnormalRuleService.createStationAbnormalRules(
                    reqVo.getStationIds(), reqVo.getRuleTypeId());

            log.info("测站异常检测规则创建完成，成功创建 {} 条规则", createdCount);
            return AjaxResult.success("成功创建 " + createdCount + " 条测站异常检测规则");

        } catch (Exception e) {
            log.error("创建测站异常检测规则失败: {}", e.getMessage(), e);
            return AjaxResult.error("创建测站异常检测规则失败: " + e.getMessage());
        }
    }

    /**
     * 根据测站ID列表和规则类型启用或禁用异常检测规则
     */
    @Operation(summary = "启用或禁用测站异常检测规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "操作成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    @Log(title = "测站异常规则", businessType = BusinessType.UPDATE)
    @PostMapping("/toggle")
    public AjaxResult toggleStationAbnormalRules(@Valid @RequestBody StationAbnormalRuleToggleReqVo reqVo) {

        try {
            log.info("开始{}测站异常检测规则，测站数量: {}, 规则类型: {}",
                    reqVo.getEnabled() ? "启用" : "禁用", reqVo.getStationIds().size(), reqVo.getRuleTypeId());

            int updatedCount = stationAbnormalRuleService.toggleStationAbnormalRules(
                    reqVo.getStationIds(), reqVo.getRuleTypeId(), reqVo.getEnabled());

            String action = reqVo.getEnabled() ? "启用" : "禁用";
            log.info("测站异常检测规则{}完成，成功{} {} 条规则", action, action, updatedCount);
            return AjaxResult.success("成功" + action + " " + updatedCount + " 条测站异常检测规则");

        } catch (Exception e) {
            String action = reqVo.getEnabled() ? "启用" : "禁用";
            log.error("{}测站异常检测规则失败: {}", action, e.getMessage(), e);
            return AjaxResult.error(action + "测站异常检测规则失败: " + e.getMessage());
        }
    }

    /**
     * 查询测站异常规则状态
     */
    @Operation(summary = "查询测站异常规则状态")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    @GetMapping("/status")
    public AjaxResult getStationAbnormalRuleStatus(
            @Parameter(description = "测站ID列表", required = true)
            @RequestParam List<String> stationIds,
            @Parameter(description = "规则类型ID (31-水位异常, 34-雨量异常)", required = true)
            @RequestParam String ruleTypeId) {
        
        try {
            log.debug("查询测站异常规则状态，测站数量: {}, 规则类型: {}", stationIds.size(), ruleTypeId);
            

            // 验证测站ID列表
            if (stationIds == null || stationIds.isEmpty()) {
                return AjaxResult.error("测站ID列表不能为空");
            }
            
            List<StationRuleStatus> statusList =
                    stationAbnormalRuleService.getStationAbnormalRuleStatus(stationIds, ruleTypeId);
            
            return AjaxResult.success(statusList);
            
        } catch (Exception e) {
            log.error("查询测站异常规则状态失败: {}", e.getMessage(), e);
            return AjaxResult.error("查询测站异常规则状态失败: " + e.getMessage());
        }
    }

    /**
     * 批量创建测站异常检测规则（支持水位和雨量）
     */
    @Operation(summary = "批量创建测站异常检测规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "创建成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    @Log(title = "测站异常规则", businessType = BusinessType.INSERT)
    @PostMapping("/batch-create")
    public AjaxResult batchCreateStationAbnormalRules(@Valid @RequestBody StationAbnormalRuleBatchCreateReqVo reqVo) {

        try {
            log.info("开始批量创建测站异常检测规则，测站数量: {}, 水位异常: {}, 雨量异常: {}",
                    reqVo.getStationIds().size(), reqVo.getCreateWaterLevel(), reqVo.getCreateRainfall());

            // 验证至少选择一种规则类型
            if (!reqVo.getCreateWaterLevel() && !reqVo.getCreateRainfall()) {
                return AjaxResult.error("至少需要选择一种异常检测类型");
            }

            int totalCreated = stationAbnormalRuleService.batchCreateStationAbnormalRules(
                    reqVo.getStationIds(), reqVo.getCreateWaterLevel(), reqVo.getCreateRainfall());

            log.info("批量创建测站异常检测规则完成，成功创建 {} 条规则", totalCreated);
            return AjaxResult.success("成功创建 " + totalCreated + " 条测站异常检测规则");

        } catch (Exception e) {
            log.error("批量创建测站异常检测规则失败: {}", e.getMessage(), e);
            return AjaxResult.error("批量创建测站异常检测规则失败: " + e.getMessage());
        }
    }

    /**
     * 根据测站ID列表和规则类型删除异常检测规则
     */
    @Operation(summary = "删除测站异常检测规则")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    @Log(title = "测站异常规则", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult deleteStationAbnormalRules(@Valid @RequestBody StationAbnormalRuleDeleteReqVo reqVo) {

        try {
            log.info("开始删除测站异常检测规则，测站数量: {}, 规则类型: {}",
                    reqVo.getStationIds().size(), reqVo.getRuleTypeId());

            // 验证规则类型
            if (!"31".equals(reqVo.getRuleTypeId()) && !"34".equals(reqVo.getRuleTypeId())) {
                return AjaxResult.error("不支持的规则类型，仅支持31(水位异常)和34(雨量异常)");
            }

            int deletedCount = stationAbnormalRuleService.deleteStationAbnormalRules(
                    reqVo.getStationIds(), reqVo.getRuleTypeId());

            log.info("测站异常检测规则删除完成，成功删除 {} 条规则", deletedCount);
            return AjaxResult.success("成功删除 " + deletedCount + " 条测站异常检测规则");

        } catch (Exception e) {
            log.error("删除测站异常检测规则失败: {}", e.getMessage(), e);
            return AjaxResult.error("删除测站异常检测规则失败: " + e.getMessage());
        }
    }

    /**
     * 查询绑定了异常检测规则的测站信息列表（分页）
     */
    @Operation(summary = "查询绑定了异常检测规则的测站信息列表（分页）")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    @GetMapping("/stations")
    public TableDataInfo<StationWithRuleInfo> getStationsWithAbnormalRules(
            @Parameter(description = "规则类型ID (31-水位异常, 34-雨量异常, 不传则查询所有)", required = false)
            @RequestParam(required = false) String ruleTypeId,
            @Parameter(description = "规则状态 (0-禁用, 1-启用, 不传则查询所有)", required = false)
            @RequestParam(required = false) Integer status,
            @Parameter(description = "测站名称关键字", required = false)
            @RequestParam(required = false) String stationName) {

        try {
            log.debug("查询绑定了异常检测规则的测站信息，规则类型: {}, 状态: {}, 测站名称: {}",
                    ruleTypeId, status, stationName);



            // 启用分页
            startPage();
            List<StationWithRuleInfo> stationList =
                    stationAbnormalRuleService.getStationsWithAbnormalRules(ruleTypeId, status, stationName);

            return getDataTable(stationList);

        } catch (Exception e) {
            log.error("查询绑定了异常检测规则的测站信息失败: {}", e.getMessage(), e);
            TableDataInfo<StationWithRuleInfo> errorResult = new TableDataInfo<>();
            errorResult.setCode(500);
            errorResult.setMsg("查询绑定了异常检测规则的测站信息失败: " + e.getMessage());
            errorResult.setTotal(0);
            errorResult.setRows(new ArrayList<>());
            return errorResult;
        }
    }
}
