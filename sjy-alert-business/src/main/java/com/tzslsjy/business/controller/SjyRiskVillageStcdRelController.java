package com.tzslsjy.business.controller;

import com.tzslsjy.business.bo.SjyRiskVillageStcdRelAddBo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelEditBo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelQueryBo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelStcdAddBo;
import com.tzslsjy.business.service.ISjyRiskVillageStcdRelService;
import com.tzslsjy.business.vo.SjyRiskVillageStcdRelVo;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 村庄关联测站编号Controller
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
@Tag(name = "村庄关联测站编号管理")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/business/relVillageStcd")
public class SjyRiskVillageStcdRelController extends BaseController {

    private final ISjyRiskVillageStcdRelService iSjyRiskVillageStcdRelService;

    /**
     * 查询村庄关联测站编号列表
     */
    @Operation(summary = "查询村庄关联测站编号列表")
    //@PreAuthorize("@ss.hasPermi('business:relVillageStcd:list')")
    @GetMapping("/list")
    public TableDataInfo<SjyRiskVillageStcdRelVo> list(SjyRiskVillageStcdRelQueryBo bo) {
        startPage();
        List<SjyRiskVillageStcdRelVo> list = iSjyRiskVillageStcdRelService.queryList(bo);
        return getDataTable(list);
    }

    /**
     * 导出村庄关联测站编号列表
     */
    @Operation(summary = "导出村庄关联测站编号列表")
    //@PreAuthorize("@ss.hasPermi('business:relVillageStcd:export')")
    @Log(title = "村庄关联测站编号", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult<SjyRiskVillageStcdRelVo> export(SjyRiskVillageStcdRelQueryBo bo) {
        List<SjyRiskVillageStcdRelVo> list = iSjyRiskVillageStcdRelService.queryList(bo);
        ExcelUtil<SjyRiskVillageStcdRelVo> util = new ExcelUtil<SjyRiskVillageStcdRelVo>(SjyRiskVillageStcdRelVo.class);
        return util.exportExcel(list, "村庄关联测站编号");
    }

    /**
     * 获取村庄关联测站编号详细信息
     */
    @Operation(summary = "获取村庄关联测站编号详细信息")
    //@PreAuthorize("@ss.hasPermi('business:relVillageStcd:query')")
    @GetMapping("/{id}")
    public AjaxResult<SjyRiskVillageStcdRelVo> getInfo(@PathVariable("id" ) Long id) {
        return AjaxResult.success(iSjyRiskVillageStcdRelService.queryById(id));
    }
    /**
     * 新增村庄关联测站编号
     */
    @Operation(summary = "新增村庄关联测站编号")
    //@PreAuthorize("@ss.hasPermi('business:relVillageStcd:add')")
    @Log(title = "村庄关联测站编号", businessType = BusinessType.INSERT)
    @PostMapping("/saveOrUpdateByStcd")
    public AjaxResult saveOrUpdateByStcd(@RequestBody SjyRiskVillageStcdRelStcdAddBo bo) {
        return toAjax(iSjyRiskVillageStcdRelService.saveOrUpdateByStcd(bo) ? 1 : 0);
    }
    /**
     * 新增村庄关联测站编号
     */
    @Operation(summary = "新增村庄关联测站编号")
    //@PreAuthorize("@ss.hasPermi('business:relVillageStcd:add')")
    @Log(title = "村庄关联测站编号", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SjyRiskVillageStcdRelAddBo bo) {
        return toAjax(iSjyRiskVillageStcdRelService.insertByAddBo(bo) ? 1 : 0);
    }

    /**
     * 修改村庄关联测站编号
     */
    @Operation(summary = "修改村庄关联测站编号")
    //@PreAuthorize("@ss.hasPermi('business:relVillageStcd:edit')")
    @Log(title = "村庄关联测站编号", businessType = BusinessType.UPDATE)
    @PutMapping()
    public AjaxResult edit(@RequestBody SjyRiskVillageStcdRelEditBo bo) {
        return toAjax(iSjyRiskVillageStcdRelService.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除村庄关联测站编号
     */
    @Operation(summary = "删除村庄关联测站编号")
    //@PreAuthorize("@ss.hasPermi('business:relVillageStcd:remove')")
    @Log(title = "村庄关联测站编号" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(iSjyRiskVillageStcdRelService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}