package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.service.ISjyAlertRuleArgService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 预警规则参数Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Tag(name = "预警规则参数管理", description = "预警规则参数管理")
@RestController
@RequestMapping("/business/alert/rule/arg")
public class SjyAlertRuleArgController extends BaseController
{
    @Autowired
    private ISjyAlertRuleArgService sjyAlertRuleArgService;

    /**
     * 查询预警规则参数列表
     */
    @Operation(summary = "查询预警规则参数列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:arg:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertRuleArg sjyAlertRuleArg)
    {
        startPage();
        List<SjyAlertRuleArg> list = sjyAlertRuleArgService.selectSjyAlertRuleArgList(sjyAlertRuleArg);
        return getDataTable(list);
    }

    /**
     * 导出预警规则参数列表
     */
    @Operation(summary = "导出预警规则参数列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:arg:export')")
    @Log(title = "预警规则参数", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertRuleArg sjyAlertRuleArg)
    {
        List<SjyAlertRuleArg> list = sjyAlertRuleArgService.selectSjyAlertRuleArgList(sjyAlertRuleArg);
        ExcelUtil<SjyAlertRuleArg> util = new ExcelUtil<SjyAlertRuleArg>(SjyAlertRuleArg.class);
        util.exportExcel(response, list, "预警规则参数数据");
    }

    /**
     * 获取预警规则参数详细信息
     */
    @Operation(summary = "获取预警规则参数详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:arg:query')")
    @GetMapping(value = "/{argId}")
    public AjaxResult<SjyAlertRuleArg> getInfo(@Parameter(name = "argId", description = "预警规则参数ID", required = true) @PathVariable("argId") Long argId)
    {
        return success(sjyAlertRuleArgService.selectSjyAlertRuleArgByArgId(argId));
    }

    /**
     * 新增预警规则参数
     */
    @Operation(summary = "新增预警规则参数")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:arg:add')")
    @Log(title = "预警规则参数", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警规则参数信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRuleArg.class)))
                          @Valid @RequestBody SjyAlertRuleArg sjyAlertRuleArg)
    {
        return toAjax(sjyAlertRuleArgService.insertSjyAlertRuleArg(sjyAlertRuleArg));
    }

    /**
     * 修改预警规则参数
     */
    @Operation(summary = "修改预警规则参数")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:arg:edit')")
    @Log(title = "预警规则参数", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Integer> edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警规则参数信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRuleArg.class)))
                           @Valid @RequestBody SjyAlertRuleArg sjyAlertRuleArg)
    {
        return toAjax(sjyAlertRuleArgService.updateSjyAlertRuleArg(sjyAlertRuleArg));
    }

    /**
     * 删除预警规则参数
     */
    @Operation(summary = "删除预警规则参数")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:arg:remove')")
    @Log(title = "预警规则参数", businessType = BusinessType.DELETE)
 @DeleteMapping("/{argIds}")
    public AjaxResult<Integer> remove(@Parameter(name = "argIds", description = "待删除预警规则参数ID数组", required = true) @PathVariable Long[] argIds)
    {
        return toAjax(sjyAlertRuleArgService.deleteSjyAlertRuleArgByArgIds(argIds));
    }
}
