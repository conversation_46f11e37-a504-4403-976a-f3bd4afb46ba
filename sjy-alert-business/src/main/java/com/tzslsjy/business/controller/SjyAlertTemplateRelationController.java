package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyAlertTemplateRelation;
import com.tzslsjy.business.service.ISjyAlertTemplateRelationService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 消息模板关联Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Tag(name = "消息模板关联管理", description = "消息模板关联管理")
@RestController
@RequestMapping("/business/alert/template/relation")
public class SjyAlertTemplateRelationController extends BaseController
{
    @Autowired
    private ISjyAlertTemplateRelationService sjyAlertTemplateRelationService;

    /**
     * 查询消息模板关联列表
     */
    @Operation(summary = "查询消息模板关联列表")
    //@PreAuthorize("@ss.hasPermi('system:relation:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyAlertTemplateRelation sjyAlertTemplateRelation)
    {
        startPage();
        List<SjyAlertTemplateRelation> list = sjyAlertTemplateRelationService.selectSjyAlertTemplateRelationList(sjyAlertTemplateRelation);
        return getDataTable(list);
    }

    /**
     * 导出消息模板关联列表
     */
    @Operation(summary = "导出消息模板关联列表")
    //@PreAuthorize("@ss.hasPermi('system:relation:export')")
    @Log(title = "消息模板关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyAlertTemplateRelation sjyAlertTemplateRelation)
    {
        List<SjyAlertTemplateRelation> list = sjyAlertTemplateRelationService.selectSjyAlertTemplateRelationList(sjyAlertTemplateRelation);
        ExcelUtil<SjyAlertTemplateRelation> util = new ExcelUtil<SjyAlertTemplateRelation>(SjyAlertTemplateRelation.class);
        util.exportExcel(response, list, "消息模板关联数据");
    }

    /**
     * 获取消息模板关联详细信息
     */
    @Operation(summary = "获取消息模板关联详细信息")
    @Parameter(name = "id", description = "消息模板关联ID", required = true)
    //@PreAuthorize("@ss.hasPermi('system:relation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(sjyAlertTemplateRelationService.selectSjyAlertTemplateRelationById(id));
    }

    /**
     * 新增消息模板关联
     */
    @Operation(summary = "新增消息模板关联")
    //@PreAuthorize("@ss.hasPermi('system:relation:add')")
    @Log(title = "消息模板关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyAlertTemplateRelation sjyAlertTemplateRelation)
    {
        return toAjax(sjyAlertTemplateRelationService.insertSjyAlertTemplateRelation(sjyAlertTemplateRelation));
    }

    /**
     * 修改消息模板关联
     */
    @Operation(summary = "修改消息模板关联")
    //@PreAuthorize("@ss.hasPermi('system:relation:edit')")
    @Log(title = "消息模板关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyAlertTemplateRelation sjyAlertTemplateRelation)
    {
        return toAjax(sjyAlertTemplateRelationService.updateSjyAlertTemplateRelation(sjyAlertTemplateRelation));
    }

    /**
     * 删除消息模板关联
     */
    @Operation(summary = "删除消息模板关联")
    @Parameter(name = "ids", description = "消息模板关联ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('system:relation:remove')")
    @Log(title = "消息模板关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(sjyAlertTemplateRelationService.deleteSjyAlertTemplateRelationByIds(ids));
    }
}
