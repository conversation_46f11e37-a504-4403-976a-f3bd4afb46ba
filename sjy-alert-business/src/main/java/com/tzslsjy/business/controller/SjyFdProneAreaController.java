package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyFdProneArea;
import com.tzslsjy.business.service.ISjyFdProneAreaService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 山洪易发区信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Tag(name = "山洪易发区信息管理", description = "山洪易发区信息管理")
@RestController
@RequestMapping("/business/prone/area")
public class SjyFdProneAreaController extends BaseController
{
    @Autowired
    private ISjyFdProneAreaService sjyFdProneAreaService;

    /**
     * 查询山洪易发区信息列表
     */
    @Operation(summary = "查询山洪易发区信息列表")
    //@PreAuthorize("@ss.hasPermi('business:area:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyFdProneArea sjyFdProneArea)
    {
        startPage();
        List<SjyFdProneArea> list = sjyFdProneAreaService.selectSjyFdProneAreaList(sjyFdProneArea);
        return getDataTable(list);
    }

    /**
     * 导出山洪易发区信息列表
     */
    @Operation(summary = "导出山洪易发区信息列表")
    //@PreAuthorize("@ss.hasPermi('business:area:export')")
    @Log(title = "山洪易发区信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyFdProneArea sjyFdProneArea)
    {
        List<SjyFdProneArea> list = sjyFdProneAreaService.selectSjyFdProneAreaList(sjyFdProneArea);
        ExcelUtil<SjyFdProneArea> util = new ExcelUtil<SjyFdProneArea>(SjyFdProneArea.class);
        util.exportExcel(response, list, "山洪易发区信息数据");
    }

    /**
     * 获取山洪易发区信息详细信息
     */
    @Operation(summary = "获取山洪易发区信息详细信息")
    @Parameter(name = "prevCode", description = "山洪易发区信息ID", required = true)
    //@PreAuthorize("@ss.hasPermi('business:area:query')")
    @GetMapping(value = "/{prevCode}")
    public AjaxResult getInfo(@PathVariable("prevCode") String prevCode)
    {
        return success(sjyFdProneAreaService.selectSjyFdProneAreaByPrevCode(prevCode));
    }

    /**
     * 新增山洪易发区信息
     */
    @Operation(summary = "新增山洪易发区信息")
    //@PreAuthorize("@ss.hasPermi('business:area:add')")
    @Log(title = "山洪易发区信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyFdProneArea sjyFdProneArea)
    {
        return toAjax(sjyFdProneAreaService.insertSjyFdProneArea(sjyFdProneArea));
    }

    /**
     * 修改山洪易发区信息
     */
    @Operation(summary = "修改山洪易发区信息")
    //@PreAuthorize("@ss.hasPermi('business:area:edit')")
    @Log(title = "山洪易发区信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyFdProneArea sjyFdProneArea)
    {
        return toAjax(sjyFdProneAreaService.updateSjyFdProneArea(sjyFdProneArea));
    }

    /**
     * 删除山洪易发区信息
     */
    @Operation(summary = "删除山洪易发区信息")
    @Parameter(name = "prevCodes", description = "山洪易发区信息ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('business:area:remove')")
    @Log(title = "山洪易发区信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{prevCodes}")
    public AjaxResult remove(@PathVariable String[] prevCodes)
    {
        return toAjax(sjyFdProneAreaService.deleteSjyFdProneAreaByPrevCodes(prevCodes));
    }
}
