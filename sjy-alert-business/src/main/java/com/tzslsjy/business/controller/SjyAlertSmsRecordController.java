package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsStatsRequest;
import com.tzslsjy.business.domain.vo.SjyAlertSmsStatsVO;
import com.tzslsjy.business.service.ISjyAlertSmsRecordService;
import com.tzslsjy.business.vo.SjyAlertStatRespVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;

import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 预警消息记录Controller
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Tag(name = "预警消息记录管理", description = "预警消息记录管理")
@RestController
@RequestMapping("/business/alert/sms/record")
public class SjyAlertSmsRecordController extends BaseController
{
    @Autowired
    private ISjyAlertSmsRecordService sjyAlertSmsRecordService;

    /**
     * 查询预警消息记录列表
     */
    @Operation(summary = "查询预警消息记录列表")
    //@PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyAlertSmsRecord sjyAlertSmsRecord)
    {
        startPage();
        List<SjyAlertSmsRecord> list = sjyAlertSmsRecordService.selectSjyAlertSmsRecordList(sjyAlertSmsRecord);
        return getDataTable(list);
    }

    /**
     * 导出预警消息记录列表
     */
    @Operation(summary = "导出预警消息记录列表")
    //@PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "预警消息记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyAlertSmsRecord sjyAlertSmsRecord)
    {
        List<SjyAlertSmsRecord> list = sjyAlertSmsRecordService.selectSjyAlertSmsRecordList(sjyAlertSmsRecord);
        ExcelUtil<SjyAlertSmsRecord> util = new ExcelUtil<SjyAlertSmsRecord>(SjyAlertSmsRecord.class);
        util.exportExcel(response, list, "预警消息记录数据");
    }

    /**
     * 获取预警消息记录详细信息
     */
    @Operation(summary = "获取预警消息记录详细信息")
    @Parameter(name = "alertSmsId", description = "预警消息记录ID", required = true)
    //@PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{alertSmsId}")
    public AjaxResult getInfo(@PathVariable("alertSmsId") Long alertSmsId)
    {
        return success(sjyAlertSmsRecordService.selectSjyAlertSmsRecordByAlertSmsId(alertSmsId));
    }

    /**
     * 新增预警消息记录
     */
    @Operation(summary = "新增预警消息记录")
    //@PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "预警消息记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyAlertSmsRecord sjyAlertSmsRecord)
    {
        return toAjax(sjyAlertSmsRecordService.insertSjyAlertSmsRecord(sjyAlertSmsRecord));
    }

    /**
     * 修改预警消息记录
     */
    @Operation(summary = "修改预警消息记录")
    //@PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "预警消息记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyAlertSmsRecord sjyAlertSmsRecord)
    {
        return toAjax(sjyAlertSmsRecordService.updateSjyAlertSmsRecord(sjyAlertSmsRecord));
    }

    /**
     * 删除预警消息记录
     */
    @Operation(summary = "删除预警消息记录")
    @Parameter(name = "alertSmsIds", description = "预警消息记录ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "预警消息记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{alertSmsIds}")
    public AjaxResult remove(@PathVariable Long[] alertSmsIds)
    {
        return toAjax(sjyAlertSmsRecordService.deleteSjyAlertSmsRecordByAlertSmsIds(alertSmsIds));
    }

    /**
     * 根据预警记录ID列表和发送方式统计预警消息发送状态
     */
    @Operation(summary = "统计预警消息发送状态")
    //@PreAuthorize("@ss.hasPermi('business:alertSmsRecord:stats')") // 根据实际权限配置
    @PostMapping("/stats")
    public TableDataInfo< SjyAlertSmsStatsVO> getSmsStats(@Valid @RequestBody com.tzslsjy.business.domain.SjyAlertSmsStatsRequest statsRequest)
    {

        List<SjyAlertSmsStatsVO> statsList = sjyAlertSmsRecordService.getSjyAlertSmsStats(statsRequest);
        return getDataTable(statsList);
    }
    /**
     * 统计几种关键预警
     */
    @Operation(summary = "统计几种关键预警")
    //@PreAuthorize("@ss.hasPermi('business:alertSmsRecord:stats')") // 根据实际权限配置
    @PostMapping("/alertStats")
    public AjaxResult<SjyAlertStatRespVo> alertStats(@Valid @RequestBody SjyAlertSmsStatsRequest statsRequest)
    {
        SjyAlertStatRespVo statsList = sjyAlertSmsRecordService.alertStats(statsRequest);
        return success(statsList);
    }
}
