package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.tzslsjy.business.domain.SjyAlertObjRecord;
import com.tzslsjy.business.service.ISjyAlertObjRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;

import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 预警对象记录Controller
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Tag(name = "预警对象记录管理", description = "预警对象记录管理")
@RestController
@RequestMapping("/business/alert/obj/record")
public class SjyAlertObjRecordController extends BaseController
{
    @Autowired
    private ISjyAlertObjRecordService sjyAlertObjRecordService;

    /**
     * 查询预警对象记录列表
     */
    @Operation(summary = "查询预警对象记录列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertObjRecord sjyAlertObjRecord)
    {
        startPage();
        List<SjyAlertObjRecord> list = sjyAlertObjRecordService.selectSjyAlertObjRecordList(sjyAlertObjRecord);
        return getDataTable(list);
    }

    /**
     * 导出预警对象记录列表
     */
    @Operation(summary = "导出预警对象记录列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "预警对象记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertObjRecord sjyAlertObjRecord)
    {
        List<SjyAlertObjRecord> list = sjyAlertObjRecordService.selectSjyAlertObjRecordList(sjyAlertObjRecord);
        ExcelUtil<SjyAlertObjRecord> util = new ExcelUtil<SjyAlertObjRecord>(SjyAlertObjRecord.class);
        util.exportExcel(response, list, "预警对象记录数据");
    }

    /**
     * 获取预警对象记录详细信息
     */
    @Operation(summary = "获取预警对象记录详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{alertRecordId}")
    public AjaxResult<SjyAlertObjRecord> getInfo(@Parameter(name = "alertRecordId", description = "预警对象记录ID", required = true) @PathVariable("alertRecordId") Long alertRecordId)
    {
        return success(sjyAlertObjRecordService.selectSjyAlertObjRecordByAlertRecordId(alertRecordId));
    }

    /**
     * 新增预警对象记录
     */
    @Operation(summary = "新增预警对象记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "预警对象记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警对象记录信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertObjRecord.class)))
                          @Valid @RequestBody SjyAlertObjRecord sjyAlertObjRecord)
    {
        return toAjax(sjyAlertObjRecordService.insertSjyAlertObjRecord(sjyAlertObjRecord));
    }

    /**
     * 修改预警对象记录
     */
    @Operation(summary = "修改预警对象记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "预警对象记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Integer> edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警对象记录信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertObjRecord.class)))
                           @Valid @RequestBody SjyAlertObjRecord sjyAlertObjRecord)
    {
        return toAjax(sjyAlertObjRecordService.updateSjyAlertObjRecord(sjyAlertObjRecord));
    }

    /**
     * 删除预警对象记录
     */
    @Operation(summary = "删除预警对象记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "预警对象记录", businessType = BusinessType.DELETE)
 @DeleteMapping("/{alertRecordIds}")
    public AjaxResult<Integer> remove(@Parameter(name = "alertRecordIds", description = "待删除预警对象记录ID数组", required = true) @PathVariable Long[] alertRecordIds)
    {
        return toAjax(sjyAlertObjRecordService.deleteSjyAlertObjRecordByAlertRecordIds(alertRecordIds));
    }
}
