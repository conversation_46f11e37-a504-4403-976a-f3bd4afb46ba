package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyFdPrevObj;
import com.tzslsjy.business.service.ISjyFdPrevObjService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 防治对象名录Controller
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@Tag(name = "防治对象名录管理", description = "防治对象名录管理")
@RestController
@RequestMapping("/business/prev/obj")
public class SjyFdPrevObjController extends BaseController
{
    @Autowired
    private ISjyFdPrevObjService sjyFdPrevObjService;

    /**
     * 查询防治对象名录列表
     */
    @Operation(summary = "查询防治对象名录列表")
    //@PreAuthorize("@ss.hasPermi('business:obj:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyFdPrevObj sjyFdPrevObj)
    {
        startPage();
        List<SjyFdPrevObj> list = sjyFdPrevObjService.selectSjyFdPrevObjList(sjyFdPrevObj);
        return getDataTable(list);
    }

    /**
     * 导出防治对象名录列表
     */
    @Operation(summary = "导出防治对象名录列表")
    //@PreAuthorize("@ss.hasPermi('business:obj:export')")
    @Log(title = "防治对象名录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyFdPrevObj sjyFdPrevObj)
    {
        List<SjyFdPrevObj> list = sjyFdPrevObjService.selectSjyFdPrevObjList(sjyFdPrevObj);
        ExcelUtil<SjyFdPrevObj> util = new ExcelUtil<SjyFdPrevObj>(SjyFdPrevObj.class);
        util.exportExcel(response, list, "防治对象名录数据");
    }

    /**
     * 获取防治对象名录详细信息
     */
    @Operation(summary = "获取防治对象名录详细信息")
    @Parameter(name = "prevCode", description = "防治对象名录ID", required = true)
    //@PreAuthorize("@ss.hasPermi('business:obj:query')")
    @GetMapping(value = "/{prevCode}")
    public AjaxResult getInfo(@PathVariable("prevCode") String prevCode)
    {
        return success(sjyFdPrevObjService.selectSjyFdPrevObjByPrevCode(prevCode));
    }

    /**
     * 新增防治对象名录
     */
    @Operation(summary = "新增防治对象名录")
    //@PreAuthorize("@ss.hasPermi('business:obj:add')")
    @Log(title = "防治对象名录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyFdPrevObj sjyFdPrevObj)
    {
        return toAjax(sjyFdPrevObjService.insertSjyFdPrevObj(sjyFdPrevObj));
    }

    /**
     * 修改防治对象名录
     */
    @Operation(summary = "修改防治对象名录")
    //@PreAuthorize("@ss.hasPermi('business:obj:edit')")
    @Log(title = "防治对象名录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyFdPrevObj sjyFdPrevObj)
    {
        return toAjax(sjyFdPrevObjService.updateSjyFdPrevObj(sjyFdPrevObj));
    }

    /**
     * 删除防治对象名录
     */
    @Operation(summary = "删除防治对象名录")
    @Parameter(name = "prevCodes", description = "防治对象名录ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('business:obj:remove')")
    @Log(title = "防治对象名录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{prevCodes}")
    public AjaxResult remove(@PathVariable String[] prevCodes)
    {
        return toAjax(sjyFdPrevObjService.deleteSjyFdPrevObjByPrevCodes(prevCodes));
    }
}
