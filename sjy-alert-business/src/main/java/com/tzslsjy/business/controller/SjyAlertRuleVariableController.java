package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyAlertRuleVariable;
import com.tzslsjy.business.service.ISjyAlertRuleVariableService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 规则标签关联Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Tag(name = "规则标签关联管理", description = "规则标签关联管理")
@RestController
@RequestMapping("/business/alert/rule/variable")
public class SjyAlertRuleVariableController extends BaseController
{
    @Autowired
    private ISjyAlertRuleVariableService sjyAlertRuleVariableService;

    /**
     * 查询规则标签关联列表
     */
    @Operation(summary = "查询规则标签关联列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:variable:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertRuleVariable sjyAlertRuleVariable)
    {
        startPage();
        List<SjyAlertRuleVariable> list = sjyAlertRuleVariableService.selectSjyAlertRuleVariableList(sjyAlertRuleVariable);
        return getDataTable(list);
    }

    /**
     * 导出规则标签关联列表
     */
    @Operation(summary = "导出规则标签关联列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:variable:export')")
    @Log(title = "规则标签关联", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertRuleVariable sjyAlertRuleVariable)
    {
        List<SjyAlertRuleVariable> list = sjyAlertRuleVariableService.selectSjyAlertRuleVariableList(sjyAlertRuleVariable);
        ExcelUtil<SjyAlertRuleVariable> util = new ExcelUtil<SjyAlertRuleVariable>(SjyAlertRuleVariable.class);
        util.exportExcel(response, list, "规则标签关联数据");
    }

    /**
     * 获取规则标签关联详细信息
     */
    @Operation(summary = "获取规则标签关联详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:variable:query')")
    @GetMapping(value = "/{ruleVariableId}")
    public AjaxResult getInfo(@Parameter(name = "ruleVariableId", description = "规则标签关联ID", required = true) @PathVariable("ruleVariableId") Long ruleVariableId)
    {
        return success(sjyAlertRuleVariableService.selectSjyAlertRuleVariableByRuleVariableId(ruleVariableId));
    }

    /**
     * 新增规则标签关联
     */
    @Operation(summary = "新增规则标签关联")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:variable:add')")
    @Log(title = "规则标签关联", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "规则标签关联信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRuleVariable.class)))
                          @Valid @RequestBody SjyAlertRuleVariable sjyAlertRuleVariable)
    {
        return toAjax(sjyAlertRuleVariableService.insertSjyAlertRuleVariable(sjyAlertRuleVariable));
    }

    /**
     * 修改规则标签关联
     */
    @Operation(summary = "修改规则标签关联")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:variable:edit')")
    @Log(title = "规则标签关联", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "规则标签关联信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertRuleVariable.class)))
                           @Valid @RequestBody SjyAlertRuleVariable sjyAlertRuleVariable)
    {
        return toAjax(sjyAlertRuleVariableService.updateSjyAlertRuleVariable(sjyAlertRuleVariable));
    }

    /**
     * 删除规则标签关联
     */
    @Operation(summary = "删除规则标签关联")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:variable:remove')")
    @Log(title = "规则标签关联", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ruleVariableIds}")
    public AjaxResult remove(@Parameter(name = "ruleVariableIds", description = "待删除规则标签关联ID数组", required = true) @PathVariable Long[] ruleVariableIds)
    {
        return toAjax(sjyAlertRuleVariableService.deleteSjyAlertRuleVariableByRuleVariableIds(ruleVariableIds));
    }
}
