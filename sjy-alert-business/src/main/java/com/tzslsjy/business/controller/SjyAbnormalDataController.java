package com.tzslsjy.business.controller;

import java.util.List;
import java.util.Arrays;

import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.tzslsjy.business.vo.SjyAbnormalDataVo;
import com.tzslsjy.business.bo.SjyAbnormalDataQueryBo;
import com.tzslsjy.business.bo.SjyAbnormalDataAddBo;
import com.tzslsjy.business.bo.SjyAbnormalDataEditBo;
import com.tzslsjy.business.service.ISjyAbnormalDataService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;

/**
 * 测站异常数据Controller
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Tag(name = "测站异常数据管理", description = "测站异常数据控制器")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/abnormal/data")
public class SjyAbnormalDataController extends BaseController {

    private final ISjyAbnormalDataService iSjyAbnormalDataService;

    /**
     * 查询测站异常数据列表
     */
    @Operation(summary = "查询测站异常数据列表")
    //@PreAuthorize("@ss.hasPermi('abnormal:data:list')")
    @GetMapping("/list")
    public TableDataInfo<SjyAbnormalDataVo> list(SjyAbnormalDataQueryBo bo) {
        startPage();
        List<SjyAbnormalDataVo> list = iSjyAbnormalDataService.queryList(bo);
        return getDataTable(list);
    }

    /**
     * 导出测站异常数据列表
     */
    @Operation(summary = "导出测站异常数据列表")
    //@PreAuthorize("@ss.hasPermi('abnormal:data:export')")
    @Log(title = "测站异常数据", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult<SjyAbnormalDataVo> export(SjyAbnormalDataQueryBo bo) {
        List<SjyAbnormalDataVo> list = iSjyAbnormalDataService.queryList(bo);
        ExcelUtil<SjyAbnormalDataVo> util = new ExcelUtil<SjyAbnormalDataVo>(SjyAbnormalDataVo.class);
        return util.exportExcel(list, "测站异常数据");
    }

    /**
     * 获取测站异常数据详细信息
     */
    @Operation(summary = "获取测站异常数据详细信息")
    //@PreAuthorize("@ss.hasPermi('abnormal:data:query')")
    @GetMapping("/info")
    public AjaxResult<SjyAbnormalDataVo> getInfo(
            @Parameter(description = "测站编号") @RequestParam String stcd,
            @Parameter(description = "时间") @RequestParam String tm,
            @Parameter(description = "类型") @RequestParam String type) {
        return AjaxResult.success(iSjyAbnormalDataService.queryByKey(stcd, tm, type));
    }

    /**
     * 新增测站异常数据
     */
    @Operation(summary = "新增测站异常数据")
    //@PreAuthorize("@ss.hasPermi('abnormal:data:add')")
    @Log(title = "测站异常数据", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SjyAbnormalDataAddBo bo) {
        return toAjax(iSjyAbnormalDataService.insertByAddBo(bo) ? 1 : 0);
    }

    /**
     * 修改测站异常数据
     */
    @Operation(summary = "修改测站异常数据")
    //@PreAuthorize("@ss.hasPermi('abnormal:data:edit')")
    @Log(title = "测站异常数据", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody SjyAbnormalDataEditBo bo) {
        return toAjax(iSjyAbnormalDataService.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除测站异常数据
     */
    @Operation(summary = "删除测站异常数据")
    //@PreAuthorize("@ss.hasPermi('abnormal:data:remove')")
    @Log(title = "测站异常数据", businessType = BusinessType.DELETE)
    @PostMapping("/delete")
    public AjaxResult remove(@RequestBody String[] stcds) {
        return toAjax(iSjyAbnormalDataService.deleteWithValidByStcds(Arrays.asList(stcds), true) ? 1 : 0);
    }

    /**
     * 根据条件删除测站异常数据
     */
    @Operation(summary = "根据条件删除测站异常数据")
    //@PreAuthorize("@ss.hasPermi('abnormal:data:remove')")
    @Log(title = "测站异常数据", businessType = BusinessType.DELETE)
    @PostMapping("/deleteByCondition")
    public AjaxResult removeByCondition(
            @Parameter(description = "测站编号") @RequestParam(required = false) String stcd,
            @Parameter(description = "类型") @RequestParam(required = false) String type,
            @Parameter(description = "开始时间") @RequestParam(required = false) String startTime,
            @Parameter(description = "结束时间") @RequestParam(required = false) String endTime) {
        return toAjax(iSjyAbnormalDataService.deleteByCondition(stcd, type, startTime, endTime) ? 1 : 0);
    }
}
