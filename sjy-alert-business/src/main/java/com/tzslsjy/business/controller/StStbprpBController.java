package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.tzslsjy.business.domain.vo.NodeTreeVO;
import com.tzslsjy.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.business.service.IStStbprpBService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 测站基础信息Controller
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Tag(name = "测站基础信息管理", description = "测站基础信息管理")
@RestController
@RequestMapping("/business/stbprp")
public class StStbprpBController extends BaseController
{
    @Autowired
    private IStStbprpBService stStbprpBService;

    /**
     * 查询测站基础信息列表
     */
    @Operation(summary = "查询测站基础信息列表")
    //@PreAuthorize("@ss.hasPermi('business:stbprp:list')")
    @GetMapping("/list")
    public TableDataInfo list(StStbprpB stStbprpB)
    {
        startPage();
        List<StStbprpB> list = stStbprpBService.selectStStbprpBList(stStbprpB);
        return getDataTable(list);
    }
    /**
     * 查询测站基础信息列表
     */
    @Operation(summary = "查询测站基础信息列表以及关联的乡镇区划树")
    //@PreAuthorize("@ss.hasPermi('business:stbprp:list')")
    @GetMapping("/listWithAdcd")
    public CommonResult<List<NodeTreeVO>> listWithAdcd(StStbprpB stStbprpB)
    {

        List<NodeTreeVO> list = stStbprpBService.listWithAdcd(stStbprpB);
        return CommonResult.success(list);
    }
    /**
     * 查询测站基础信息列表
     */
    @Operation(summary = "查询测站基础信息列表以及关联的村")
    //@PreAuthorize("@ss.hasPermi('business:stbprp:list')")
    @GetMapping("/listWithVillage")
    public TableDataInfo listWithVillage(StStbprpB stStbprpB)
    {
        startPage();
        List<StStbprpB> list = stStbprpBService.selectStStbprpBListWithVillage(stStbprpB);
        return getDataTable(list);
    }
    /**
     * 导出测站基础信息列表
     */
    @Operation(summary = "导出测站基础信息列表")
    //@PreAuthorize("@ss.hasPermi('business:stbprp:export')")
    @Log(title = "测站基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StStbprpB stStbprpB)
    {
        List<StStbprpB> list = stStbprpBService.selectStStbprpBList(stStbprpB);
        ExcelUtil<StStbprpB> util = new ExcelUtil<StStbprpB>(StStbprpB.class);
        util.exportExcel(response, list, "测站基础信息数据");
    }

    /**
     * 获取测站基础信息详细信息
     */
    @Operation(summary = "获取测站基础信息详细信息")
    @Parameter(name = "STCD", description = "测站基础信息ID", required = true)
    //@PreAuthorize("@ss.hasPermi('business:stbprp:query')")
    @GetMapping(value = "/{STCD}")
    public AjaxResult getInfo(@PathVariable("STCD") String STCD)
    {
        return success(stStbprpBService.selectStStbprpBBySTCD(STCD));
    }

    /**
     * 新增测站基础信息
     */
    @Operation(summary = "新增测站基础信息")
    //@PreAuthorize("@ss.hasPermi('business:stbprp:add')")
    @Log(title = "测站基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody StStbprpB stStbprpB)
    {
        return toAjax(stStbprpBService.insertStStbprpB(stStbprpB));
    }

    /**
     * 修改测站基础信息
     */
    @Operation(summary = "修改测站基础信息")
    //@PreAuthorize("@ss.hasPermi('business:stbprp:edit')")
    @Log(title = "测站基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody StStbprpB stStbprpB)
    {
        return toAjax(stStbprpBService.updateStStbprpB(stStbprpB));
    }

    /**
     * 删除测站基础信息
     */
    @Operation(summary = "删除测站基础信息")
    @Parameter(name = "STCDs", description = "测站基础信息ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('business:stbprp:remove')")
    @Log(title = "测站基础信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{STCDs}")
    public AjaxResult remove(@PathVariable String[] STCDs)
    {
        return toAjax(stStbprpBService.deleteStStbprpBBySTCDs(STCDs));
    }
}
