package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.business.domain.SjyAlertStSituation;
import com.tzslsjy.business.service.ISjyAlertStSituationService;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 测站预警情况Controller
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@RestController
@RequestMapping("/business/situation")
public class SjyAlertStSituationController extends BaseController {
    @Autowired
    private ISjyAlertStSituationService sjyAlertStSituationService;

    /**
     * 查询测站预警情况列表
     */
    @PreAuthorize("@ss.hasPermi('business:situation:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyAlertStSituation sjyAlertStSituation) {
        startPage();
        List<SjyAlertStSituation> list = sjyAlertStSituationService.selectSjyAlertStSituationList(sjyAlertStSituation);
        return getDataTable(list);
    }

    /**
     * 导出测站预警情况列表
     */
    @PreAuthorize("@ss.hasPermi('business:situation:export')")
    @Log(title = "测站预警情况", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyAlertStSituation sjyAlertStSituation) {
        List<SjyAlertStSituation> list = sjyAlertStSituationService.selectSjyAlertStSituationList(sjyAlertStSituation);
        ExcelUtil<SjyAlertStSituation> util = new ExcelUtil<SjyAlertStSituation>(SjyAlertStSituation.class);
        util.exportExcel(response, list, "测站预警情况数据");
    }

    /**
     * 获取测站预警情况详细信息
     */
    @PreAuthorize("@ss.hasPermi('business:situation:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Integer id) {
        return success(sjyAlertStSituationService.selectSjyAlertStSituationById(id));
    }

    /**
     * 新增测站预警情况
     */
    @PreAuthorize("@ss.hasPermi('business:situation:add')")
    @Log(title = "测站预警情况", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SjyAlertStSituation sjyAlertStSituation) {
        return toAjax(sjyAlertStSituationService.insertSjyAlertStSituation(sjyAlertStSituation));
    }

    /**
     * 修改测站预警情况
     */
    @PreAuthorize("@ss.hasPermi('business:situation:edit')")
    @Log(title = "测站预警情况", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SjyAlertStSituation sjyAlertStSituation) {
        return toAjax(sjyAlertStSituationService.updateSjyAlertStSituation(sjyAlertStSituation));
    }

    /**
     * 删除测站预警情况
     */
    @PreAuthorize("@ss.hasPermi('business:situation:remove')")
    @Log(title = "测站预警情况", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Integer[] ids) {
        return toAjax(sjyAlertStSituationService.deleteSjyAlertStSituationByIds(ids));
    }
}