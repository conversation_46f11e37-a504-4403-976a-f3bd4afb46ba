package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.tzslsjy.business.domain.SjyAlertSmsMergeRecord;
import com.tzslsjy.business.service.ISjyAlertSmsMergeRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;

import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 预警消息合并记录Controller
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Tag(name = "预警消息合并记录管理", description = "预警消息合并记录管理")
@RestController
@RequestMapping("/business/alert/merge/record")
public class SjyAlertSmsMergeRecordController extends BaseController
{
    @Autowired
    private ISjyAlertSmsMergeRecordService sjyAlertSmsMergeRecordService;

    /**
     * 查询预警消息合并记录列表
     */
    @Operation(summary = "查询预警消息合并记录列表")
    //@PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord)
    {
        startPage();
        List<SjyAlertSmsMergeRecord> list = sjyAlertSmsMergeRecordService.selectSjyAlertSmsMergeRecordList(sjyAlertSmsMergeRecord);
        return getDataTable(list);
    }

    /**
     * 导出预警消息合并记录列表
     */
    @Operation(summary = "导出预警消息合并记录列表")
    //@PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "预警消息合并记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord)
    {
        List<SjyAlertSmsMergeRecord> list = sjyAlertSmsMergeRecordService.selectSjyAlertSmsMergeRecordList(sjyAlertSmsMergeRecord);
        ExcelUtil<SjyAlertSmsMergeRecord> util = new ExcelUtil<SjyAlertSmsMergeRecord>(SjyAlertSmsMergeRecord.class);
        util.exportExcel(response, list, "预警消息合并记录数据");
    }

    /**
     * 获取预警消息合并记录详细信息
     */
    @Operation(summary = "获取预警消息合并记录详细信息")
    @Parameter(name = "smsMergeId", description = "预警消息合并记录ID", required = true)
    //@PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{smsMergeId}")
    public AjaxResult getInfo(@PathVariable("smsMergeId") Long smsMergeId)
    {
        return success(sjyAlertSmsMergeRecordService.selectSjyAlertSmsMergeRecordBySmsMergeId(smsMergeId));
    }

    /**
     * 新增预警消息合并记录
     */
    @Operation(summary = "新增预警消息合并记录")
    //@PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "预警消息合并记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Valid @RequestBody SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord)
    {
        return toAjax(sjyAlertSmsMergeRecordService.insertSjyAlertSmsMergeRecord(sjyAlertSmsMergeRecord));
    }

    /**
     * 修改预警消息合并记录
     */
    @Operation(summary = "修改预警消息合并记录")
    //@PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "预警消息合并记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Valid @RequestBody SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord)
    {
        return toAjax(sjyAlertSmsMergeRecordService.updateSjyAlertSmsMergeRecord(sjyAlertSmsMergeRecord));
    }

    /**
     * 删除预警消息合并记录
     */
    @Operation(summary = "删除预警消息合并记录")
    @Parameter(name = "smsMergeIds", description = "预警消息合并记录ID数组", required = true)
    //@PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "预警消息合并记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{smsMergeIds}")
    public AjaxResult remove(@PathVariable Long[] smsMergeIds)
    {
        return toAjax(sjyAlertSmsMergeRecordService.deleteSjyAlertSmsMergeRecordBySmsMergeIds(smsMergeIds));
    }
}
