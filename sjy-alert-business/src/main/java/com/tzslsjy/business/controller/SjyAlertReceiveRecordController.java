package com.tzslsjy.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.tzslsjy.business.domain.SjyAlertReceiveRecord;
import com.tzslsjy.business.service.ISjyAlertReceiveRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springdoc.api.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.enums.BusinessType;

import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.common.core.page.TableDataInfo;

/**
 * 预警接收记录Controller
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Tag(name = "预警接收记录管理", description = "预警接收记录管理")
@RestController
@RequestMapping("/business/alert/receive/record")
public class SjyAlertReceiveRecordController extends BaseController
{
    @Autowired
    private ISjyAlertReceiveRecordService sjyAlertReceiveRecordService;

    /**
     * 查询预警接收记录列表
     */
    @Operation(summary = "查询预警接收记录列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "查询成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = TableDataInfo.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:list')")
    @GetMapping("/list")
    public TableDataInfo list(@ParameterObject SjyAlertReceiveRecord sjyAlertReceiveRecord)
    {
        startPage();
        List<SjyAlertReceiveRecord> list = sjyAlertReceiveRecordService.selectSjyAlertReceiveRecordList(sjyAlertReceiveRecord);
        return getDataTable(list);
    }

    /**
     * 导出预警接收记录列表
     */
    @Operation(summary = "导出预警接收记录列表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "导出成功",
                    content = @Content(mediaType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:export')")
    @Log(title = "预警接收记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, @ParameterObject SjyAlertReceiveRecord sjyAlertReceiveRecord)
    {
        List<SjyAlertReceiveRecord> list = sjyAlertReceiveRecordService.selectSjyAlertReceiveRecordList(sjyAlertReceiveRecord);
        ExcelUtil<SjyAlertReceiveRecord> util = new ExcelUtil<SjyAlertReceiveRecord>(SjyAlertReceiveRecord.class);
        util.exportExcel(response, list, "预警接收记录数据");
    }

    /**
     * 获取预警接收记录详细信息
     */
    @Operation(summary = "获取预警接收记录详细信息")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "获取成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:query')")
    @GetMapping(value = "/{receiveId}")
    public AjaxResult<SjyAlertReceiveRecord> getInfo(@Parameter(name = "receiveId", description = "预警接收记录ID", required = true) @PathVariable("receiveId") Long receiveId)
    {
        return success(sjyAlertReceiveRecordService.selectSjyAlertReceiveRecordByReceiveId(receiveId));
    }

    /**
     * 新增预警接收记录
     */
    @Operation(summary = "新增预警接收记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "新增成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:add')")
    @Log(title = "预警接收记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult<Integer> add(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警接收记录信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertReceiveRecord.class)))
                          @Valid @RequestBody SjyAlertReceiveRecord sjyAlertReceiveRecord)
    {
        return toAjax(sjyAlertReceiveRecordService.insertSjyAlertReceiveRecord(sjyAlertReceiveRecord));
    }

    /**
     * 修改预警接收记录
     */
    @Operation(summary = "修改预警接收记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "修改成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:edit')")
    @Log(title = "预警接收记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult<Integer> edit(@io.swagger.v3.oas.annotations.parameters.RequestBody(description = "预警接收记录信息", required = true,
                            content = @Content(schema = @Schema(implementation = SjyAlertReceiveRecord.class)))
                           @Valid @RequestBody SjyAlertReceiveRecord sjyAlertReceiveRecord)
    {
        return toAjax(sjyAlertReceiveRecordService.updateSjyAlertReceiveRecord(sjyAlertReceiveRecord));
    }

    /**
     * 删除预警接收记录
     */
    @Operation(summary = "删除预警接收记录")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "删除成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    //@PreAuthorize("@ss.hasPermi('system:record:remove')")
    @Log(title = "预警接收记录", businessType = BusinessType.DELETE)
 @DeleteMapping("/{receiveIds}")
    public AjaxResult<Integer> remove(@Parameter(name = "receiveIds", description = "待删除预警接收记录ID数组", required = true) @PathVariable Long[] receiveIds)
    {
        return toAjax(sjyAlertReceiveRecordService.deleteSjyAlertReceiveRecordByReceiveIds(receiveIds));
    }

    /**
     * 从SMS记录同步数据到接收记录表
     */
    @Operation(summary = "从SMS记录同步数据到接收记录表")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "同步成功",
                    content = @Content(mediaType = "application/json",
                            schema = @Schema(implementation = AjaxResult.class)))
    })
    @Log(title = "预警接收记录", businessType = BusinessType.OTHER)
    @PostMapping("/sync/from-sms")
    public AjaxResult<Integer> syncFromSmsRecord(@Parameter(name = "minutes", description = "最近几分钟", required = true) @RequestParam Integer minutes)
    {
        if (minutes == null || minutes <= 0) {
            return error("参数minutes必须大于0");
        }
//        if (minutes > 1440) { // 限制最大24小时
//            return error("参数minutes不能超过1440分钟(24小时)");
//        }
        
        int syncCount = sjyAlertReceiveRecordService.syncFromSmsRecord(minutes);
        return success("同步成功，共同步" + syncCount + "条记录");
    }
}
