package com.tzslsjy.business.controller;

import java.util.List;
import java.util.Arrays;

import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.tzslsjy.business.vo.SjyPjStVo;
import com.tzslsjy.business.bo.SjyPjStQueryBo;
import com.tzslsjy.business.bo.SjyPjStAddBo;
import com.tzslsjy.business.bo.SjyPjStEditBo;
import com.tzslsjy.business.service.ISjyPjStService;

import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

/**
 * 工程测站关联Controller
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Tag(name = "工程测站关联管理", description = "工程测站关联控制器")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/lhpj/pjst")
public class SjyPjStController extends BaseController {

    private final ISjyPjStService iSjyPjStService;

    /**
     * 查询工程测站关联列表
     */
    @Operation(summary = "查询工程测站关联列表")
    //@PreAuthorize("@ss.hasPermi('lhpj:pjst:list')")
    @GetMapping("/list")
    public TableDataInfo<SjyPjStVo> list(SjyPjStQueryBo bo) {
        startPage();
        List<SjyPjStVo> list = iSjyPjStService.queryList(bo);
        return getDataTable(list);
    }

    /**
     * 导出工程测站关联列表
     */
    @Operation(summary = "导出工程测站关联列表")
    //@PreAuthorize("@ss.hasPermi('lhpj:pjst:export')")
    @Log(title = "工程测站关联", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult<SjyPjStVo> export(SjyPjStQueryBo bo) {
        List<SjyPjStVo> list = iSjyPjStService.queryList(bo);
        ExcelUtil<SjyPjStVo> util = new ExcelUtil<SjyPjStVo>(SjyPjStVo.class);
        return util.exportExcel(list, "工程测站关联");
    }

    /**
     * 获取工程测站关联详细信息
     */
    @Operation(summary = "获取工程测站关联详细信息")
    //@PreAuthorize("@ss.hasPermi('lhpj:pjst:query')")
    @GetMapping("/{id}")
    public AjaxResult<SjyPjStVo> getInfo(@PathVariable("id" ) Long id) {
        return AjaxResult.success(iSjyPjStService.queryById(id));
    }

    /**
     * 新增工程测站关联
     */
    @Operation(summary = "新增工程测站关联")
    //@PreAuthorize("@ss.hasPermi('lhpj:pjst:add')")
    @Log(title = "工程测站关联", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SjyPjStAddBo bo) {
        return toAjax(iSjyPjStService.insertByAddBo(bo) ? 1 : 0);
    }

    /**
     * 修改工程测站关联
     */
    @Operation(summary = "修改工程测站关联")
    //@PreAuthorize("@ss.hasPermi('lhpj:pjst:edit')")
    @Log(title = "工程测站关联", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody SjyPjStEditBo bo) {
        return toAjax(iSjyPjStService.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除工程测站关联
     */
    @Operation(summary = "删除工程测站关联")
    //@PreAuthorize("@ss.hasPermi('lhpj:pjst:remove')")
    @Log(title = "工程测站关联" , businessType = BusinessType.DELETE)
    @PostMapping("/delete/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(iSjyPjStService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}