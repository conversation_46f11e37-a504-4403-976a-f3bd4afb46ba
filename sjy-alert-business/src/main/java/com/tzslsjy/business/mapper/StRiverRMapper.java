package com.tzslsjy.business.mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.tzslsjy.business.domain.StRiverR;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

/**
 * 河道水情Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
@DataSource(DataSourceType.SHARDING)
public interface StRiverRMapper
{
    /**
     * 查询河道水情
     *
     * @param STCD 河道水情主键
     * @return 河道水情
     */
    public StRiverR selectStRiverRBySTCD(String STCD);

    /**
     * 查询河道水情列表
     *
     * @param stRiverR 河道水情
     * @return 河道水情集合
     */
    public List<StRiverR> selectStRiverRList(StRiverR stRiverR);

    /**
     * 新增河道水情
     *
     * @param stRiverR 河道水情
     * @return 结果
     */
    public int insertStRiverR(StRiverR stRiverR);

    /**
     * 修改河道水情
     *
     * @param stRiverR 河道水情
     * @return 结果
     */
    public int updateStRiverR(StRiverR stRiverR);

    /**
     * 删除河道水情
     *
     * @param STCD 河道水情主键
     * @return 结果
     */
    public int deleteStRiverRBySTCD(String STCD);

    /**
     * 批量删除河道水情
     *
     * @param STCDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStRiverRBySTCDs(String[] STCDs);

    StRiverR selectLastestStRiverRBySTCD(@Param("stcd") String stationId, @Param("startTm")Date startTm, @Param("endTm")Date endTm);

    BigDecimal selectLastestValBySTCD(String stationId);

    public List<String> selectExistStcdsByStcds(@Param("stcds") List<String> stcds, @Param("lastTongTime") Date lastTongTime, @Param("currentTime") Date currentTime);
}
