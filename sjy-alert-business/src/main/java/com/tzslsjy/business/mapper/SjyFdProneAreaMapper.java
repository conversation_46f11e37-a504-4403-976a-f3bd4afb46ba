package com.tzslsjy.business.mapper;

import java.util.List;
import com.tzslsjy.business.domain.SjyFdProneArea;

/**
 * 山洪易发区信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface SjyFdProneAreaMapper 
{
    /**
     * 查询山洪易发区信息
     * 
     * @param prevCode 山洪易发区信息主键
     * @return 山洪易发区信息
     */
    public SjyFdProneArea selectSjyFdProneAreaByPrevCode(String prevCode);

    /**
     * 查询山洪易发区信息列表
     * 
     * @param sjyFdProneArea 山洪易发区信息
     * @return 山洪易发区信息集合
     */
    public List<SjyFdProneArea> selectSjyFdProneAreaList(SjyFdProneArea sjyFdProneArea);

    /**
     * 新增山洪易发区信息
     * 
     * @param sjyFdProneArea 山洪易发区信息
     * @return 结果
     */
    public int insertSjyFdProneArea(SjyFdProneArea sjyFdProneArea);

    /**
     * 修改山洪易发区信息
     * 
     * @param sjyFdProneArea 山洪易发区信息
     * @return 结果
     */
    public int updateSjyFdProneArea(SjyFdProneArea sjyFdProneArea);

    /**
     * 删除山洪易发区信息
     * 
     * @param prevCode 山洪易发区信息主键
     * @return 结果
     */
    public int deleteSjyFdProneAreaByPrevCode(String prevCode);

    /**
     * 批量删除山洪易发区信息
     * 
     * @param prevCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyFdProneAreaByPrevCodes(String[] prevCodes);
}
