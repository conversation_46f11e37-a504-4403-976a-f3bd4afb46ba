package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAbnormalData;
import com.tzslsjy.business.bo.SjyAbnormalDataQueryBo;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 测站异常数据Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@DataSource(DataSourceType.INFO)
public interface SjyAbnormalDataMapper {

    /**
     * 获取列表
     * @return
     */
    List<SjyAbnormalData> getList();

    /**
     * 根据主键查询单个记录
     * @param stcd 测站编号
     * @param tm 时间
     * @param type 类型
     * @return
     */
    SjyAbnormalData selectByKey(@Param("stcd") String stcd, @Param("tm") Date tm, @Param("type") String type);

    /**
     * 根据条件查询列表
     * @param bo 查询条件
     * @return
     */
    List<SjyAbnormalData> selectByCondition(SjyAbnormalDataQueryBo bo);

    /**
     * 插入记录
     * @param entity 实体对象
     * @return
     */
    int insert(SjyAbnormalData entity);

    /**
     * 根据条件更新记录
     * @param entity 实体对象
     * @param stcd 测站编号
     * @param tm 时间
     * @param type 类型
     * @return
     */
    int updateByKey(@Param("entity") SjyAbnormalData entity, @Param("stcd") String stcd, @Param("tm") Date tm, @Param("type") String type);

    /**
     * 根据测站编号删除记录
     * @param stcds 测站编号集合
     * @return
     */
    int deleteByStcds(@Param("stcds") Collection<String> stcds);

    /**
     * 根据条件删除记录
     * @param stcd 测站编号
     * @param type 类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    int deleteByCondition(@Param("stcd") String stcd, @Param("type") String type, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 批量查询异常数据
     * @param stcds 测站编号列表
     * @param type 数据类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异常数据列表
     */
    List<SjyAbnormalData> selectBatchByCondition(@Param("stcds") List<String> stcds, @Param("types")  List<String> type, @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
