package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleNodeRelation;
import com.tzslsjy.business.domain.SjyAlertTemplateRelation;
import com.tzslsjy.business.domain.SjyAlertRuleType;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;


/**
 * 预警规则Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface SjyAlertRuleMapper 
{
    /**
     * 查询预警规则
     * 
     * @param ruleId 预警规则主键
     * @return 预警规则
     */
    public SjyAlertRule selectSjyAlertRuleByRuleId(Long ruleId);

    /**
     * 查询预警规则列表
     * 
     * @param sjyAlertRule 预警规则
     * @return 预警规则集合
     */
    public List<SjyAlertRule> selectSjyAlertRuleList(SjyAlertRule sjyAlertRule);

    /**
     * 新增预警规则
     * 
     * @param sjyAlertRule 预警规则
     * @return 结果
     */
    public int insertSjyAlertRule(SjyAlertRule sjyAlertRule);

    /**
     * 修改预警规则
     * 
     * @param sjyAlertRule 预警规则
     * @return 结果
     */
    public int updateSjyAlertRule(SjyAlertRule sjyAlertRule);

    /**
     * 删除预警规则
     * 
     * @param ruleId 预警规则主键
     * @return 结果
     */
    public int deleteSjyAlertRuleByRuleId(Long ruleId);

    /**
     * 批量删除预警规则
     * 
     * @param ruleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertRuleByRuleIds(Long[] ruleIds);

    List<SjyAlertRule> selectSjyAlertRuleByRuleType(@Param("ruleTypeId") Long ruleTypeId);

    SjyAlertRule getFloodBaseRule();

    List<SjyAlertRule> selectSjyAlertRuleByStcd(List<String> list);

    /**
     * 根据规则ID列表查询节点关系
     *
     * @param ruleIds 规则ID列表
     * @return 节点关系集合
     */
    List<SjyAlertRuleNodeRelation> selectNodeRelationsByRuleIds(@Param("ruleIds") List<Long> ruleIds);

    /**
     * 根据参数ID列表查询节点关系
     *
     * @param argIds 参数ID列表
     * @return 节点关系集合
     */
    List<SjyAlertRuleNodeRelation> selectNodeRelationsByArgIds(@Param("argIds") List<Long> argIds);

    /**
     * 根据规则ID列表查询模板关系
     *
     * @param ruleIds 规则ID列表
     * @return 模板关系集合
     */
    List<SjyAlertTemplateRelation> selectTemplateRelationsByRuleIds(@Param("ruleIds") List<Long> ruleIds);

    /**
     * 根据参数ID列表查询模板关系
     *
     * @param argIds 参数ID列表
     * @return 模板关系集合
     */
    List<SjyAlertTemplateRelation> selectTemplateRelationsByArgIds(@Param("argIds") List<Long> argIds);

    List<SjyAlertRule> selectSimpleRuleList(SjyAlertRule queryRule);

    /**
     * 根据测站ID列表和规则类型查询规则
     *
     * @param stationIds 测站ID列表
     * @param ruleTypeId 规则类型ID
     * @return 规则列表
     */
    List<SjyAlertRule> selectByStationIdsAndRuleType(@Param("stationIds") List<String> stationIds, @Param("ruleTypeId") String ruleTypeId);

    /**
     * 根据规则类型查询所有规则
     *
     * @param ruleTypeId 规则类型ID
     * @return 规则列表
     */
    List<SjyAlertRule> selectByRuleType(@Param("ruleTypeId") String ruleTypeId);
}
