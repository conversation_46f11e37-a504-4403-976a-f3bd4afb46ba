package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyFolder;
import com.tzslsjy.business.bo.SjyFolderQueryBo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文件夹信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface SjyFolderMapper extends BaseMapper<SjyFolder> {

    List<SjyFolder> selectByCIdAndType(@Param("cId") String id,@Param("cType") String number);

    /**
     * 根据条件查询文件夹列表
     *
     * @param bo 查询条件
     * @return 文件夹列表
     */
    List<SjyFolder> selectFolderListByBo(SjyFolderQueryBo bo);
}