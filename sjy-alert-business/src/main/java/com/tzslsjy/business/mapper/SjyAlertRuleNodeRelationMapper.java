package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertRuleNodeRelation;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 规则接收节点关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SjyAlertRuleNodeRelationMapper
{
    /**
     * 查询规则接收节点关联
     *
     * @param relationId 规则接收节点关联主键
     * @return 规则接收节点关联
     */
    public SjyAlertRuleNodeRelation selectSjyAlertRuleNodeRelationByRelationId(Long relationId);

    /**
     * 查询规则接收节点关联列表
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 规则接收节点关联集合
     */
    public List<SjyAlertRuleNodeRelation> selectSjyAlertRuleNodeRelationList(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation);

    /**
     * 新增规则接收节点关联
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 结果
     */
    public int insertSjyAlertRuleNodeRelation(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation);

    /**
     * 修改规则接收节点关联
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 结果
     */
    public int updateSjyAlertRuleNodeRelation(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation);

    /**
     * 删除规则接收节点关联
     *
     * @param relationId 规则接收节点关联主键
     * @return 结果
     */
    public int deleteSjyAlertRuleNodeRelationByRelationId(Long relationId);

    /**
     * 批量删除规则接收节点关联
     *
     * @param relationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertRuleNodeRelationByRelationIds(Long[] relationIds);

    /**
     * 批量插入规则接收节点关联
     *
     * @param list 规则接收节点关联列表
     * @return 结果
     */
    public int batchInsert(List<SjyAlertRuleNodeRelation> list);

    /**
     * 根据规则ID删除与规则直接关联的节点关系 (arg_id IS NULL)
     *
     * @param ruleId 规则ID
     * @return 结果
     */
    public int deleteByRuleIdAndArgIdIsNull(@Param("ruleId") Long ruleId);

    /**
     * 根据参数ID删除关联的节点关系
     *
     * @param argId 参数ID
     * @return 结果
     */
    public int deleteByArgId(@Param("argId") Long argId);

    /**
     * 根据规则ID查询与规则直接关联的节点关系 (arg_id IS NULL)
     *
     * @param ruleId 规则ID
     * @return 规则接收节点关联集合
     */
    List<SjyAlertRuleNodeRelation> selectByRuleIdAndArgIdIsNull(@Param("ruleId") Long ruleId);

    List<SjyAlertPerson> selectPersonByArgId(@Param("argId")String argId);

    public List<String> selectNodeIdsByArgId(String argId);

    List<String> selectNodeIdsByArgIdAndAdcd(@Param("argId")String argId, @Param("adcd")String adcd);

    List<String> selectNodeIdsByArgIdAndNodeId(@Param("argId")String argId,@Param("nodeId") String nodeId);
}
