package com.tzslsjy.business.mapper;

import java.util.List;
import java.util.Map;

import com.tzslsjy.business.domain.SjyAlertTemplateRelation;

/**
 * 消息模板关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SjyAlertTemplateRelationMapper
{
    /**
     * 查询消息模板关联
     *
     * @param id 消息模板关联主键
     * @return 消息模板关联
     */
    public SjyAlertTemplateRelation selectSjyAlertTemplateRelationById(Long id);

    /**
     * 查询消息模板关联列表
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 消息模板关联集合
     */
    public List<SjyAlertTemplateRelation> selectSjyAlertTemplateRelationList(SjyAlertTemplateRelation sjyAlertTemplateRelation);

    /**
     * 新增消息模板关联
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 结果
     */
    public int insertSjyAlertTemplateRelation(SjyAlertTemplateRelation sjyAlertTemplateRelation);

    /**
     * 修改消息模板关联
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 结果
     */
    public int updateSjyAlertTemplateRelation(SjyAlertTemplateRelation sjyAlertTemplateRelation);

    /**
     * 删除消息模板关联
     *
     * @param id 消息模板关联主键
     * @return 结果
     */
    public int deleteSjyAlertTemplateRelationById(Long id);

    /**
     * 批量删除消息模板关联
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertTemplateRelationByIds(Long[] ids);

    /**
     * 批量插入消息模板关联
     *
     * @param list 消息模板关联列表
     * @return 结果
     */
    public int batchInsert(List<SjyAlertTemplateRelation> list);

    /**
     * 根据关联ID和关联类型删除消息模板关联
     *
     * @param params 参数 (relationId, relationType)
     * @return 结果
     */
    public int deleteByRelationIdAndType(Map<String, Object> params);
}
