package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertNodePost;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param; // 需要导入 @Param

import java.util.List;

/**
 * 节点职位Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@DataSource(DataSourceType.SLAVE)
public interface SjyAlertNodePostMapper extends BaseMapper<SjyAlertNodePost> {

    /**
     * 批量插入节点职位
     *
     * @param nodePosts 节点职位列表
     */
    void insertBatch(@Param("list") List<SjyAlertNodePost> nodePosts);

    /**
     * 根据节点ID查询节点职位列表
     *
     * @param nodeId 节点ID
     * @return 节点职位列表
     */
    List<SjyAlertNodePost> selectSjyAlertNodePostByNodeId(String nodeId);

    /**
     * 根据ID批量删除节点职位
     *
     * @param ids 需要删除的节点职位ID列表
     * @return 影响行数
     */
    int deleteSjyAlertNodePostByIds(@Param("ids") List<String> ids);

    /**
     * 修改节点职位
     *
     * @param sjyAlertNodePost 节点职位对象
     * @return 影响行数
     */
    int updateSjyAlertNodePost(SjyAlertNodePost sjyAlertNodePost);
}