package com.tzslsjy.business.mapper;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertStSituationHis;
import org.apache.ibatis.annotations.Param;

/**
 * 测站预警情况历史Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface SjyAlertStSituationHisMapper {

    /**
     * 新增测站预警情况历史
     *
     * @param sjyAlertStSituationHis 测站预警情况历史
     * @return 结果
     */
    int insertSjyAlertStSituationHis(SjyAlertStSituationHis sjyAlertStSituationHis);

    /**
     * 批量新增测站预警情况历史
     *
     * @param list 测站预警情况历史列表
     * @return 结果
     */
    int batchInsertSjyAlertStSituationHis(List<SjyAlertStSituationHis> list);

    /**
     * 查询测站预警情况历史列表
     *
     * @param sjyAlertStSituationHis 测站预警情况历史
     * @return 测站预警情况历史集合
     */
    List<SjyAlertStSituationHis> selectSjyAlertStSituationHisList(SjyAlertStSituationHis sjyAlertStSituationHis);

}