package com.tzslsjy.business.mapper;

import java.util.List;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.business.domain.vo.NodeTreeVO;
import com.tzslsjy.business.vo.ReservoirReqVo;
import com.tzslsjy.business.vo.ReservoirVo;
import com.tzslsjy.business.vo.RiverReqVo;
import com.tzslsjy.business.vo.RiverVo;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

/**
 * 测站基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@DataSource(DataSourceType.SLAVE)
public interface StStbprpBMapper
{
    /**
     * 查询测站基础信息
     *
     * @param STCD 测站基础信息主键
     * @return 测站基础信息
     */
    public StStbprpB selectStStbprpBBySTCD(String STCD);

    /**
     * 查询测站基础信息列表
     *
     * @param stStbprpB 测站基础信息
     * @return 测站基础信息集合
     */
    public List<StStbprpB> selectStStbprpBList(StStbprpB stStbprpB);

    /**
     * 新增测站基础信息
     *
     * @param stStbprpB 测站基础信息
     * @return 结果
     */
    public int insertStStbprpB(StStbprpB stStbprpB);

    /**
     * 修改测站基础信息
     *
     * @param stStbprpB 测站基础信息
     * @return 结果
     */
    public int updateStStbprpB(StStbprpB stStbprpB);

    /**
     * 删除测站基础信息
     *
     * @param STCD 测站基础信息主键
     * @return 结果
     */
    public int deleteStStbprpBBySTCD(String STCD);

    /**
     * 批量删除测站基础信息
     *
     * @param STCDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStStbprpBBySTCDs(String[] STCDs);

    List<StStbprpB> selectByAdcd(String adcd);

    List< StStbprpB> getStnmStcd(List<String> stcds);

    List<StStbprpB> selectStStbprpBBySTCDs(@Param("stcds") List<String> stcds);

    /**
     * 查询测站基础信息列表（包含关联的村庄信息）
     *
     * @param stStbprpB 测站基础信息
     * @return 测站基础信息集合
     */
    public List<StStbprpB> selectStStbprpBListWithVillage(StStbprpB stStbprpB);

    /**
     * 查询测站基础信息列表（用于村庄关联查询的第一步，支持分页）
     *
     * @param stStbprpB 测站基础信息查询条件
     * @return 测站基础信息集合
     */
    public List<StStbprpB> selectStStbprpBListForVillage(StStbprpB stStbprpB);

    List<ReservoirVo> getRsInfo(ReservoirReqVo reservoirReqVo);

    List<RiverVo> getRvInfo(RiverReqVo vo);

    /**
     * 根据行政区划查询测站树形结构
     *
     * @param stStbprpB 查询条件
     * @return 树形结构数据
     */
    List<NodeTreeVO> selectStationTreeByAdcd(StStbprpB stStbprpB);

    /**
     * 查询所有水位测站编码
     *
     * @return 水位测站编码列表
     */
    List<String> selectAllWaterStationStcds();

    /**
     * 查询所有雨量测站编码
     *
     * @return 雨量测站编码列表
     */
    List<String> selectAllRainfallStationStcds();


}
