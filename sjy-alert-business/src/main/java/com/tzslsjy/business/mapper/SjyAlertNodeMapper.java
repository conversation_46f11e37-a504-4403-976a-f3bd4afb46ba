package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertNode;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;


/**
 * 节点Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@DataSource(DataSourceType.SLAVE)
public interface SjyAlertNodeMapper
{
    /**
     * 查询节点
     *
     * @param nodeId 节点主键
     * @return 节点
     */
    public SjyAlertNode selectSjyAlertNodeByNodeId(String nodeId);

    /**
     * 查询节点列表
     *
     * @param sjyAlertNode 节点
     * @return 节点集合
     */
    public List<SjyAlertNode> selectSjyAlertNodeList(SjyAlertNode sjyAlertNode);

    /**
     * 新增节点
     *
     * @param sjyAlertNode 节点
     * @return 结果
     */
    public int insertSjyAlertNode(SjyAlertNode sjyAlertNode);

    /**
     * 修改节点
     *
     * @param sjyAlertNode 节点
     * @return 结果
     */
    public int updateSjyAlertNode(SjyAlertNode sjyAlertNode);

    /**
     * 删除节点
     *
     * @param nodeId 节点主键
     * @return 结果
     */
    public int deleteSjyAlertNodeByNodeId(String nodeId);

    /**
     * 批量删除节点
     *
     * @param nodeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertNodeByNodeIds(String[] nodeIds);

    List<SjyAlertPerson> selectPersonByVillageIdAndArgId(@Param("argId") String argId);
    
    /**
     * 根据行政区划和参数ID查询接收人
     *
     * @param adnm 行政区划代码
     * @param argId 参数ID
     * @return 接收人列表
     */
    List<SjyAlertPerson> selectPersonByAdnmAndArgId(@Param("adnm") String adnm, @Param("argId") String argId);
    /**
     * 根据祖先ID查询所有子孙节点的ID列表 (对应XML中的 selectNodeIdsByAncestor)
     * @param ancestorId 祖先节点ID (String)
     * @return 子孙节点ID列表
     */
    List<String> selectNodeIdsByAncestor(@Param("ancestorId") String ancestorId);
}
