package com.tzslsjy.business.mapper;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.tzslsjy.business.domain.StPptnR;
import com.tzslsjy.business.vo.RainInfoHourReqVo;
import com.tzslsjy.business.vo.RainInfoHourVo;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

/**
 * 雨量Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */

public interface StPptnRMapper
{
    /**
     * 查询雨量
     *
     * @param STCD 雨量主键
     * @return 雨量
     */
    @DataSource(DataSourceType.SHARDING)
    public StPptnR selectStPptnRBySTCD(String STCD);

    /**
     * 查询雨量列表
     *
     * @param stPptnR 雨量
     * @return 雨量集合
     */
    @DataSource(DataSourceType.SHARDING)
    public List<StPptnR> selectStPptnRList(StPptnR stPptnR);

    /**
     * 新增雨量
     *
     * @param stPptnR 雨量
     * @return 结果
     */
    @DataSource(DataSourceType.SHARDING)
    public int insertStPptnR(StPptnR stPptnR);

    /**
     * 修改雨量
     *
     * @param stPptnR 雨量
     * @return 结果
     */
    @DataSource(DataSourceType.SHARDING)
    public int updateStPptnR(StPptnR stPptnR);

    /**
     * 删除雨量
     *
     * @param STCD 雨量主键
     * @return 结果
     */
    @DataSource(DataSourceType.SHARDING)
    public int deleteStPptnRBySTCD(String STCD);

    /**
     * 批量删除雨量
     *
     * @param STCDs 需要删除的数据主键集合
     * @return 结果
     */
    @DataSource(DataSourceType.SHARDING)
    public int deleteStPptnRBySTCDs(String[] STCDs);
    @DataSource(DataSourceType.SHARDING)
    BigDecimal selectOverPptnByTm(@Param("stcd") String stationId, @Param("duration")Date duration, @Param("threshold")String threshold);

    @DataSource(DataSourceType.SHARDING)
    List<StPptnR> selectLastPPtnByTm(@Param("stcd") String stationId, @Param("limitNum")int limitNum, @Param("startTm")Date startTm,@Param("endTm") Date endTm);
    @DataSource(DataSourceType.SHARDING)
    Date selectLastestTmByTm(@Param("stcd") String stationId, @Param("endTm")Date endTm);
    @DataSource(DataSourceType.SHARDING)
    BigDecimal selectTotalRainfallByTimeRange(@Param("stcd") String stationId,  @Param("startTm")Date startTm,@Param("endTm") Date endTm);

    //public List<StPptnR> selectStPptnRByStcds(List<String> equipmentCodes, Date lastTongTime, Date currentTime);
    @DataSource(DataSourceType.SHARDING)
    public List<String> selectExistStcdsByStcds(@Param("stcds") List<String> stcds, @Param("lastTongTime") Date lastTongTime, @Param("currentTime") Date currentTime);

    // ====== 新增的方法用于 StPptnHistD 模块 ======
    
    /**
     * 获取测站数据（基本信息+特征值）
     *
     * @param vo
     * @return
     */
    @DataSource(DataSourceType.SLAVE)
    List<RainInfoHourVo> loadPage(RainInfoHourReqVo vo);

    /**
     * 获取月累计雨量
     *
     * @param rainInfoVo
     * @return
     */
    @DataSource(DataSourceType.SHARDING)
    List<RainInfoHourVo> getSumMonthRain(RainInfoHourReqVo rainInfoVo);

    /**
     * 获取剩余雨量
     *
     * @param vo
     * @return
     */
    @DataSource(DataSourceType.SHARDING)
    List<RainInfoHourVo> getSumLeftRain(RainInfoHourReqVo vo);

    /**
     * 获取日雨量数据
     *
     * @param vo
     * @return
     */
    @DataSource(DataSourceType.SHARDING)
    List<RainInfoHourVo> getDayRains(RainInfoHourReqVo vo);
}
