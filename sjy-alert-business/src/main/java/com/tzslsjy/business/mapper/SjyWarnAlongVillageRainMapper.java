package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyWarnAlongVillageRain;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预警危险村雨量阈值Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@DataSource(DataSourceType.INFO)
public interface SjyWarnAlongVillageRainMapper extends BaseMapper<SjyWarnAlongVillageRain> {

    List<SjyWarnAlongVillageRain> selectByAdcd(@Param("adcd") String adcd);
}