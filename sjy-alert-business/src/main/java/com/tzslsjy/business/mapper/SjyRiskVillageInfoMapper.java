package com.tzslsjy.business.mapper;

import com.tzslsjy.business.bo.SjyRiskVillageInfoQueryBo;
import com.tzslsjy.business.domain.SjyRiskVillageInfo;
import com.tzslsjy.business.vo.SjyRiskVillageInfoVo;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;

/**
 * 村落基本情况Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface SjyRiskVillageInfoMapper extends BaseMapper<SjyRiskVillageInfo> {

    @DataSource(DataSourceType.INFO)
    SjyRiskVillageInfo selectWithId(String id);

    @DataSource(DataSourceType.INFO)
    List<SjyRiskVillageInfo> riskAreaStatistics(SjyRiskVillageInfoQueryBo bo);

    @DataSource(DataSourceType.INFO)
    List<SjyRiskVillageInfoVo> queryList(SjyRiskVillageInfoQueryBo bo);
}