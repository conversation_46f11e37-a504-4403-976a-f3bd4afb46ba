package com.tzslsjy.business.mapper;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertRuleVariable;

/**
 * 规则标签关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SjyAlertRuleVariableMapper
{
    /**
     * 查询规则标签关联
     *
     * @param ruleVariableId 规则标签关联主键
     * @return 规则标签关联
     */
    public SjyAlertRuleVariable selectSjyAlertRuleVariableByRuleVariableId(Long ruleVariableId);

    /**
     * 查询规则标签关联列表
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 规则标签关联集合
     */
    public List<SjyAlertRuleVariable> selectSjyAlertRuleVariableList(SjyAlertRuleVariable sjyAlertRuleVariable);

    /**
     * 新增规则标签关联
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 结果
     */
    public int insertSjyAlertRuleVariable(SjyAlertRuleVariable sjyAlertRuleVariable);

    /**
     * 修改规则标签关联
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 结果
     */
    public int updateSjyAlertRuleVariable(SjyAlertRuleVariable sjyAlertRuleVariable);

    /**
     * 删除规则标签关联
     *
     * @param ruleVariableId 规则标签关联主键
     * @return 结果
     */
    public int deleteSjyAlertRuleVariableByRuleVariableId(Long ruleVariableId);

    /**
     * 批量删除规则标签关联
     *
     * @param ruleVariableIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertRuleVariableByRuleVariableIds(Long[] ruleVariableIds);
}
