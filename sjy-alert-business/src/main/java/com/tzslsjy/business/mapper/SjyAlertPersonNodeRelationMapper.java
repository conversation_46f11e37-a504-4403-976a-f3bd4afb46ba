package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertPersonNodeRelation;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.List;


/**
 * 人员节点关联Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@DataSource(DataSourceType.SLAVE)
public interface SjyAlertPersonNodeRelationMapper
{
    /**
     * 查询人员节点关联
     *
     * @param relationId 人员节点关联主键
     * @return 人员节点关联
     */
    public SjyAlertPersonNodeRelation selectSjyAlertPersonNodeRelationByRelationId(Long relationId);

    /**
     * 查询人员节点关联列表
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 人员节点关联集合
     */
    public List<SjyAlertPersonNodeRelation> selectSjyAlertPersonNodeRelationList(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation);

    /**
     * 新增人员节点关联
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 结果
     */
    public int insertSjyAlertPersonNodeRelation(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation);

    /**
     * 修改人员节点关联
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 结果
     */
    public int updateSjyAlertPersonNodeRelation(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation);

    /**
     * 删除人员节点关联
     *
     * @param relationId 人员节点关联主键
     * @return 结果
     */
    public int deleteSjyAlertPersonNodeRelationByRelationId(Long relationId);

    /**
     * 批量删除人员节点关联
     *
     * @param relationIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertPersonNodeRelationByRelationIds(Long[] relationIds);

    List<SjyAlertPersonNodeRelation> selectPersonByNodeIds(  @Param("list") List<Long> nodeIds);

}
