package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.List;


/**
 * 人员Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@DataSource(DataSourceType.SLAVE)
public interface SjyAlertPersonMapper 
{
    /**
     * 查询人员
     * 
     * @param personId 人员主键
     * @return 人员
     */
    public SjyAlertPerson selectSjyAlertPersonByPersonId(String personId);

    /**
     * 查询人员列表
     * 
     * @param sjyAlertPerson 人员
     * @return 人员集合
     */
    public List<SjyAlertPerson> selectSjyAlertPersonList(SjyAlertPerson sjyAlertPerson);

    /**
     * 新增人员
     * 
     * @param sjyAlertPerson 人员
     * @return 结果
     */
    public int insertSjyAlertPerson(SjyAlertPerson sjyAlertPerson);

    /**
     * 修改人员
     * 
     * @param sjyAlertPerson 人员
     * @return 结果
     */
    public int updateSjyAlertPerson(SjyAlertPerson sjyAlertPerson);

    /**
     * 删除人员
     * 
     * @param personId 人员主键
     * @return 结果
     */
    public int deleteSjyAlertPersonByPersonId(String personId);

    /**
     * 批量删除人员
     * 
     * @param personIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertPersonByPersonIds(String[] personIds);

    /**
     * 根据ID列表批量查询人员信息
     *
     * @param personIds 人员ID列表
     * @return 人员列表
     */
    public List<SjyAlertPerson> selectSjyAlertPersonByIds(List<String> personIds);

    List<SjyAlertPerson> selectPersonByNodeIds(@Param("nodeIds") List<String> nodes);
}
