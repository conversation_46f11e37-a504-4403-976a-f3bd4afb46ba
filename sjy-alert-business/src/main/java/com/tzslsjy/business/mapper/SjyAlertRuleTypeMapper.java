package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertRuleType;

import java.util.List;


/**
 * 规则类型Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface SjyAlertRuleTypeMapper
{
    /**
     * 查询规则类型
     *
     * @param ruleTypeId 规则类型主键
     * @return 规则类型
     */
    public SjyAlertRuleType selectSjyAlertRuleTypeByRuleTypeId(Long ruleTypeId);

    /**
     * 查询规则类型列表
     *
     * @param sjyAlertRuleType 规则类型
     * @return 规则类型集合
     */
    public List<SjyAlertRuleType> selectSjyAlertRuleTypeList(SjyAlertRuleType sjyAlertRuleType);

    /**
     * 新增规则类型
     *
     * @param sjyAlertRuleType 规则类型
     * @return 结果
     */
    public int insertSjyAlertRuleType(SjyAlertRuleType sjyAlertRuleType);

    /**
     * 修改规则类型
     *
     * @param sjyAlertRuleType 规则类型
     * @return 结果
     */
    public int updateSjyAlertRuleType(SjyAlertRuleType sjyAlertRuleType);

    /**
     * 删除规则类型
     *
     * @param ruleTypeId 规则类型主键
     * @return 结果
     */
    public int deleteSjyAlertRuleTypeByRuleTypeId(Long ruleTypeId);

    /**
     * 批量删除规则类型
     *
     * @param ruleTypeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertRuleTypeByRuleTypeIds(Long[] ruleTypeIds);

    SjyAlertRuleType selectSjyAlertRuleTypeByTypeCode(String typeCode);
}
