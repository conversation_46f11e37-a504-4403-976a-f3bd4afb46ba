package com.tzslsjy.business.mapper;

import java.util.List;
import com.tzslsjy.business.domain.SjyFdStInfo;
import org.apache.ibatis.annotations.Param;

/**
 * 自动监测站基本信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface SjyFdStInfoMapper
{
    /**
     * 查询自动监测站基本信息
     *
     * @param stCode 自动监测站基本信息主键
     * @return 自动监测站基本信息
     */
    public SjyFdStInfo selectSjyFdStInfoByStCode(String stCode);

    /**
     * 查询自动监测站基本信息列表
     *
     * @param sjyFdStInfo 自动监测站基本信息
     * @return 自动监测站基本信息集合
     */
    public List<SjyFdStInfo> selectSjyFdStInfoList(SjyFdStInfo sjyFdStInfo);

    /**
     * 新增自动监测站基本信息
     *
     * @param sjyFdStInfo 自动监测站基本信息
     * @return 结果
     */
    public int insertSjyFdStInfo(SjyFdStInfo sjyFdStInfo);

    /**
     * 修改自动监测站基本信息
     *
     * @param sjyFdStInfo 自动监测站基本信息
     * @return 结果
     */
    public int updateSjyFdStInfo(SjyFdStInfo sjyFdStInfo);

    /**
     * 删除自动监测站基本信息
     *
     * @param stCode 自动监测站基本信息主键
     * @return 结果
     */
    public int deleteSjyFdStInfoByStCode(String stCode);

    /**
     * 批量删除自动监测站基本信息
     *
     * @param stCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyFdStInfoByStCodes(String[] stCodes);

    List<String> selectAllRainfallStationCodesByAdcd(@Param("adcd") String number);

    SjyFdStInfo selectSjyFdStInfoByEquiID(String stationId);
}
