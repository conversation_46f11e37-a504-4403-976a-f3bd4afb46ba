package com.tzslsjy.business.mapper;


import com.tzslsjy.business.bo.StPptnHistDo;
import com.tzslsjy.business.bo.StPptnHistRainLineRespVo;
import com.tzslsjy.business.bo.StPptnHistRespVo;
import com.tzslsjy.business.domain.StPptnHistD;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 单站多年日降雨Mapper接口
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@DataSource(DataSourceType.SLAVE)
public interface StPptnHistDMapper extends BaseMapper<StPptnHistD> {

    void insertOrUpdateBatch(@Param("list")List<StPptnHistD> list);

    List<StPptnHistRespVo> selectSumByMonthAndDay(StPptnHistDo stPptnHistDo);
    List<StPptnHistRespVo> selectSumByAllMonthAndDay(StPptnHistDo stPptnHistDo);
    List<StPptnHistRainLineRespVo> selectByMonthAndDay(StPptnHistDo stPptnHistDo);

}