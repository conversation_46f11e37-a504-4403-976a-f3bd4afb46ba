package com.tzslsjy.business.mapper.data;

import com.tzslsjy.business.domain.data.StTideR;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

/**
 * 潮汐水情Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@DataSource(DataSourceType.SHARDING)
public interface StTideRMapper extends BaseMapper<StTideR> {

    List<String> selectExistStcdsByStcds(@Param("stcds") List<String> stcds, @Param("lastTongTime") Date lastTongTime, @Param("currentTime") Date currentTime);

}