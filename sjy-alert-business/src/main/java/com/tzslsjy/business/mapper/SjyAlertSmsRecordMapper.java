package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsStatsRequest;
import com.tzslsjy.business.domain.vo.SjyAlertSmsStatsVO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预警消息记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface SjyAlertSmsRecordMapper 
{
    /**
     * 查询预警消息记录
     * 
     * @param alertSmsId 预警消息记录主键
     * @return 预警消息记录
     */
    public SjyAlertSmsRecord selectSjyAlertSmsRecordByAlertSmsId(Long alertSmsId);

    /**
     * 查询预警消息记录列表
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 预警消息记录集合
     */
    public List<SjyAlertSmsRecord> selectSjyAlertSmsRecordList(SjyAlertSmsRecord sjyAlertSmsRecord);

    /**
     * 新增预警消息记录
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 结果
     */
    public int insertSjyAlertSmsRecord(SjyAlertSmsRecord sjyAlertSmsRecord);

    /**
     * 修改预警消息记录
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 结果
     */
    public int updateSjyAlertSmsRecord(SjyAlertSmsRecord sjyAlertSmsRecord);

    /**
     * 删除预警消息记录
     * 
     * @param alertSmsId 预警消息记录主键
     * @return 结果
     */
    public int deleteSjyAlertSmsRecordByAlertSmsId(Long alertSmsId);

    /**
     * 批量删除预警消息记录
     * 
     * @param alertSmsIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertSmsRecordByAlertSmsIds(Long[] alertSmsIds);

    /**
     * 查询指定时间范围内的SMS记录
     * 
     * @param startTime 开始时间
     * @return SMS记录列表
     */
    public List<SjyAlertSmsRecord> selectSjyAlertSmsRecordsByTimeRange(@Param("startTime") Date startTime);

    /**
     * 根据预警记录ID列表和发送方式统计预警消息发送状态
     *
     * @param statsRequest 统计请求参数
     * @return 统计结果列表
     */
    public List<SjyAlertSmsStatsVO> selectSjyAlertSmsStats(SjyAlertSmsStatsRequest statsRequest);

    Integer alertCount(@Param("startTm") Date sendStartTime, @Param("endTm")Date sendEndTime, @Param("ruleTypeId")String number);


}
