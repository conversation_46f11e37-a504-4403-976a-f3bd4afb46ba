package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertRuleArg;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 预警规则参数Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SjyAlertRuleArgMapper
{
    /**
     * 查询预警规则参数
     *
     * @param argId 预警规则参数主键
     * @return 预警规则参数
     */
    public SjyAlertRuleArg selectSjyAlertRuleArgByArgId(Long argId);

    /**
     * 查询预警规则参数列表
     *
     * @param sjyAlertRuleArg 预警规则参数
     * @return 预警规则参数集合
     */
    public List<SjyAlertRuleArg> selectSjyAlertRuleArgList(SjyAlertRuleArg sjyAlertRuleArg);

    /**
     * 新增预警规则参数
     *
     * @param sjyAlertRuleArg 预警规则参数
     * @return 结果
     */
    public int insertSjyAlertRuleArg(SjyAlertRuleArg sjyAlertRuleArg);

    /**
     * 修改预警规则参数
     *
     * @param sjyAlertRuleArg 预警规则参数
     * @return 结果
     */
    public int updateSjyAlertRuleArg(SjyAlertRuleArg sjyAlertRuleArg);

    /**
     * 删除预警规则参数
     *
     * @param argId 预警规则参数主键
     * @return 结果
     */
    public int deleteSjyAlertRuleArgByArgId(Long argId);

    /**
     * 批量删除预警规则参数
     *
     * @param argIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertRuleArgByArgIds(Long[] argIds);

    /**
     * 根据规则ID查询其下所有参数的ID列表
     *
     * @param ruleId 规则ID
     * @return 参数ID列表
     */
    List<Long> selectArgIdsByRuleId(@Param("ruleId") Long ruleId);

    /**
     * 根据规则ID删除其下所有参数
     *
     * @param ruleId 规则ID
     * @return 结果
     */
    int deleteByRuleId(@Param("ruleId") Long ruleId);

}
