package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyRiskVillageStcdRel;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 村庄关联测站编号Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface SjyRiskVillageStcdRelMapper extends BaseMapper<SjyRiskVillageStcdRel> {


    List<SjyRiskVillageStcdRel> selectByVillageId(@Param("villageId") String id);

    /**
     * 根据测站编码列表批量查询村庄关联信息
     *
     * @param stcds 测站编码列表
     * @return 村庄关联信息列表
     */

    List<SjyRiskVillageStcdRel> selectBatchByStcds(@Param("stcds") List<String> stcds);
}