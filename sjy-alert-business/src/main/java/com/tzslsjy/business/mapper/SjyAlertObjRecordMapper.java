package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertObjRecord;

import java.util.List;


/**
 * 预警对象记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface SjyAlertObjRecordMapper 
{
    /**
     * 查询预警对象记录
     * 
     * @param alertRecordId 预警对象记录主键
     * @return 预警对象记录
     */
    public SjyAlertObjRecord selectSjyAlertObjRecordByAlertRecordId(Long alertRecordId);

    /**
     * 查询预警对象记录列表
     * 
     * @param sjyAlertObjRecord 预警对象记录
     * @return 预警对象记录集合
     */
    public List<SjyAlertObjRecord> selectSjyAlertObjRecordList(SjyAlertObjRecord sjyAlertObjRecord);

    /**
     * 新增预警对象记录
     * 
     * @param sjyAlertObjRecord 预警对象记录
     * @return 结果
     */
    public int insertSjyAlertObjRecord(SjyAlertObjRecord sjyAlertObjRecord);

    /**
     * 修改预警对象记录
     * 
     * @param sjyAlertObjRecord 预警对象记录
     * @return 结果
     */
    public int updateSjyAlertObjRecord(SjyAlertObjRecord sjyAlertObjRecord);

    /**
     * 删除预警对象记录
     * 
     * @param alertRecordId 预警对象记录主键
     * @return 结果
     */
    public int deleteSjyAlertObjRecordByAlertRecordId(Long alertRecordId);

    /**
     * 批量删除预警对象记录
     * 
     * @param alertRecordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertObjRecordByAlertRecordIds(Long[] alertRecordIds);
}
