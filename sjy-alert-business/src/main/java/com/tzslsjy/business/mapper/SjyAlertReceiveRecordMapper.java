package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertReceiveRecord;

import java.util.List;


/**
 * 预警接收记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface SjyAlertReceiveRecordMapper 
{
    /**
     * 查询预警接收记录
     * 
     * @param receiveId 预警接收记录主键
     * @return 预警接收记录
     */
    public SjyAlertReceiveRecord selectSjyAlertReceiveRecordByReceiveId(Long receiveId);

    /**
     * 查询预警接收记录列表
     * 
     * @param sjyAlertReceiveRecord 预警接收记录
     * @return 预警接收记录集合
     */
    public List<SjyAlertReceiveRecord> selectSjyAlertReceiveRecordList(SjyAlertReceiveRecord sjyAlertReceiveRecord);

    /**
     * 新增预警接收记录
     * 
     * @param sjyAlertReceiveRecord 预警接收记录
     * @return 结果
     */
    public int insertSjyAlertReceiveRecord(SjyAlertReceiveRecord sjyAlertReceiveRecord);

    /**
     * 修改预警接收记录
     * 
     * @param sjyAlertReceiveRecord 预警接收记录
     * @return 结果
     */
    public int updateSjyAlertReceiveRecord(SjyAlertReceiveRecord sjyAlertReceiveRecord);

    /**
     * 删除预警接收记录
     * 
     * @param receiveId 预警接收记录主键
     * @return 结果
     */
    public int deleteSjyAlertReceiveRecordByReceiveId(Long receiveId);

    /**
     * 批量删除预警接收记录
     * 
     * @param receiveIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertReceiveRecordByReceiveIds(Long[] receiveIds);
}
