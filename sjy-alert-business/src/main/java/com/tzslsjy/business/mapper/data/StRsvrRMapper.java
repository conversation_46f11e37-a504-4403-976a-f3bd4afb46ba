package com.tzslsjy.business.mapper.data;

import com.tzslsjy.business.domain.data.StRsvrR;

import java.util.Date;
import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tzslsjy.business.vo.ReservoirReqVo;
import com.tzslsjy.business.vo.ReservoirVo;
import com.tzslsjy.business.vo.ZvarlVo;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

/**
 * 水库水情Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */

public interface StRsvrRMapper extends BaseMapper<StRsvrR> {
    @DataSource(DataSourceType.SHARDING)
    List<String> selectExistStcdsByStcds(@Param("stcds") List<String> stcds, @Param("lastTongTime") Date lastTongTime, @Param("currentTime") Date currentTime);

    /**
     * 根据测站编码查询最新的水库水情信息
     *
     * @param stcd 测站编码
     * @return 最新的水库水情信息
     */
    @DataSource(DataSourceType.SHARDING)
    StRsvrR selectLatestByStcd(@Param("stcd") String stcd,@Param("startTm") Date startTm );
    @DataSource(DataSourceType.SLAVE)
    List<ReservoirVo> getSts(ReservoirReqVo vo);
    @DataSource(DataSourceType.SHARDING)
    List<ReservoirVo> getVal(ReservoirReqVo vo);
    @DataSource(DataSourceType.SLAVE)
    List<ZvarlVo> getZvarls(@Param("stcd") String stcd);
}