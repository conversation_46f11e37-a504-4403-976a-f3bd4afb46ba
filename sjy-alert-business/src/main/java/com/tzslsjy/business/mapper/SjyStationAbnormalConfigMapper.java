package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyStationAbnormalConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 测站异常预警配置Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@DataSource(DataSourceType.INFO)
public interface SjyStationAbnormalConfigMapper extends BaseMapper<SjyStationAbnormalConfig> {

    /**
     * 根据测站编号和配置类型查询配置
     * @param stcd 测站编号
     * @param configType 配置类型
     * @return 配置信息
     */
    SjyStationAbnormalConfig selectByStcdAndType(@Param("stcd") String stcd, @Param("configType") Integer configType);

    /**
     * 根据配置类型查询所有配置
     * @param configType 配置类型
     * @return 配置列表
     */
    List<SjyStationAbnormalConfig> selectByConfigType(@Param("configType") Integer configType);

    /**
     * 根据配置类型查询启用的配置
     * @param configType 配置类型
     * @return 启用的配置列表
     */
    List<SjyStationAbnormalConfig> selectEnabledByConfigType(@Param("configType") Integer configType);

    /**
     * 批量更新启用状态
     * @param stcds 测站编号列表
     * @param configType 配置类型
     * @param enabled 启用状态
     * @return 更新数量
     */
    int batchUpdateEnabled(@Param("stcds") List<String> stcds, 
                          @Param("configType") Integer configType, 
                          @Param("enabled") Integer enabled);

    /**
     * 更新全局开关
     * @param configType 配置类型
     * @param globalSwitch 全局开关状态
     * @return 更新数量
     */
    int updateGlobalSwitch(@Param("configType") Integer configType, 
                          @Param("globalSwitch") Integer globalSwitch);

    /**
     * 获取全局开关状态
     * @param configType 配置类型
     * @return 全局开关状态
     */
    Integer getGlobalSwitch(@Param("configType") Integer configType);
}
