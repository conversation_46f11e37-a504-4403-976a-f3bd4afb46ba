package com.tzslsjy.business.mapper;

import java.util.List;

import com.tzslsjy.business.domain.SjyFdProneArea;
import com.tzslsjy.business.domain.SjyFdStObj;

/**
 * 自动监测站名录Mapper接口
 *
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface SjyFdStObjMapper
{
    /**
     * 查询自动监测站名录
     *
     * @param stCode 自动监测站名录主键
     * @return 自动监测站名录
     */
    public SjyFdStObj selectSjyFdStObjByStCode(String stCode);

    /**
     * 查询自动监测站名录列表
     *
     * @param sjyFdStObj 自动监测站名录
     * @return 自动监测站名录集合
     */
    public List<SjyFdStObj> selectSjyFdStObjList(SjyFdStObj sjyFdStObj);

    /**
     * 新增自动监测站名录
     *
     * @param sjyFdStObj 自动监测站名录
     * @return 结果
     */
    public int insertSjyFdStObj(SjyFdStObj sjyFdStObj);

    /**
     * 修改自动监测站名录
     *
     * @param sjyFdStObj 自动监测站名录
     * @return 结果
     */
    public int updateSjyFdStObj(SjyFdStObj sjyFdStObj);

    /**
     * 删除自动监测站名录
     *
     * @param stCode 自动监测站名录主键
     * @return 结果
     */
    public int deleteSjyFdStObjByStCode(String stCode);

    /**
     * 批量删除自动监测站名录
     *
     * @param stCodes 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyFdStObjByStCodes(String[] stCodes);

    List<SjyFdProneArea> selectVillageByStcd(String stcd);

    List<SjyFdProneArea> selectVillageByStCode(String stationId);


    SjyFdStObj selectSjyFdStObjByStcd(String stcd);

    SjyFdStObj selectOneByStcd(String stationId);
}
