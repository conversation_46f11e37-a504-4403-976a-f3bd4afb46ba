package com.tzslsjy.business.mapper;

import com.tzslsjy.business.domain.SjyAlertSmsMergeRecord;

import java.util.List;

/**
 * 预警消息合并记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface SjyAlertSmsMergeRecordMapper 
{
    /**
     * 查询预警消息合并记录
     * 
     * @param smsMergeId 预警消息合并记录主键
     * @return 预警消息合并记录
     */
    public SjyAlertSmsMergeRecord selectSjyAlertSmsMergeRecordBySmsMergeId(Long smsMergeId);

    /**
     * 查询预警消息合并记录列表
     * 
     * @param sjyAlertSmsMergeRecord 预警消息合并记录
     * @return 预警消息合并记录集合
     */
    public List<SjyAlertSmsMergeRecord> selectSjyAlertSmsMergeRecordList(SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord);

    /**
     * 新增预警消息合并记录
     * 
     * @param sjyAlertSmsMergeRecord 预警消息合并记录
     * @return 结果
     */
    public int insertSjyAlertSmsMergeRecord(SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord);

    /**
     * 修改预警消息合并记录
     * 
     * @param sjyAlertSmsMergeRecord 预警消息合并记录
     * @return 结果
     */
    public int updateSjyAlertSmsMergeRecord(SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord);

    /**
     * 删除预警消息合并记录
     * 
     * @param smsMergeId 预警消息合并记录主键
     * @return 结果
     */
    public int deleteSjyAlertSmsMergeRecordBySmsMergeId(Long smsMergeId);

    /**
     * 批量删除预警消息合并记录
     * 
     * @param smsMergeIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSjyAlertSmsMergeRecordBySmsMergeIds(Long[] smsMergeIds);
}
