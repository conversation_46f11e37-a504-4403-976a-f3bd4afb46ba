package com.tzslsjy.business.mapper.comm;

import com.tzslsjy.business.domain.comm.BusinessCommCity;
import com.tzslsjy.business.domain.comm.BusinessCommCItyTree;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * @auther seven
 * @create 2021-05-11 10:55:10
 * @describe 行政区划、城市乡镇mapper类 (Business specific)
 */

public interface BusinessCommCityMapper extends BaseMapper<BusinessCommCity> { // Renamed interface
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCity> getCitys();
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCity> getCityCounty();
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCity> getStreet();
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCity> getStreetByAddvcd(@Param("addvcd") String addvcd);
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCity> pageList(BusinessCommCity vo);
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCItyTree> getAll(BusinessCommCity vo);
    @DataSource(DataSourceType.SLAVE)
     String   getNameByAddvcd(String addvcd);
    @DataSource(DataSourceType.SLAVE)
    String   getNamesByAddvcd(String addvcd);
    @DataSource(DataSourceType.SLAVE)
    List<String> getTableNames(@Param("name") String name);

    @DataSource(DataSourceType.MASTER)
    List<String> getTableNamesDB1(@Param("name") String name);
    @DataSource(DataSourceType.SLAVE)
    String getFullNameByAdcd(String addCode);
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCity> getCity();
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCity> getCity1();
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCity> getCcounty(String addvcd);
    @DataSource(DataSourceType.SLAVE)
    String getAddvcdByNameAndPid(@Param("name")String text, @Param("pid")String addvcd);
    @DataSource(DataSourceType.SLAVE)
    List<BusinessCommCItyTree> getAllWithVillage(BusinessCommCity vo);
    @DataSource(DataSourceType.INFO)
    List<BusinessCommCItyTree> getVillage(BusinessCommCity vo);
}