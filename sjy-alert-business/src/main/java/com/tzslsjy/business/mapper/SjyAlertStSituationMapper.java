package com.tzslsjy.business.mapper;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertStSituation;
import org.apache.ibatis.annotations.Param;

/**
 * 测站预警情况Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface SjyAlertStSituationMapper {

    /**
     * 查询测站预警情况列表
     *
     * @param sjyAlertStSituation 测站预警情况
     * @return 测站预警情况集合
     */
    List<SjyAlertStSituation> selectSjyAlertStSituationList(SjyAlertStSituation sjyAlertStSituation);

    /**
     * 查询测站预警情况
     *
     * @param id 测站预警情况主键
     * @return 测站预警情况
     */
    SjyAlertStSituation selectSjyAlertStSituationById(Integer id);

    /**
     * 新增测站预警情况
     *
     * @param sjyAlertStSituation 测站预警情况
     * @return 结果
     */
    int insertSjyAlertStSituation(SjyAlertStSituation sjyAlertStSituation);

    /**
     * 修改测站预警情况
     *
     * @param sjyAlertStSituation 测站预警情况
     * @return 结果
     */
    int updateSjyAlertStSituation(SjyAlertStSituation sjyAlertStSituation);

    /**
     * 删除测站预警情况
     *
     * @param id 测站预警情况主键
     * @return 结果
     */
    int deleteSjyAlertStSituationById(Integer id);

    /**
     * 批量删除测站预警情况
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSjyAlertStSituationByIds(Integer[] ids);

    /**
     * 根据测站编码、关联ID和关联类型查询预警情况
     *
     * @param stcd 测站编码
     * @param cId 关联ID
     * @param cType 关联类型
     * @return 测站预警情况
     */
    SjyAlertStSituation selectByStcdAndRelation(@Param("stcd") String stcd, @Param("cId")Integer cId, @Param("cType")Integer cType);

    /**
     * 更新或插入测站预警情况
     *
     * @param sjyAlertStSituation 测站预警情况
     * @return 结果
     */
    int insertOrUpdate(SjyAlertStSituation sjyAlertStSituation);

    /**
     * 查询所有仍在预警状态的测站（状态为1或2）
     *
     * @param cId 关联ID
     * @param cType 关联类型
     * @return 预警测站列表
     */
    List<SjyAlertStSituation> selectActiveAlertStations(@Param("cId")Integer cId, @Param("cType")Integer cType);
}