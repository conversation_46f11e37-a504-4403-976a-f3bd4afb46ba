package com.tzslsjy.business.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;

/**
 * 村庄关联测站编号添加对象 sjy_risk_village_stcd_rel
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "村庄关联测站编号添加对象测站多个 adcd")
public class SjyRiskVillageStcdRelStcdAddBo {

    /** 多个逗号隔开 */
    @NotBlank(message = "村庄id不能为空")
    @Schema(description = "村庄adcd 多个逗号隔开")
    private String adcds;

    /** 测站编码 */
    @NotBlank(message = "测站编码不能为空")
    @Schema(description = "测站编码")
    private String stcd;


}