package com.tzslsjy.business.bo;

import com.tzslsjy.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Map;
import java.util.HashMap;

import java.math.BigDecimal;


/**
 * 工程测站关联分页查询对象 sjy_pj_st
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "工程测站关联分页查询对象")
public class SjyPjStQueryBo extends BaseEntity {

	/** 分页大小 */
	@Schema(description = "分页大小")
	private Integer pageSize;
	/** 当前页数 */
	@Schema(description = "当前页数")
	private Integer pageNum;
	/** 排序列 */
	@Schema(description = "排序列")
	private String orderByColumn;
	/** 排序的方向desc或者asc */
	@Schema(description = "排序的方向", example = "asc,desc")
	private String isAsc;


	/** 测站编码 */
	@Schema(description = "测站编码")
	private String stcd;
	/** 工程编码 */
	@Schema(description = "工程编码")
	private String pjcd;
	/** 是否主站 */
	@Schema(description = "是否主站")
	private String isMian;
	/** 是否雨量站 */
	@Schema(description = "是否雨量站")
	private String isRain;
	/** 雨量权重 */
	@Schema(description = "雨量权重")
	private BigDecimal rainWeight;
	/** 工程类型 */
	@Schema(description = "工程类型")
	private String projType;
	/** 排序 */
	@Schema(description = "排序")
	private Long orderNum;
	/** 是否删除;是否删除:1未删除，2删除 */
	@Schema(description = "是否删除;是否删除:1未删除，2删除")
	private Long izDel;

}