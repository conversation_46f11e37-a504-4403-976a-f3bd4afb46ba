package com.tzslsjy.business.bo;

import com.tzslsjy.business.domain.SjyRiskVillageStcdRel;
import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;


import java.math.BigDecimal;
import java.util.List;

/**
 * 村落基本情况添加对象 sjy_risk_village_info
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@Schema(description = "村落基本情况添加对象")
public class SjyRiskVillageInfoAddBo {

    /** 乡镇街道 */
    @Schema(description = "乡镇街道")
    private String townName;
    /** 行政村代码 */
    @Schema(description = "行政村代码")
    private String adcd;
    /** 行政村名称 */
    @Schema(description = "行政村名称")
    private String villageAdmin;
    /** 自然村名称 */
    @Schema(description = "自然村名称")
    private String villageNatural;
    /** 自然村编码 */
    @Schema(description = "自然村编码")
    private String cadcd;
    /** 自然村辖区面积(km²) */
    @Schema(description = "自然村辖区面积(km²)")
    private BigDecimal area;
    /** 自然村内总户数 */
    @Schema(description = "自然村内总户数")
    private Long houseNum;
    /** 自然村内总人口 */
    @Schema(description = "自然村内总人口")
    private Long popuNum;
    /** 自然村中心位置经度 */
    @Schema(description = "自然村中心位置经度")
    private BigDecimal lgtd;
    /** 自然村中心位置纬度 */
    @Schema(description = "自然村中心位置纬度")
    private BigDecimal lttd;
    /** 危险区等级 (例如: 高, 危, 极高) (Excel注释为*危险区等级, 但示例有空值) */
    @Schema(description = "危险区等级 (例如: 高, 危, 极高) (Excel注释为*危险区等级, 但示例有空值)")
    private Long riskLevel;
    /** 转移安置点 */
    @Schema(description = "转移安置点")
    private String transPlace;
    /** 应急物资 */
    @Schema(description = "应急物资")
    private String urgentResource;
    /** 预警设备 */
    @Schema(description = "预警设备")
    private String warnDevice;
    /** 人工监测设施 */
    @Schema(description = "人工监测设施")
    private String monitorDevice;
    /** 创建人 */
    @Schema(description = "创建人")
    private String createBy;
    /** 创建时间 */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /** 更新人 */
    @Schema(description = "更新人")
    private String updateBy;
    /** 更新时间 */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /** 是否删除;是否删除:1未删除，2删除 */
    @Schema(description = "是否删除;是否删除:1未删除，2删除")
    private Long izDel;
    /** 备注;冗余，可用于数据库层面临时操作时的标记 */
    @Schema(description = "备注;冗余，可用于数据库层面临时操作时的标记")
    private String remark;
    @Schema(description = "关联测站列表")
    private List<SjyRiskVillageStcdRel> sts;
}