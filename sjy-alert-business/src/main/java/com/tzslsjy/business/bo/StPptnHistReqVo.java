package com.tzslsjy.business.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 单站多年日降雨添加对象 st_pptn_hist_d
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Data
@Schema(description = "单站历史降雨分析请求对象")
public class StPptnHistReqVo {

    /**  */
    @Schema(description = "测站编号")
    private String stcd;
    /**  */
    @Schema(description = "开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTm;
    /**  */
    @Schema(description = "结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTm;

    /**  */
    @Schema(description = "测站名称")
    private String stnm;
    /**  */
    @Schema(description = "类型（1县市区 2 测站）")
    private Integer type;
    /**  */
    @Schema(description = "行政区划")
    private String addvcd;
}