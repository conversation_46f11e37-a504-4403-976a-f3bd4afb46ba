package com.tzslsjy.business.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 单站多年日降雨添加对象 st_pptn_hist_d
 *
 * <AUTHOR>
 * @date 2023-05-15
 */
@Data
@Schema(description = "单站多年日降雨添加对象")
public class StPptnHistDAddBo {

    /** 测站编号 */
    @Schema(description = "测站编号")
    private String stcd;
    /** 站名 */
    @Schema(description = "站名")
    private String stnm;
    /** 时间 */
    @Schema(description = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tm;
    /** 降水量 */
    @Schema(description = "降水量")
    private BigDecimal drp;
    /** 月份 */
    @Schema(description = "月份")
    private Long month;
    /** 日期 */
    @Schema(description = "日期")
    private Long day;
    /** 类型（1县市区 2 测站） */
    @Schema(description = "类型（1县市区 2 测站）")
    private Long type;
    /** 行政区划编号 */
    @Schema(description = "行政区划编号")
    private String addvcd;
}