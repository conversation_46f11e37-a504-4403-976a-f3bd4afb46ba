package com.tzslsjy.business.bo;

import com.tzslsjy.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.Map;
import java.util.HashMap;



/**
 * 节点职位分页查询对象 sjy_alert_node_post
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "节点职位分页查询对象")
public class SjyAlertNodePostQueryBo extends BaseEntity {

	/** 分页大小 */
	@Schema(description = "分页大小")
	private Integer pageSize;
	/** 当前页数 */
	@Schema(description = "当前页数")
	private Integer pageNum;
	/** 排序列 */
	@Schema(description = "排序列")
	private String orderByColumn;
	/** 排序的方向desc或者asc */
	@Schema(description = "排序的方向", example = "asc,desc")
	private String isAsc;


	/** 节点id */
	@Schema(description = "节点id")
	private Long nodeId;
	/** 职位名称 */
	@Schema(description = "职位名称")
	private String postName;

}
