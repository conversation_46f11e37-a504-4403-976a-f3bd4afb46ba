package com.tzslsjy.business.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 村庄关联测站编号添加对象 sjy_risk_village_stcd_rel
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "村庄关联测站编号添加对象")
public class SjyRiskVillageStcdRelAddBo {

    /** 村庄id */
    @NotBlank(message = "村庄id不能为空")
    @Schema(description = "村庄id")
    private String villageId;

    /** 测站编码 */
    @NotBlank(message = "测站编码不能为空")
    @Schema(description = "测站编码")
    private String stcd;

    /** 行政区划编码 */
    private String adcd;

    /** 备注 */
    @Schema(description = "备注")
    private String remark;

    /** 村落类型 */
    @Schema(description = "村落类型")
    private Integer villageType;

    /** 是否在同一小流域 */
    @Schema(description = "是否在同一小流域")
    private Integer isSameCa;

    /** 位置(上游,下游) */
    @Schema(description = "位置(上游,下游)")
    private Integer positionType;

    /** 距离 */
    @Schema(description = "距离")
    private BigDecimal positionDistinct;

}