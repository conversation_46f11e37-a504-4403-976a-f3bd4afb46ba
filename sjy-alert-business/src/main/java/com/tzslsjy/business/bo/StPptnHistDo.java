package com.tzslsjy.business.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 单站多年日降雨添加对象 st_pptn_hist_d
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Data
@Schema(description = "单站历史降雨分析请求对象")
public class StPptnHistDo {

    /**  */
    @Schema(description = "测站编号")
    private String stcd;
    /**  */
    @Schema(description = "开始时间")
    private Integer startDay;
    /**  */
    @Schema(description = "结束时间")
    private Integer endDay;
    /**  */
    @Schema(description = "开始时间")
    private Integer startMonth;
    /**  */
    @Schema(description = "结束时间")
    private Integer endMonth;
    /**  */
    @Schema(description = "测站名称")
    private String stnm;
    /**  */
    @Schema(description = "类型（1县市区 2 测站）")
    private Integer type;
    /**  */
    @Schema(description = "行政区划")
    private String addvcd;
}