package com.tzslsjy.business.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;



/**
 * 节点职位添加对象 sjy_alert_node_post
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@Schema(description = "节点职位添加对象")
public class SjyAlertNodePostAddBo {

    /** 节点id */
    @Schema(description = "节点id")
    private Long nodeId;
    /** 职位名称 */
    @Schema(description = "职位名称")
    private String postName;
    /** 创建人 */
    @Schema(description = "创建人")
    private String createBy;
    /** 创建时间 */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /** 更新人 */
    @Schema(description = "更新人")
    private String updateBy;
    /** 更新时间 */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /** 备注 */
    @Schema(description = "备注")
    private String remark;
}
