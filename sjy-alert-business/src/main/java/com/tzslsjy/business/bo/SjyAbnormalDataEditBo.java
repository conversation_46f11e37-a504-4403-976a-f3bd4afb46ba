package com.tzslsjy.business.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;
import java.math.BigDecimal;

/**
 * 测站异常数据编辑对象 sjy_abnormal_data
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Data
@Schema(description = "测站异常数据编辑对象")
public class SjyAbnormalDataEditBo {

    /** 测站编号 */
    @Schema(description = "测站编号")
    private String stcd;

    /** 值 */
    @Schema(description = "值")
    private BigDecimal val;

    /** 与上个时间点差值 */
    @Schema(description = "与上个时间点差值")
    private BigDecimal diff;

    /** 类型 */
    @Schema(description = "类型")
    private String type;

    /** 时间 */
    @Schema(description = "时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date tm;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
