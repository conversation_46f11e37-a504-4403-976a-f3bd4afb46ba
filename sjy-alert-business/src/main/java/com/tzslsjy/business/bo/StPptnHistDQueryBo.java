package com.tzslsjy.business.bo;

import com.tzslsjy.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 单站多年日降雨分页查询对象 st_pptn_hist_d
 *
 * <AUTHOR>
 * @date 2023-05-15
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "单站多年日降雨分页查询对象")
public class StPptnHistDQueryBo extends BaseEntity {

	/** 分页大小 */
	@Schema(description = "分页大小")
	private Integer pageSize;
	/** 当前页数 */
	@Schema(description = "当前页数")
	private Integer pageNum;
	/** 排序列 */
	@Schema(description = "排序列")
	private String orderByColumn;
	/** 排序的方向desc或者asc */
	@Schema(description = "排序的方向", example = "asc,desc")
	private String isAsc;


	/** 测站编号 */
	@Schema(description = "测站编号")
	private String stcd;
	/** 站名 */
	@Schema(description = "站名")
	private String stnm;
	/** 时间 */
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@Schema(description = "时间")
	private Date tm;
	/** 降水量 */
	@Schema(description = "降水量")
	private BigDecimal drp;
	/** 月份 */
	@Schema(description = "月份")
	private Long month;
	/** 日期 */
	@Schema(description = "日期")
	private Long day;
	/** 类型（1县市区 2 测站） */
	@Schema(description = "类型（1县市区 2 测站）")
	private Long type;
	/** 行政区划编号 */
	@Schema(description = "行政区划编号")
	private String addvcd;

}