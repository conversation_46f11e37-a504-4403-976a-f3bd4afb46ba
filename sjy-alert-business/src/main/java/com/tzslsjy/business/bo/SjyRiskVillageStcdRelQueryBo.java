package com.tzslsjy.business.bo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 村庄关联测站编号分页查询对象 sjy_risk_village_stcd_rel
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)

@Schema(description = "村庄关联测站编号分页查询对象")
public class SjyRiskVillageStcdRelQueryBo   {

	/** 分页大小 */
	@Schema(description = "分页大小")
	private Integer pageSize;
	/** 当前页数 */
	@Schema(description = "当前页数")
	private Integer pageNum;
	/** 排序列 */
	@Schema(description = "排序列")
	private String orderByColumn;
	/** 排序的方向desc或者asc */
	@Schema(description = "排序的方向", example = "asc,desc")
	private String isAsc;

	/** 村庄id */
	@Schema(description = "村庄id")
	private String villageId;
	/** 测站编码 */
	@Schema(description = "测站编码")
	private String stcd;

	/** 行政区划编码 */
	@Schema(description = "行政区划编码")
	private String adcd;

	/** 创建人 */
	@Schema(description = "创建人")
	private String createBy;

	/** 创建时间 */
	@Schema(description = "创建时间")
	private Date createTime;

	/** 更新人 */
	@Schema(description = "更新人")
	private String updateBy;

	/** 更新时间 */
	@Schema(description = "更新时间")
	private Date updateTime;

	/** 备注 */
	@Schema(description = "备注")
	private String remark;

	/** 村落类型 */
	@Schema(description = "村落类型")
	private Integer villageType;

	/** 是否在同一小流域 */
	@Schema(description = "是否在同一小流域")
	private Integer isSameCa;

	/** 位置(上游,下游) */
	@Schema(description = "位置(上游,下游)")
	private Integer positionType;

	/** 距离 */
	@Schema(description = "距离")
	private BigDecimal positionDistinct;

}