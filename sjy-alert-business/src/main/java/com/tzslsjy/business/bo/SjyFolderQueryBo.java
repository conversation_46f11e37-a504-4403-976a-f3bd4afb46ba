package com.tzslsjy.business.bo;

import com.tzslsjy.common.core.domain.BaseEntity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件夹信息分页查询对象 sjy_folder
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件夹信息分页查询对象")
public class SjyFolderQueryBo extends BaseEntity {

	/** 分页大小 */
	@Schema(description = "分页大小")
	private Integer pageSize;
	/** 当前页数 */
	@Schema(description = "当前页数")
	private Integer pageNum;
	/** 排序列 */
	@Schema(description = "排序列")
	private String orderByColumn;
	/** 排序的方向desc或者asc */
	@Schema(description = "排序的方向", example = "asc,desc")
	private String isAsc;
	/** 文件夹类型 */
	@Schema(description = "文件夹类型(事件：0-影像资料，1-水文资料，2-处置资料，4-灾情资料)")
	private String folderType;
	/** 所属ID类型 */
	@Schema(description = "所属id 类型，1-村庄，2-工程，3-测站，4-事件复盘")
	private String cType;

	/** 文件夹名称 */
	@Schema(description = "文件夹名称")
	private String name;
	/** 父文件夹ID，NULL表示根目录 */
	@Schema(description = "父文件夹ID，NULL表示根目录")
	private String parentFolderId;
	/** 所属ID */
	@Schema(description = "所属ID")
	private String cId;
	/** 是否删除;是否删除:1未删除，2删除 */
	@Schema(description = "是否删除;是否删除:1未删除，2删除")
	private Long izDel;

}