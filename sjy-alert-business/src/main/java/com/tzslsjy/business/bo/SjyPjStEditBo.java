package com.tzslsjy.business.bo;

import io.swagger.v3.oas.annotations.media.Schema;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import java.util.Date;

import java.math.BigDecimal;

/**
 * 工程测站关联编辑对象 sjy_pj_st
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Data
@Schema(description = "工程测站关联编辑对象")
public class SjyPjStEditBo {


    /**  */
    @Schema(description = "")
    private Long id;

    /** 测站编码 */
    @Schema(description = "测站编码")
    private String stcd;

    /** 工程编码 */
    @Schema(description = "工程编码")
    private String pjcd;

    /** 是否主站 */
    @Schema(description = "是否主站")
    private String isMian;

    /** 是否雨量站 */
    @Schema(description = "是否雨量站")
    private String isRain;

    /** 雨量权重 */
    @Schema(description = "雨量权重")
    private BigDecimal rainWeight;

    /** 工程类型 */
    @Schema(description = "工程类型")
    private String projType;

    /** 排序 */
    @Schema(description = "排序")
    private Long orderNum;

    /** 更新人 */
    @Schema(description = "更新人")
    private Long updateBy;

    /** 更新时间 */
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /** 是否删除;是否删除:1未删除，2删除 */
    @Schema(description = "是否删除;是否删除:1未删除，2删除")
    private Long izDel;

    /** 备注;冗余，可用于数据库层面临时操作时的标记 */
    @Schema(description = "备注;冗余，可用于数据库层面临时操作时的标记")
    private String remark;
}