package com.tzslsjy.business.listener;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.service.scheduler.AlertRuleSchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * 预警规则变更监听器
 * 监听预警规则的增删改操作，自动更新对应的定时任务
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class AlertRuleChangeListener {

    @Autowired
    private AlertRuleSchedulerService alertRuleSchedulerService;

    /**
     * 监听预警规则创建事件
     */
    @EventListener
    @Async
    public void handleAlertRuleCreated(AlertRuleCreatedEvent event) {
        log.info("监听到预警规则创建事件: {}", event.getRule().getRuleName());
        
        try {
            boolean success = alertRuleSchedulerService.createAlertRuleJob(event.getRule());
            if (success) {
                log.info("成功为新创建的预警规则创建定时任务: {}", event.getRule().getRuleName());
            } else {
                log.warn("为新创建的预警规则创建定时任务失败: {}", event.getRule().getRuleName());
            }
        } catch (Exception e) {
            log.error("处理预警规则创建事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 监听预警规则更新事件
     */
    @EventListener
    @Async
    public void handleAlertRuleUpdated(AlertRuleUpdatedEvent event) {
        log.info("监听到预警规则更新事件: {}", event.getRule().getRuleName());
        
        try {
            boolean success = alertRuleSchedulerService.updateAlertRuleJob(event.getRule());
            if (success) {
                log.info("成功更新预警规则的定时任务: {}", event.getRule().getRuleName());
            } else {
                log.warn("更新预警规则的定时任务失败: {}", event.getRule().getRuleName());
            }
        } catch (Exception e) {
            log.error("处理预警规则更新事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 监听预警规则删除事件
     */
    @EventListener
    @Async
    public void handleAlertRuleDeleted(AlertRuleDeletedEvent event) {
        log.info("监听到预警规则删除事件: {}", event.getRuleId());
        
        try {
            boolean success = alertRuleSchedulerService.deleteAlertRuleJob(event.getRuleId());
            if (success) {
                log.info("成功删除预警规则的定时任务: {}", event.getRuleId());
            } else {
                log.warn("删除预警规则的定时任务失败: {}", event.getRuleId());
            }
        } catch (Exception e) {
            log.error("处理预警规则删除事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 监听预警规则状态变更事件
     */
    @EventListener
    @Async
    public void handleAlertRuleStatusChanged(AlertRuleStatusChangedEvent event) {
        log.info("监听到预警规则状态变更事件: {} -> {}", 
                event.getRuleId(), event.getNewStatus());
        
        try {
            if (event.getNewStatus() == 1) {
                // 规则启用，创建或恢复任务
                if (event.getRule() != null) {
                    alertRuleSchedulerService.createAlertRuleJob(event.getRule());
                } else {
                    alertRuleSchedulerService.resumeAlertRuleJob(event.getRuleId());
                }
                log.info("预警规则启用，任务已创建/恢复: {}", event.getRuleId());
            } else {
                // 规则禁用，暂停或删除任务
                alertRuleSchedulerService.pauseAlertRuleJob(event.getRuleId());
                log.info("预警规则禁用，任务已暂停: {}", event.getRuleId());
            }
        } catch (Exception e) {
            log.error("处理预警规则状态变更事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 预警规则创建事件
     */
    public static class AlertRuleCreatedEvent {
        private final SjyAlertRule rule;

        public AlertRuleCreatedEvent(SjyAlertRule rule) {
            this.rule = rule;
        }

        public SjyAlertRule getRule() {
            return rule;
        }
    }

    /**
     * 预警规则更新事件
     */
    public static class AlertRuleUpdatedEvent {
        private final SjyAlertRule rule;

        public AlertRuleUpdatedEvent(SjyAlertRule rule) {
            this.rule = rule;
        }

        public SjyAlertRule getRule() {
            return rule;
        }
    }

    /**
     * 预警规则删除事件
     */
    public static class AlertRuleDeletedEvent {
        private final Long ruleId;

        public AlertRuleDeletedEvent(Long ruleId) {
            this.ruleId = ruleId;
        }

        public Long getRuleId() {
            return ruleId;
        }
    }

    /**
     * 预警规则状态变更事件
     */
    public static class AlertRuleStatusChangedEvent {
        private final Long ruleId;
        private final Integer oldStatus;
        private final Integer newStatus;
        private final SjyAlertRule rule;

        public AlertRuleStatusChangedEvent(Long ruleId, Integer oldStatus, Integer newStatus, SjyAlertRule rule) {
            this.ruleId = ruleId;
            this.oldStatus = oldStatus;
            this.newStatus = newStatus;
            this.rule = rule;
        }

        public Long getRuleId() {
            return ruleId;
        }

        public Integer getOldStatus() {
            return oldStatus;
        }

        public Integer getNewStatus() {
            return newStatus;
        }

        public SjyAlertRule getRule() {
            return rule;
        }
    }
}
