package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.business.mapper.SjyAlertRuleMapper;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.service.ISjyAlertRuleService;
import com.tzslsjy.business.service.StationAbnormalRuleService;
import com.tzslsjy.business.vo.StationRuleStatus;
import com.tzslsjy.business.vo.StationWithRuleInfo;
import com.tzslsjy.common.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测站异常规则服务实现类
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
@Slf4j
public class StationAbnormalRuleServiceImpl implements StationAbnormalRuleService {

    @Autowired
    private ISjyAlertRuleService alertRuleService;

    @Autowired
    private SjyAlertRuleMapper alertRuleMapper;

    @Autowired
    private StStbprpBMapper stStbprpBMapper;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 规则类型映射
    private static final Map<String, String> RULE_TYPE_NAMES;
    static {
        Map<String, String> map = new HashMap<>();
        map.put("31", "水位异常预警");
        map.put("34", "雨量异常预警");
        RULE_TYPE_NAMES = Collections.unmodifiableMap(map);
    }

    @Override
    public int createStationAbnormalRules(List<String> stationIds, String ruleTypeId) {
        log.info("开始创建测站异常检测规则，测站数量: {}, 规则类型: {}", stationIds.size(), ruleTypeId);

        // 验证规则类型
        validateRuleType(ruleTypeId);

        // 批量查询测站信息
        List<StStbprpB> stations = stStbprpBMapper.selectStStbprpBBySTCDs(stationIds);
        if (CollectionUtils.isEmpty(stations)) {
            throw new RuntimeException("未找到有效的测站信息");
        }

        // 查询已存在的规则，避免重复创建
        List<SjyAlertRule> existingRules = alertRuleMapper.selectByStationIdsAndRuleType(stationIds, ruleTypeId);
        Set<String> existingStationIds = existingRules.stream()
                .map(SjyAlertRule::getAlertStcd)
                .collect(Collectors.toSet());

        int createdCount = 0;
        for (StStbprpB station : stations) {
            if (existingStationIds.contains(station.getStcd())) {
                log.debug("测站 {} 的{}规则已存在，跳过创建", station.getStcd(), RULE_TYPE_NAMES.get(ruleTypeId));
                continue;
            }

            try {
                SjyAlertRule rule = createRuleForStation(station, ruleTypeId);
                alertRuleService.insertSjyAlertRule(rule);
                createdCount++;
                log.debug("成功创建测站 {} 的{}规则", station.getStcd(), RULE_TYPE_NAMES.get(ruleTypeId));
            } catch (Exception e) {
                log.error("创建测站 {} 的{}规则失败: {}", station.getStcd(), RULE_TYPE_NAMES.get(ruleTypeId), e.getMessage(), e);
            }
        }

        log.info("测站异常检测规则创建完成，成功创建 {} 条规则", createdCount);
        return createdCount;
    }

    @Override
    @Transactional
    public int toggleStationAbnormalRules(List<String> stationIds, String ruleTypeId, Boolean enabled) {
        log.info("开始{}测站异常检测规则，测站数量: {}, 规则类型: {}", 
                enabled ? "启用" : "禁用", stationIds.size(), ruleTypeId);

        // 验证规则类型
        validateRuleType(ruleTypeId);

        // 查询现有规则
        List<SjyAlertRule> existingRules = alertRuleMapper.selectByStationIdsAndRuleType(stationIds, ruleTypeId);
        if (CollectionUtils.isEmpty(existingRules)) {
            log.warn("未找到需要更新的规则");
            return 0;
        }

        int updatedCount = 0;
        Integer status = enabled ? 1 : 0;
        
        for (SjyAlertRule rule : existingRules) {
            if (!Objects.equals(rule.getStatus(), status)) {
                rule.setStatus(status);
                rule.setUpdateTime(DateUtils.getNowDate());
                alertRuleService.updateSjyAlertRule(rule);
                updatedCount++;
                log.debug("成功{}测站 {} 的{}规则", enabled ? "启用" : "禁用", 
                        rule.getAlertStcd(), RULE_TYPE_NAMES.get(ruleTypeId));
            }
        }

        log.info("测站异常检测规则{}完成，成功{} {} 条规则", 
                enabled ? "启用" : "禁用", enabled ? "启用" : "禁用", updatedCount);
        return updatedCount;
    }

    @Override
    public List<StationRuleStatus> getStationAbnormalRuleStatus(List<String> stationIds, String ruleTypeId) {
        log.debug("查询测站异常规则状态，测站数量: {}, 规则类型: {}", stationIds.size(), ruleTypeId);

        // 验证规则类型
        validateRuleType(ruleTypeId);

        // 批量查询测站信息
        List<StStbprpB> stations = stStbprpBMapper.selectStStbprpBBySTCDs(stationIds);
        Map<String, StStbprpB> stationMap = stations.stream()
                .collect(Collectors.toMap(StStbprpB::getStcd, s -> s));

        // 查询现有规则
        List<SjyAlertRule> existingRules = alertRuleMapper.selectByStationIdsAndRuleType(stationIds, ruleTypeId);
        Map<String, SjyAlertRule> ruleMap = existingRules.stream()
                .collect(Collectors.toMap(SjyAlertRule::getAlertStcd, r -> r));

        List<StationRuleStatus> statusList = new ArrayList<>();
        for (String stationId : stationIds) {
            StStbprpB station = stationMap.get(stationId);
            SjyAlertRule rule = ruleMap.get(stationId);

            StationRuleStatus status = new StationRuleStatus();
            status.setStationId(stationId);
            status.setStationName(station != null ? station.getStnm() : "未知测站");
            status.setRuleTypeId(ruleTypeId);
            status.setRuleTypeName(RULE_TYPE_NAMES.get(ruleTypeId));

            if (rule != null) {
                status.setRuleExists(true);
                status.setRuleId(rule.getRuleId());
                status.setRuleName(rule.getRuleName());
                status.setStatus(rule.getStatus());
                status.setStatusDesc(rule.getStatus() == 1 ? "启用" : "禁用");
                status.setCreateTime(rule.getCreateTime() != null ? 
                        DateUtil.formatDateTime(rule.getCreateTime()) : null);
                status.setUpdateTime(rule.getUpdateTime() != null ? 
                        DateUtil.formatDateTime(rule.getUpdateTime()) : null);
            } else {
                status.setRuleExists(false);
                status.setStatus(0);
                status.setStatusDesc("未创建");
            }

            statusList.add(status);
        }

        return statusList;
    }

    @Override
    @Transactional
    public int batchCreateStationAbnormalRules(List<String> stationIds, Boolean createWaterLevel, Boolean createRainfall) {
        log.info("开始批量创建测站异常检测规则，测站数量: {}, 水位异常: {}, 雨量异常: {}", 
                stationIds.size(), createWaterLevel, createRainfall);

        int totalCreated = 0;

        if (createWaterLevel) {
            int waterLevelCreated = createStationAbnormalRules(stationIds, "31");
            totalCreated += waterLevelCreated;
            log.info("创建水位异常规则 {} 条", waterLevelCreated);
        }

        if (createRainfall) {
            int rainfallCreated = createStationAbnormalRules(stationIds, "34");
            totalCreated += rainfallCreated;
            log.info("创建雨量异常规则 {} 条", rainfallCreated);
        }

        log.info("批量创建测站异常检测规则完成，总计创建 {} 条规则", totalCreated);
        return totalCreated;
    }

    @Override
    @Transactional
    public int deleteStationAbnormalRules(List<String> stationIds, String ruleTypeId) {
        log.info("开始删除测站异常检测规则，测站数量: {}, 规则类型: {}", stationIds.size(), ruleTypeId);

        // 验证规则类型
        validateRuleType(ruleTypeId);

        // 查询现有规则
        List<SjyAlertRule> existingRules = alertRuleMapper.selectByStationIdsAndRuleType(stationIds, ruleTypeId);
        if (CollectionUtils.isEmpty(existingRules)) {
            log.warn("未找到需要删除的规则");
            return 0;
        }

        int deletedCount = 0;
        for (SjyAlertRule rule : existingRules) {
            try {
                alertRuleService.deleteSjyAlertRuleByRuleId(rule.getRuleId());
                deletedCount++;
                log.debug("成功删除测站 {} 的{}规则", rule.getAlertStcd(), RULE_TYPE_NAMES.get(ruleTypeId));
            } catch (Exception e) {
                log.error("删除测站 {} 的{}规则失败: {}", rule.getAlertStcd(), RULE_TYPE_NAMES.get(ruleTypeId), e.getMessage(), e);
            }
        }

        log.info("测站异常检测规则删除完成，成功删除 {} 条规则", deletedCount);
        return deletedCount;
    }

    @Override
    public List<StationWithRuleInfo> getStationsWithAbnormalRules(String ruleTypeId, Integer status, String stationName) {
        log.debug("查询绑定了异常检测规则的测站信息，规则类型: {}, 状态: {}, 测站名称: {}",
                ruleTypeId, status, stationName);

        try {
            // 验证规则类型（如果提供）
            if (ruleTypeId != null) {
                validateRuleType(ruleTypeId);
            }

            // 查询规则列表 - 这里PageHelper会自动应用分页
            List<SjyAlertRule> rules;
            if (ruleTypeId != null) {
                rules = alertRuleMapper.selectByRuleType(ruleTypeId);
                // 如果有状态过滤条件，进行过滤
                if (status != null) {
                    rules = rules.stream()
                            .filter(rule -> Objects.equals(rule.getStatus(), status))
                            .collect(Collectors.toList());
                }
            } else {
                // 分别查询水位异常和雨量异常规则
                List<SjyAlertRule> waterLevelRules = alertRuleMapper.selectByRuleType("31");
                List<SjyAlertRule> rainfallRules = alertRuleMapper.selectByRuleType("34");

                rules = new ArrayList<>();
                rules.addAll(waterLevelRules);
                rules.addAll(rainfallRules);

                // 如果有状态过滤条件，进行过滤
                if (status != null) {
                    rules = rules.stream()
                            .filter(rule -> Objects.equals(rule.getStatus(), status))
                            .collect(Collectors.toList());
                }
            }

            if (CollectionUtils.isEmpty(rules)) {
                log.debug("未找到符合条件的规则");
                return new ArrayList<>();
            }

            // 提取测站ID列表
            List<String> stationIds = rules.stream()
                    .map(SjyAlertRule::getAlertStcd)
                    .filter(Objects::nonNull)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(stationIds)) {
                log.debug("规则中未找到有效的测站ID");
                return new ArrayList<>();
            }

            // 批量查询测站信息
            List<StStbprpB> stations = stStbprpBMapper.selectStStbprpBBySTCDs(stationIds);
            Map<String, StStbprpB> stationMap = stations.stream()
                    .collect(Collectors.toMap(StStbprpB::getStcd, s -> s));

            // 构建结果列表并应用测站名称过滤
            List<StationWithRuleInfo> result = new ArrayList<>();
            for (SjyAlertRule rule : rules) {
                String stationId = rule.getAlertStcd();
                StStbprpB station = stationMap.get(stationId);

                if (station != null) {
                    // 按测站名称过滤
                    if (stationName != null && !stationName.trim().isEmpty()) {
                        if (station.getStnm() == null || !station.getStnm().contains(stationName.trim())) {
                            continue; // 跳过不匹配的测站
                        }
                    }

                    StationWithRuleInfo info = buildStationWithRuleInfo(station, rule);
                    result.add(info);
                }
            }

            // 按测站名称排序
            result.sort(Comparator.comparing(StationWithRuleInfo::getStationName,
                    Comparator.nullsLast(String::compareTo)));

            log.debug("查询到 {} 个绑定了异常检测规则的测站", result.size());
            return result;

        } catch (Exception e) {
            log.error("查询绑定了异常检测规则的测站信息失败: {}", e.getMessage(), e);
            throw new RuntimeException("查询测站信息失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为单个测站创建异常检测规则
     */
    private SjyAlertRule createRuleForStation(StStbprpB station, String ruleTypeId) {
        try {
            SjyAlertRule rule = new SjyAlertRule();
            
            // 基本信息
            String ruleTypeName = RULE_TYPE_NAMES.get(ruleTypeId);
            rule.setRuleName(ruleTypeName + "-" + station.getStnm());
            rule.setRuleTypeId(ruleTypeId);
            rule.setRuleModel("1"); // 默认模式
            rule.setParentId(0); // 顶级规则
            rule.setSendWay("1"); // 短信发送
            rule.setAlertStcd(station.getStcd());
            rule.setAlertAdnm(station.getAddvcd9Nm());
            rule.setAlertLevel(1); // 默认预警等级
            rule.setStatus(1); // 默认启用
            rule.setCronExpressions("0 */5 * * * ?"); // 每5分钟执行一次
            rule.setReviewMinute(0); // 不需要审核
            rule.setCreateTime(DateUtils.getNowDate());

            // 创建规则参数
            SjyAlertRuleArg ruleArg = createRuleArgForStation(station, ruleTypeId);
            rule.setSjyAlertRuleArgs(Collections.singletonList(ruleArg));

            return rule;
        } catch (Exception e) {
            log.error("为测站 {} 创建{}规则失败: {}", station.getStcd(), RULE_TYPE_NAMES.get(ruleTypeId), e.getMessage(), e);
            throw new RuntimeException("创建规则失败: " + e.getMessage(), e);
        }
    }

    /**
     * 为测站创建规则参数
     */
    private SjyAlertRuleArg createRuleArgForStation(StStbprpB station, String ruleTypeId) {
        try {
            SjyAlertRuleArg ruleArg = new SjyAlertRuleArg();
            ruleArg.setRelationType("1"); // 规则类型
            ruleArg.setCreateTime(DateUtils.getNowDate());

            // 根据规则类型创建不同的参数配置
            Map<String, Object> argJson = new HashMap<>();

            if ("31".equals(ruleTypeId)) {
                // 水位异常参数 - 使用新的标准化格式
                Map<String, Object> waterLevelParam = new HashMap<>();
                waterLevelParam.put("during", "1");    // 默认1小时时间周期
                waterLevelParam.put("threshold", "1"); // 默认水位变化阈值1米
                argJson.put("waterLevel", waterLevelParam);

                // 保留兼容性参数（可选）
                argJson.put("waterStcds", Collections.singletonList(station.getStcd()));
                argJson.put("overWaterVal", "1.0");
                argJson.put("overWaterEnable", "1");

                log.debug("创建水位异常规则参数（新格式）: 测站={}, 时间周期=1小时, 阈值=1米", station.getStcd());
            } else if ("34".equals(ruleTypeId)) {
                // 雨量异常参数 - 使用新的标准化格式
                Map<String, Object> rainfallParam = new HashMap<>();
                rainfallParam.put("during", "1");     // 默认1小时时间周期
                rainfallParam.put("threshold", "15"); // 默认雨量阈值15毫米
                argJson.put("rainfall", rainfallParam);

                // 保留兼容性参数（可选）
                argJson.put("rainStcds", Collections.singletonList(station.getStcd()));
                argJson.put("overRainVal", "15.0");
                argJson.put("overRainEnable", "1");

                log.debug("创建雨量异常规则参数（新格式）: 测站={}, 时间周期=1小时, 阈值=15毫米", station.getStcd());
            }

            ruleArg.setArgJson(objectMapper.writeValueAsString(argJson));
            return ruleArg;
        } catch (Exception e) {
            log.error("为测站 {} 创建规则参数失败: {}", station.getStcd(), e.getMessage(), e);
            throw new RuntimeException("创建规则参数失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建测站规则信息
     */
    private StationWithRuleInfo buildStationWithRuleInfo(StStbprpB station, SjyAlertRule rule) {
        StationWithRuleInfo info = new StationWithRuleInfo();

        // 测站基本信息
        info.setStationId(station.getStcd());
        info.setStationName(station.getStnm());
        info.setStationLocation(station.getStlc());
        info.setLongitude(station.getLgtd() != null ? station.getLgtd().toString() : null);
        info.setLatitude(station.getLttd() != null ? station.getLttd().toString() : null);
        info.setStationType(station.getSttp());
        info.setAdminName(station.getAddvcd9Nm());

        // 规则信息
        info.setRuleId(rule.getRuleId());
        info.setRuleName(rule.getRuleName());
        info.setRuleTypeId(rule.getRuleTypeId());
        info.setRuleTypeName(RULE_TYPE_NAMES.get(rule.getRuleTypeId()));
        info.setStatus(rule.getStatus());
        info.setStatusDesc(rule.getStatus() == 1 ? "启用" : "禁用");
        info.setCronExpressions(rule.getCronExpressions());
        info.setArgJson(rule.getArgJson());

        // 时间信息
        info.setCreateTime(rule.getCreateTime() != null ?
                DateUtil.formatDateTime(rule.getCreateTime()) : null);
        info.setUpdateTime(rule.getUpdateTime() != null ?
                DateUtil.formatDateTime(rule.getUpdateTime()) : null);

        return info;
    }

    /**
     * 验证规则类型
     */
    private void validateRuleType(String ruleTypeId) {
//        if (!"31".equals(ruleTypeId) && !"34".equals(ruleTypeId)) {
//            throw new IllegalArgumentException("不支持的规则类型: " + ruleTypeId + "，仅支持31(水位异常)和34(雨量异常)");
//        }
    }
}
