package com.tzslsjy.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.tzslsjy.business.bo.SjyPjStAddBo;
import com.tzslsjy.business.bo.SjyPjStEditBo;
import com.tzslsjy.business.bo.SjyPjStQueryBo;
import com.tzslsjy.business.domain.SjyPjSt;
import com.tzslsjy.business.mapper.SjyPjStMapper;
import com.tzslsjy.business.service.ISjyPjStService;
import com.tzslsjy.business.vo.SjyPjStVo;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 工程测站关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
@Service
@DataSource(DataSourceType.INFO)
public class SjyPjStServiceImpl extends ServiceImpl<SjyPjStMapper, SjyPjSt> implements ISjyPjStService {

    @Override
    public SjyPjStVo queryById(Long id){
        SjyPjSt db = this.baseMapper.selectById(id);
        return BeanUtil.toBean(db, SjyPjStVo.class);
    }

    @Override
    public List<SjyPjStVo> queryList(SjyPjStQueryBo bo) {
        LambdaQueryWrapper<SjyPjSt> lqw = Wrappers.lambdaQuery();
        lqw.like(StrUtil.isNotBlank(bo.getStcd()), SjyPjSt::getStcd, bo.getStcd());
        lqw.like(StrUtil.isNotBlank(bo.getPjcd()), SjyPjSt::getPjcd, bo.getPjcd());
        lqw.like(StrUtil.isNotBlank(bo.getIsMian()), SjyPjSt::getIsMian, bo.getIsMian());
        lqw.like(StrUtil.isNotBlank(bo.getIsRain()), SjyPjSt::getIsRain, bo.getIsRain());
        lqw.like(bo.getRainWeight() != null, SjyPjSt::getRainWeight, bo.getRainWeight());
        lqw.like(StrUtil.isNotBlank(bo.getProjType()), SjyPjSt::getProjType, bo.getProjType());
        lqw.like(bo.getOrderNum() != null, SjyPjSt::getOrderNum, bo.getOrderNum());
        lqw.like(bo.getIzDel() != null, SjyPjSt::getIzDel, bo.getIzDel());
        List<SjyPjStVo> sjyPjStVos = entity2Vo(this.list(lqw));
        return sjyPjStVos;
    }

    /**
    * 实体类转化成视图对象
    *
    * @param collection 实体类集合
    * @return
    */
    private List<SjyPjStVo> entity2Vo(Collection<SjyPjSt> collection) {
        List<SjyPjStVo> voList = collection.stream()
                .map(any -> BeanUtil.toBean(any, SjyPjStVo.class))
                .collect(Collectors.toList());
        if (collection instanceof Page) {
            Page<SjyPjSt> page = (Page<SjyPjSt>)collection;
            Page<SjyPjStVo> pageVo = new Page<>();
            BeanUtil.copyProperties(page,pageVo);
            pageVo.addAll(voList);
            voList = pageVo;
        }
        return voList;
    }

    @Override
    public Boolean insertByAddBo(SjyPjStAddBo bo) {
        SjyPjSt add = BeanUtil.toBean(bo, SjyPjSt.class);
        validEntityBeforeSave(add);
        return this.save(add);
    }

    @Override
    public Boolean updateByEditBo(SjyPjStEditBo bo) {
        SjyPjSt update = BeanUtil.toBean(bo, SjyPjSt.class);
        validEntityBeforeSave(update);
        return this.updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(SjyPjSt entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return this.removeByIds(ids);
    }
}