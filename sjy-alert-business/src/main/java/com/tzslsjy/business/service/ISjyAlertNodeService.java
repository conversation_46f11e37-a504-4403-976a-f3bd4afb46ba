package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertNode;
import com.tzslsjy.business.domain.vo.NodeTreeVO;

import java.util.List;


/**
 * 节点Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface ISjyAlertNodeService 
{
    /**
     * 查询节点
     * 
     * @param nodeId 节点主键
     * @return 节点
     */
    public SjyAlertNode selectSjyAlertNodeByNodeId(String nodeId);

    /**
     * 查询节点列表
     * 
     * @param sjyAlertNode 节点
     * @return 节点集合
     */
    public List<SjyAlertNode> selectSjyAlertNodeList(SjyAlertNode sjyAlertNode);

    /**
     * 新增节点
     * 
     * @param sjyAlertNode 节点
     * @return 结果
     */
    public int insertSjyAlertNode(SjyAlertNode sjyAlertNode);

    /**
     * 修改节点
     * 
     * @param sjyAlertNode 节点
     * @return 结果
     */
    public int updateSjyAlertNode(SjyAlertNode sjyAlertNode);

    /**
     * 批量删除节点
     * 
     * @param nodeIds 需要删除的节点主键集合
     * @return 结果
     */
    public int deleteSjyAlertNodeByNodeIds(String[] nodeIds);

    /**
     * 删除节点信息
     * 
     * @param nodeId 节点主键
     * @return 结果
     */
    public int deleteSjyAlertNodeByNodeId(String nodeId);

    /**
     * 构建节点树（包含关联的人员及特定人员的勾选状态）
     *
     * @param sjyAlertNode 查询条件，可用于过滤节点
     * @param personId 人员ID，用于确定哪些节点或人员需要被标记为选中（如果业务需要）
     * @return 节点树列表
     */
    public List<NodeTreeVO> buildNodeTreeWithPersons(SjyAlertNode sjyAlertNode, Long personId);
}
