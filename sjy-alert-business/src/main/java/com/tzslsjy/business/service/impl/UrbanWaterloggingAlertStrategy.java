package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.mapper.SjyAlertStSituationMapper;
import com.tzslsjy.business.service.AlertRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 城市内涝水位预警规则策略
 * 特点：
 * 1. 每个监测站点独立评估
 * 2. 无阈值概念 - 当水位超过汛限值时立即触发预警
 */
@Slf4j
@Component
public class UrbanWaterloggingAlertStrategy implements AlertRuleStrategy {

    @Autowired
    SjyAlertStSituationMapper sjyAlertStSituationMapper;

    @Override
    public String getType() {
        return "26"; // 城市内涝水位预警类型
    }

    @Override
    public boolean evaluate(AlertContext context) {
        log.debug("开始评估城市内涝水位预警规则");

        try {
            Map<String, Object> data = context.getData();
            if (data == null || !data.containsKey("argResults") || !(data.get("argResults") instanceof List)) {
                log.warn("城市内涝水位预警数据格式不正确或缺少argResults");
                return false;
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");

            if (CollectionUtils.isEmpty(argResults)) {
                log.warn("参数组结果为空，无法评估城市内涝水位预警");
                return false;
            }

            // 检查是否有任何测站触发预警
            for (Map<String, Object> argResult : argResults) {
                if (argResult.containsKey("triggeredStations")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> triggeredStations =
                            (List<Map<String, Object>>) argResult.get("triggeredStations");
                    if (!CollectionUtils.isEmpty(triggeredStations)) {
                        log.info("城市内涝水位预警触发，参数组ID: {}, 触发测站数: {}",
                                argResult.get("argId"), triggeredStations.size());
                        return true;
                    }
                }
            }

            log.debug("城市内涝水位预警评估完成，未触发预警");
            return false;

        } catch (Exception e) {
            log.error("评估城市内涝水位预警规则时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    public boolean supportsRuleType(String ruleTypeId) {
        return "26".equals(ruleTypeId); // 城市内涝水位预警类型
    }
}