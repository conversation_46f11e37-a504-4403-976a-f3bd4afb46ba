package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyStationAbnormalConfig;
import com.baomidou.mybatisplus.extension.service.IService;

import java.math.BigDecimal;
import java.util.List;

/**
 * 测站异常预警配置Service接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface ISjyStationAbnormalConfigService extends IService<SjyStationAbnormalConfig> {

    /**
     * 根据测站编号和配置类型查询配置
     * @param stcd 测站编号
     * @param configType 配置类型：1-数据接收超时，2-水位异常变化，3-雨量异常
     * @return 配置信息
     */
    SjyStationAbnormalConfig getConfigByStcdAndType(String stcd, Integer configType);

    /**
     * 根据配置类型查询所有配置
     * @param configType 配置类型
     * @return 配置列表
     */
    List<SjyStationAbnormalConfig> getConfigsByType(Integer configType);

    /**
     * 根据配置类型查询启用的配置
     * @param configType 配置类型
     * @return 启用的配置列表
     */
    List<SjyStationAbnormalConfig> getEnabledConfigsByType(Integer configType);

    /**
     * 获取数据接收超时阈值（分钟）
     * @param stcd 测站编号，如果为null则返回默认值
     * @return 超时阈值
     */
    Integer getDataTimeoutThreshold(String stcd);

    /**
     * 获取水位变化阈值（米）
     * @param stcd 测站编号，如果为null则返回默认值
     * @return 水位变化阈值
     */
    BigDecimal getWaterLevelThreshold(String stcd);

    /**
     * 获取雨量阈值（毫米）
     * @param stcd 测站编号，如果为null则返回默认值
     * @return 雨量阈值
     */
    BigDecimal getRainfallThreshold(String stcd);

    /**
     * 检查水位异常检测是否启用
     * @param stcd 测站编号
     * @return 是否启用
     */
    Boolean isWaterLevelDetectionEnabled(String stcd);

    /**
     * 检查雨量异常检测是否启用
     * @param stcd 测站编号
     * @return 是否启用
     */
    Boolean isRainfallDetectionEnabled(String stcd);

    /**
     * 检查全局开关是否启用
     * @param configType 配置类型：2-水位异常变化，3-雨量异常
     * @return 是否启用
     */
    Boolean isGlobalSwitchEnabled(Integer configType);

    /**
     * 批量更新测站的启用状态
     * @param stcds 测站编号列表
     * @param configType 配置类型
     * @param enabled 启用状态
     * @return 是否成功
     */
    Boolean batchUpdateEnabled(List<String> stcds, Integer configType, Boolean enabled);

    /**
     * 更新全局开关
     * @param configType 配置类型：2-水位异常变化，3-雨量异常
     * @param enabled 启用状态
     * @return 是否成功
     */
    Boolean updateGlobalSwitch(Integer configType, Boolean enabled);

    /**
     * 一键启用/禁用所有检测
     * @param enabled 启用状态
     * @return 是否成功
     */
    Boolean toggleAllDetection(Boolean enabled);

    /**
     * 保存或更新配置
     * @param config 配置信息
     * @return 是否成功
     */
    Boolean saveOrUpdateConfig(SjyStationAbnormalConfig config);

    /**
     * 初始化默认配置
     * @param stcds 测站编号列表
     * @return 是否成功
     */
    Boolean initDefaultConfigs(List<String> stcds);
}
