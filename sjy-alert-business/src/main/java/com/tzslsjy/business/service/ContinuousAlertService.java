package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertStSituation;

import java.util.List;
import java.util.Map;

/**
 * 持续预警服务接口
 * 负责防止重复发送预警，并处理每日8点的持续预警汇总
 */
public interface ContinuousAlertService {

    /**
     * 处理水库水位持续预警
     * 
     * @param rule 预警规则
     * @param triggeredReservoirs 触发预警的水库列表
     * @return 是否需要发送预警消息
     */
    boolean processReservoirContinuousAlert(SjyAlertRule rule, List<Map<String, Object>> triggeredReservoirs);

    /**
     * 处理河道水位持续预警
     * 
     * @param rule 预警规则
     * @param triggeredRivers 触发预警的河道列表
     * @return 是否需要发送预警消息
     */
    boolean processRiverContinuousAlert(SjyAlertRule rule, List<Map<String, Object>> triggeredRivers);

    /**
     * 处理城市内涝水位持续预警
     * 
     * @param rule 预警规则
     * @param triggeredStations 触发预警的城市内涝测站列表
     * @return 是否需要发送预警消息
     */
    boolean processUrbanFloodContinuousAlert(SjyAlertRule rule, List<Map<String, Object>> triggeredStations);

    /**
     * 生成每日8点持续预警汇总
     * 
     * @param ruleId 规则ID
     * @param alertType 预警类型（"reservoir" 或 "river"）
     * @return 仍在预警状态的测站列表
     */
    List<SjyAlertStSituation> generateDailyContinuousAlertSummary(Long ruleId, String alertType);

    /**
     * 检查是否应该发送持续预警
     * 只在状态变化时（正常→预警 或 预警级别变化）或每日8点发送
     * 
     * @param stcd 测站编码
     * @param ruleId 规则ID
     * @param newStatus 新的预警状态
     * @param isDailySummary 是否为每日汇总
     * @return 是否应该发送预警
     */
    boolean shouldSendAlert(String stcd, Long ruleId, Integer newStatus, boolean isDailySummary);

    /**
     * 更新测站预警状态并返回是否状态发生变化
     * 
     * @param stcd 测站编码
     * @param ruleId 规则ID
     * @param status 预警状态
     * @return 是否状态发生变化
     */
    boolean updateStationStatus(String stcd, Long ruleId, Integer status);

    /**
     * 根据水位级别确定预警状态
     * 
     * @param currentLevel 当前水位
     * @param warningLevel 警戒水位
     * @param guaranteeLevel 保证水位（可为null）
     * @return 预警状态（0=正常，1=超警戒，2=超保证）
     */
    Integer determineAlertStatus(Double currentLevel, Double warningLevel, Double guaranteeLevel);

    /**
     * 恢复正常状态：将不再超警的测站状态更新为正常
     * 该方法应该在数据提供阶段调用，用于处理之前在预警状态但现在恢复正常的测站
     * 
     * @param ruleId 规则ID
     * @param currentTriggeredStcds 当前触发预警的测站编码列表
     * @param alertType 预警类型（"reservoir" 或 "river"）
     * @return 恢复正常的测站数量
     */
    int restoreNormalStations(Long ruleId, List<String> currentTriggeredStcds, String alertType);

    /**
     * 根据规则ID获取所有仍在预警状态的测站编码
     * 
     * @param ruleId 规则ID
     * @return 预警状态测站编码列表
     */
    List<String> getActiveAlertStationCodes(Long ruleId);
}