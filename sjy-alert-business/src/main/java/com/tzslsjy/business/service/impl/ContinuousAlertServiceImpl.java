package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertStSituation;
import com.tzslsjy.business.service.ContinuousAlertService;
import com.tzslsjy.business.service.ISjyAlertStSituationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 持续预警服务实现
 * 负责防止重复发送预警，并处理每日8点的持续预警汇总
 */
@Service
@Slf4j
public class ContinuousAlertServiceImpl implements ContinuousAlertService {

    @Autowired
    private ISjyAlertStSituationService alertStSituationService;

    private static final Integer RELATION_TYPE_RULE_INSTANCE = 2; // 关联类型：规则实例

    @Override
    public boolean processReservoirContinuousAlert(SjyAlertRule rule, List<Map<String, Object>> triggeredReservoirs) {
        log.debug("处理水库水位持续预警，规则ID: {}, 触发测站数: {}", rule.getRuleId(), triggeredReservoirs.size());
        
        boolean hasStatusChange = false;
        
        // 处理触发预警的测站
        for (Map<String, Object> reservoir : triggeredReservoirs) {
            String stcd = (String) reservoir.get("stcd");
            Double currentLevel = getDoubleValue(reservoir, "currentRz");
            Double warningLevel = getDoubleValue(reservoir, "fsltdz"); // 汛限水位作为警戒水位
            Double guaranteeLevel = getDoubleValue(reservoir, "bzfdz"); // 保证水位
            
            Integer newStatus = determineAlertStatus(currentLevel, warningLevel, guaranteeLevel);
            
            boolean statusChanged = updateStationStatus(stcd, rule.getRuleId(), newStatus);
            if (statusChanged) {
                hasStatusChange = true;
                log.info("水库测站 {} 预警状态发生变化，新状态: {}", stcd, newStatus);
            }
        }
        
        // 注意：不再处理测站恢复正常的情况，因为数据提供阶段已经过滤了数据
        // 恢复正常的逻辑应该在数据提供阶段或者通过其他机制处理
        // 这里只处理实际触发预警的测站状态变化
        
        return hasStatusChange;
    }

    @Override
    public boolean processRiverContinuousAlert(SjyAlertRule rule, List<Map<String, Object>> triggeredRivers) {
        log.debug("处理河道水位持续预警，规则ID: {}, 触发测站数: {}", rule.getRuleId(), triggeredRivers.size());
        
        boolean hasStatusChange = false;
        
        // 处理触发预警的测站
        for (Map<String, Object> river : triggeredRivers) {
            String stcd = (String) river.get("stcd");
            Double currentLevel = getDoubleValue(river, "z");
            Double warningLevel = getDoubleValue(river, "wrz"); // 警戒水位
            Double guaranteeLevel = getDoubleValue(river, "bzz"); // 保证水位
            
            Integer newStatus = determineAlertStatus(currentLevel, warningLevel, guaranteeLevel);
            
            boolean statusChanged = updateStationStatus(stcd, rule.getRuleId(), newStatus);
            if (statusChanged) {
                hasStatusChange = true;
                log.info("河道测站 {} 预警状态发生变化，新状态: {}", stcd, newStatus);
            }
        }
        
        // 注意：不再处理测站恢复正常的情况，因为数据提供阶段已经过滤了数据
        // 恢复正常的逻辑应该在数据提供阶段或者通过其他机制处理
        // 这里只处理实际触发预警的测站状态变化
        
        return hasStatusChange;
    }

    @Override
    public List<SjyAlertStSituation> generateDailyContinuousAlertSummary(Long ruleId, String alertType) {
        log.info("生成每日8点持续预警汇总，规则ID: {}, 预警类型: {}", ruleId, alertType);
        
        List<SjyAlertStSituation> activeStations = alertStSituationService.selectActiveAlertStations(
                ruleId.intValue(), RELATION_TYPE_RULE_INSTANCE);
        
        log.info("规则 {} 当前仍在预警状态的测站数: {}", ruleId, activeStations.size());
        
        return activeStations;
    }

    @Override
    public boolean shouldSendAlert(String stcd, Long ruleId, Integer newStatus, boolean isDailySummary) {
        if (isDailySummary) {
            // 每日8点汇总，超警必发（状态为1或2）
            return newStatus != null && newStatus > 0;
        }
        
        // 非每日汇总，只在状态变化时发送
        SjyAlertStSituation existing = alertStSituationService.selectByStcdAndRelation(
                stcd, ruleId.intValue(), RELATION_TYPE_RULE_INSTANCE);
        
        if (existing == null) {
            // 新预警
            return newStatus != null && newStatus > 0;
        }
        
        // 状态发生变化时发送
        return !existing.getStatus().equals(newStatus);
    }

    @Override
    public boolean updateStationStatus(String stcd, Long ruleId, Integer status) {
        return alertStSituationService.updateStationAlertStatus(
                stcd, ruleId.intValue(), RELATION_TYPE_RULE_INSTANCE, status, "system");
    }

    @Override
    public Integer determineAlertStatus(Double currentLevel, Double warningLevel, Double guaranteeLevel) {
        if (currentLevel == null || warningLevel == null) {
            return 0; // 正常
        }
        
        if (guaranteeLevel != null && currentLevel > guaranteeLevel) {
            return 2; // 超保证
        }
        
        if (currentLevel > warningLevel) {
            return 1; // 超警戒
        }
        
        return 0; // 正常
    }

    /**
     * 安全获取Double值
     */
    private Double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        
        if (value instanceof Double) {
            return (Double) value;
        } else if (value instanceof Number) {
            return ((Number) value).doubleValue();
        } else if (value instanceof String) {
            try {
                return Double.parseDouble((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串 '{}' 转换为Double", value);
                return null;
            }
        }
        
        return null;
    }

    @Override
    public int restoreNormalStations(Long ruleId, List<String> currentTriggeredStcds, String alertType) {
        log.debug("开始恢复正常状态，规则ID: {}, 预警类型: {}, 当前触发测站数: {}", 
                ruleId, alertType, currentTriggeredStcds.size());
        
        // 获取所有仍在预警状态的测站
        List<SjyAlertStSituation> activeStations = alertStSituationService.selectActiveAlertStations(
                ruleId.intValue(), RELATION_TYPE_RULE_INSTANCE);
        
        if (CollectionUtils.isEmpty(activeStations)) {
            log.debug("规则 {} 没有仍在预警状态的测站", ruleId);
            return 0;
        }
        
        Set<String> currentTriggeredSet = new HashSet<>(currentTriggeredStcds);
        int restoredCount = 0;
        
        for (SjyAlertStSituation activeSituation : activeStations) {
            String stcd = activeSituation.getStcd();
            
            // 如果该测站不在当前触发列表中，说明已恢复正常
            if (!currentTriggeredSet.contains(stcd)) {
                boolean statusChanged = updateStationStatus(stcd, ruleId, 0); // 更新为正常状态
                if (statusChanged) {
                    restoredCount++;
                    log.info("{}测站 {} 已恢复正常，原状态: {}", alertType, stcd, activeSituation.getStatus());
                }
            }
        }
        
        log.info("规则 {} 共恢复 {} 个测站为正常状态", ruleId, restoredCount);
        return restoredCount;
    }

    @Override
    public boolean processUrbanFloodContinuousAlert(SjyAlertRule rule, List<Map<String, Object>> triggeredStations) {
        log.debug("处理城市内涝水位持续预警，规则ID: {}, 触发测站数: {}", rule.getRuleId(), triggeredStations.size());
        
        boolean hasStatusChange = false;
        
        // 处理触发预警的测站
        for (Map<String, Object> station : triggeredStations) {
            String stcd = (String) station.get("stcd");
            Double currentLevel = getDoubleValue(station, "currentLevel");
            Double warningLevel = getDoubleValue(station, "warningLevel");
            
            // 城市内涝特点：只要超警戒值都算超，没有保证水位概念
            Integer newStatus = determineUrbanFloodAlertStatus(currentLevel, warningLevel);
            
            boolean statusChanged = updateStationStatus(stcd, rule.getRuleId(), newStatus);
            if (statusChanged) {
                hasStatusChange = true;
                log.info("城市内涝测站 {} 预警状态发生变化，新状态: {}", stcd, newStatus);
            }
        }
        
        return hasStatusChange;
    }

    /**
     * 确定城市内涝预警状态
     * 城市内涝特点：没有阈值，只要超警戒值都算超
     */
    private Integer determineUrbanFloodAlertStatus(Double currentLevel, Double warningLevel) {
        if (currentLevel == null || warningLevel == null) {
            return 0; // 正常
        }

        if (currentLevel > warningLevel) {
            return 1; // 超警戒
        }

        return 0; // 正常
    }

    @Override
    public List<String> getActiveAlertStationCodes(Long ruleId) {
        List<SjyAlertStSituation> activeStations = alertStSituationService.selectActiveAlertStations(
                ruleId.intValue(), RELATION_TYPE_RULE_INSTANCE);
        
        return activeStations.stream()
                .map(SjyAlertStSituation::getStcd)
                .collect(Collectors.toList());
    }
}