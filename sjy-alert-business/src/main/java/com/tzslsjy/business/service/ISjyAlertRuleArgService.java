package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertRuleArg;

import javax.validation.Valid;

/**
 * 预警规则参数服务接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISjyAlertRuleArgService
{
    /**
     * 根据ID查询预警规则参数
     *
     * @param argId 预警规则参数主键
     * @return 预警规则参数
     */
    public SjyAlertRuleArg selectSjyAlertRuleArgById(Long argId);

    /**
     * 查询预警规则参数列表
     *
     * @param arg 预警规则参数
     * @return 预警规则参数集合
     */
    public List<SjyAlertRuleArg> selectSjyAlertRuleArgList(SjyAlertRuleArg arg);

    SjyAlertRuleArg selectSjyAlertRuleArgByArgId(Long argId);

    int insertSjyAlertRuleArg(@Valid SjyAlertRuleArg sjyAlertRuleArg);

    int updateSjyAlertRuleArg(@Valid SjyAlertRuleArg sjyAlertRuleArg);

    int deleteSjyAlertRuleArgByArgIds(Long[] argIds);
    int deleteSjyAlertRuleArgByArgId(Long argId);
}
