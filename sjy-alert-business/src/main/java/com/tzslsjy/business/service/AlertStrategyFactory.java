package com.tzslsjy.business.service;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class AlertStrategyFactory {
    private Map<String, AlertRuleStrategy> strategies = new HashMap<>();
    
    @Autowired
    private List<AlertRuleStrategy> strategyList;

    @PostConstruct
    public void init() {
        // 自动注册所有策略
        for (AlertRuleStrategy strategy : strategyList) {
            strategies.put(strategy.getType(), strategy);
        }
    }

    // 获取策略
    public AlertRuleStrategy getStrategy(String ruleType) {
        return strategies.get(ruleType);
    }
}