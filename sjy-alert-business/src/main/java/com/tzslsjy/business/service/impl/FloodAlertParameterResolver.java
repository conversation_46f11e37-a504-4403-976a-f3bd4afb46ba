package com.tzslsjy.business.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import com.tzslsjy.common.core.domain.entity.SysDictData;
import com.tzslsjy.common.core.domain.entity.SysDictType;
import com.tzslsjy.system.service.ISysConfigService;
import com.tzslsjy.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 山洪预警参数解析器
 * 专门负责解析山洪预警相关的规则参数
 */
@Component
@Slf4j
public class FloodAlertParameterResolver implements AlertRuleParameterResolver {

    @Autowired
    private ISysConfigService sysConfigService;
    @Autowired
    private ISysDictTypeService sysDictTypeService;
    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("1");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析山洪预警规则参数");

        List<Map<String, Object>> paramGroups = new ArrayList<>();
        
        // 按参数组ID分组
        Map<Long, List<SjyAlertRuleArg>> groupedParams = ruleParams.stream()
                .collect(Collectors.groupingBy(SjyAlertRuleArg::getArgId));

        for (Map.Entry<Long, List<SjyAlertRuleArg>> entry : groupedParams.entrySet()) {
            Long argId = entry.getKey();
            List<SjyAlertRuleArg> argParams = entry.getValue();
            
            Map<String, Object> queryParams = buildQueryParamsForGroup(rule, argParams, argId, tm);
            if (!queryParams.isEmpty()) {
                paramGroups.add(queryParams);
            }
        }

        return paramGroups;
    }

    /**
     * 为特定参数组构建查询参数
     */
    private Map<String, Object> buildQueryParamsForGroup(SjyAlertRule rule, List<SjyAlertRuleArg> argParams, Long argId, Date tm) {
        Map<String, Object> queryParams = new HashMap<>();
        
        // 添加基本参数
        queryParams.put("ruleId", rule.getRuleId());
        queryParams.put("ruleName", rule.getRuleName());
        queryParams.put("ruleTypeId", rule.getRuleTypeId());
        queryParams.put("argId", argId);
        queryParams.put("tm", tm);
        
        // 添加测站信息
        if (rule.getAlertStcd() != null && !rule.getAlertStcd().isEmpty()) {
            queryParams.put("stcd", rule.getAlertStcd());
        }
        
        // 添加村庄信息
        if (rule.getAlertAdnm() != null && !rule.getAlertAdnm().isEmpty()) {
            queryParams.put("villageIds", Arrays.asList(rule.getAlertAdnm().split(",")));
        }
        
        // 解析参数数据
        List<SjyAlertFloodParamData> paramDataList = parseFloodParameters(argParams);
        queryParams.put("floodParams", paramDataList);
        
        return queryParams;
    }

    /**
     * 解析山洪预警参数
     */
    private List<SjyAlertFloodParamData> parseFloodParameters(List<SjyAlertRuleArg> argParams) {
        List<SjyAlertFloodParamData> paramDataList = new ArrayList<>();
        
        for (SjyAlertRuleArg param : argParams) {
            try {
                String argJson = param.getArgJson();
                log.debug("解析山洪预警参数，参数ID: {}, JSON: {}", param.getArgId(), argJson);
                SjyAlertFloodJsonData floodJsonData = JSON.parseObject(argJson, SjyAlertFloodJsonData.class);
                
                SjyAlertFloodParamData paramData = new SjyAlertFloodParamData();
                paramData.setArgId(param.getArgId());
                paramData.setAlertLevel(param.getAlertLevel());
                
                if (floodJsonData.getOverWaterValue() != null) {
                    paramData.setOverWaterValue(floodJsonData.getOverWaterValue());
                }
                
                // 根据系统配置选择含水量列表
                List<SjyFloodRainWarningItem> rainItems = selectRainWarningItems(floodJsonData);
                paramData.setOverRainList(rainItems);
                
                paramDataList.add(paramData);
                
            } catch (Exception e) {
                log.error("解析山洪预警参数失败，参数ID: {}, 错误: {}", param.getArgId(), e.getMessage());
            }
        }
        
        return paramDataList;
    }

    /**
     * 根据系统配置选择雨量预警项
     */
    private List<SjyFloodRainWarningItem> selectRainWarningItems(SjyAlertFloodJsonData floodJsonData) {
        List<SysDictData> dicts = sysDictTypeService.selectDictDataByType("flood_water_content");
        String level75Config = dicts.stream().filter(e -> e.getDictLabel().contains("75")).findFirst().map(SysDictData::getDictValue).orElse("0");
        String level90Config = dicts.stream().filter(e -> e.getDictLabel().contains("90")).findFirst().map(SysDictData::getDictValue).orElse("0");
        
        List<SjyFloodRainWarningItem> items;
        if ("1".equals(level75Config)) {
            items = floodJsonData.getOverRain75List();
        } else if ("1".equals(level90Config)) {
            items = floodJsonData.getOverRain90List();
        } else {
            // 默认使用75%含水量
            items = floodJsonData.getOverRain75List();
        }
        
        return items != null ? items : new ArrayList<>();
    }
} 