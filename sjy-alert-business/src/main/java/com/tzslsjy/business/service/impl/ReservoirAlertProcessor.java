package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import com.tzslsjy.business.service.ContinuousAlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 水库水位预警处理器
 */
@Component
@Slf4j
public class ReservoirAlertProcessor implements AlertProcessor, TypedAlertProcessor {

    @Autowired
    @Qualifier("reservoirAlertTemplateProcessor")
    private AlertTemplateProcessor templateProcessor;
    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService; // 用于获取接收人
    @Autowired
    private SjyAlertNodeMapper alertNodeMapper; // 用于获取接收人
    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper; // 用于获取接收人


    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper;

    @Autowired
    private ContinuousAlertService continuousAlertService;

    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public String getSupportedType() {
        return "25"; // 水库水位预警类型
    }

    @Override
    public void process(AlertContext context) {
        log.info("开始处理水库水位预警，规则ID: {}", context.getRule().getRuleId());

        try {
            Map<String, Object> data = context.getData();
            SjyAlertRule rule = context.getRule();

            if (data == null || rule == null || !Boolean.TRUE.equals(data.get("overallTriggered"))) {
                log.debug("水库水位预警未触发或数据不完整，跳过消息生成");
                if (nextProcessor != null) nextProcessor.process(context);
                return;
            }

            // 检查是否为每日汇总模式
            boolean isDailySummary = Boolean.TRUE.equals(data.get("isDailySummary"));
            if (isDailySummary) {
                processDailySummary(context);
                if (nextProcessor != null) nextProcessor.process(context);
                return;
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");
            if (CollectionUtils.isEmpty(argResults)) {
                log.warn("参数组结果为空");
                if (nextProcessor != null) nextProcessor.process(context);
                return;
            }

            List<SjyAlertSmsRecord> allRecords = new ArrayList<>();

            for (Map<String, Object> argResult : argResults) {
                // 检查是否有按行政区划分组的数据
                @SuppressWarnings("unchecked")
                Map<String, List<Map<String, Object>>> triggeredReservoirGroups = 
                        (Map<String, List<Map<String, Object>>>) argResult.get("triggeredReservoirGroups");

                if (triggeredReservoirGroups != null && !triggeredReservoirGroups.isEmpty()) {
                    // 处理按行政区划分组数据
                    String argId = argResult.get("argId").toString();
                    List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
                    if (templates.isEmpty()) {
                        log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                        continue;
                    }

                    // 为每个行政区划分组生成消息
                    for (Map.Entry<String, List<Map<String, Object>>> groupEntry : triggeredReservoirGroups.entrySet()) {
                        String groupKey = groupEntry.getKey(); // 格式: "9_331082001" 或 "6_331082"
                        List<Map<String, Object>> groupReservoirs = groupEntry.getValue();
                        
                        if (CollectionUtils.isEmpty(groupReservoirs)) {
                            continue;
                        }
                        
                        String jurisdiction = extractJurisdictionFromGroupKey(groupKey);
                        String jurisdictionLevel = groupKey.substring(0, 1); // "9" 或 "6"
                        
                        log.info("处理{}位行政区划 {} 的水库水位预警，包含 {} 个测站", jurisdictionLevel, jurisdiction, groupReservoirs.size());
                        
                        for (SjyAlertSmsTemplate template : templates) {
                            Map<String, Object> templateData = new HashMap<>(argResult);
                            templateData.put("list", groupReservoirs);
                            templateData.put("jurisdiction", jurisdiction);
                            templateData.put("jurisdictionLevel", jurisdictionLevel);
                            
                            SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                            if (alert != null) {
                                // 根据行政区划获取对应的接收人员
                                List<SjyAlertPerson> receivers = getReceiversByJurisdiction(rule, argId, jurisdiction);
                                List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                                List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                                allRecords.addAll(mergedAlerts);
                                log.info("{}位行政区划 {} 生成 {} 条水库水位预警消息", jurisdictionLevel, jurisdiction, mergedAlerts.size());
                            }
                        }
                    }
                }

                // 检查是否有按工程编码分组的数据
                @SuppressWarnings("unchecked")
                Map<String, List<Map<String, Object>>> triggeredReservoirGroupsByPjcd = 
                        (Map<String, List<Map<String, Object>>>) argResult.get("triggeredReservoirGroupsByPjcd");

                if (triggeredReservoirGroupsByPjcd != null && !triggeredReservoirGroupsByPjcd.isEmpty()) {
                    // 处理按工程编码分组数据
                    String argId = argResult.get("argId").toString();
                    List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
                    if (templates.isEmpty()) {
                        log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                        continue;
                    }

                    // 为每个工程编码分组生成消息
                    for (Map.Entry<String, List<Map<String, Object>>> groupEntry : triggeredReservoirGroupsByPjcd.entrySet()) {
                        String groupKey = groupEntry.getKey(); // 格式: "pjcd_工程编码"
                        List<Map<String, Object>> groupReservoirs = groupEntry.getValue();
                        
                        if (CollectionUtils.isEmpty(groupReservoirs)) {
                            continue;
                        }
                        
                        String pjcd = extractPjcdFromGroupKey(groupKey);
                        
                        log.info("处理工程编码 {} 的水库水位预警，包含 {} 个测站", pjcd, groupReservoirs.size());
                        
                        for (SjyAlertSmsTemplate template : templates) {
                            Map<String, Object> templateData = new HashMap<>(argResult);
                            templateData.put("list", groupReservoirs);
                            templateData.put("pjcd", pjcd);
                            templateData.put("groupType", "pjcd"); // 标识按工程编码分组
                            
                            SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                            if (alert != null) {
                                // 根据工程编码获取对应的接收人员
                                List<SjyAlertPerson> receivers = getReceiversByRscd(rule, argId, pjcd);
                                List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                                List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                                allRecords.addAll(mergedAlerts);
                                log.info("工程编码 {} 生成 {} 条水库水位预警消息", pjcd, mergedAlerts.size());
                            }
                        }
                    }
                }

                // 如果既没有按行政区划分组也没有按工程编码分组，则兼容原有逻辑：处理未分组的数据
                if ((triggeredReservoirGroups == null || triggeredReservoirGroups.isEmpty()) && 
                    (triggeredReservoirGroupsByPjcd == null || triggeredReservoirGroupsByPjcd.isEmpty())) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> triggeredReservoirs = 
                            (List<Map<String, Object>>) argResult.get("triggeredReservoirs");

                    if (CollectionUtils.isEmpty(triggeredReservoirs)) {
                        continue; 
                    }
                    
                    String argId = argResult.get("argId").toString();
                    List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
                    if (templates.isEmpty()) {
                        log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                        continue;
                    }

                    for (SjyAlertSmsTemplate template : templates) {
                        Map<String, Object> templateData = new HashMap<>(argResult);
                        templateData.put("list", triggeredReservoirs); 
                        
                        SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                        if (alert != null) {
                            List<SjyAlertPerson> receivers = getReceivers(rule, argId);
                            List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                            List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                            allRecords.addAll(mergedAlerts);
                            log.info("参数组 {} 生成 {} 条水库水位预警消息（兼容模式）", argId, mergedAlerts.size());
                        }
                    }
                }

                // 处理持续预警逻辑（针对每个参数组）
//                @SuppressWarnings("unchecked")
//                List<Map<String, Object>> allTriggeredReservoirs = getAllTriggeredReservoirs(argResult);
//                if (!CollectionUtils.isEmpty(allTriggeredReservoirs)) {
//                    // 更新测站状态，但不再依赖状态变化来决定是否发送预警
//                    // 因为数据提供阶段已经过滤了数据，只包含需要预警的测站
//                    continuousAlertService.processReservoirContinuousAlert(rule, allTriggeredReservoirs);
//                    log.info("水库预警规则 {} 参数组 {} 已更新测站状态", rule.getRuleId(), argResult.get("argId"));
//                }
            }
            //过滤掉没有接收人的消息
//            allRecords = allRecords.stream()
//                    .filter(e->e.getPersonId()!=null && !e.getPersonId().isEmpty())
//                    .collect(Collectors.toList());
            context.setRecords(allRecords);
            log.info("总共生成 {} 条水库水位预警消息", allRecords.size());

        } catch (Exception e) {
            log.error("处理水库水位预警失败: {}", e.getMessage(), e);
        }

        // 传递给下一个处理器
        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    private List<SjyAlertSmsTemplate> getTemplates(Long ruleId, String argId) {
        List<SjyAlertSmsTemplate> templates = alertSmsTemplateMapper.selectOneByRelId(argId, "2");
        if (CollectionUtils.isEmpty(templates)) {
            log.warn("未找到规则ID {}，类型8的模板", ruleId);
            return new ArrayList<>();
        }
        return templates;
    }

    private List<SjyAlertPerson> getReceivers(SjyAlertRule rule, String argId) {
        try {
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgId(argId);
            return getReceiversFromNodeIds(nodeIds, rule.getRuleId(), argId, "参数组 " + argId);
        } catch (Exception e) {
            log.error("获取接收人员失败，规则ID: {}, 参数ID: {}, 错误: {}", rule.getRuleId(), argId, e.getMessage(), e);
            return getDefaultAdmin();
        }
    }

    /**
     * 从分组键中提取行政区划编码
     * @param groupKey 格式如 "9_331082001" 或 "6_331082"
     * @return 行政区划编码
     */
    private String extractJurisdictionFromGroupKey(String groupKey) {
        if (groupKey != null && groupKey.length() > 2) {
            return groupKey.substring(2); // 跳过 "9_" 或 "6_" 前缀
        }
        return "";
    }

    /**
     * 从分组键中提取工程编码
     * @param groupKey 格式如 "pjcd_工程编码"
     * @return 工程编码
     */
    private String extractPjcdFromGroupKey(String groupKey) {
        if (groupKey != null && groupKey.startsWith("pjcd_")) {
            return groupKey.substring(5); // 跳过 "pjcd_" 前缀
        }
        return "";
    }

    /**
     * 根据编码获取接收人员
     * @param rule 预警规则
     * @param argId 参数组ID
     * @return 接收人员列表
     */
    private List<SjyAlertPerson> getReceiversByRscd(SjyAlertRule rule, String argId, String rscd ) {
        try {
            String nodeId = "rs-" + rscd;
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgIdAndNodeId(argId, nodeId);
            return getReceiversFromNodeIds(nodeIds, rule.getRuleId(), argId, "工程编码 " + rscd);
        } catch (Exception e) {
            log.error("根据工程编码 {} 获取接收人员失败: {}", rscd, e.getMessage(), e);
            return getDefaultAdmin();
        }
    }
    /**
     * 根据行政区划获取接收人员
     * @param rule 预警规则
     * @param argId 参数组ID
     * @return 接收人员列表
     */
    private List<SjyAlertPerson> getReceiversByJurisdiction(SjyAlertRule rule, String argId, String adcd ) {
        try {
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgIdAndAdcd(argId, adcd);
            return getReceiversFromNodeIds(nodeIds, rule.getRuleId(), argId, "行政区划 " + adcd);
        } catch (Exception e) {
            log.error("根据行政区划 {} 获取接收人员失败: {}", adcd, e.getMessage(), e);
            return getDefaultAdmin();
        }
    }

    /**
     * 处理每日8点持续预警汇总 - 8点超警必发
     */
    private void processDailySummary(AlertContext context) {
        log.info("开始处理水库水位每日8点超警汇总");
        
        Map<String, Object> data = context.getData();
        SjyAlertRule rule = context.getRule();
        
        @SuppressWarnings("unchecked")
        List<SjyAlertStSituation> activeStations = (List<SjyAlertStSituation>) data.get("activeStations");
        @SuppressWarnings("unchecked")
        List<SjyAlertStSituation> warningStations = (List<SjyAlertStSituation>) data.get("warningStations");
        @SuppressWarnings("unchecked")
        List<SjyAlertStSituation> guaranteeStations = (List<SjyAlertStSituation>) data.get("guaranteeStations");
        
        // 8点超警必发：只要有超警测站就发送，不管是否为空
        if (CollectionUtils.isEmpty(activeStations)) {
            log.info("水库预警规则 {} 没有仍在预警状态的测站，跳过发送", rule.getRuleId());
            context.setRecords(new ArrayList<>());
            return;
        }
        
        List<SjyAlertSmsRecord> summaryRecords = new ArrayList<>();
        
        // 生成每日持续预警汇总消息
        try {
            // 获取默认模板（简化处理，实际可能需要专门的汇总模板）
            List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), "summary");
            if (templates.isEmpty()) {
                log.warn("未找到规则ID {} 的汇总模板，使用默认接收人", rule.getRuleId());
                // 创建简单的汇总消息
                SjyAlertSmsRecord summaryAlert = createDefaultSummaryAlert(rule, warningStations, guaranteeStations);
                List<SjyAlertPerson> receivers = getReceivers(rule, "summary");
                for (SjyAlertPerson receiver : receivers) {
                    SjyAlertSmsRecord record = new SjyAlertSmsRecord();
                    record.setRuleId(String.valueOf(rule.getRuleId()));
                    record.setContent(summaryAlert.getContent());
                    record.setPersonId(receiver.getPersonId());
                    summaryRecords.add(record);
                }
            }
        } catch (Exception e) {
            log.error("生成每日持续预警汇总失败: {}", e.getMessage(), e);
        }
        
        context.setRecords(summaryRecords);
        log.info("生成 {} 条水库水位每日持续预警汇总消息", summaryRecords.size());
    }

    /**
     * 创建默认的8点超警汇总消息
     */
    private SjyAlertSmsRecord createDefaultSummaryAlert(SjyAlertRule rule, 
                                                        List<SjyAlertStSituation> warningStations, 
                                                        List<SjyAlertStSituation> guaranteeStations) {
        StringBuilder content = new StringBuilder();
        content.append("【水库水位8点超警汇总】\n");
        content.append("规则：").append(rule.getRuleName()).append("\n");
        content.append("汇总时间：每日8:00\n");
        
        if (!CollectionUtils.isEmpty(guaranteeStations)) {
            content.append("超保证水位测站(").append(guaranteeStations.size()).append("个)：");
            content.append(guaranteeStations.stream()
                    .map(SjyAlertStSituation::getStcd)
                    .collect(Collectors.joining(", ")));
            content.append("\n");
        }
        
        if (!CollectionUtils.isEmpty(warningStations)) {
            content.append("超警戒水位测站(").append(warningStations.size()).append("个)：");
            content.append(warningStations.stream()
                    .map(SjyAlertStSituation::getStcd)
                    .collect(Collectors.joining(", ")));
            content.append("\n");
        }
        
        content.append("请相关责任人密切关注水位变化，做好防范措施。");
        
        SjyAlertSmsRecord record = new SjyAlertSmsRecord();
        record.setRuleId(String.valueOf(rule.getRuleId()));
        record.setContent(content.toString());
        
        return record;
    }

    /**
     * 获取参数组中所有触发的水库（合并所有分组）
     */
    private List<Map<String, Object>> getAllTriggeredReservoirs(Map<String, Object> argResult) {
        List<Map<String, Object>> allTriggered = new ArrayList<>();
        
        // 获取按行政区划分组的数据
        @SuppressWarnings("unchecked")
        Map<String, List<Map<String, Object>>> triggeredReservoirGroups = 
                (Map<String, List<Map<String, Object>>>) argResult.get("triggeredReservoirGroups");
        if (triggeredReservoirGroups != null) {
            triggeredReservoirGroups.values().forEach(allTriggered::addAll);
        }
        
        // 获取按工程编码分组的数据
        @SuppressWarnings("unchecked")
        Map<String, List<Map<String, Object>>> triggeredReservoirGroupsByPjcd = 
                (Map<String, List<Map<String, Object>>>) argResult.get("triggeredReservoirGroupsByPjcd");
        if (triggeredReservoirGroupsByPjcd != null) {
            triggeredReservoirGroupsByPjcd.values().forEach(allTriggered::addAll);
        }
        
        // 获取未分组的数据
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> triggeredReservoirs = 
                (List<Map<String, Object>>) argResult.get("triggeredReservoirs");
        if (triggeredReservoirs != null) {
            allTriggered.addAll(triggeredReservoirs);
        }
        
        // 去重（基于stcd）
        return allTriggered.stream()
                .collect(Collectors.groupingBy(reservoir -> reservoir.get("stcd")))
                .values()
                .stream()
                .map(group -> group.get(0)) // 每个测站只保留一个
                .collect(Collectors.toList());
    }

    /**
     * 通用的从节点ID获取接收人方法
     */
    private List<SjyAlertPerson> getReceiversFromNodeIds(List<String> nodeIds, Long ruleId, String argId, String context) {
        if (nodeIds == null || nodeIds.isEmpty()) {
            log.debug("未找到 {} 的节点ID，使用默认管理员", context);
            return getDefaultAdmin();
        }
        
        try {
            List<SjyAlertPerson> receivers = sjyAlertPersonService.selectByMemberIds(nodeIds);
            if (receivers.isEmpty()) {
                log.warn("规则ID {} 及其参数组 {} 的 {} 未配置接收人或未找到，使用默认管理员", ruleId, argId, context);
                return getDefaultAdmin();
            }
            return receivers.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("从节点ID获取接收人失败，{}: {}", context, e.getMessage(), e);
            return getDefaultAdmin();
        }
    }

    /**
     * 获取默认管理员
     */
    private List<SjyAlertPerson> getDefaultAdmin() {
        SjyAlertPerson admin = new SjyAlertPerson();
        admin.setName("admin");
        admin.setPhone("15306587076");
        return Collections.singletonList(admin);
    }
} 