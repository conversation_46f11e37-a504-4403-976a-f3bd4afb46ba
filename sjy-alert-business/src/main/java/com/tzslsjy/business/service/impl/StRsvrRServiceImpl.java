package com.tzslsjy.business.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.tzslsjy.business.domain.data.StRsvrR;
import com.tzslsjy.business.mapper.data.StRsvrRMapper;
import com.tzslsjy.business.service.IStRsvrRService;
import com.tzslsjy.business.vo.*;
import com.tzslsjy.common.core.domain.entity.SysDictData;
import com.tzslsjy.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 水库水情Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
@Service
@Slf4j
public class StRsvrRServiceImpl extends ServiceImpl<StRsvrRMapper, StRsvrR> implements IStRsvrRService {
    // --- Constants ---
    private static final String DATE_FORMAT_MMDD = "MMdd";
    private static final String DICT_TYPE_RSVRTP = "RSVRTP";
    private static final String CONFIG_KEY_WATER_LATEST_AGEING = "water.latest.ageing";
    private static final int DEFAULT_QUERY_DURATION_HOURS = 6;
    private static final int DEFAULT_HISTORY_QUERY_DURATION_HOURS = 3;
    private static final int DEFAULT_OFFSET_MINUTES = -30;
    private static final int DEFAULT_OFFSET_MINUTES_NEG_5 = -5;
    private static final int DEFAULT_OFFSET_HOURS_NEG_1 = -1;
    private static final int DEFAULT_OFFSET_HOURS_NEG_2 = -2;
    private static final int DEFAULT_OFFSET_HOURS_NEG_6 = -6;
    private static final double DEFAULT_MODULUS = 0.8;
    private static final int SCALE_CAPACITY = 4; // Scale for capacity calculations (W)
    private static final int SCALE_DISPLAY_2 = 2; // Scale for general display
    private static final int SCALE_DISPLAY_1 = 1; // Scale for block display
    private static final int SCALE_RATE = 4;      // Scale for rate calculations
    private static final int SCALE_INTERMEDIATE_K = 2; // Scale for intermediate block calculation
    private static final BigDecimal BIG_DECIMAL_1000 = BigDecimal.valueOf(1000);
    private static final BigDecimal BIG_DECIMAL_100 = BigDecimal.valueOf(100);
    private static final BigDecimal BIG_DECIMAL_10 = BigDecimal.valueOf(10);
    private static final String UNKNOWN_SCALE_NAME = "未知规模";
    private static final Long UNKNOWN_SCALE_SORT = 1000L;
    private static final int STATUS_NORMAL = 0;
    private static final int STATUS_OVER_FLOOD_LIMIT = 1;
    private static final int STATUS_OVER_NORMAL = 2;
    private static final int STATUS_NO_DATA = 6;

    @Resource
    private ISysConfigService sysConfigService;
    @Override
    public List<ReservoirVo> rsWithRz(ReservoirReqVo vo) {
        // 1. Prepare Request & Pagination
        prepareReservoirRequest(vo);
        if (vo.getPageSize() != null && vo.getPageNum() != null) {
            PageHelper.startPage(vo.getPageNum(), vo.getPageSize());
        }

        // 2. Fetch Data
        List<ReservoirVo> stations = baseMapper.getSts(vo); // Basic station info
        List<ReservoirVo> waterLevels = getVal(vo); // Water level data within the time range

        Map<String, List<ZvarlVo>> zvarlMap = getSortedZvarlMap(); // Capacity curve data (cached/optimized)
         // Keep first on duplicate

        // 3. Process Data
        Map<String, List<ReservoirVo>> waterLevelMap = groupWaterLevelsByStcdForReservoirVo(waterLevels); // Use specific overload
        stations.forEach(station -> {
            String stcd = station.getStcd();

            List<ReservoirVo> levelsForStation = waterLevelMap.get(stcd);
            List<ZvarlVo> zvarlForStation = zvarlMap.get(stcd);
            processSingleStationForPageList(station, levelsForStation, zvarlForStation );
        });

        // 4. Calculate Status & Filter
        stations.forEach(this::calculateAndUpdateReservoirStatus); // Use helper method
       return stations;
    }

    /**
     * Calculates and updates the status of a ReservoirVo based on its Rz level.
     */
    private void calculateAndUpdateReservoirStatus(ReservoirVo station) {
        BigDecimal rz = station.getRz();
        BigDecimal fsltdz = station.getFsltdz(); // Flood limit level
        BigDecimal normz = station.getNormz();   // Normal level

        if (rz == null) {
            station.setStatus(STATUS_NO_DATA);
        } else {
            station.setStatus(STATUS_NORMAL); // Default to normal
            if (fsltdz != null && rz.compareTo(fsltdz) > 0) {
                station.setStatus(STATUS_OVER_FLOOD_LIMIT);
                station.setOverRz(NumberUtil.sub(rz,fsltdz));
            }
            // Over normal overrides over flood limit status
            if (normz != null && rz.compareTo(normz) > 0) {
                station.setStatus(STATUS_OVER_NORMAL);
                station.setOverRz(NumberUtil.sub(rz,normz));
            }
        }
    }
    /**
     * Processes a single station for the pageList method.
     */
    private void processSingleStationForPageList(ReservoirVo station, List<ReservoirVo> levels, List<ZvarlVo> zvarlVoList ) {
        String stcd = station.getStcd();
        station.setStatus(STATUS_NORMAL); // Default status
        station.setWptn("未知"); // Default pattern

        // List<ReservoirVo> levels = waterLevelMap.get(stcd); // Now passed as argument
        if (CollectionUtil.isNotEmpty(levels)) {
            // Find the latest valid record
            ReservoirVo latestLevel = levels.stream()
                    .filter(l -> l.getTm() != null && l.getRz() != null)
                    .max(Comparator.comparing(ReservoirVo::getTm))
                    .orElse(null);

            if (latestLevel != null) {
                station.setTm(latestLevel.getTm());
                station.setRz(latestLevel.getRz());
                station.setWptn( latestLevel.getRwptn() );
                // Status is calculated later based on Rz

                // Calculate capacities and rates if ZVARL data exists
                // List<ZvarlVo> zvarlVoList = zvarlMap.get(stcd); // Now passed as argument
                if (zvarlVoList != null) {
                    calculateCapacitiesAndRatesForPageList(station, zvarlVoList);

                    scalePageListValuesForDisplay(station);
                }
            }
        }
        // If no water level data, Norcp might still be calculable if Normz exists
        else if (station.getNormz() != null && zvarlVoList != null) { // check zvarlVoList null
            // List<ZvarlVo> zvarlVoListForNorcp = zvarlMap.get(stcd); // Now passed as argument
            station.setNorcp(getZValByRz(zvarlVoList, station.getNormz()));
            scalePageListValuesForDisplay(station); // Scale Norcp if calculated
        }
    }
    /**
     * Scales BigDecimal values in ReservoirVo for display in pageList.
     */
    private void scalePageListValuesForDisplay(ReservoirVo station) {
        station.setRz(scale(station.getRz(), SCALE_DISPLAY_2));
        station.setNorcp(scale(station.getNorcp(), SCALE_DISPLAY_2));
        station.setNorRz(scale(station.getNorRz(), SCALE_DISPLAY_2));
        station.setFsltdw(scale(station.getFsltdw(), SCALE_DISPLAY_2));
        // Rates (NorRate, XRate) are typically percentages or ratios, scaling might depend on display requirements
        // Blocks (Nblock, Xblock) are already scaled during calculation
    }
    /**
     * Calculates Norcp, NorRz, Fsltdw, and NorRate for pageList.
     */
    private void calculateCapacitiesAndRatesForPageList(ReservoirVo station, List<ZvarlVo> zvarlVoList) {
        station.setNorcp(getZValByRz(zvarlVoList, station.getNormz()));
        station.setNorRz(getZValByRz(zvarlVoList, station.getRz())); // Capacity at current Rz
        station.setFsltdw(getZValByRz(zvarlVoList, station.getFsltdz()));

        // Calculate Normal Rate (NorRate)
        BigDecimal norRz = station.getNorRz();
        BigDecimal norcp = station.getNorcp();
        BigDecimal actcp = station.getActcp();
        String stcd = station.getStcd();
        BigDecimal ckflzLevel = station.getCkflz(); // Check flood level

        if (norRz != null) {
            // Special handling for specific stcds
            if ((stcd.equals("70802640") || stcd.equals("70802660")) && ckflzLevel != null) {
                BigDecimal jhCapacity = getZValByRz(zvarlVoList, ckflzLevel); // Capacity at check flood level
                if (isPositive(jhCapacity)) {
                    station.setNorRate(safeDivide(norRz, jhCapacity, SCALE_RATE));
                    return; // Rate calculated based on special rule
                }
            }
            // Standard calculation
            if (isPositive(norcp)) {
                station.setNorRate(safeDivide(norRz, norcp, SCALE_RATE));
            } else if (isPositive(actcp)) {
                // Fallback to Actcp if Norcp is invalid
                station.setNorRate(safeDivide(norRz, actcp, SCALE_RATE));
            }
        }
        // Calculate Flood Limit Rate (XRate) - Assuming XRate is rate against Fsltdw
        if (norRz != null && isPositive(station.getFsltdw())) {
            station.setXRate(safeDivide(norRz, station.getFsltdw(), SCALE_RATE));
        }
    }
    /**
     * Calculates the capacity (W) for a given water level (Rz) using linear interpolation
     * based on a pre-sorted list of ZvarlVo points.
     *
     * @param sortedZvarlList Pre-sorted list of ZvarlVo by Rz.
     * @param rz              The water level to interpolate for.
     * @return The calculated capacity (W), or null if input is invalid.
     */
    BigDecimal getZValByRz(List<ZvarlVo> sortedZvarlList, BigDecimal rz) {
        if (rz == null || CollectionUtil.isEmpty(sortedZvarlList) || sortedZvarlList.size() < 2) {
            return null;
        }

        // Handle edge cases: below the first point or above the last point
        ZvarlVo first = sortedZvarlList.get(0);
        ZvarlVo last = sortedZvarlList.get(sortedZvarlList.size() - 1);

        if (rz.compareTo(first.getRz()) <= 0) {
            // Extrapolate below (or return first W if rz == first.getRz) - Simple linear extrapolation from origin (0,0) assumed by original code
            if (first.getRz() != null && first.getRz().compareTo(BigDecimal.ZERO) != 0) {
                // Avoid division by zero if first Rz is 0
                return safeMultiply(rz, safeDivide(first.getW(), first.getRz(), SCALE_CAPACITY * 2)) // Use higher intermediate scale
                        .setScale(SCALE_CAPACITY, RoundingMode.HALF_UP);
            } else {
                return first.getW() != null ? first.getW().setScale(SCALE_CAPACITY, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(SCALE_CAPACITY); // Return 0 or first W if Rz is 0
            }
        }
        if (rz.compareTo(last.getRz()) >= 0) {
            // Extrapolate above (or return last W if rz == last.getRz) - Simple linear extrapolation from last two points assumed by original code
            ZvarlVo secondLast = sortedZvarlList.get(sortedZvarlList.size() - 2);
            return interpolate(rz, secondLast.getRz(), secondLast.getW(), last.getRz(), last.getW());
        }


        // Interpolate within the range using binary search or linear scan
        // Linear scan for simplicity, assuming list size is moderate
        for (int i = 0; i < sortedZvarlList.size() - 1; i++) {
            ZvarlVo p1 = sortedZvarlList.get(i);
            ZvarlVo p2 = sortedZvarlList.get(i + 1);

            // Ensure points are valid for interpolation
            if (p1.getRz() == null || p1.getW() == null || p2.getRz() == null || p2.getW() == null || p1.getRz().compareTo(p2.getRz()) == 0) {
                continue; // Skip invalid segment
            }

            if (rz.compareTo(p1.getRz()) >= 0 && rz.compareTo(p2.getRz()) <= 0) {
                return interpolate(rz, p1.getRz(), p1.getW(), p2.getRz(), p2.getW());
            }
        }

        log.warn("Interpolation failed for Rz: {}. Could not find suitable segment in ZVARL list.", rz); // Fixed logging call
        return null; // Should not happen if edge cases and loop are correct
    }
    /**
     * Calculates the water level (Rz) for a given capacity (W) using linear interpolation
     * based on a list of ZvarlVo points sorted by W.
     *
     * @param sortedZvarlListByW List of ZvarlVo sorted by W.
     * @param w                  The capacity to interpolate for.
     * @return The calculated water level (Rz), or null if input is invalid.
     */
    BigDecimal getRzByZVal(List<ZvarlVo> sortedZvarlListByW, BigDecimal w) {
        if (w == null || CollectionUtil.isEmpty(sortedZvarlListByW) || sortedZvarlListByW.size() < 2) {
            return null;
        }

        // Ensure the list is sorted by W (caller should ideally provide sorted list)
        // If not guaranteed, uncomment the next line, but it's inefficient to sort every time.
        // sortedZvarlListByW = sortedZvarlListByW.stream().filter(z -> z != null && z.getW() != null).sorted(Comparator.comparing(ZvarlVo::getW)).collect(Collectors.toList());
        // if (sortedZvarlListByW.size() < 2) return null;


        // Handle edge cases: below the first point or above the last point
        ZvarlVo first = sortedZvarlListByW.get(0);
        ZvarlVo last = sortedZvarlListByW.get(sortedZvarlListByW.size() - 1);

        if (w.compareTo(first.getW()) <= 0) {
            // Extrapolate below (or return first Rz if w == first.getW) - Simple linear extrapolation from origin (0,0) assumed
            if (first.getW() != null && first.getW().compareTo(BigDecimal.ZERO) != 0) {
                return safeMultiply(w, safeDivide(first.getRz(), first.getW(), SCALE_CAPACITY * 2))
                        .setScale(SCALE_DISPLAY_2, RoundingMode.HALF_UP); // Rz usually displayed with 2 decimals
            } else {
                return first.getRz() != null ? first.getRz().setScale(SCALE_DISPLAY_2, RoundingMode.HALF_UP) : BigDecimal.ZERO.setScale(SCALE_DISPLAY_2);
            }
        }
        if (w.compareTo(last.getW()) >= 0) {
            // Extrapolate above (or return last Rz if w == last.getW) - Simple linear extrapolation from last two points assumed
            ZvarlVo secondLast = sortedZvarlListByW.get(sortedZvarlListByW.size() - 2);
            return interpolate(w, secondLast.getW(), secondLast.getRz(), last.getW(), last.getRz());
        }


        // Interpolate within the range using linear scan
        for (int i = 0; i < sortedZvarlListByW.size() - 1; i++) {
            ZvarlVo p1 = sortedZvarlListByW.get(i);
            ZvarlVo p2 = sortedZvarlListByW.get(i + 1);

            // Ensure points are valid for interpolation
            if (p1.getRz() == null || p1.getW() == null || p2.getRz() == null || p2.getW() == null || p1.getW().compareTo(p2.getW()) == 0) {
                continue; // Skip invalid segment
            }


            if (w.compareTo(p1.getW()) >= 0 && w.compareTo(p2.getW()) <= 0) {
                // Interpolate Rz based on W: Rz = Rz1 + (W - W1) * (Rz2 - Rz1) / (W2 - W1)
                return interpolate(w, p1.getW(), p1.getRz(), p2.getW(), p2.getRz());
            }
        }

        log.warn("Interpolation failed for W: {}. Could not find suitable segment in ZVARL list.", w); // Fixed logging call
        return null; // Should not happen
    }
    /**
     * Groups a list of ReservoirVo by STCD.
     */
    private Map<String, List<ReservoirVo>> groupWaterLevelsByStcdForReservoirVo(List<ReservoirVo> waterLevels) { // Specific overload
        return Optional.ofNullable(waterLevels).orElse(Collections.emptyList()).stream()
                .filter(w -> w != null && w.getStcd() != null)
                .collect(Collectors.groupingBy(ReservoirVo::getStcd));
    }
    /**
     * Fetches, groups, and sorts ZVARL data by STCD.
     * The list for each STCD is sorted by Rz.
     * Consider adding caching.
     */
    // @Cacheable("sortedZvarlMap")
    private Map<String, List<ZvarlVo>> getSortedZvarlMap() {
        List<ZvarlVo> zvarlVos = baseMapper.getZvarls(null);
        return Optional.ofNullable(zvarlVos).orElse(Collections.emptyList()).stream()
                .filter(z -> z != null && z.getStcd() != null)
                .collect(Collectors.groupingBy(ZvarlVo::getStcd))
                .entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .filter(z -> z.getRz() != null) // Ensure rz is not null for sorting
                                .sorted(Comparator.comparing(ZvarlVo::getRz))
                                .collect(Collectors.toList())
                ));
    }

    /**
     * Prepares the ReservoirReqVo by setting default times and formatting bgmd.
     */
    private void prepareReservoirRequest(ReservoirReqVo vo) {
        int duringAge = getConfiguredQueryDuration();
        Date now = new Date();

        if (vo.getEndTm() == null || vo.getEndTm().after(now)) {
            vo.setEndTm(now);
        }
        if (vo.getStartTm() == null) {
            vo.setStartTm(DateUtil.offsetMinute(vo.getEndTm(), DEFAULT_OFFSET_MINUTES));
        }

        // Adjust start time if duration is too long
        if (DateUtil.between(vo.getStartTm(), vo.getEndTm(), DateUnit.HOUR) > duringAge) {
            vo.setStartTm(DateUtil.offsetHour(vo.getEndTm(), -duringAge));
        }

        // Set default bgmd (MMdd format) if empty
        if (StrUtil.isEmpty(vo.getBgmd())) {
            vo.setBgmd(DateUtil.format(vo.getEndTm(), DATE_FORMAT_MMDD));
        }
    }
    /**
     * Fetches the latest water level data (StRsvrR) within a specified duration before the end time.
     *
     * @param vo Request object containing end time and station criteria.
     * @return List of ReservoirVo containing the latest water levels.
     */
    public List<ReservoirVo> getVal(ReservoirReqVo vo) {
        int duringAge = getConfiguredQueryDuration(); // Use helper to get configured duration

        // Adjust start time if the requested range exceeds the configured duration
        if (vo.getStartTm() == null || DateUtil.between(vo.getStartTm(), vo.getEndTm(), DateUnit.HOUR) > duringAge) {
            // Ensure endTm is set before calculating startTm
            if (vo.getEndTm() == null) {
                vo.setEndTm(new Date());
            }
            vo.setStartTm(DateUtil.offsetHour(vo.getEndTm(), -duringAge));
        }
        // Fetch data using the adjusted time range
        return baseMapper.getVal(vo);
    }
    /**
     * Gets the configured maximum query duration in hours.
     */
    private int getConfiguredQueryDuration() {
        String configValue = sysConfigService.selectConfigByKey(CONFIG_KEY_WATER_LATEST_AGEING);
        try {
            return StrUtil.isNotEmpty(configValue) ? Integer.parseInt(configValue) : DEFAULT_QUERY_DURATION_HOURS;
        } catch (NumberFormatException e) {
            log.warn("Invalid configuration value for {}: {}. Using default value {}.", // Fixed logging call
                    CONFIG_KEY_WATER_LATEST_AGEING, configValue, DEFAULT_QUERY_DURATION_HOURS, e);
            return DEFAULT_QUERY_DURATION_HOURS;
        }
    }
    /**
     * Helper for linear interpolation: y = y1 + (x - x1) * (y2 - y1) / (x2 - x1)
     * Handles potential division by zero and nulls.
     */
    private BigDecimal interpolate(BigDecimal x, BigDecimal x1, BigDecimal y1, BigDecimal x2, BigDecimal y2) {
        if (x == null || x1 == null || y1 == null || x2 == null || y2 == null) {
            return null;
        }
        BigDecimal xDiff = safeSubtract(x2, x1);
        if (xDiff.compareTo(BigDecimal.ZERO) == 0) {
            // Avoid division by zero - return y1 or y2 if x matches x1 or x2
            return x.compareTo(x1) == 0 ? y1 : (x.compareTo(x2) == 0 ? y2 : null); // Or average, or log error
        }
        BigDecimal yDiff = safeSubtract(y2, y1);
        BigDecimal xRatio = safeDivide(safeSubtract(x, x1), xDiff, SCALE_CAPACITY * 2); // Use higher intermediate precision
        BigDecimal interpolatedValue = safeAdd(y1, safeMultiply(xRatio, yDiff));

        // Determine appropriate scale based on context (e.g., capacity vs. level)
        int scale = (y1.scale() > 2 || y2.scale() > 2) ? SCALE_CAPACITY : SCALE_DISPLAY_2; // Heuristic guess
        return scale(interpolatedValue, scale);
    }

    // --- Safe BigDecimal Operations ---

    private BigDecimal safeAdd(BigDecimal augend, BigDecimal addend) {
        if (augend == null) return addend;
        if (addend == null) return augend;
        return augend.add(addend);
    }

    private BigDecimal safeSubtract(BigDecimal minuend, BigDecimal subtrahend) {
        if (minuend == null) return (subtrahend == null) ? BigDecimal.ZERO : subtrahend.negate();
        if (subtrahend == null) return minuend;
        return minuend.subtract(subtrahend);
    }

    private BigDecimal safeMultiply(BigDecimal multiplicand, BigDecimal multiplier) {
        if (multiplicand == null || multiplier == null) return null;
        return multiplicand.multiply(multiplier);
    }

    private BigDecimal safeDivide(BigDecimal dividend, BigDecimal divisor, int scale) {
        if (dividend == null || divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
            return null; // Or throw exception, or return specific value like ZERO
        }
        try {
            return dividend.divide(divisor, scale, RoundingMode.HALF_UP);
        } catch (ArithmeticException e) {
            log.error("Division error: {} / {} with scale {}: {}", dividend, divisor, scale, e.getMessage()); // Fixed logging call
            return null; // Or handle error as appropriate
        }
    }
    private BigDecimal safesub(BigDecimal dividend, BigDecimal divisor, int scale) {
        if (dividend == null || divisor == null || divisor.compareTo(BigDecimal.ZERO) == 0) {
            return null; // Or throw exception, or return specific value like ZERO
        }
        try {
            return dividend.subtract(divisor).setScale( scale, RoundingMode.HALF_UP);
        } catch (ArithmeticException e) {
            log.error("Division error: {} / {} with scale {}: {}", dividend, divisor, scale, e.getMessage()); // Fixed logging call
            return null; // Or handle error as appropriate
        }
    }
    /**
     * Scales a BigDecimal value to the specified scale using HALF_UP rounding.
     * Returns null if the input is null.
     */
    private BigDecimal scale(BigDecimal value, int scale) {
        return (value != null) ? value.setScale(scale, RoundingMode.HALF_UP) : null;
    }

    /**
     * Checks if a BigDecimal is not null and greater than zero.
     */
    private boolean isPositive(BigDecimal value) {
        return value != null && value.compareTo(BigDecimal.ZERO) > 0;
    }
}