package com.tzslsjy.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.tzslsjy.business.bo.SjyAlertNodePostAddBo;
import com.tzslsjy.business.bo.SjyAlertNodePostQueryBo;
import com.tzslsjy.business.bo.SjyAlertNodePostEditBo;
import com.tzslsjy.business.domain.SjyAlertNodePost;
import com.tzslsjy.business.mapper.SjyAlertNodePostMapper;
import com.tzslsjy.business.vo.SjyAlertNodePostVo;
import com.tzslsjy.business.service.ISjyAlertNodePostService;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 节点职位Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Service
public class SjyAlertNodePostServiceImpl extends ServiceImpl<SjyAlertNodePostMapper, SjyAlertNodePost> implements ISjyAlertNodePostService {

    @Override
    public SjyAlertNodePostVo queryById(Long id){
        SjyAlertNodePost db = this.baseMapper.selectById(id);
        return BeanUtil.toBean(db, SjyAlertNodePostVo.class);
    }

    @Override
    public List<SjyAlertNodePostVo> queryList(SjyAlertNodePostQueryBo bo) {
        LambdaQueryWrapper<SjyAlertNodePost> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getNodeId() != null, SjyAlertNodePost::getNodeId, bo.getNodeId());
        lqw.like(StrUtil.isNotBlank(bo.getPostName()), SjyAlertNodePost::getPostName, bo.getPostName());
        return entity2Vo(this.list(lqw));
    }

    /**
    * 实体类转化成视图对象
    *
    * @param collection 实体类集合
    * @return
    */
    private List<SjyAlertNodePostVo> entity2Vo(Collection<SjyAlertNodePost> collection) {
        List<SjyAlertNodePostVo> voList = collection.stream()
                .map(any -> BeanUtil.toBean(any, SjyAlertNodePostVo.class))
                .collect(Collectors.toList());
        if (collection instanceof Page) {
            Page<SjyAlertNodePost> page = (Page<SjyAlertNodePost>)collection;
            Page<SjyAlertNodePostVo> pageVo = new Page<>();
            BeanUtil.copyProperties(page,pageVo);
            pageVo.addAll(voList);
            voList = pageVo;
        }
        return voList;
    }

    @Override
    public Boolean insertByAddBo(SjyAlertNodePostAddBo bo) {
        SjyAlertNodePost add = BeanUtil.toBean(bo, SjyAlertNodePost.class);
        validEntityBeforeSave(add);
        return this.save(add);
    }

    @Override
    public Boolean updateByEditBo(SjyAlertNodePostEditBo bo) {
        SjyAlertNodePost update = BeanUtil.toBean(bo, SjyAlertNodePost.class);
        validEntityBeforeSave(update);
        return this.updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(SjyAlertNodePost entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return this.removeByIds(ids);
    }
}
