package com.tzslsjy.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyFdProneAreaMapper;
import com.tzslsjy.business.domain.SjyFdProneArea;
import com.tzslsjy.business.service.ISjyFdProneAreaService;

/**
 * 山洪易发区信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class SjyFdProneAreaServiceImpl implements ISjyFdProneAreaService 
{
    @Autowired
    private SjyFdProneAreaMapper sjyFdProneAreaMapper;

    /**
     * 查询山洪易发区信息
     * 
     * @param prevCode 山洪易发区信息主键
     * @return 山洪易发区信息
     */
    @Override
    public SjyFdProneArea selectSjyFdProneAreaByPrevCode(String prevCode)
    {
        return sjyFdProneAreaMapper.selectSjyFdProneAreaByPrevCode(prevCode);
    }

    /**
     * 查询山洪易发区信息列表
     * 
     * @param sjyFdProneArea 山洪易发区信息
     * @return 山洪易发区信息
     */
    @Override
    public List<SjyFdProneArea> selectSjyFdProneAreaList(SjyFdProneArea sjyFdProneArea)
    {
        return sjyFdProneAreaMapper.selectSjyFdProneAreaList(sjyFdProneArea);
    }

    /**
     * 新增山洪易发区信息
     * 
     * @param sjyFdProneArea 山洪易发区信息
     * @return 结果
     */
    @Override
    public int insertSjyFdProneArea(SjyFdProneArea sjyFdProneArea)
    {
        return sjyFdProneAreaMapper.insertSjyFdProneArea(sjyFdProneArea);
    }

    /**
     * 修改山洪易发区信息
     * 
     * @param sjyFdProneArea 山洪易发区信息
     * @return 结果
     */
    @Override
    public int updateSjyFdProneArea(SjyFdProneArea sjyFdProneArea)
    {
        return sjyFdProneAreaMapper.updateSjyFdProneArea(sjyFdProneArea);
    }

    /**
     * 批量删除山洪易发区信息
     * 
     * @param prevCodes 需要删除的山洪易发区信息主键
     * @return 结果
     */
    @Override
    public int deleteSjyFdProneAreaByPrevCodes(String[] prevCodes)
    {
        return sjyFdProneAreaMapper.deleteSjyFdProneAreaByPrevCodes(prevCodes);
    }

    /**
     * 删除山洪易发区信息信息
     * 
     * @param prevCode 山洪易发区信息主键
     * @return 结果
     */
    @Override
    public int deleteSjyFdProneAreaByPrevCode(String prevCode)
    {
        return sjyFdProneAreaMapper.deleteSjyFdProneAreaByPrevCode(prevCode);
    }
}
