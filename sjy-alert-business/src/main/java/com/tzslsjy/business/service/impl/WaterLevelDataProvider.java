//package com.tzslsjy.business.service.impl;
//
//import com.tzslsjy.business.domain.SjyAlertRule;
//import com.tzslsjy.business.domain.SjyAlertRuleArg;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.stereotype.Component;
//
//import java.util.*;
//
///**
// * 水位数据提供者
// * 负责获取水位监测数据并进行预警评估
// */
//@Component
//@Slf4j
//public class WaterLevelDataProvider extends AbstractAlertDataProvider {
//
//    @Autowired
//    private JdbcTemplate jdbcTemplate;
//
//    // 支持的规则类型
//    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("water_level");
//
//    @Override
//    public List<String> getSupportedTypes() {
//        return SUPPORTED_TYPES;
//    }
//
//    @Override
//    protected void customizeQueryParams(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Map<String, Object> queryParams) {
//        // 添加水位特有的查询参数
//        queryParams.put("dataType", "water_level");
//
//        // 设置默认查询参数
//        setDefaultParameters(queryParams);
//
//        // 从规则参数中提取专用配置
//        extractParametersFromRuleArgs(ruleParams, queryParams);
//    }
//
//    /**
//     * 设置默认查询参数
//     */
//    private void setDefaultParameters(Map<String, Object> queryParams) {
//        // 如果没有设置时间范围，设置默认值
//        if (!queryParams.containsKey("timeRange")) {
//            queryParams.put("timeRange", 24); // 默认24小时
//        }
//
//        // 添加其他默认参数
//        if (!queryParams.containsKey("maxRecords")) {
//            queryParams.put("maxRecords", 1); // 默认只查最近一条记录
//        }
//    }
//
//    /**
//     * 从规则参数中提取专用配置
//     */
//    private void extractParametersFromRuleArgs(List<SjyAlertRuleArg> ruleParams, Map<String, Object> queryParams) {
//        if (ruleParams == null) return;
//
//        for (SjyAlertRuleArg param : ruleParams) {
//            // 处理水位阈值
//            if ("waterLevelThreshold".equals(param.getArgType())) {
//                try {
//                    double threshold = Double.parseDouble(param.getArgJson());
//                    queryParams.put("threshold", threshold);
//                } catch (NumberFormatException e) {
//                    log.warn("无法解析水位阈值: {}", param.getArgJson());
//                }
//            }
//            // 处理观测时间
//            else if ("observationTime".equals(param.getArgType())) {
//                try {
//                    int hours = Integer.parseInt(param.getArgJson());
//                    queryParams.put("timeRange", hours);
//                } catch (NumberFormatException e) {
//                    log.warn("无法解析观测时间: {}", param.getArgJson());
//                }
//            }
//        }
//    }
//
//    @Override
//    protected Map<String, Object> fetchDataInternal(String ruleTypeCode, Map<String, Object> queryParams, List<SjyAlertRuleArg> ruleParams) {
//        Map<String, Object> result = new HashMap<>();
//
//        try {
//            // 验证必要参数
//            validateRequiredParameters(queryParams);
//
//            // 查询水位数据
//            fetchWaterLevelData(queryParams, result);
//
//            // 处理参数组和规则元数据
//            processMetadata(ruleParams, result);
//
//            // 添加标准数据字段
//            addStandardFields(ruleTypeCode, queryParams, result);
//
//        } catch (Exception e) {
//            handleDataFetchException(e, result);
//        }
//
//        return result;
//    }
//
//    /**
//     * 验证必要参数
//     */
//    private void validateRequiredParameters(Map<String, Object> queryParams) {
//        String stationId = (String) queryParams.get("stcd");
//        if (stationId == null || stationId.isEmpty()) {
//            throw new IllegalArgumentException("缺少必要参数: stationId");
//        }
//    }
//
//    /**
//     * 查询水位数据
//     */
//    private void fetchWaterLevelData(Map<String, Object> queryParams, Map<String, Object> result) {
//        String stationId = (String) queryParams.get("stcd");
//        int maxRecords = queryParams.containsKey("maxRecords") ?
//                         (int) queryParams.get("maxRecords") : 1;
//
//        try {
//            // 查询最新的水位数据
//            Map<String, Object> waterData = jdbcTemplate.queryForMap(
//                "SELECT station_id, water_level, warning_level, time " +
//                "FROM water_level_data " +
//                "WHERE station_id = ? " +
//                "ORDER BY time DESC LIMIT ?",
//                stationId, maxRecords
//            );
//
//            // 提取数据字段
//            if (waterData != null) {
//                result.put("waterLevel", waterData.get("water_level"));
//                result.put("threshold", waterData.getOrDefault("warning_level", queryParams.get("threshold")));
//                result.put("stationId", waterData.get("station_id"));
//                result.put("measureTime", waterData.get("time"));
//            }
//
//            // 查询测站信息
//            try {
//                Map<String, Object> stationInfo = jdbcTemplate.queryForMap(
//                    "SELECT station_name, station_type, longitude, latitude " +
//                    "FROM station_info WHERE station_id = ?",
//                    stationId
//                );
//
//                if (stationInfo != null) {
//                    result.put("stationName", stationInfo.get("station_name"));
//                    result.put("stationType", stationInfo.get("station_type"));
//
//                    Map<String, Object> locationMap = new HashMap<>();
//                    locationMap.put("longitude", stationInfo.get("longitude"));
//                    locationMap.put("latitude", stationInfo.get("latitude"));
//                    result.put("stationLocation", locationMap);
//                }
//            } catch (Exception e) {
//                log.warn("获取测站信息失败: {}", e.getMessage());
//                // 继续处理，不中断流程
//            }
//        } catch (Exception e) {
//            log.error("查询水位数据失败: {}", e.getMessage(), e);
//            throw e; // 重新抛出异常
//        }
//    }
//
//    /**
//     * 处理元数据
//     */
//    private void processMetadata(List<SjyAlertRuleArg> ruleParams, Map<String, Object> result) {
//        // 添加参数组信息
//        if (ruleParams != null && !ruleParams.isEmpty()) {
//            Map<String, List<SjyAlertRuleArg>> paramGroups = groupParametersByGroup(ruleParams);
//            result.put("paramGroups", paramGroups);
//        }
//    }
//
//    /**
//     * 添加标准数据字段
//     */
//    private void addStandardFields(String ruleTypeCode, Map<String, Object> queryParams, Map<String, Object> result) {
//        result.put("dataType", "water_level");
//        result.put("ruleType", ruleTypeCode);
//        result.put("queryTime", new Date());
//
//        // 复制查询参数中的关键字段到结果中
//        if (queryParams.containsKey("threshold") && !result.containsKey("threshold")) {
//            result.put("threshold", queryParams.get("threshold"));
//        }
//    }
//
//    /**
//     * 处理数据获取异常
//     */
//    private void handleDataFetchException(Exception e, Map<String, Object> result) {
//        log.error("获取水位数据失败: {}", e.getMessage(), e);
//
//        // 记录错误信息到结果中
//        result.put("error", true);
//        result.put("errorMessage", e.getMessage());
//        result.put("errorType", e.getClass().getName());
//
//        // 考虑是否抛出异常中断流程，还是返回部分数据
//        throw new RuntimeException("获取水位数据失败", e);
//    }
//
//    /**
//     * 按组分类参数
//     */
//    private Map<String, List<SjyAlertRuleArg>> groupParametersByGroup(List<SjyAlertRuleArg> params) {
//        Map<String, List<SjyAlertRuleArg>> groups = new HashMap<>();
//
//        for (SjyAlertRuleArg param : params) {
//            String groupId = param.getGroupId() != null ? param.getGroupId() : "default";
//
//            if (!groups.containsKey(groupId)) {
//                groups.put(groupId, new ArrayList<>());
//            }
//
//            groups.get(groupId).add(param);
//        }
//
//        return groups;
//    }
//}
