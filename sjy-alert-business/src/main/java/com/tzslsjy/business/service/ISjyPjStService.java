package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyPjSt;
import com.tzslsjy.business.vo.SjyPjStVo;
import com.tzslsjy.business.bo.SjyPjStQueryBo;
import com.tzslsjy.business.bo.SjyPjStAddBo;
import com.tzslsjy.business.bo.SjyPjStEditBo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * 工程测站关联Service接口
 *
 * <AUTHOR>
 * @date 2025-05-15
 */
public interface ISjyPjStService extends IService<SjyPjSt> {
	/**
	 * 查询单个
	 * @return
	 */
	SjyPjStVo queryById(Long id);

	/**
	 * 查询列表
	 */
	List<SjyPjStVo> queryList(SjyPjStQueryBo bo);

	/**
	 * 根据新增业务对象插入工程测站关联
	 * @param bo 工程测站关联新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(SjyPjStAddBo bo);

	/**
	 * 根据编辑业务对象修改工程测站关联
	 * @param bo 工程测站关联编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(SjyPjStEditBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}