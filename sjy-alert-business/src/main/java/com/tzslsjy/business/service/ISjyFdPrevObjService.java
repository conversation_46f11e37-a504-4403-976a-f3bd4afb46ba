package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyFdPrevObj;

/**
 * 防治对象名录Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface ISjyFdPrevObjService 
{
    /**
     * 查询防治对象名录
     * 
     * @param prevCode 防治对象名录主键
     * @return 防治对象名录
     */
    public SjyFdPrevObj selectSjyFdPrevObjByPrevCode(String prevCode);

    /**
     * 查询防治对象名录列表
     * 
     * @param sjyFdPrevObj 防治对象名录
     * @return 防治对象名录集合
     */
    public List<SjyFdPrevObj> selectSjyFdPrevObjList(SjyFdPrevObj sjyFdPrevObj);

    /**
     * 新增防治对象名录
     * 
     * @param sjyFdPrevObj 防治对象名录
     * @return 结果
     */
    public int insertSjyFdPrevObj(SjyFdPrevObj sjyFdPrevObj);

    /**
     * 修改防治对象名录
     * 
     * @param sjyFdPrevObj 防治对象名录
     * @return 结果
     */
    public int updateSjyFdPrevObj(SjyFdPrevObj sjyFdPrevObj);

    /**
     * 批量删除防治对象名录
     * 
     * @param prevCodes 需要删除的防治对象名录主键集合
     * @return 结果
     */
    public int deleteSjyFdPrevObjByPrevCodes(String[] prevCodes);

    /**
     * 删除防治对象名录信息
     * 
     * @param prevCode 防治对象名录主键
     * @return 结果
     */
    public int deleteSjyFdPrevObjByPrevCode(String prevCode);
}
