package com.tzslsjy.business.service.impl;

import java.util.List;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.domain.SjyAlertRuleNodeRelation;
import com.tzslsjy.business.service.ISjyAlertRuleNodeRelationService;

/**
 * 规则接收节点关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SjyAlertRuleNodeRelationServiceImpl implements ISjyAlertRuleNodeRelationService
{
    @Autowired
    private SjyAlertRuleNodeRelationMapper sjyAlertRuleNodeRelationMapper;

    /**
     * 查询规则接收节点关联
     *
     * @param relationId 规则接收节点关联主键
     * @return 规则接收节点关联
     */
    @Override
    public SjyAlertRuleNodeRelation selectSjyAlertRuleNodeRelationByRelationId(Long relationId)
    {
        return sjyAlertRuleNodeRelationMapper.selectSjyAlertRuleNodeRelationByRelationId(relationId);
    }

    /**
     * 查询规则接收节点关联列表
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 规则接收节点关联
     */
    @Override
    public List<SjyAlertRuleNodeRelation> selectSjyAlertRuleNodeRelationList(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation)
    {
        return sjyAlertRuleNodeRelationMapper.selectSjyAlertRuleNodeRelationList(sjyAlertRuleNodeRelation);
    }

    /**
     * 新增规则接收节点关联
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 结果
     */
    @Override
    public int insertSjyAlertRuleNodeRelation(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation)
    {
        sjyAlertRuleNodeRelation.setCreateTime(DateUtils.getNowDate());
        return sjyAlertRuleNodeRelationMapper.insertSjyAlertRuleNodeRelation(sjyAlertRuleNodeRelation);
    }

    /**
     * 修改规则接收节点关联
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 结果
     */
    @Override
    public int updateSjyAlertRuleNodeRelation(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation)
    {
        sjyAlertRuleNodeRelation.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertRuleNodeRelationMapper.updateSjyAlertRuleNodeRelation(sjyAlertRuleNodeRelation);
    }

    /**
     * 批量删除规则接收节点关联
     *
     * @param relationIds 需要删除的规则接收节点关联主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertRuleNodeRelationByRelationIds(Long[] relationIds)
    {
        return sjyAlertRuleNodeRelationMapper.deleteSjyAlertRuleNodeRelationByRelationIds(relationIds);
    }

    /**
     * 删除规则接收节点关联信息
     *
     * @param relationId 规则接收节点关联主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertRuleNodeRelationByRelationId(Long relationId)
    {
        return sjyAlertRuleNodeRelationMapper.deleteSjyAlertRuleNodeRelationByRelationId(relationId);
    }
}
