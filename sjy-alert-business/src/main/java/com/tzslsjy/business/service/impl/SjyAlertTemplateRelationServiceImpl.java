package com.tzslsjy.business.service.impl;

import java.util.List;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyAlertTemplateRelationMapper;
import com.tzslsjy.business.domain.SjyAlertTemplateRelation;
import com.tzslsjy.business.service.ISjyAlertTemplateRelationService;

/**
 * 消息模板关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SjyAlertTemplateRelationServiceImpl implements ISjyAlertTemplateRelationService
{
    @Autowired
    private SjyAlertTemplateRelationMapper sjyAlertTemplateRelationMapper;

    /**
     * 查询消息模板关联
     *
     * @param id 消息模板关联主键
     * @return 消息模板关联
     */
    @Override
    public SjyAlertTemplateRelation selectSjyAlertTemplateRelationById(Long id)
    {
        return sjyAlertTemplateRelationMapper.selectSjyAlertTemplateRelationById(id);
    }

    /**
     * 查询消息模板关联列表
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 消息模板关联
     */
    @Override
    public List<SjyAlertTemplateRelation> selectSjyAlertTemplateRelationList(SjyAlertTemplateRelation sjyAlertTemplateRelation)
    {
        return sjyAlertTemplateRelationMapper.selectSjyAlertTemplateRelationList(sjyAlertTemplateRelation);
    }

    /**
     * 新增消息模板关联
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 结果
     */
    @Override
    public int insertSjyAlertTemplateRelation(SjyAlertTemplateRelation sjyAlertTemplateRelation)
    {
        sjyAlertTemplateRelation.setCreateTime(DateUtils.getNowDate());
        return sjyAlertTemplateRelationMapper.insertSjyAlertTemplateRelation(sjyAlertTemplateRelation);
    }

    /**
     * 修改消息模板关联
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 结果
     */
    @Override
    public int updateSjyAlertTemplateRelation(SjyAlertTemplateRelation sjyAlertTemplateRelation)
    {
        sjyAlertTemplateRelation.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertTemplateRelationMapper.updateSjyAlertTemplateRelation(sjyAlertTemplateRelation);
    }

    /**
     * 批量删除消息模板关联
     *
     * @param ids 需要删除的消息模板关联主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertTemplateRelationByIds(Long[] ids)
    {
        return sjyAlertTemplateRelationMapper.deleteSjyAlertTemplateRelationByIds(ids);
    }

    /**
     * 删除消息模板关联信息
     *
     * @param id 消息模板关联主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertTemplateRelationById(Long id)
    {
        return sjyAlertTemplateRelationMapper.deleteSjyAlertTemplateRelationById(id);
    }
}
