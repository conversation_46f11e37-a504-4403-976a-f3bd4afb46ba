package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUtil;
import com.tzslsjy.business.domain.SjyAbnormalData;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyStationAbnormalParamData;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsRecordMapper;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.service.AlertDataProvider;
import com.tzslsjy.business.service.ISjyAbnormalDataService;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.ISjyAlertStSituationService;
import com.tzslsjy.business.mapper.StRiverRMapper;
import com.tzslsjy.business.mapper.data.StRsvrRMapper;
import com.tzslsjy.business.mapper.data.StTideRMapper;
import com.tzslsjy.business.mapper.data.StWasRMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 水位异常预警数据提供者
 * 专注于水位异常检测，不包含超时监测功能
 * 支持规则类型：31
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class WaterLevelAbnormalDataProvider implements AlertDataProvider {

    @Autowired
    private ISjyAbnormalDataService abnormalDataService;

    @Autowired
    private StRiverRMapper stRiverRMapper;

    @Autowired
    private StRsvrRMapper stRsvrRMapper;

    @Autowired
    private StTideRMapper stTideRMapper;

    @Autowired
    private StWasRMapper stWasRMapper;

    @Autowired
    private StStbprpBMapper stStbprpBMapper;

    @Autowired
    private ISjyAlertStSituationService alertStSituationService;

    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper;

    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService;

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("31");
    private static final Integer RELATION_TYPE_RULE_INSTANCE = 2; // 规则实例关联类型

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date currentTime) {
        log.debug("开始获取水位异常预警数据，规则类型: {}, 当前时间: {}", ruleTypeCode, currentTime);
        Map<String, Object> result = new HashMap<>();

        try {
            @SuppressWarnings("unchecked")
            SjyStationAbnormalParamData params = (SjyStationAbnormalParamData) queryParams.get("stationAbnormalParams");
            List<StStbprpB> stbprpBList = stStbprpBMapper.selectByAdcd("331082");

            if (params == null || stbprpBList.isEmpty()) {
                log.warn("水位异常预警参数为空或测站列表为空");
                result.put("alertTriggered", false);
                result.put("message", "参数配置有误，无法进行异常检测。");
                addStandardFields(ruleTypeCode, result, queryParams);
                return result;
            }

            // 检查全局开关
            if (!params.getGlobalSwitchEnabled()) {
                log.info("水位异常预警全局开关已关闭，跳过检测");
                result.put("alertTriggered", false);
                result.put("message", "水位异常预警全局开关已关闭。");
                addStandardFields(ruleTypeCode, result, queryParams);
                return result;
            }

            // 检查水位异常检测开关
            if (!params.getWaterLevelDetectionEnabled()) {
                log.info("水位异常检测开关已关闭，跳过检测");
                result.put("alertTriggered", false);
                result.put("message", "水位异常检测开关已关闭。");
                addStandardFields(ruleTypeCode, result, queryParams);
                return result;
            }

            // 获取规则ID
            Long ruleId = (Long) queryParams.get("ruleId");
            if (ruleId == null) {
                log.warn("规则ID为空，无法进行异常检测");
                result.put("alertTriggered", false);
                result.put("message", "规则ID为空，无法进行异常检测。");
                addStandardFields(ruleTypeCode, result, queryParams);
                return result;
            }

            // 执行水位异常检测
            List<Map<String, Object>> abnormalResults = new ArrayList<>();
            checkWaterLevelAbnormal(params, currentTime, ruleId, abnormalResults);

            // 构建结果
            result.put("abnormalResults", abnormalResults);
            result.put("alertTriggered", !abnormalResults.isEmpty());
            result.put("totalAbnormalCount", abnormalResults.size());

            // 添加模板需要的参数
            result.put("duringHour", params.getWaterLevelDetectionDuringHours());
            result.put("waterLevelThreshold", params.getWaterLevelDetectionThreshold());

            if (!abnormalResults.isEmpty()) {
                result.put("message", buildAbnormalMessage(abnormalResults));
                result.put("alertLevel", determineAlertLevel(abnormalResults));

                // 丰富测站信息并按行政区划分组
                List<Map<String, Object>> enrichedResults = enrichStationInfo(abnormalResults);
                Map<String, List<Map<String, Object>>> groupsByJurisdiction = groupAbnormalResultsByJurisdiction(enrichedResults);

                result.put("enrichedAbnormalResults", enrichedResults);
                result.put("abnormalResultGroups", groupsByJurisdiction);
            } else {
                result.put("message", "所有测站水位数据正常");
                result.put("alertLevel", "0");
            }

            addStandardFields(ruleTypeCode, result, queryParams);
            log.info("水位异常预警数据获取完成，触发状态: {}", result.get("alertTriggered"));
            return result;

        } catch (Exception e) {
            log.error("获取水位异常预警数据失败: {}", e.getMessage(), e);
            result.put("alertTriggered", false);
            result.put("message", "获取水位异常预警数据失败: " + e.getMessage());
            result.put("error", e.getMessage());
            addStandardFields(ruleTypeCode, result, queryParams);
            return result;
        }
    }

    /**
     * 检查水位异常
     * 使用新的标准化参数格式，支持可配置的时间周期
     */
    private void checkWaterLevelAbnormal(SjyStationAbnormalParamData params, Date currentTime, Long ruleId,
                                        List<Map<String, Object>> results) {
        try {
            // 使用新的时间周期计算方式：将小时转换为分钟
            Integer duringHours = params.getWaterLevelDetectionDuringHours();
            Integer duringMinutes = duringHours * 60;
            Date startTime = DateUtil.offsetMinute(currentTime, -duringMinutes);

            log.debug("水位异常检查时间范围: {} 小时（{}分钟），从 {} 到 {}",
                    duringHours, duringMinutes, startTime, currentTime);

            // 获取水位测站列表（兼容旧版本，新版本中每个规则只针对单个测站）
            List<String> waterStcds = params.getWaterStcdsForDetection();

            // 如果水位测站列表为空，查询所有水位测站（主要用于兼容旧版本）
            if (waterStcds.isEmpty()) {
                waterStcds = stStbprpBMapper.selectAllWaterStationStcds();
                log.debug("水位测站列表为空，使用所有水位测站: {} 个测站", waterStcds.size());
            }

            // 批量查询测站基础信息，获取测站名称
            Map<String, String> stationNameMap = new HashMap<>();
            if (!waterStcds.isEmpty()) {
                List<com.tzslsjy.business.domain.StStbprpB> stationInfos = stStbprpBMapper.selectStStbprpBBySTCDs(waterStcds);
                stationNameMap = stationInfos.stream()
                        .collect(Collectors.toMap(
                                com.tzslsjy.business.domain.StStbprpB::getStcd,
                                station -> station.getStnm() != null ? station.getStnm() : station.getStcd(),
                                (existing, replacement) -> existing
                        ));
            }

            // 批量查询所有水位测站的异常数据
            List<com.tzslsjy.business.vo.SjyAbnormalDataVo> abnormalDataList = abnormalDataService.queryBatchByCondition(
                    waterStcds, Arrays.asList("TT","DD","ZZ","RR"), startTime, currentTime);

            log.debug("批量查询到 {} 条水位异常记录", abnormalDataList.size());

            // 按测站分组
            Map<String, List<com.tzslsjy.business.vo.SjyAbnormalDataVo>> stationAbnormalMap = abnormalDataList.stream()
                    .collect(Collectors.groupingBy(com.tzslsjy.business.vo.SjyAbnormalDataVo::getStcd));

            // 检查每个测站的水位异常
            for (String stcd : waterStcds) {
                List<com.tzslsjy.business.vo.SjyAbnormalDataVo> stationAbnormals = stationAbnormalMap.get(stcd);
                
                if (!CollectionUtils.isEmpty(stationAbnormals)) {
                    // 找出最大变化值
                    com.tzslsjy.business.vo.SjyAbnormalDataVo maxDiffRecord = stationAbnormals.stream()
                            .filter(a -> a.getDiff() != null)
                            .max(Comparator.comparing(a -> a.getDiff().abs()))
                            .orElse(null);
                    
                    // 使用新的阈值获取方法
                    BigDecimal threshold = params.getWaterLevelDetectionThreshold();

                    if (maxDiffRecord != null && maxDiffRecord.getDiff().abs().compareTo(threshold) >= 0) {
                        Map<String, Object> abnormal = new HashMap<>();
                        abnormal.put("stcd", stcd);
                        abnormal.put("stnm", stationNameMap.get(stcd)); // 添加测站名称
                        abnormal.put("type", "水位异常");
                        abnormal.put("value", maxDiffRecord.getVal());
                        abnormal.put("diff", maxDiffRecord.getDiff());
                        abnormal.put("threshold", threshold);
                        abnormal.put("time", maxDiffRecord.getTm());
                        abnormal.put("alertLevel", "1"); // 默认预警等级

                        results.add(abnormal);
                        log.warn("检测到水位异常: 测站={}({}), 变化值={}米, 阈值={}米, 检测周期={}小时",
                                stcd, stationNameMap.get(stcd), maxDiffRecord.getDiff(), threshold, duringHours);
                    }
                }
            }
        } catch (Exception e) {
            log.error("检查水位异常失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 构建异常消息
     */
    private String buildAbnormalMessage(List<Map<String, Object>> abnormalResults) {
        StringBuilder message = new StringBuilder("测站异常预警，");
        
        // 水位异常
        List<Map<String, Object>> waterAbnormals = abnormalResults.stream()
                .filter(a -> "水位异常".equals(a.get("type")))
                .collect(Collectors.toList());
        
        if (!waterAbnormals.isEmpty()) {
            message.append("发生水位异常：");
            for (int i = 0; i < waterAbnormals.size(); i++) {
                Map<String, Object> abnormal = waterAbnormals.get(i);
                message.append(abnormal.get("stcd"));
                message.append("测站超").append(abnormal.get("diff")).append("米");
                
                if (i < waterAbnormals.size() - 1) {
                    message.append("，");
                }
            }
        }
        
        return message.toString();
    }

    /**
     * 确定预警等级
     */
    private String determineAlertLevel(List<Map<String, Object>> abnormalResults) {
        // 简单实现：有异常就返回1级
        return abnormalResults.isEmpty() ? "0" : "1";
    }

    /**
     * 丰富测站信息
     */
    private List<Map<String, Object>> enrichStationInfo(List<Map<String, Object>> abnormalResults) {
        List<Map<String, Object>> enrichedResults = new ArrayList<>();
        
        for (Map<String, Object> abnormal : abnormalResults) {
            String stcd = (String) abnormal.get("stcd");
            StStbprpB station = stStbprpBMapper.selectStStbprpBBySTCD(stcd);
            
            if (station != null) {
                Map<String, Object> enriched = new HashMap<>(abnormal);
                enriched.put("stnm", station.getStnm());
                enriched.put("adcd", station.getAddvcd9());
                enriched.put("adnm", station.getAddvcd9Nm());
                enriched.put("lgtd", station.getLgtd());
                enriched.put("lttd", station.getLttd());
                enriched.put("sttp", station.getSttp());
                enrichedResults.add(enriched);
            } else {
                enrichedResults.add(abnormal);
            }
        }
        
        return enrichedResults;
    }

    /**
     * 按行政区划分组异常结果
     */
    private Map<String, List<Map<String, Object>>> groupAbnormalResultsByJurisdiction(List<Map<String, Object>> enrichedResults) {
        Map<String, List<Map<String, Object>>> groups = new HashMap<>();
        
        for (Map<String, Object> result : enrichedResults) {
            String adcd = (String) result.get("adcd");
            if (adcd != null) {
                // 6位行政区划
                String adcd6 = adcd.substring(0, 6);
                groups.computeIfAbsent(adcd6, k -> new ArrayList<>()).add(result);
                
                // 9位行政区划
                if (adcd.length() >= 9) {
                    String adcd9 = adcd.substring(0, 9);
                    groups.computeIfAbsent(adcd9, k -> new ArrayList<>()).add(result);
                }
            }
        }
        
        return groups;
    }

    /**
     * 添加标准字段
     */
    private void addStandardFields(String ruleTypeCode, Map<String, Object> result, Map<String, Object> queryParams) {
        result.put("ruleTypeCode", ruleTypeCode);
        result.put("argId", queryParams.get("argId"));
        result.put("ruleId", queryParams.get("ruleId"));
        result.put("queryTime", new Date());
    }
}
