package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertRuleNodeRelation;

/**
 * 规则接收节点关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISjyAlertRuleNodeRelationService
{
    /**
     * 查询规则接收节点关联
     *
     * @param relationId 规则接收节点关联主键
     * @return 规则接收节点关联
     */
    public SjyAlertRuleNodeRelation selectSjyAlertRuleNodeRelationByRelationId(Long relationId);

    /**
     * 查询规则接收节点关联列表
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 规则接收节点关联集合
     */
    public List<SjyAlertRuleNodeRelation> selectSjyAlertRuleNodeRelationList(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation);

    /**
     * 新增规则接收节点关联
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 结果
     */
    public int insertSjyAlertRuleNodeRelation(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation);

    /**
     * 修改规则接收节点关联
     *
     * @param sjyAlertRuleNodeRelation 规则接收节点关联
     * @return 结果
     */
    public int updateSjyAlertRuleNodeRelation(SjyAlertRuleNodeRelation sjyAlertRuleNodeRelation);

    /**
     * 批量删除规则接收节点关联
     *
     * @param relationIds 需要删除的规则接收节点关联主键集合
     * @return 结果
     */
    public int deleteSjyAlertRuleNodeRelationByRelationIds(Long[] relationIds);

    /**
     * 删除规则接收节点关联信息
     *
     * @param relationId 规则接收节点关联主键
     * @return 结果
     */
    public int deleteSjyAlertRuleNodeRelationByRelationId(Long relationId);
}
