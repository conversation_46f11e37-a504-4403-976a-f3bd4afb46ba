package com.tzslsjy.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.domain.SjyStationAbnormalConfig;
import com.tzslsjy.business.mapper.SjyStationAbnormalConfigMapper;
import com.tzslsjy.business.service.ISjyAlertRuleArgService;
import com.tzslsjy.business.service.ISjyStationAbnormalConfigService;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 测站异常预警配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
@DataSource(DataSourceType.INFO)
@Slf4j
public class SjyStationAbnormalConfigServiceImpl extends ServiceImpl<SjyStationAbnormalConfigMapper, SjyStationAbnormalConfig>
        implements ISjyStationAbnormalConfigService {

    @Autowired
    private ISjyAlertRuleArgService alertRuleArgService;

    private final ObjectMapper objectMapper = new ObjectMapper();

    // 默认配置值
    private static final Integer DEFAULT_TIMEOUT_MINUTES = 60;
    private static final BigDecimal DEFAULT_WATER_LEVEL_THRESHOLD = new BigDecimal("1.0");
    private static final BigDecimal DEFAULT_RAINFALL_THRESHOLD = new BigDecimal("15.0");

    @Override
    public SjyStationAbnormalConfig getConfigByStcdAndType(String stcd, Integer configType) {
        return this.baseMapper.selectByStcdAndType(stcd, configType);
    }

    @Override
    public List<SjyStationAbnormalConfig> getConfigsByType(Integer configType) {
        return this.baseMapper.selectByConfigType(configType);
    }

    @Override
    public List<SjyStationAbnormalConfig> getEnabledConfigsByType(Integer configType) {
        return this.baseMapper.selectEnabledByConfigType(configType);
    }

    @Override
    public Integer getDataTimeoutThreshold(String stcd) {
        Map<String, Object> config = getStationAbnormalConfig(stcd);
        if (config != null && config.containsKey("timeoutMinutes")) {
            return Integer.valueOf(config.get("timeoutMinutes").toString());
        }
        return DEFAULT_TIMEOUT_MINUTES;
    }

    @Override
    public BigDecimal getWaterLevelThreshold(String stcd) {
        Map<String, Object> config = getStationAbnormalConfig(stcd);
        if (config != null && config.containsKey("overWaterVal")) {
            return new BigDecimal(config.get("overWaterVal").toString());
        }
        return DEFAULT_WATER_LEVEL_THRESHOLD;
    }

    @Override
    public BigDecimal getRainfallThreshold(String stcd) {
        Map<String, Object> config = getStationAbnormalConfig(stcd);
        if (config != null && config.containsKey("overRainVal")) {
            return new BigDecimal(config.get("overRainVal").toString());
        }
        return DEFAULT_RAINFALL_THRESHOLD;
    }

    @Override
    public Boolean isWaterLevelDetectionEnabled(String stcd) {
        Map<String, Object> config = getStationAbnormalConfig(stcd);
        if (config != null && config.containsKey("overWaterEnable")) {
            return "1".equals(config.get("overWaterEnable").toString());
        }
        return true; // 默认启用
    }

    @Override
    public Boolean isRainfallDetectionEnabled(String stcd) {
        Map<String, Object> config = getStationAbnormalConfig(stcd);
        if (config != null && config.containsKey("overRainEnable")) {
            return "1".equals(config.get("overRainEnable").toString());
        }
        return true; // 默认启用
    }

    @Override
    public Boolean isGlobalSwitchEnabled(Integer configType) {
        // 从规则参数中获取全局开关状态
        Map<String, Object> config = getStationAbnormalConfig(null);
        if (config != null) {
            if (configType == 2 && config.containsKey("overWaterEnable")) {
                return "1".equals(config.get("overWaterEnable").toString());
            }
            if (configType == 3 && config.containsKey("overRainEnable")) {
                return "1".equals(config.get("overRainEnable").toString());
            }
        }
        return true; // 默认启用
    }

    @Override
    @Transactional
    public Boolean batchUpdateEnabled(List<String> stcds, Integer configType, Boolean enabled) {
        try {
            int enabledValue = enabled ? 1 : 0;
            int updatedCount = this.baseMapper.batchUpdateEnabled(stcds, configType, enabledValue);
            log.info("批量更新测站异常预警配置，配置类型: {}, 测站数量: {}, 更新数量: {}", 
                    configType, stcds.size(), updatedCount);
            return updatedCount > 0;
        } catch (Exception e) {
            log.error("批量更新测站异常预警配置失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean updateGlobalSwitch(Integer configType, Boolean enabled) {
        try {
            int enabledValue = enabled ? 1 : 0;
            int updatedCount = this.baseMapper.updateGlobalSwitch(configType, enabledValue);
            log.info("更新全局开关，配置类型: {}, 启用状态: {}, 更新数量: {}", 
                    configType, enabled, updatedCount);
            return updatedCount > 0;
        } catch (Exception e) {
            log.error("更新全局开关失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean toggleAllDetection(Boolean enabled) {
        try {
            // 更新水位异常检测全局开关
            updateGlobalSwitch(2, enabled);
            // 更新雨量异常检测全局开关
            updateGlobalSwitch(3, enabled);
            
            log.info("一键切换所有检测开关，启用状态: {}", enabled);
            return true;
        } catch (Exception e) {
            log.error("一键切换所有检测开关失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean saveOrUpdateConfig(SjyStationAbnormalConfig config) {
        try {
            SjyStationAbnormalConfig existingConfig = getConfigByStcdAndType(config.getStcd(), config.getConfigType());
            
            if (existingConfig != null) {
                // 更新现有配置
                config.setId(existingConfig.getId());
                config.setUpdateTime(new Date());
                return this.updateById(config);
            } else {
                // 创建新配置
                config.setCreateTime(new Date());
                return this.save(config);
            }
        } catch (Exception e) {
            log.error("保存或更新测站异常预警配置失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public Boolean initDefaultConfigs(List<String> stcds) {
        try {
            for (String stcd : stcds) {
                // 初始化数据接收超时配置
                initConfigIfNotExists(stcd, 1, new BigDecimal(DEFAULT_TIMEOUT_MINUTES), "分钟");
                
                // 初始化水位异常变化配置
                initConfigIfNotExists(stcd, 2, DEFAULT_WATER_LEVEL_THRESHOLD, "米");
                
                // 初始化雨量异常配置
                initConfigIfNotExists(stcd, 3, DEFAULT_RAINFALL_THRESHOLD, "毫米");
            }
            
            log.info("初始化默认配置完成，测站数量: {}", stcds.size());
            return true;
        } catch (Exception e) {
            log.error("初始化默认配置失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 如果配置不存在则初始化
     */
    private void initConfigIfNotExists(String stcd, Integer configType, BigDecimal threshold, String unit) {
        SjyStationAbnormalConfig existingConfig = getConfigByStcdAndType(stcd, configType);
        if (existingConfig == null) {
            SjyStationAbnormalConfig config = new SjyStationAbnormalConfig();
            config.setStcd(stcd);
            config.setConfigType(configType);
            config.setThreshold(threshold);
            config.setUnit(unit);
            config.setEnabled(1); // 默认启用
            config.setGlobalSwitch(1); // 默认全局开关启用
            config.setCreateTime(new Date());
            this.save(config);
        }
    }

    /**
     * 从SjyAlertRuleArg的arg_json字段获取测站异常配置
     * @param stcd 测站编号，如果为null则获取全局配置
     * @return 配置Map
     */
    private Map<String, Object> getStationAbnormalConfig(String stcd) {
        try {
            // 查找测站异常预警规则类型的参数配置
            SjyAlertRuleArg queryArg = new SjyAlertRuleArg();
            queryArg.setArgType("stationAbnormal");
            List<SjyAlertRuleArg> ruleArgs = alertRuleArgService.selectSjyAlertRuleArgList(queryArg);

            if (ruleArgs.isEmpty()) {
                log.warn("未找到测站异常预警规则参数配置");
                return null;
            }

            // 解析第一个匹配的配置
            SjyAlertRuleArg ruleArg = ruleArgs.get(0);
            if (!StringUtils.hasText(ruleArg.getArgJson())) {
                log.warn("测站异常预警规则参数配置为空");
                return null;
            }

            Map<String, Object> config = objectMapper.readValue(ruleArg.getArgJson(),
                    new TypeReference<Map<String, Object>>() {});

            // 如果指定了测站编号，检查该测站是否在配置的测站列表中
            if (stcd != null && config.containsKey("stcds")) {
                @SuppressWarnings("unchecked")
                List<String> configuredStcds = (List<String>) config.get("stcds");
                if (configuredStcds != null && !configuredStcds.contains(stcd)) {
                    log.debug("测站 {} 不在配置的测站列表中", stcd);
                    return null;
                }
            }

            return config;

        } catch (Exception e) {
            log.error("获取测站异常配置失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
