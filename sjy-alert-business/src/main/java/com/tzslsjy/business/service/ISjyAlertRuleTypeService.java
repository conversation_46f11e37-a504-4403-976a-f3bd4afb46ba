package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertRuleType;

import javax.validation.Valid;
import java.util.List;

/**
 * 规则类型服务接口
 */
public interface ISjyAlertRuleTypeService {
    /**
     * 根据ID查询规则类型
     */
    SjyAlertRuleType selectSjyAlertRuleTypeById(Long ruleTypeId);

    /**
     * 根据类型编码查询规则类型
     */
    SjyAlertRuleType selectSjyAlertRuleTypeByTypeCode(String typeCode);

    /**
     * 查询规则类型列表
     */
    List<SjyAlertRuleType> selectSjyAlertRuleTypeList(SjyAlertRuleType ruleType);

    SjyAlertRuleType selectSjyAlertRuleTypeByRuleTypeId(Long ruleTypeId);

    int insertSjyAlertRuleType(@Valid SjyAlertRuleType sjyAlertRuleType);

    int updateSjyAlertRuleType(@Valid SjyAlertRuleType sjyAlertRuleType);

    int deleteSjyAlertRuleTypeByRuleTypeIds(Long[] ruleTypeIds);
    int deleteSjyAlertRuleTypeByRuleTypeId(Long ruleTypeId);

    /**
     * 查询规则类型树形列表
     *
     * @param ruleType 规则类型
     * @return 规则类型树形列表
     */
    List<SjyAlertRuleType> selectSjyAlertRuleTypeTree(SjyAlertRuleType ruleType);
}
