package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.service.AlertRuleStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
public class WaterLevelAlertStrategy implements AlertRuleStrategy {
    private static final Logger logger = LoggerFactory.getLogger(WaterLevelAlertStrategy.class);

    @Override
    public boolean evaluate(AlertContext context) {
        logger.debug("评估水位预警规则");

        try {
            Map<String, Object> data = context.getData();
            List<SjyAlertRuleArg> ruleParams = context.getRuleParams();

            // 检查必要参数
            if (data == null || !data.containsKey("waterLevel")) {
                logger.warn("缺少水位数据，无法评估水位预警规则");
                return false;
            }

            // 获取水位值
            Double waterLevel = Double.valueOf(data.get("waterLevel").toString());
            
            // 获取阈值（默认100.0）
            Double threshold = 100.0;
            
            // 从规则参数中查找阈值
            if (ruleParams != null) {
                for (SjyAlertRuleArg param : ruleParams) {
                    if ("waterLevelThreshold".equals(param.getArgType())) {
                        // 假设阈值存储在argJson字段中
                        String argJson = param.getArgJson();
                        try {
                            threshold = Double.valueOf(argJson);
                            break;
                        } catch (NumberFormatException e) {
                            logger.warn("无法解析水位阈值参数: {}", argJson);
                        }
                    }
                }
            }
            
            // 水位预警逻辑
            boolean triggered = waterLevel > threshold;

            logger.info("水位预警规则评估结果: {}, 当前水位: {}, 阈值: {}",
                    triggered ? "触发" : "未触发", waterLevel, threshold);

            return triggered;
        } catch (Exception e) {
            logger.error("水位预警规则评估失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getType() {
        return "water_level";
    }
}
