package com.tzslsjy.business.service.impl.notifier;

import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.mapper.SjyAlertPersonMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 浙政钉预警通知实现
 */
@Component
public class ZheZhengdingAlertNotifier extends AbstractAlertNotifier {



    @Autowired
    private SjyAlertPersonMapper personMapper;

    @Override
    public String getNotifierType() {
        return "zhezhengding";
    }

    @Override
    public String getNotifierName() {
        return "浙政钉通知";
    }

    @Override
    protected boolean doSendAlert(SjyAlertSmsRecord record) {
        try {
            // 获取接收人信息
            String personId = record.getPersonId();
            if (personId == null) {
                logger.warn("预警记录未指定接收人ID");
                return false;
            }

            // 查询接收人信息


            // 准备消息内容


            // 调用浙政钉服务



            return true;
        } catch (Exception e) {
            logger.error("发送浙政钉消息失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 准备浙政钉消息数据
     */
    private Map<String, Object> prepareMessageData(SjyAlertSmsRecord record, SjyAlertPerson person) {
       return new HashMap<>();
    }

    /**
     * 生成预警标题
     */
    private String getAlertTitle(SjyAlertSmsRecord record) {
        String levelText = "";
        if (record.getAlertLevel() != null) {
            switch (record.getAlertLevel().intValue()) {
                case 3:
                    levelText = "【紧急】红色预警";
                    break;
                case 2:
                    levelText = "【重要】橙色预警";
                    break;
                default:
                    levelText = "【预警】黄色预警";
                    break;
            }
        }

        // 提取简短标题，最多30个字符
        String content = record.getContent();
        String shortContent = content.length() > 30 ?
            content.substring(0, 27) + "..." : content;

        return levelText + ": " + shortContent;
    }

    @Override
    public boolean supports(SjyAlertSmsRecord record) {
        if (!super.supports(record)) {
            return false;
        }

        // 浙政钉支持所有规则类型
        return true;
    }
}
