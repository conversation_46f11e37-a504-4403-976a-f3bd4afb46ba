package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.StRiverR;

/**
 * 河道水情Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IStRiverRService 
{
    /**
     * 查询河道水情
     * 
     * @param STCD 河道水情主键
     * @return 河道水情
     */
    public StRiverR selectStRiverRBySTCD(String STCD);

    /**
     * 查询河道水情列表
     * 
     * @param stRiverR 河道水情
     * @return 河道水情集合
     */
    public List<StRiverR> selectStRiverRList(StRiverR stRiverR);

    /**
     * 新增河道水情
     * 
     * @param stRiverR 河道水情
     * @return 结果
     */
    public int insertStRiverR(StRiverR stRiverR);

    /**
     * 修改河道水情
     * 
     * @param stRiverR 河道水情
     * @return 结果
     */
    public int updateStRiverR(StRiverR stRiverR);

    /**
     * 批量删除河道水情
     * 
     * @param STCDs 需要删除的河道水情主键集合
     * @return 结果
     */
    public int deleteStRiverRBySTCDs(String[] STCDs);

    /**
     * 删除河道水情信息
     * 
     * @param STCD 河道水情主键
     * @return 结果
     */
    public int deleteStRiverRBySTCD(String STCD);
}
