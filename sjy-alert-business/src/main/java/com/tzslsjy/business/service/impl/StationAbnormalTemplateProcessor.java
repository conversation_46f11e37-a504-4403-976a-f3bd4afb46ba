package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 测站异常预警模板处理器
 *
 * 注意：此类已被拆分为独立的水位异常和雨量异常模板处理器，推荐使用：
 * - {@link WaterLevelAbnormalTemplateProcessor} 水位异常预警模板处理器
 * - {@link RainfallAbnormalTemplateProcessor} 雨量异常预警模板处理器
 *
 * 此类保留以维持向后兼容性。
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class StationAbnormalTemplateProcessor implements AlertTemplateProcessor {

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate template, Map<String, Object> data) {
        try {
            log.debug("使用已弃用的StationAbnormalTemplateProcessor，推荐使用独立的水位异常和雨量异常模板处理器");

            // 测站异常预警特有的数据处理
            enrichStationAbnormalSpecificData(template, data);

            // 使用默认处理器进行模板处理
            return defaultTemplateProcessor.processTemplate(context, template, data);
        } catch (Exception e) {
            log.error("处理测站异常预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeAlerts(context, alert, receivers);
    }

    /**
     * 测站异常预警特有的数据处理
     */
    private void enrichStationAbnormalSpecificData(SjyAlertSmsTemplate template, Map<String, Object> data) {
        data.put("alertTypeText", "测站异常预警");

        // 处理合并的异常结果
        @SuppressWarnings("unchecked")
        Map<String, List<Map<String, Object>>> groupedResults =
                (Map<String, List<Map<String, Object>>>) data.get("groupedAbnormalResults");

        log.debug("groupedResults: {}", groupedResults);

        if (groupedResults != null && !groupedResults.isEmpty()) {
            StringBuilder combinedMessage = new StringBuilder();

            // 处理超时异常
            List<Map<String, Object>> timeoutResults = groupedResults.get("timeout");
            if (timeoutResults != null && !timeoutResults.isEmpty()) {
                combinedMessage.append("发生测站数据超时");

                // 获取超时时长（假设所有超时测站的超时时长相同，取第一个）
                Object timeoutMinutes = timeoutResults.get(0).get("timeoutMinutes");
                if (timeoutMinutes != null) {
                    combinedMessage.append(timeoutMinutes).append("分钟");
                }

                combinedMessage.append("：");
                for (int i = 0; i < timeoutResults.size(); i++) {
                    String stcd = (String) timeoutResults.get(i).get("stcd");
                    combinedMessage.append(stcd).append("测站");
                    if (i < timeoutResults.size() - 1) {
                        combinedMessage.append("、");
                    }
                }
            }

            // 处理水位异常
            List<Map<String, Object>> waterLevelResults = groupedResults.get("water_level");
            if (waterLevelResults != null && !waterLevelResults.isEmpty()) {
                if (combinedMessage.length() > 0) { // 如果前面已有内容
                    combinedMessage.append("，");
                }
                combinedMessage.append("发生水位异常：");
                for (int i = 0; i < waterLevelResults.size(); i++) {
                    Map<String, Object> result = waterLevelResults.get(i);
                    String stcd = (String) result.get("stcd");
                    Object difference = result.get("difference");
                    combinedMessage.append(stcd).append("测站超").append(difference).append("m");
                    if (i < waterLevelResults.size() - 1) {
                        combinedMessage.append("、");
                    }
                }
            }

            // 处理雨量异常
            List<Map<String, Object>> rainfallResults = groupedResults.get("rainfall");
            if (rainfallResults != null && !rainfallResults.isEmpty()) {
                if (combinedMessage.length() > 0) { // 如果前面已有内容
                    combinedMessage.append("，");
                }
                combinedMessage.append("发生雨量异常：");
                for (int i = 0; i < rainfallResults.size(); i++) {
                    Map<String, Object> result = rainfallResults.get(i);
                    String stcd = (String) result.get("stcd");
                    Object currentValue = result.get("currentValue");
                    combinedMessage.append(stcd).append("测站超").append(currentValue).append("mm");
                    if (i < rainfallResults.size() - 1) {
                        combinedMessage.append("、");
                    }
                }
            }

            String finalMessage = combinedMessage.toString();
            data.put("content", finalMessage);
            data.put("message", finalMessage);

            // 设置模板需要的字段
            data.put("abnormalTypeDisplay", "");

            log.debug("构建的合并消息: {}", finalMessage);
        } else {
            log.warn("groupedResults为空或null，无法构建消息");
            data.put("message", "检测到问题");
            data.put("abnormalTypeDisplay", "");
        }

        // 确保必要字段存在
        if (!data.containsKey("message") || data.get("message") == null) {
            data.put("message", "测站异常检测到问题，请及时处理。");
        }

        // 添加时间信息
        data.put("detectionTime", new java.util.Date());

        log.debug("测站异常预警数据处理完成，总异常数量: {}", data.get("totalAbnormalCount"));
    }
}
