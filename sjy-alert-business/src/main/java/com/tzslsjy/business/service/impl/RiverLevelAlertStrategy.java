package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.service.AlertRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 河道水位预警规则策略
 */
@Slf4j
@Component
public class RiverLevelAlertStrategy implements AlertRuleStrategy {

    @Override
    public boolean evaluate(AlertContext context) {
        log.debug("开始评估河道水位预警规则");

        try {
            Map<String, Object> data = context.getData();
            if (data == null || !data.containsKey("argResults") || !(data.get("argResults") instanceof List)) {
                log.warn("河道水位预警数据格式不正确或缺少argResults");
                return false;
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");

            if (CollectionUtils.isEmpty(argResults)) {
                log.warn("参数组结果为空，无法评估河道水位预警");
                return false;
            }

            for (Map<String, Object> argResult : argResults) {
                if (argResult.containsKey("triggeredRivers")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> triggeredRivers = 
                            (List<Map<String, Object>>) argResult.get("triggeredRivers");
                    if (!CollectionUtils.isEmpty(triggeredRivers)) {
                        log.info("河道水位预警触发，参数组ID: {}", argResult.get("argId"));
                        return true;
                    }
                }
            }

            log.debug("河道水位预警未触发");
            return false;

        } catch (Exception e) {
            log.error("评估河道水位预警规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getType() {
        return "15"; // 对应河道水位预警类型
    }
} 