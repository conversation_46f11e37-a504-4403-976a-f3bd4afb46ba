package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.service.AlertRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 8时水文预警规则策略
 */
@Slf4j
@Component
public class HydrologyReportStrategy implements AlertRuleStrategy {

    @Override
    public boolean evaluate(AlertContext context) {
        log.debug("开始评估8时水文预警规则");

        try {
            Map<String, Object> data = context.getData();
            if (data == null) {
                log.warn("8时水文预警数据为空");
                return false;
            }
            data = ( Map<String, Object>) ((List)data.get("argResults")).get(0);
            // 检查是否成功生成了报告内容
            if (data.containsKey("reportGenerated") && Boolean.TRUE.equals(data.get("reportGenerated"))) {
                log.info("8时水文预警报告生成成功，触发发送");
                return true;
            }

            // 即使没有明确的成功标记，只要有content内容就触发
            if (data.containsKey("content") && data.get("content") != null) {
                String content = data.get("content").toString();
                if (!content.trim().isEmpty() && !content.contains("异常") && !content.contains("有误")) {
                    log.info("8时水文预警报告内容正常，触发发送");
                    return true;
                }
            }

            log.debug("8时水文预警报告未成功生成或内容异常，不触发发送");
            return false;

        } catch (Exception e) {
            log.error("评估8时水文预警规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getType() {
        return "13"; // 对应8时水文预警类型
    }
} 