package com.tzslsjy.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.tzslsjy.business.bo.SjyAbnormalDataAddBo;
import com.tzslsjy.business.bo.SjyAbnormalDataEditBo;
import com.tzslsjy.business.bo.SjyAbnormalDataQueryBo;
import com.tzslsjy.business.domain.SjyAbnormalData;
import com.tzslsjy.business.mapper.SjyAbnormalDataMapper;
import com.tzslsjy.business.service.ISjyAbnormalDataService;
import com.tzslsjy.business.vo.SjyAbnormalDataVo;
import com.github.pagehelper.Page;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 测站异常数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@Service
@RequiredArgsConstructor
@DataSource(DataSourceType.INFO)
public class SjyAbnormalDataServiceImpl implements ISjyAbnormalDataService {

    private final SjyAbnormalDataMapper sjyAbnormalDataMapper;

    @Override
    public SjyAbnormalDataVo queryByKey(String stcd, String tm, String type) {
        Date tmDate = null;
        if (StrUtil.isNotBlank(tm)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                tmDate = sdf.parse(tm);
            } catch (ParseException e) {
                // 时间格式错误，返回null
                return null;
            }
        }
        SjyAbnormalData db = sjyAbnormalDataMapper.selectByKey(stcd, tmDate, type);
        return BeanUtil.toBean(db, SjyAbnormalDataVo.class);
    }

    @Override
    public List<SjyAbnormalDataVo> queryList(SjyAbnormalDataQueryBo bo) {
        List<SjyAbnormalData> list = sjyAbnormalDataMapper.selectByCondition(bo);
        return entity2Vo(list);
    }

    @Override
    public List<SjyAbnormalDataVo> queryBatchByCondition(List<String> stcds, List<String> types, Date startTime, Date endTime) {
        List<SjyAbnormalData> list = sjyAbnormalDataMapper.selectBatchByCondition(stcds, types, startTime, endTime);
        return entity2Vo(list);
    }

    /**
     * 实体类转化成视图对象
     *
     * @param collection 实体类集合
     * @return
     */
    private List<SjyAbnormalDataVo> entity2Vo(Collection<SjyAbnormalData> collection) {
        List<SjyAbnormalDataVo> voList = collection.stream()
                .map(any -> BeanUtil.toBean(any, SjyAbnormalDataVo.class))
                .collect(Collectors.toList());
        if (collection instanceof Page) {
            Page<SjyAbnormalData> page = (Page<SjyAbnormalData>) collection;
            Page<SjyAbnormalDataVo> pageVo = new Page<>();
            BeanUtil.copyProperties(page, pageVo);
            pageVo.addAll(voList);
            voList = pageVo;
        }
        return voList;
    }

    @Override
    public Boolean insertByAddBo(SjyAbnormalDataAddBo bo) {
        SjyAbnormalData add = BeanUtil.toBean(bo, SjyAbnormalData.class);
        validEntityBeforeSave(add);
        if (add.getCreateTime() == null) {
            add.setCreateTime(new Date());
        }
        return sjyAbnormalDataMapper.insert(add) > 0;
    }

    @Override
    public Boolean updateByEditBo(SjyAbnormalDataEditBo bo) {
        SjyAbnormalData update = BeanUtil.toBean(bo, SjyAbnormalData.class);
        validEntityBeforeSave(update);

        // 根据联合条件更新
        return sjyAbnormalDataMapper.updateByKey(update, update.getStcd(), update.getTm(), update.getType()) > 0;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(SjyAbnormalData entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByStcds(Collection<String> stcds, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return sjyAbnormalDataMapper.deleteByStcds(stcds) > 0;
    }

    @Override
    public Boolean deleteByCondition(String stcd, String type, String startTime, String endTime) {
        Date startDate = null;
        Date endDate = null;

        if (StrUtil.isNotBlank(startTime)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                startDate = sdf.parse(startTime);
            } catch (ParseException e) {
                // 时间格式错误，忽略开始时间条件
            }
        }

        if (StrUtil.isNotBlank(endTime)) {
            try {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                endDate = sdf.parse(endTime);
            } catch (ParseException e) {
                // 时间格式错误，忽略结束时间条件
            }
        }

        return sjyAbnormalDataMapper.deleteByCondition(stcd, type, startDate, endDate) > 0;
    }
}
