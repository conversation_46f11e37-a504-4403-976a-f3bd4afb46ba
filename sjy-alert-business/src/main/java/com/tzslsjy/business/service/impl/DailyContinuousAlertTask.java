package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertStSituation;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.service.ContinuousAlertService;
import com.tzslsjy.business.service.ISjyAlertRuleService;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 每日8点持续预警汇总任务
 */
@Component
@Slf4j
public class DailyContinuousAlertTask {

    @Autowired
    private ContinuousAlertService continuousAlertService;
    
    @Autowired
    private ISjyAlertRuleService alertRuleService;
    
    @Autowired
    private AlertStrategyFactory strategyFactory;

    /**
     * 每日8点执行持续预警汇总
     * Cron表达式：0 0 8 * * ? 表示每天上午8点执行
     */
    @Scheduled(cron = "0 0 8 * * ?")
    public void executeDaily8AMContinuousAlert() {
        log.info("开始执行每日8点持续预警汇总任务");
        
        try {
            // 处理水库水位持续预警
            processReservoirContinuousAlerts();
            
            // 处理河道水位持续预警  
            processRiverContinuousAlerts();
            
            log.info("每日8点持续预警汇总任务执行完成");
            
        } catch (Exception e) {
            log.error("执行每日8点持续预警汇总任务时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理水库水位持续预警汇总
     */
    private void processReservoirContinuousAlerts() {
        log.info("开始处理水库水位持续预警汇总");
        
        // 查询所有启用的水库水位预警规则（类型25）
        List<SjyAlertRule> reservoirRules = getActiveRulesByType("25");
        
        for (SjyAlertRule rule : reservoirRules) {
            try {
                List<SjyAlertStSituation> activeStations = continuousAlertService
                        .generateDailyContinuousAlertSummary(rule.getRuleId(), "reservoir");
                
                if (!CollectionUtils.isEmpty(activeStations)) {
                    log.info("水库预警规则 {} 有 {} 个测站仍在预警状态", rule.getRuleName(), activeStations.size());
                    
                    // 发送持续预警汇总消息
                    sendContinuousAlertSummary(rule, activeStations, "reservoir");
                }
                
            } catch (Exception e) {
                log.error("处理水库预警规则 {} 的持续预警汇总时发生错误: {}", rule.getRuleName(), e.getMessage(), e);
            }
        }
    }

    /**
     * 处理河道水位持续预警汇总
     */
    private void processRiverContinuousAlerts() {
        log.info("开始处理河道水位持续预警汇总");
        
        // 查询所有启用的河道水位预警规则（类型15）
        List<SjyAlertRule> riverRules = getActiveRulesByType("15");
        
        for (SjyAlertRule rule : riverRules) {
            try {
                List<SjyAlertStSituation> activeStations = continuousAlertService
                        .generateDailyContinuousAlertSummary(rule.getRuleId(), "river");
                
                if (!CollectionUtils.isEmpty(activeStations)) {
                    log.info("河道预警规则 {} 有 {} 个测站仍在预警状态", rule.getRuleName(), activeStations.size());
                    
                    // 发送持续预警汇总消息
                    sendContinuousAlertSummary(rule, activeStations, "river");
                }
                
            } catch (Exception e) {
                log.error("处理河道预警规则 {} 的持续预警汇总时发生错误: {}", rule.getRuleName(), e.getMessage(), e);
            }
        }
    }

    /**
     * 根据预警类型获取启用的规则
     */
    private List<SjyAlertRule> getActiveRulesByType(String ruleTypeId) {
        SjyAlertRule queryCondition = new SjyAlertRule();
        queryCondition.setRuleTypeId(ruleTypeId);
        queryCondition.setStatus(1); // 启用状态
        
        List<SjyAlertRule> allRules = alertRuleService.selectSjyAlertRuleList(queryCondition);
        
        return allRules.stream()
                .filter(rule -> rule.getStatus() != null && rule.getStatus() == 1)
                .collect(Collectors.toList());
    }

    /**
     * 发送持续预警汇总消息
     */
    private void sendContinuousAlertSummary(SjyAlertRule rule, List<SjyAlertStSituation> activeStations, String alertType) {
        try {
            log.info("发送持续预警汇总消息，规则: {}, 预警类型: {}, 测站数: {}", 
                    rule.getRuleName(), alertType, activeStations.size());
            
            // 构建持续预警汇总的上下文
            AlertContext context = buildContinuousAlertContext(rule, activeStations, alertType);
            
            // 获取对应的处理器并处理
            String processorType = "reservoir".equals(alertType) ? "25" : "15";
            AlertProcessor processor = getProcessorByType(processorType);
            
            if (processor != null) {
                processor.process(context);
                log.info("持续预警汇总消息发送完成，规则: {}", rule.getRuleName());
            } else {
                log.warn("未找到类型 {} 的预警处理器", processorType);
            }
            
        } catch (Exception e) {
            log.error("发送持续预警汇总消息时发生错误，规则: {}, 错误: {}", rule.getRuleName(), e.getMessage(), e);
        }
    }

    /**
     * 构建持续预警汇总的上下文
     */
    private AlertContext buildContinuousAlertContext(SjyAlertRule rule, List<SjyAlertStSituation> activeStations, String alertType) {
        AlertContext context = new AlertContext();
        context.setRule(rule);
        
        Map<String, Object> data = new HashMap<>();
        data.put("isDailySummary", true);
        data.put("alertType", alertType);
        data.put("activeStations", activeStations);
        
        // 按状态分组
        Map<Integer, List<SjyAlertStSituation>> groupedByStatus = activeStations.stream()
                .collect(Collectors.groupingBy(SjyAlertStSituation::getStatus));
        
        data.put("warningStations", groupedByStatus.getOrDefault(1, new ArrayList<>())); // 超警戒
        data.put("guaranteeStations", groupedByStatus.getOrDefault(2, new ArrayList<>())); // 超保证
        
        context.setData(data);
        
        return context;
    }

    /**
     * 根据类型获取处理器（简化版，实际应该通过Spring容器管理）
     */
    private AlertProcessor getProcessorByType(String processorType) {
        // 这里应该通过Spring的ApplicationContext获取对应的处理器Bean
        // 暂时返回null，需要在实际集成时完善
        log.warn("需要实现处理器获取逻辑，类型: {}", processorType);
        return null;
    }
}