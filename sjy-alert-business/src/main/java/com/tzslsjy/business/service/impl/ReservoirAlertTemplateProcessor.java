package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * 水库水位预警模板处理器
 */
@Component
@Slf4j
public class ReservoirAlertTemplateProcessor implements AlertTemplateProcessor {

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate smsTemplate, Map<String, Object> data) {
        log.debug("处理水库水位预警模板，模板ID: {}, 数据: {}", smsTemplate.getTemplateId(), data);
        try {
            // data中应包含 triggeredReservoirs 列表
            // DefaultAlertTemplateProcessor 会使用 ListFormat "{addvcdNm}{stnm}超汛限{overWrz}米" 和 ListSeparator "，" 来格式化列表。
            // 模板内容: "{tm}，{list}，请严格按照本年度水库山塘控运计划执行..."

            // 从 triggeredReservoirs 中获取第一个记录的时间作为全局 {tm}
            if (data.containsKey("triggeredReservoirs")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> triggeredReservoirs = (List<Map<String, Object>>) data.get("triggeredReservoirs");
                if (!CollectionUtils.isEmpty(triggeredReservoirs)) {
                    Map<String, Object> firstReservoir = triggeredReservoirs.get(0);
                    if (firstReservoir.containsKey("tm")) {
                        data.put("tm", firstReservoir.get("tm")); // 将列表中的时间提升为全局时间
                    }
                }
            }
            return defaultTemplateProcessor.processTemplate(context, smsTemplate, data);
        } catch (Exception e) {
            log.error("处理水库水位预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> people) {
        return defaultTemplateProcessor.mergeAlerts(context, alert, people);
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }
} 