package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUtil;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.StPptnRMapper;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.service.AlertDataProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 重构后的雨量预警数据提供者
 * 职责单一：只负责根据解析后的查询参数获取雨量预警数据
 */
@Component("refactoredRainfallAlertDataProvider")
@Slf4j
public class RefactoredRainfallAlertDataProvider implements AlertDataProvider {

    @Autowired
    private StPptnRMapper stPptnRMapper;

    @Autowired
    private StStbprpBMapper stStbprpBMapper;

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("2");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date tm) {
        log.debug("开始获取雨量预警数据，规则类型: {}", ruleTypeCode);

        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证必要参数
            validateRequiredParams(queryParams);

            // 获取雨量预警参数数据
            @SuppressWarnings("unchecked")
            List<SjyRainfallParamData> rainfallParams = (List<SjyRainfallParamData>) queryParams.get("rainfallParams");
            
            if (rainfallParams == null || rainfallParams.isEmpty()) {
                log.warn("未找到雨量预警参数数据");
                return result;
            }

            // 获取需要查询的测站列表
            List<StStbprpB> stations = getStationsToQuery(queryParams);
            if (stations == null || stations.isEmpty()) {
                log.warn("未找到需要查询的雨量测站");
                return result;
            }

            // 获取雨量数据
            List<Map<String, Object>> stationResults = new ArrayList<>();
            List<Map<String, Object>> triggeredRainfalls = new ArrayList<>();

            for (StStbprpB station : stations) {
                Map<String, Object> stationData = fetchStationRainfallData(station, rainfallParams, tm);

                if (!stationData.isEmpty() && stationData.containsKey("rainfallList")) {
                    stationResults.add(stationData);

                    // 收集所有触发的雨量数据，用于分组
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> rainfallList =
                            (List<Map<String, Object>>) stationData.get("rainfallList");
                    if (rainfallList != null) {
                        // 雨量数据已经包含了所有必要信息，直接添加
                        triggeredRainfalls.addAll(rainfallList);
                    }
                }
            }

            // 按行政区划和时间段分组
            Map<String, List<Map<String, Object>>> groupsByJurisdictionAndDuration =
                    groupByJurisdictionAndDuration(triggeredRainfalls);

            // 设置结果
            if (!stationResults.isEmpty()) {
                result.put("alertData", stationResults);
                result.put("stationResults", stationResults);
                result.put("adcdRainfallMap", groupsByJurisdictionAndDuration); // 使用新的分组结果
                result.put("hasTriggered", true);
                result.put("endTm", tm);
                log.info("雨量预警数据提供完成，共 {} 个测站触发预警，分为 {} 个行政区划和时间段组合",
                        stationResults.size(), groupsByJurisdictionAndDuration.size());
            }

            // 添加参数信息
            result.put("argId", queryParams.get("argId"));
            addStandardFields(ruleTypeCode, queryParams, result);

        } catch (Exception e) {
            handleDataFetchException(e, result);
        }

        return result;
    }

    /**
     * 验证必要参数
     */
    private void validateRequiredParams(Map<String, Object> queryParams) {
        if (!queryParams.containsKey("rainfallParams")) {
            throw new IllegalArgumentException("缺少雨量预警参数数据");
        }
    }

    /**
     * 获取需要查询的测站列表
     */
    private List<StStbprpB> getStationsToQuery(Map<String, Object> queryParams) {
        List<StStbprpB> stations = null;
        
        try {
            if (queryParams.containsKey("specifiedStations")) {
                // 使用规则指定的测站
                @SuppressWarnings("unchecked")
                List<String> specifiedStations = (List<String>) queryParams.get("specifiedStations");
                stations = new ArrayList<>();
                for (String stcd : specifiedStations) {
                    StStbprpB station = stStbprpBMapper.selectStStbprpBBySTCD(stcd);
                    if (station != null) {
                        stations.add(station);
                    }
                }
            } else {
                // 查询所有雨量测站 (可根据行政区划过滤)
                stations = stStbprpBMapper.selectByAdcd("331082"); // 默认临海市
            }
        } catch (Exception e) {
            log.error("获取测站列表失败: {}", e.getMessage(), e);
        }
        
        return stations;
    }

    /**
     * 获取单个测站的雨量数据 - 按时间段分组
     */
    private Map<String, Object> fetchStationRainfallData(StStbprpB station, List<SjyRainfallParamData> paramDataList, Date endTm) {
        Map<String, Object> result = new HashMap<>();
        Map<String, List<Map<String, Object>>> rainfallGroupsByDuration = new HashMap<>();
        List<Map<String, Object>> allRainfallList = new ArrayList<>();

        String stationId = station.getStcd();
        String adcd = station.getAddvcd9();
        String addvcd9Nm = station.getAddvcd9Nm();
        String stName = station.getStnm();

        try {
            // 处理各个参数项
            for (SjyRainfallParamData paramData : paramDataList) {
                if (paramData.getThresholdItems() == null || paramData.getThresholdItems().isEmpty()) {
                    continue;
                }

                for (SjyRainfallThresholdItem item : paramData.getThresholdItems()) {
                    // 查询指定时间段的降雨量
                    Integer hours = item.getTm();
                    if (hours == null || hours <= 0) continue;

                    Date startTm = DateUtil.offsetHour(endTm, -hours);
                    Double threshold = item.getVal() != null ? item.getVal() : -1;

                    // 查询此测站的累计降雨量
                    BigDecimal totalRainfall = stPptnRMapper.selectTotalRainfallByTimeRange(stationId, startTm, endTm);

                    if (totalRainfall != null && (threshold < 0 || totalRainfall.doubleValue() >= threshold)) {
                        // 记录此测站的降雨数据
                        Map<String, Object> rainfallData = createRainfallDataMap(
                            stationId, stName, adcd, addvcd9Nm, startTm, endTm, hours, totalRainfall, threshold, paramData.getArgId());

                        // 按时间段分组
                        String durationKey = hours + "h";
                        rainfallGroupsByDuration.computeIfAbsent(durationKey, k -> new ArrayList<>()).add(rainfallData);

                        // 同时添加到总列表（保持兼容性）
                        allRainfallList.add(rainfallData);

                        log.debug("测站 {} 在 {} 小时内降雨量 {} mm，超过阈值 {} mm", stationId, hours, totalRainfall, threshold);
                    }
                }

                // 添加参数级别信息
                result.put("alertLevel", paramData.getAlertLevel());
                result.put("argId", paramData.getArgId());
            }

            // 将雨量数据添加到结果中
            if (!allRainfallList.isEmpty()) {
                result.put("rainfallList", allRainfallList); // 保持兼容性，所有数据的列表
                result.put("list", allRainfallList); // 保持兼容性
                result.put("rainfallGroupsByDuration", rainfallGroupsByDuration); // 按时间段分组的数据
                result.put("endTm", endTm);
                // 添加基础测站信息
                result.put("stcd", stationId);
                result.put("stnm", stName);
                result.put("adcd", adcd);
                result.put("addvcd9Nm", addvcd9Nm);

                log.info("测站 {} 触发雨量预警，共 {} 个时间段分组，总计 {} 条记录",
                        stationId, rainfallGroupsByDuration.size(), allRainfallList.size());
            }
            
        } catch (Exception e) {
            log.error("获取测站{}的降雨数据失败: {}", stationId, e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 创建雨量数据Map
     */
    private Map<String, Object> createRainfallDataMap(String stcd, String stName, String adcd, String addvcd9Nm,
                                                      Date startTm, Date endTm, Integer hours,
                                                      BigDecimal totalRainfall, Double threshold, Long argId) {
        Map<String, Object> rainfallData = new HashMap<>();
        rainfallData.put("adcd", adcd);
        rainfallData.put("stcd", stcd);
        rainfallData.put("stnm", stName);
        rainfallData.put("addvcdNm", addvcd9Nm);
        rainfallData.put("startTm", DateUtil.format(startTm, "MM月dd日HH时"));
        rainfallData.put("endTm", DateUtil.format(endTm, "dd日HH时"));
        rainfallData.put("duration", hours);
        rainfallData.put("duringTm", hours + "小时");
        rainfallData.put("durationKey", hours + "h"); // 时间段分组键
        rainfallData.put("rainfall", totalRainfall);
        rainfallData.put("threshold", threshold);
        rainfallData.put("argId", argId); // 参数组ID
        return rainfallData;
    }

    /**
     * 按行政区划和时间段分组 - 参考RiverLevelAlertDataProvider的实现，并增加时间段分组
     * @param triggeredRainfalls 触发的雨量列表
     * @return 按行政区划和时间段分组的结果
     */
    private Map<String, List<Map<String, Object>>> groupByJurisdictionAndDuration(List<Map<String, Object>> triggeredRainfalls) {
        Map<String, List<Map<String, Object>>> groups = new HashMap<>();

        for (Map<String, Object> rainfall : triggeredRainfalls) {
            String jurisdiction = (String) rainfall.get("adcd");
            String durationKey = (String) rainfall.get("durationKey"); // 如 "3h", "6h"

            if (jurisdiction == null || jurisdiction.length() < 6 || durationKey == null) {
                continue; // 跳过无效的行政区划或时间段
            }

            // 9位行政区划 + 时间段分组
            String jurisdiction9 = jurisdiction.length() >= 9 ? jurisdiction.substring(0, 9) : jurisdiction;
            String groupKey9 = "9_" + jurisdiction9 + "_" + durationKey; // 如 "9_331082001_3h"
            groups.computeIfAbsent(groupKey9, k -> new ArrayList<>()).add(rainfall);

            // 6位行政区划 + 时间段分组
            String jurisdiction6 = jurisdiction.substring(0, 6);
            String groupKey6 = "6_" + jurisdiction6 + "_" + durationKey; // 如 "6_331082_3h"
            groups.computeIfAbsent(groupKey6, k -> new ArrayList<>()).add(rainfall);
        }

        log.info("雨量预警按行政区划和时间段分组完成，共分为 {} 组", groups.size());
        return groups;
    }

    /**
     * 添加标准字段
     */
    private void addStandardFields(String ruleTypeCode, Map<String, Object> queryParams, Map<String, Object> result) {
        result.put("dataType", "2");  // 2代表雨量数据
        result.put("ruleType", ruleTypeCode);
        result.put("queryTime", new Date());
    }

    /**
     * 处理数据获取异常
     */
    private void handleDataFetchException(Exception e, Map<String, Object> result) {
        log.error("获取雨量预警数据失败: {}", e.getMessage(), e);
        result.put("error", true);
        result.put("errorMessage", e.getMessage());
        throw new RuntimeException("获取雨量预警数据失败", e);
    }
} 