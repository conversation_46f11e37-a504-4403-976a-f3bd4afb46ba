package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import com.tzslsjy.business.service.ContinuousAlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 城市内涝水位预警处理器
 * 特点：
 * 1. 每个监测站点独立处理
 * 2. 无阈值概念 - 当水位超过汛限值时立即触发预警
 * 3. 使用特定的模板格式进行消息格式化
 */
@Component
@Slf4j
public class UrbanWaterloggingAlertProcessor implements AlertProcessor, TypedAlertProcessor {

    @Autowired
    @Qualifier("urbanWaterloggingAlertTemplateProcessor")
    private AlertTemplateProcessor templateProcessor;
    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService; // 用于获取接收人
    @Autowired
    private SjyAlertNodeMapper alertNodeMapper; // 用于获取接收人
    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper; // 用于获取接收人
    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper;
    @Autowired
    private ContinuousAlertService continuousAlertService;

    private AlertProcessor nextProcessor;

    @Override
    public void setNext(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public List<SjyAlertSmsRecord> process(AlertContext context) {
        log.debug("开始处理城市内涝水位预警，规则ID: {}", context.getRule().getRuleId());
        
        List<SjyAlertSmsRecord> records = new ArrayList<>();
        
        try {
            // 检查是否支持当前规则类型
            if (!supportsRuleType(context.getRule().getRuleTypeId())) {
                log.debug("不支持的规则类型: {}, 传递给下一个处理器", context.getRule().getRuleTypeId());
                return nextProcessor != null ? nextProcessor.process(context) : Collections.emptyList();
            }

            Map<String, Object> data = context.getData();
            if (data == null || !data.containsKey("argResults")) {
                log.warn("城市内涝水位预警数据格式不正确");
                return Collections.emptyList();
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");
            
            if (CollectionUtils.isEmpty(argResults)) {
                log.warn("参数组结果为空");
                return Collections.emptyList();
            }

            // 处理每个参数组的结果
            for (Map<String, Object> argResult : argResults) {
                if (argResult.containsKey("triggeredStations")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> triggeredStations = 
                            (List<Map<String, Object>>) argResult.get("triggeredStations");
                    
                    if (!CollectionUtils.isEmpty(triggeredStations)) {
                        List<SjyAlertSmsRecord> argRecords = processTriggeredStations(context, triggeredStations);
                        records.addAll(argRecords);
                    }
                }
            }

            log.debug("城市内涝水位预警处理完成，生成{}条预警记录", records.size());
            
        } catch (Exception e) {
            log.error("处理城市内涝水位预警时发生错误: {}", e.getMessage(), e);
        }
        
        return records;
    }

    /**
     * 处理触发预警的测站列表
     */
    private List<SjyAlertSmsRecord> processTriggeredStations(AlertContext context, List<Map<String, Object>> triggeredStations) {
        List<SjyAlertSmsRecord> records = new ArrayList<>();

        try {
            // 从第一个触发的测站获取argId
            String argId = triggeredStations.get(0).get("argId").toString();

            // 获取模板
            SjyAlertSmsTemplate template = getAlertTemplate(context.getRule().getRuleId(), argId);
            if (template == null) {
                log.warn("未找到城市内涝水位预警模板，规则ID: {}, 参数ID: {}", context.getRule().getRuleId(), argId);
                return Collections.emptyList();
            }

            // 准备模板数据
            Map<String, Object> templateData = new HashMap<>();
            templateData.put("triggeredStations", triggeredStations);
            templateData.put("list", triggeredStations); // 为了兼容模板处理器

            // 处理模板
            SjyAlertSmsRecord record = templateProcessor.processTemplate(context, template, templateData);
            if (record != null) {
                // 获取接收人列表
                List<SjyAlertPerson> recipients = getRecipients(context.getRule(), argId);
                
                // 为每个接收人创建预警记录
                for (SjyAlertPerson recipient : recipients) {
                    SjyAlertSmsRecord recipientRecord = new SjyAlertSmsRecord();
                    recipientRecord.setRuleId(record.getRuleId());
                    recipientRecord.setContent(record.getContent());
                    recipientRecord.setAlertLevel(record.getAlertLevel());
                    recipientRecord.setRecipientId(recipient.getPersonId());
                    recipientRecord.setRecipientName(recipient.getPersonName());
                    recipientRecord.setRecipientPhone(recipient.getPhone());
                    records.add(recipientRecord);
                }
            }
            
        } catch (Exception e) {
            log.error("处理触发预警的测站列表时发生错误: {}", e.getMessage(), e);
        }
        
        return records;
    }

    /**
     * 获取预警模板
     */
    private SjyAlertSmsTemplate getAlertTemplate(Long ruleId, String argId) {
        try {
            // 根据参数ID查询模板
            List<SjyAlertSmsTemplate> templates = alertSmsTemplateMapper.selectOneByRelId(argId, "2");
            if (!CollectionUtils.isEmpty(templates)) {
                return templates.get(0); // 返回第一个可用模板
            }
        } catch (Exception e) {
            log.error("获取预警模板失败: {}", e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取接收人列表
     */
    private List<SjyAlertPerson> getRecipients(SjyAlertRule rule, String argId) {
        try {
            // 根据参数ID获取关联的节点
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgId(argId);
            if (CollectionUtils.isEmpty(nodeIds)) {
                return Collections.emptyList();
            }

            // 根据节点获取接收人
            List<Long> nodeIdLongs = nodeIds.stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            return sjyAlertPersonService.getPersonsByNodeIds(nodeIdLongs);

        } catch (Exception e) {
            log.error("获取接收人列表失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    @Override
    public boolean supportsRuleType(String ruleTypeId) {
        return "26".equals(ruleTypeId); // 城市内涝水位预警类型
    }
}
