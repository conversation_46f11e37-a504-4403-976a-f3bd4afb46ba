package com.tzslsjy.business.service.impl;

import java.util.List;

import com.tzslsjy.business.domain.SjyAlertObjRecord;
import com.tzslsjy.business.mapper.SjyAlertObjRecordMapper;
import com.tzslsjy.business.service.ISjyAlertObjRecordService;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 预警对象记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
public class SjyAlertObjRecordServiceImpl implements ISjyAlertObjRecordService
{
    @Autowired
    private SjyAlertObjRecordMapper sjyAlertObjRecordMapper;

    /**
     * 查询预警对象记录
     * 
     * @param alertRecordId 预警对象记录主键
     * @return 预警对象记录
     */
    @Override
    public SjyAlertObjRecord selectSjyAlertObjRecordByAlertRecordId(Long alertRecordId)
    {
        return sjyAlertObjRecordMapper.selectSjyAlertObjRecordByAlertRecordId(alertRecordId);
    }

    /**
     * 查询预警对象记录列表
     * 
     * @param sjyAlertObjRecord 预警对象记录
     * @return 预警对象记录
     */
    @Override
    public List<SjyAlertObjRecord> selectSjyAlertObjRecordList(SjyAlertObjRecord sjyAlertObjRecord)
    {
        return sjyAlertObjRecordMapper.selectSjyAlertObjRecordList(sjyAlertObjRecord);
    }

    /**
     * 新增预警对象记录
     * 
     * @param sjyAlertObjRecord 预警对象记录
     * @return 结果
     */
    @Override
    public int insertSjyAlertObjRecord(SjyAlertObjRecord sjyAlertObjRecord)
    {
        sjyAlertObjRecord.setCreateTime(DateUtils.getNowDate());
        return sjyAlertObjRecordMapper.insertSjyAlertObjRecord(sjyAlertObjRecord);
    }

    /**
     * 修改预警对象记录
     * 
     * @param sjyAlertObjRecord 预警对象记录
     * @return 结果
     */
    @Override
    public int updateSjyAlertObjRecord(SjyAlertObjRecord sjyAlertObjRecord)
    {
        sjyAlertObjRecord.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertObjRecordMapper.updateSjyAlertObjRecord(sjyAlertObjRecord);
    }

    /**
     * 批量删除预警对象记录
     * 
     * @param alertRecordIds 需要删除的预警对象记录主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertObjRecordByAlertRecordIds(Long[] alertRecordIds)
    {
        return sjyAlertObjRecordMapper.deleteSjyAlertObjRecordByAlertRecordIds(alertRecordIds);
    }

    /**
     * 删除预警对象记录信息
     * 
     * @param alertRecordId 预警对象记录主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertObjRecordByAlertRecordId(Long alertRecordId)
    {
        return sjyAlertObjRecordMapper.deleteSjyAlertObjRecordByAlertRecordId(alertRecordId);
    }
}
