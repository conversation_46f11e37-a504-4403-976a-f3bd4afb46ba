package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleArg;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预警规则参数解析器接口
 * 负责将规则和规则参数解析为查询参数
 */
public interface AlertRuleParameterResolver {
    
    /**
     * 获取此解析器支持的规则类型列表
     */
    List<String> getSupportedTypes();

    /**
     * 检查是否支持指定的规则类型
     */
    default boolean supportsType(String ruleTypeCode) {
        return getSupportedTypes().contains(ruleTypeCode);
    }
    
    /**
     * 解析规则和规则参数，生成查询参数
     * 
     * @param rule 预警规则
     * @param ruleParams 规则参数列表
     * @param tm 预警时间
     * @return 按参数组分组的查询参数列表
     */
    List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm);
    
    /**
     * 获取解析器名称（用于日志记录）
     */
    default String getResolverName() {
        return this.getClass().getSimpleName();
    }
} 