package com.tzslsjy.business.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.domain.SjyStationAbnormalParamData;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 测站异常预警参数解析器
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class StationAbnormalParameterResolver implements AlertRuleParameterResolver {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析测站异常预警规则参数，规则ID: {}", rule.getRuleId());
        List<Map<String, Object>> paramGroups = new ArrayList<>();

        // 获取规则配置的测站列表
        List<String> globalStcds = new ArrayList<>();
        if (StringUtils.hasText(rule.getAlertStcd())) {
            globalStcds.addAll(Arrays.asList(rule.getAlertStcd().split(",")));
        }

        // 按参数组ID分组
        Map<Long, List<SjyAlertRuleArg>> groupedParams = ruleParams.stream()
                .collect(Collectors.groupingBy(SjyAlertRuleArg::getArgId));

        for (Map.Entry<Long, List<SjyAlertRuleArg>> entry : groupedParams.entrySet()) {
            Long argId = entry.getKey();
            List<SjyAlertRuleArg> argParamsForGroup = entry.getValue();
            Map<String, Object> queryParams = buildQueryParamsForGroup(rule, argParamsForGroup, argId, tm, globalStcds);
            if (!queryParams.isEmpty()) {
                paramGroups.add(queryParams);
            }
        }

        // 如果没有参数配置，使用默认配置
        if (paramGroups.isEmpty() && !globalStcds.isEmpty()) {
            Map<String, Object> defaultParams = buildDefaultQueryParams(rule, globalStcds, tm);
            paramGroups.add(defaultParams);
        }

        log.debug("测站异常预警参数解析完成，共生成 {} 个参数组", paramGroups.size());
        return paramGroups;
    }

    /**
     * 为参数组构建查询参数
     */
    private Map<String, Object> buildQueryParamsForGroup(SjyAlertRule rule, List<SjyAlertRuleArg> argParams,
                                                         Long argId, Date tm, List<String> globalStcds) {
        Map<String, Object> queryParams = new HashMap<>();
        SjyStationAbnormalParamData paramData = new SjyStationAbnormalParamData();

        try {
            // 解析JSON参数
            for (SjyAlertRuleArg arg : argParams) {
                if (StringUtils.hasText(arg.getArgJson())) {
                    Map<String, Object> argMap = objectMapper.readValue(arg.getArgJson(),
                            new TypeReference<Map<String, Object>>() {});

                    // 解析新的JSON格式：
                    // {"overWaterVal":"1.0","overRainVal":"15.0","overWaterEnable":"1","overRainEnable":"1","timeoutMinutes":"60","rainStcds":["13123a","13123b"],"waterStcds":["13123a","13123b"]}

                    // 水位异常阈值
                    if (argMap.containsKey("overWaterVal")) {
                        paramData.setWaterLevelThreshold(new java.math.BigDecimal(argMap.get("overWaterVal").toString()));
                    }

                    // 雨量异常阈值
                    if (argMap.containsKey("overRainVal")) {
                        paramData.setRainfallThreshold(new java.math.BigDecimal(argMap.get("overRainVal").toString()));
                    }

                    // 水位异常检测开关
                    if (argMap.containsKey("overWaterEnable")) {
                        paramData.setWaterLevelDetectionEnabled("1".equals(argMap.get("overWaterEnable").toString()));
                    }

                    // 雨量异常检测开关
                    if (argMap.containsKey("overRainEnable")) {
                        paramData.setRainfallDetectionEnabled("1".equals(argMap.get("overRainEnable").toString()));
                    }

                    // 水位测站列表
                    if (argMap.containsKey("waterStcds")) {
                        @SuppressWarnings("unchecked")
                        List<String> waterStcdList = (List<String>) argMap.get("waterStcds");
                        paramData.setWaterStcds(waterStcdList);
                    }

                    // 雨量测站列表
                    if (argMap.containsKey("rainStcds")) {
                        @SuppressWarnings("unchecked")
                        List<String> rainStcdList = (List<String>) argMap.get("rainStcds");
                        paramData.setRainStcds(rainStcdList);
                    }

                    // 超时检测测站列表
                    if (argMap.containsKey("timeStcds")) {
                        @SuppressWarnings("unchecked")
                        List<String> timeStcdList = (List<String>) argMap.get("timeStcds");
                        paramData.setTimeStcds(timeStcdList);
                    }

                    // 数据接收超时阈值（如果有配置）
                    if (argMap.containsKey("timeoutMinutes")) {
                        paramData.setDataTimeoutMinutes(Integer.valueOf(argMap.get("timeoutMinutes").toString()));
                    }
                }
            }

            // 如果没有配置测站列表，使用全局测站列表
            if (paramData.getAllStcdsForDetection().isEmpty()) {
                // 如果新格式和旧格式都没有配置，使用全局测站列表作为兼容
                if ((paramData.getWaterStcds() == null || paramData.getWaterStcds().isEmpty()) &&
                    (paramData.getRainStcds() == null || paramData.getRainStcds().isEmpty()) &&
                    (paramData.getStcds() == null || paramData.getStcds().isEmpty())) {
                    paramData.setStcds(globalStcds);
                }
            }

            queryParams.put("stationAbnormalParams", paramData);
            queryParams.put("argId", argId);
            queryParams.put("currentTime", tm);

        } catch (Exception e) {
            log.error("解析测站异常预警参数失败，argId: {}, error: {}", argId, e.getMessage(), e);
        }

        return queryParams;
    }

    /**
     * 构建默认查询参数
     */
    private Map<String, Object> buildDefaultQueryParams(SjyAlertRule rule, List<String> globalStcds, Date tm) {
        Map<String, Object> queryParams = new HashMap<>();
        SjyStationAbnormalParamData paramData = new SjyStationAbnormalParamData();
        paramData.setStcds(globalStcds);

        queryParams.put("stationAbnormalParams", paramData);
        queryParams.put("argId", "default");
        queryParams.put("currentTime", tm);

        return queryParams;
    }

    @Override
    public List<String> getSupportedTypes() {
        return Arrays.asList("53"); // 测站异常预警类型
    }
}
