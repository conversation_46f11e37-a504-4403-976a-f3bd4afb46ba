package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertPersonNodeRelation;

/**
 * 人员节点关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISjyAlertPersonNodeRelationService
{
    /**
     * 查询人员节点关联
     *
     * @param relationId 人员节点关联主键
     * @return 人员节点关联
     */
    public SjyAlertPersonNodeRelation selectSjyAlertPersonNodeRelationByRelationId(Long relationId);

    /**
     * 查询人员节点关联列表
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 人员节点关联集合
     */
    public List<SjyAlertPersonNodeRelation> selectSjyAlertPersonNodeRelationList(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation);

    /**
     * 新增人员节点关联
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 结果
     */
    public int insertSjyAlertPersonNodeRelation(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation);

    /**
     * 修改人员节点关联
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 结果
     */
    public int updateSjyAlertPersonNodeRelation(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation);

    /**
     * 批量删除人员节点关联
     *
     * @param relationIds 需要删除的人员节点关联主键集合
     * @return 结果
     */
    public int deleteSjyAlertPersonNodeRelationByRelationIds(Long[] relationIds);

    /**
     * 删除人员节点关联信息
     *
     * @param relationId 人员节点关联主键
     * @return 结果
     */
    public int deleteSjyAlertPersonNodeRelationByRelationId(Long relationId);
}
