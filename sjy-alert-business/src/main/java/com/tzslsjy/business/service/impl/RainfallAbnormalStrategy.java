package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.service.AlertRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 雨量异常预警策略
 * 专注于雨量异常预警条件评估
 * 支持规则类型：34
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class RainfallAbnormalStrategy implements AlertRuleStrategy {



    @Override
    public boolean evaluate(AlertContext context) {
        try {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) context.getData().get("argResults");

            if (CollectionUtils.isEmpty(argResults)) {
                log.debug("雨量异常预警参数组结果为空");
                return false;
            }

            for (Map<String, Object> argResult : argResults) {
                if (argResult.containsKey("abnormalResults")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> abnormalResults =
                            (List<Map<String, Object>>) argResult.get("abnormalResults");
                    if (!CollectionUtils.isEmpty(abnormalResults)) {
                        log.info("雨量异常预警触发，参数组ID: {}", argResult.get("argId"));
                        return true;
                    }
                }
            }

            log.debug("雨量异常预警未触发");
            return false;

        } catch (Exception e) {
            log.error("评估雨量异常预警规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getType() {
        return "34"; // 对应雨量异常预警类型
    }
}
