package com.tzslsjy.business.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.tzslsjy.business.domain.vo.NodeTreeVO;
import com.tzslsjy.business.domain.comm.BusinessCommCity;
import com.tzslsjy.business.domain.SjyRiskVillageStcdRel;
import com.tzslsjy.business.mapper.comm.BusinessCommCityMapper;
import com.tzslsjy.business.mapper.SjyRiskVillageStcdRelMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.business.service.IStStbprpBService;

/**
 * 测站基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
@Service
public class StStbprpBServiceImpl implements IStStbprpBService
{
    @Autowired
    private StStbprpBMapper stStbprpBMapper;

    @Autowired
    private BusinessCommCityMapper businessCommCityMapper;

    @Autowired
    private SjyRiskVillageStcdRelMapper sjyRiskVillageStcdRelMapper;

    /**
     * 查询测站基础信息
     *
     * @param STCD 测站基础信息主键
     * @return 测站基础信息
     */
    @Override
    public StStbprpB selectStStbprpBBySTCD(String STCD)
    {
        return stStbprpBMapper.selectStStbprpBBySTCD(STCD);
    }

    /**
     * 查询测站基础信息列表
     *
     * @param stStbprpB 测站基础信息
     * @return 测站基础信息
     */
    @Override
    public List<StStbprpB> selectStStbprpBList(StStbprpB stStbprpB)
    {
        return stStbprpBMapper.selectStStbprpBList(stStbprpB);
    }

    /**
     * 新增测站基础信息
     *
     * @param stStbprpB 测站基础信息
     * @return 结果
     */
    @Override
    public int insertStStbprpB(StStbprpB stStbprpB)
    {
        return stStbprpBMapper.insertStStbprpB(stStbprpB);
    }

    /**
     * 修改测站基础信息
     *
     * @param stStbprpB 测站基础信息
     * @return 结果
     */
    @Override
    public int updateStStbprpB(StStbprpB stStbprpB)
    {
        return stStbprpBMapper.updateStStbprpB(stStbprpB);
    }

    /**
     * 批量删除测站基础信息
     *
     * @param STCDs 需要删除的测站基础信息主键
     * @return 结果
     */
    @Override
    public int deleteStStbprpBBySTCDs(String[] STCDs)
    {
        return stStbprpBMapper.deleteStStbprpBBySTCDs(STCDs);
    }

    /**
     * 删除测站基础信息信息
     *
     * @param STCD 测站基础信息主键
     * @return 结果
     */
    @Override
    public int deleteStStbprpBBySTCD(String STCD)
    {
        return stStbprpBMapper.deleteStStbprpBBySTCD(STCD);
    }

    /**
     * 查询测站基础信息列表（包含关联的村庄信息）
     * 采用两步查询解决分页问题：
     * 1. 先分页查询测站基础信息（使用专门的查询方法）
     * 2. 再根据测站编码批量查询关联的村庄信息
     *
     * @param stStbprpB 测站基础信息
     * @return 测站基础信息
     */
    @Override
    public List<StStbprpB> selectStStbprpBListWithVillage(StStbprpB stStbprpB)
    {
        // 第一步：先查询测站基础信息（支持分页和查询条件）
        List<StStbprpB> stationList = stStbprpBMapper.selectStStbprpBListForVillage(stStbprpB);

        if (stationList == null || stationList.isEmpty()) {
            return stationList;
        }

        // 第二步：提取测站编码列表
        List<String> stcdList = stationList.stream()
                .map(StStbprpB::getStcd)
                .collect(Collectors.toList());

        // 第三步：批量查询村庄关联信息
        List<SjyRiskVillageStcdRel> villageRelList = sjyRiskVillageStcdRelMapper.selectBatchByStcds(stcdList);

        // 第四步：按测站编码分组村庄关联信息
        Map<String, List<SjyRiskVillageStcdRel>> villageRelMap = villageRelList.stream()
                .collect(Collectors.groupingBy(SjyRiskVillageStcdRel::getStcd));

        // 第五步：为每个测站设置关联的村庄信息
        stationList.forEach(station -> {
            List<SjyRiskVillageStcdRel> relList = villageRelMap.get(station.getStcd());
            station.setVillageStcdRels(relList != null ? relList : new ArrayList<>());
        });

        return stationList;
    }

    @Override
    public List<NodeTreeVO> listWithAdcd(StStbprpB stStbprpB) {
        try {
            // 1. 获取指定行政区划下的所有测站信息
            List<StStbprpB> stationList = this.selectStStbprpBList(stStbprpB);
            if (stationList == null || stationList.isEmpty()) {
                return new ArrayList<>();
            }

            // 2. 获取所有相关的行政区划信息
            BusinessCommCity vo = new BusinessCommCity();
            vo.setAddvcd("3310");
            List<BusinessCommCity> cityList = businessCommCityMapper.pageList(vo);
            //vo.setAddvcd("331002");
           // cityList.addAll(businessCommCityMapper.pageList(vo));
            Map<String, BusinessCommCity> cityMap = cityList.stream()
                    .collect(Collectors.toMap(BusinessCommCity::getJurisdictionNum, city -> city, (v1, v2) -> v1));

            // 3. 按行政区划分组测站
            Map<String, List<StStbprpB>> stationGroupMap = stationList.stream()
                    .filter(station -> StringUtils.hasText(station.getAddvcd9()))
                    .collect(Collectors.groupingBy(station -> {
                        // 使用9位行政区划编码的前6位作为分组键（县级）
                        String addvcd9 = station.getAddvcd9();
                        return addvcd9 ;
                    }));

            // 4. 构建树形结构
            List<NodeTreeVO> treeNodes = new ArrayList<>();
            
            for (Map.Entry<String, List<StStbprpB>> entry : stationGroupMap.entrySet()) {
                String countyCode = entry.getKey();
                List<StStbprpB> stations = entry.getValue();
                
                // 创建县级节点
                BusinessCommCity county = cityMap.get(countyCode);
                NodeTreeVO countyNode = new NodeTreeVO();
                countyNode.setId(countyCode);
                countyNode.setLabel(county != null ? county.getJurisdiction() : "未知区域");
                countyNode.setType("area");
                
                // 创建测站节点
                List<NodeTreeVO> stationNodes = stations.stream()
                        .map(station -> {
                            NodeTreeVO stationNode = new NodeTreeVO();
                            stationNode.setId(station.getStcd());
                            stationNode.setLabel(station.getStnm());
                            stationNode.setType("station");
                            return stationNode;
                        })
                        .collect(Collectors.toList());
                
                countyNode.setChildren(stationNodes);
                treeNodes.add(countyNode);
            }
            treeNodes.sort(Comparator.comparing(NodeTreeVO::getId));
            return treeNodes;
            
        } catch (Exception e) {
            e.printStackTrace();
            // 记录异常日志（如果有日志框架）
            // log.error("构建测站树形结构失败", e);
            return new ArrayList<>();
        }
    }
}
