package com.tzslsjy.business.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 8时水文预警参数解析器
 */
@Component
@Slf4j
public class HydrologyReportParameterResolver implements AlertRuleParameterResolver {

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("13"); // 8时水文预警类型

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析8时水文预警规则参数，规则ID: {}", rule.getRuleId());
        List<Map<String, Object>> paramGroups = new ArrayList<>();
        paramGroups.add(buildQueryParamsForGroup(rule, ruleParams, 1L, tm)); // 1L 为8时水文预警的参数ID
        return paramGroups;
    }

    private Map<String, Object> buildQueryParamsForGroup(SjyAlertRule rule, List<SjyAlertRuleArg> argParams, Long argId, Date tm) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ruleId", rule.getRuleId());
        queryParams.put("ruleName", rule.getRuleName());
        queryParams.put("ruleTypeId", rule.getRuleTypeId());
        queryParams.put("argId", argId);
        queryParams.put("tm", tm);

        // 解析8时水文预警参数
        List<SjyHydrologyReportParamData> hydrologyReportParamData = new ArrayList<>();
        for (SjyAlertRuleArg param : argParams) {
            if (param.getArgId().equals(argId)) {
                try {
                    SjyHydrologyReportJsonData jsonData = JSON.parseObject(param.getArgJson(), SjyHydrologyReportJsonData.class);
                    SjyHydrologyReportParamData paramData = new SjyHydrologyReportParamData();
                    paramData.setArgId(param.getArgId());
                    paramData.setAlertLevel(param.getAlertLevel());
                    if (jsonData != null) {
                        paramData.setReportType(jsonData.getReportType());
                    }
                    hydrologyReportParamData.add(paramData);
                } catch (Exception e) {
                    log.error("解析8时水文预警参数失败，参数ID: {}, JSON: {}, 错误: {}",
                            param.getArgId(), param.getArgJson(), e.getMessage());
                }
            }
        }
        queryParams.put("hydrologyReportParams", hydrologyReportParamData);

        return queryParams;
    }
} 