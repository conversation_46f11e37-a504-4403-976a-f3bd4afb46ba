package com.tzslsjy.business.service.impl;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.*;
import com.tzslsjy.business.service.ISjyAlertRuleService;
import com.tzslsjy.common.utils.DateUtils;
import com.tzslsjy.common.utils.SecurityUtils;
import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.validation.Valid;

/**
 * 预警规则Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
@AllArgsConstructor
public class SjyAlertRuleServiceImpl implements ISjyAlertRuleService
{


    private final SjyAlertRuleMapper sjyAlertRuleMapper;
    private final SjyAlertRuleArgMapper sjyAlertRuleArgMapper;
    private final SjyAlertRuleNodeRelationMapper sjyAlertRuleNodeRelationMapper;
    private final SjyAlertTemplateRelationMapper sjyAlertTemplateRelationMapper;
    private final SjyAlertPersonNodeRelationMapper personNodeRelationMapper;
    private final SjyAlertPersonMapper personMapper;
    @Override
    public SjyAlertRule selectSjyAlertRuleById(Long ruleId) {
        // 1. 查询基础的rule和arg数据
        SjyAlertRule rule = sjyAlertRuleMapper.selectSjyAlertRuleByRuleId(ruleId);
        if (rule == null) {
            return null;
        }

        // 2. 填充节点和模板关系数据
        fillNodeAndTemplateRelations(Collections.singletonList(rule));

        return rule;
    }

    @Override
    public List<SjyAlertRule> selectSjyAlertRuleList(SjyAlertRule sjyAlertRule) {
        // 1. 先查询基础的rule和arg数据
        List<SjyAlertRule> sjyAlertRules = sjyAlertRuleMapper.selectSjyAlertRuleList(sjyAlertRule);

        // 2. 填充节点和模板关系数据
        fillNodeAndTemplateRelations(sjyAlertRules);

        return sjyAlertRules;
    }

    /**
     * 为规则列表填充节点和模板关系数据
     * 
     * @param rules 规则列表
     */
    private void fillNodeAndTemplateRelations(List<SjyAlertRule> rules) {
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

        // 收集所有规则ID和参数ID
        List<Long> ruleIds = rules.stream()
                .map(SjyAlertRule::getRuleId)
                .collect(Collectors.toList());

        List<Long> argIds = rules.stream()
                .filter(rule -> !CollectionUtils.isEmpty(rule.getSjyAlertRuleArgs()))
                .flatMap(rule -> rule.getSjyAlertRuleArgs().stream())
                .map(SjyAlertRuleArg::getArgId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        // 分别查询node和template关系
        Map<Long, List<SjyAlertRuleNodeRelation>> ruleNodeRelationsMap = new HashMap<>();
        Map<Long, List<SjyAlertRuleNodeRelation>> argNodeRelationsMap = new HashMap<>();
        Map<Long, List<SjyAlertTemplateRelation>> ruleTemplateRelationsMap = new HashMap<>();
        Map<Long, List<SjyAlertTemplateRelation>> argTemplateRelationsMap = new HashMap<>();

        // 查询规则级别的node关系
        if (!CollectionUtils.isEmpty(ruleIds)) {
            List<SjyAlertRuleNodeRelation> ruleNodeRelations = sjyAlertRuleMapper.selectNodeRelationsByRuleIds(ruleIds);
            ruleNodeRelationsMap = ruleNodeRelations.stream()
                    .collect(Collectors.groupingBy(SjyAlertRuleNodeRelation::getRuleId));

            List<SjyAlertTemplateRelation> ruleTemplateRelations = sjyAlertRuleMapper.selectTemplateRelationsByRuleIds(ruleIds);
            ruleTemplateRelationsMap = ruleTemplateRelations.stream()
                    .collect(Collectors.groupingBy(SjyAlertTemplateRelation::getRelationId));
        }

        // 查询参数级别的node关系
        if (!CollectionUtils.isEmpty(argIds)) {
            List<SjyAlertRuleNodeRelation> argNodeRelations = sjyAlertRuleMapper.selectNodeRelationsByArgIds(argIds);
            argNodeRelationsMap = argNodeRelations.stream()
                    .collect(Collectors.groupingBy(SjyAlertRuleNodeRelation::getArgId));

            List<SjyAlertTemplateRelation> argTemplateRelations = sjyAlertRuleMapper.selectTemplateRelationsByArgIds(argIds);
            argTemplateRelationsMap = argTemplateRelations.stream()
                    .collect(Collectors.groupingBy(SjyAlertTemplateRelation::getRelationId));
        }

        // 收集所有personId并查询person信息
        List<SjyAlertRuleNodeRelation> allNodeRelations = new ArrayList<>();
        allNodeRelations.addAll(ruleNodeRelationsMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        allNodeRelations.addAll(argNodeRelationsMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));

        List<String> personIds = allNodeRelations.stream()
                .map(SjyAlertRuleNodeRelation::getPersonId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        Map<String, SjyAlertPerson> nodePersonMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(personIds)) {
            nodePersonMap = personMapper.selectSjyAlertPersonByIds(personIds)
                    .stream()
                    .collect(Collectors.toMap(
                            SjyAlertPerson::getPersonId,
                            Function.identity(),
                            (o1, o2) -> o1));
        }

        // 组装数据
        final Map<String, SjyAlertPerson> finalNodePersonMap = nodePersonMap;
        for (SjyAlertRule rule : rules) {
            Long ruleId = rule.getRuleId();

            // 设置规则级别的node和template关系
            List<SjyAlertRuleNodeRelation> ruleNodeRelations = ruleNodeRelationsMap.getOrDefault(ruleId, new ArrayList<>());
            ruleNodeRelations.forEach(relation -> {
                // 重新组装完整的 nodeId
                assembleFullNodeId(relation);

                String personId = relation.getPersonId();
                if (personId != null && finalNodePersonMap.containsKey(personId)) {
                    relation.setPersonName(finalNodePersonMap.get(personId).getName());
                }
            });
            rule.setRuleNodeRelations(ruleNodeRelations);

            List<SjyAlertTemplateRelation> ruleTemplateRelations = ruleTemplateRelationsMap.getOrDefault(ruleId, new ArrayList<>());
            rule.setRuleTemplateRelations(ruleTemplateRelations);
            rule.setTemplateId(ruleTemplateRelations.stream()
                    .map(SjyAlertTemplateRelation::getTemplateId)
                    .filter(Objects::nonNull)  // 过滤掉null值
                    .findFirst().orElse(null));
            // 设置参数级别的node和template关系
            if (!CollectionUtils.isEmpty(rule.getSjyAlertRuleArgs())) {
                for (SjyAlertRuleArg arg : rule.getSjyAlertRuleArgs()) {
                    Long argId = arg.getArgId();

                    List<SjyAlertRuleNodeRelation> argNodeRelations = argNodeRelationsMap.getOrDefault(argId, new ArrayList<>());
                    argNodeRelations.forEach(relation -> {
                        // 重新组装完整的 nodeId
                        assembleFullNodeId(relation);

                        String personId = relation.getPersonId();
                        if (personId != null && finalNodePersonMap.containsKey(personId)) {
                            relation.setPersonName(finalNodePersonMap.get(personId).getName());
                        }
                    });
                    arg.setArgNodeRelations(argNodeRelations);

                    List<SjyAlertTemplateRelation> argTemplateRelations = argTemplateRelationsMap.getOrDefault(argId, new ArrayList<>());
                    arg.setArgTemplateRelations(argTemplateRelations);
                    arg.setTemplateId(argTemplateRelations.stream()
                            .map(SjyAlertTemplateRelation::getTemplateId)
                            .filter(Objects::nonNull)  // 过滤掉null值
                            .findFirst().orElse(null));
                }
            }
        }
    }

    @Override
    @Transactional
    public int insertSjyAlertRule(SjyAlertRule sjyAlertRule) {
        sjyAlertRule.setCreateTime(DateUtils.getNowDate());
      //  sjyAlertRule.setCreateBy(SecurityUtils.getUsername());
        sjyAlertRuleMapper.insertSjyAlertRule(sjyAlertRule);
        Long ruleId = sjyAlertRule.getRuleId();

        if (ruleId == null) {
            throw new RuntimeException("Failed to retrieve generated ruleId after insert.");
        }

        insertRuleNodeRelations(ruleId, null, sjyAlertRule.getRuleNodeRelations());
        insertRuleTemplateRelations(ruleId, null, sjyAlertRule.getTemplateId(), null, "1"); // "1" for rule, ignore ruleTemplateRelations

        if (!CollectionUtils.isEmpty(sjyAlertRule.getSjyAlertRuleArgs())) {
            for (SjyAlertRuleArg arg : sjyAlertRule.getSjyAlertRuleArgs()) {
                arg.setRelationId(ruleId);
                arg.setRelationType("1");
               // arg.setCreateBy(SecurityUtils.getUsername());
                arg.setCreateTime(DateUtils.getNowDate());
                sjyAlertRuleArgMapper.insertSjyAlertRuleArg(arg);
                Long argId = arg.getArgId();

                if (argId == null) {
                    throw new RuntimeException("Failed to retrieve generated argId for ruleId: " + ruleId);
                }
                insertRuleNodeRelations(ruleId, argId, arg.getArgNodeRelations());
                insertRuleTemplateRelations(null, argId, arg.getTemplateId(), null, "2"); // "2" for arg, ignore argTemplateRelations
            }
        }
        return 1;
    }

    private void insertRuleNodeRelations(Long ruleId, Long argId, List<SjyAlertRuleNodeRelation> nodeRelations) {
        if (!CollectionUtils.isEmpty(nodeRelations)) {
            List<SjyAlertRuleNodeRelation> toInsert = new ArrayList<>();
            for (SjyAlertRuleNodeRelation relation : nodeRelations) {
                relation.setRuleId(ruleId);
                relation.setArgId(argId);
                //relation.setCreateBy(SecurityUtils.getUsername());
                relation.setCreateTime(DateUtils.getNowDate());
                relation.setRelationId(null); // Ensure it's treated as new

                // 处理 nodeId 前缀
                processNodeIdPrefix(relation);

                toInsert.add(relation);
            }
            if (!toInsert.isEmpty()) {
                sjyAlertRuleNodeRelationMapper.batchInsert(toInsert);
            }
        }
    }

    private void insertRuleTemplateRelations(Long ruleId, Long argId, List<SjyAlertTemplateRelation> templateRelations, String relationType) {
        insertRuleTemplateRelations(ruleId, argId, null, templateRelations, relationType);
    }

    private void insertRuleTemplateRelations(Long ruleId, Long argId, Long templateId, List<SjyAlertTemplateRelation> templateRelations, String relationType) {
        // 只处理templateId，忽略templateRelations列表
        if (templateId == null) {
            return;
        }

        Long parentEntityId = relationType.equals("1") ? ruleId : argId;

        // 只处理templateId，创建单个模板关系
        SjyAlertTemplateRelation relation = new SjyAlertTemplateRelation();
        relation.setRelationId(parentEntityId);
        relation.setRelationType(relationType);
        relation.setTemplateId(templateId);
        relation.setCreateTime(DateUtils.getNowDate());

        sjyAlertTemplateRelationMapper.insertSjyAlertTemplateRelation(relation);
    }

    @Override
    @Transactional
    public int updateSjyAlertRule(SjyAlertRule sjyAlertRule) {
        sjyAlertRule.setUpdateTime(DateUtils.getNowDate());
        //sjyAlertRule.setUpdateBy(SecurityUtils.getUsername());
        sjyAlertRuleMapper.updateSjyAlertRule(sjyAlertRule);
        Long ruleId = sjyAlertRule.getRuleId();

        if (ruleId == null) {
            throw new IllegalArgumentException("RuleId cannot be null for update operation.");
        }

        synchronizeRuleNodeRelations(ruleId, null, sjyAlertRule.getRuleNodeRelations());
        synchronizeRuleTemplateRelations(ruleId, "1", sjyAlertRule.getTemplateId(), null); // ignore ruleTemplateRelations
        synchronizeSjyAlertRuleArgs(ruleId, sjyAlertRule.getSjyAlertRuleArgs());

        return 1;
    }

    private void synchronizeSjyAlertRuleArgs(Long ruleId, List<SjyAlertRuleArg> incomingArgs) {
        SjyAlertRuleArg filter = new SjyAlertRuleArg();
        filter.setRelationId(ruleId);
        filter.setRelationType("1");
        List<SjyAlertRuleArg> existingArgsFromDB = sjyAlertRuleArgMapper.selectSjyAlertRuleArgList(filter);

        Map<Long, SjyAlertRuleArg> existingArgsMap = existingArgsFromDB.stream()
                .collect(Collectors.toMap(SjyAlertRuleArg::getArgId, Function.identity()));

        List<SjyAlertRuleArg> argsToInsert = new ArrayList<>();
        List<Long> processedArgIds = new ArrayList<>(); // Keep track of IDs from incoming list + newly inserted

        if (!CollectionUtils.isEmpty(incomingArgs)) {
            for (SjyAlertRuleArg incomingArg : incomingArgs) {
                incomingArg.setRelationId(ruleId);
                incomingArg.setRelationType("1");

                if (incomingArg.getArgId() != null && existingArgsMap.containsKey(incomingArg.getArgId())) {
                    // Existing arg - update
                //    incomingArg.setUpdateBy(SecurityUtils.getUsername());
                    incomingArg.setUpdateTime(DateUtils.getNowDate());
                    sjyAlertRuleArgMapper.updateSjyAlertRuleArg(incomingArg);
                    processedArgIds.add(incomingArg.getArgId());

                    synchronizeRuleNodeRelations(ruleId, incomingArg.getArgId(), incomingArg.getArgNodeRelations());
                    synchronizeRuleTemplateRelations(incomingArg.getArgId(), "2", incomingArg.getTemplateId(), null); // ignore argTemplateRelations
                } else {
                    // New arg - prepare for insert
               //     incomingArg.setCreateBy(SecurityUtils.getUsername());
                    incomingArg.setCreateTime(DateUtils.getNowDate());
                    incomingArg.setArgId(null);
                    argsToInsert.add(incomingArg);
                }
            }
        }

        for (SjyAlertRuleArg argToInsert : argsToInsert) {
            sjyAlertRuleArgMapper.insertSjyAlertRuleArg(argToInsert); // argId will be populated
            Long newArgId = argToInsert.getArgId();
            if (newArgId == null) {
                throw new RuntimeException("Failed to retrieve generated argId for new SjyAlertRuleArg for ruleId: " + ruleId);
            }
            processedArgIds.add(newArgId);
            // For new args, their sub-collections are all new, so simple insert
            insertRuleNodeRelations(ruleId, newArgId, argToInsert.getArgNodeRelations());
            insertRuleTemplateRelations(null, newArgId, argToInsert.getTemplateId(), null, "2"); // ignore argTemplateRelations
        }

        // Delete args that were in DB but not in processedArgIds list
        List<Long> argIdsToDelete = new ArrayList<>();
        for (Long existingArgId : existingArgsMap.keySet()) {
            if (!processedArgIds.contains(existingArgId)) {
                argIdsToDelete.add(existingArgId);
            }
        }

        if (!CollectionUtils.isEmpty(argIdsToDelete)) {
            for (Long argIdToDel : argIdsToDelete) {
                sjyAlertRuleNodeRelationMapper.deleteByArgId(argIdToDel);
                Map<String, Object> params = new HashMap<>();
                params.put("relationId", argIdToDel);
                params.put("relationType", "2");
                sjyAlertTemplateRelationMapper.deleteByRelationIdAndType(params);
                sjyAlertRuleArgMapper.deleteSjyAlertRuleArgByArgId(argIdToDel);
            }
        }
    }

    private void synchronizeRuleNodeRelations(Long ruleId, Long argId, List<SjyAlertRuleNodeRelation> incomingRelations) {
        List<SjyAlertRuleNodeRelation> existingRelations;
        if (argId == null) {
            existingRelations = sjyAlertRuleNodeRelationMapper.selectByRuleIdAndArgIdIsNull(ruleId);
        } else {
            SjyAlertRuleNodeRelation filter = new SjyAlertRuleNodeRelation();
            filter.setRuleId(ruleId);
            filter.setArgId(argId);
            existingRelations = sjyAlertRuleNodeRelationMapper.selectSjyAlertRuleNodeRelationList(filter);
        }

        Map<Long, SjyAlertRuleNodeRelation> existingMap = existingRelations.stream()
                .collect(Collectors.toMap(SjyAlertRuleNodeRelation::getRelationId, Function.identity()));

        List<SjyAlertRuleNodeRelation> toInsert = new ArrayList<>();
        List<Long> processedRelationIds = new ArrayList<>();

        if (!CollectionUtils.isEmpty(incomingRelations)) {
            for (SjyAlertRuleNodeRelation incoming : incomingRelations) {
                incoming.setRuleId(ruleId);
                incoming.setArgId(argId);

                // 处理 nodeId 前缀
                processNodeIdPrefix(incoming);

                if (incoming.getRelationId() != null && existingMap.containsKey(incoming.getRelationId())) {
                  //  incoming.setUpdateBy(SecurityUtils.getUsername());
                    incoming.setUpdateTime(DateUtils.getNowDate());
                    sjyAlertRuleNodeRelationMapper.updateSjyAlertRuleNodeRelation(incoming);
                    processedRelationIds.add(incoming.getRelationId());
                } else {
                 //   incoming.setCreateBy(SecurityUtils.getUsername());
                    incoming.setCreateTime(DateUtils.getNowDate());
                    incoming.setRelationId(null);
                    toInsert.add(incoming);
                }
            }
        }

        if (!toInsert.isEmpty()) {
            sjyAlertRuleNodeRelationMapper.batchInsert(toInsert);
            // Add newly inserted IDs to processedRelationIds if they are auto-generated and needed for further logic (not in this case)
            // For deletion logic, we only care about IDs that came from the client or were already in DB.
        }

        List<Long> idsToDelete = new ArrayList<>();
        for (Long existingId : existingMap.keySet()) {
            if (!processedRelationIds.contains(existingId)) {
                // Check if this existing ID was part of the original incoming list (even if it was to be updated)
                // This ensures we don't delete something that was intended to be kept (and updated)
                boolean foundInIncomingWithId = false;
                if (!CollectionUtils.isEmpty(incomingRelations)) {
                    for(SjyAlertRuleNodeRelation incoming : incomingRelations) {
                        if(existingId.equals(incoming.getRelationId())) {
                            foundInIncomingWithId = true;
                            break;
                        }
                    }
                }
                if(!foundInIncomingWithId) { // If it was not in incoming list with an ID, it's to be deleted.
                    idsToDelete.add(existingId);
                }
            }
        }
        // A simpler way for deletion: if an existing ID is not among the IDs of the items that were processed (updated or identified as existing)
        // AND it's not among the IDs of items that were newly inserted (if we could track them, but batchInsert doesn't return them easily).
        // The current logic: delete if existing ID is not in processedRelationIds. This means if an item was in DB,
        // and it was NOT in the incoming list (with its ID), it will be deleted.
        // This is correct. If an incoming item has a null ID, it's new and doesn't affect this.
        // If an incoming item has an ID and it matched an existing one, it's in processedRelationIds.
        // So, existingMap.keySet() - processedRelationIds = idsToDelete.

        // Corrected deletion logic:
        idsToDelete.clear(); // Reset
        for(Map.Entry<Long, SjyAlertRuleNodeRelation> entry : existingMap.entrySet()){
            boolean foundInIncoming = false;
            if(!CollectionUtils.isEmpty(incomingRelations)){
                for(SjyAlertRuleNodeRelation incomingRel : incomingRelations){
                    if(entry.getKey().equals(incomingRel.getRelationId())){
                        foundInIncoming = true;
                        break;
                    }
                }
            }
            if(!foundInIncoming){
                idsToDelete.add(entry.getKey());
            }
        }


        if (!idsToDelete.isEmpty()) {
            sjyAlertRuleNodeRelationMapper.deleteSjyAlertRuleNodeRelationByRelationIds(idsToDelete.toArray(new Long[0]));
        }
    }

    private void synchronizeRuleTemplateRelations(Long parentEntityId, String relationType, Long templateId, List<SjyAlertTemplateRelation> incomingRelations) {
        // 查询现有的模板关系
        SjyAlertTemplateRelation filter = new SjyAlertTemplateRelation();
        filter.setRelationId(parentEntityId);
        filter.setRelationType(relationType);
        List<SjyAlertTemplateRelation> existingRelations = sjyAlertTemplateRelationMapper.selectSjyAlertTemplateRelationList(filter);

        // 删除所有现有的模板关系
        if (!existingRelations.isEmpty()) {
            Long[] idsToDelete = existingRelations.stream()
                    .map(SjyAlertTemplateRelation::getId)
                    .toArray(Long[]::new);
            sjyAlertTemplateRelationMapper.deleteSjyAlertTemplateRelationByIds(idsToDelete);
        }

        // 只处理templateId，如果不为空则创建新的模板关系
        if (templateId != null) {
            SjyAlertTemplateRelation relation = new SjyAlertTemplateRelation();
            relation.setRelationId(parentEntityId);
            relation.setRelationType(relationType);
            relation.setTemplateId(templateId);
            relation.setCreateTime(DateUtils.getNowDate());
            sjyAlertTemplateRelationMapper.insertSjyAlertTemplateRelation(relation);
        }
    }

    @Override
    @Transactional
    public int deleteSjyAlertRuleByRuleId(Long ruleId) {
        List<Long> argIds = sjyAlertRuleArgMapper.selectArgIdsByRuleId(ruleId);
        if (!CollectionUtils.isEmpty(argIds)) {
            for (Long argId : argIds) {
                sjyAlertRuleNodeRelationMapper.deleteByArgId(argId);
                Map<String, Object> paramsArgTemplate = new HashMap<>();
                paramsArgTemplate.put("relationId", argId);
                paramsArgTemplate.put("relationType", "2");
                sjyAlertTemplateRelationMapper.deleteByRelationIdAndType(paramsArgTemplate);
            }
            sjyAlertRuleArgMapper.deleteByRuleId(ruleId);
        }
        sjyAlertRuleNodeRelationMapper.deleteByRuleIdAndArgIdIsNull(ruleId);
        Map<String, Object> paramsRuleTemplate = new HashMap<>();
        paramsRuleTemplate.put("relationId", ruleId);
        paramsRuleTemplate.put("relationType", "1");
        sjyAlertTemplateRelationMapper.deleteByRelationIdAndType(paramsRuleTemplate);
        return sjyAlertRuleMapper.deleteSjyAlertRuleByRuleId(ruleId);
    }

    @Override
    public int insertOrUpdateSjyAlertRule(SjyAlertRule sjyAlertRule) {
        if (sjyAlertRule.getRuleId() == null) {
            // 可以考虑在这里添加一些默认值的设置，如果前端没有传递 ruleId，
            // 但业务上期望这是一个全新的实体，确保其他关联ID也为空。
            // 不过，当前的 insertSjyAlertRule 和 updateSjyAlertRule 逻辑已经比较健壮。
            return this.insertSjyAlertRule(sjyAlertRule);
        } else {
            // 在调用更新之前，可以先查询一下数据库中是否存在该 ruleId 的记录，
            // 如果不存在，是抛出异常还是转为插入，取决于业务需求。
            // 当前的 updateSjyAlertRule 方法如果更新一个不存在的 ID，通常不会报错，但也不会有任何记录被更新。
            // 为保持与现有逻辑一致，直接调用 update。
            return this.updateSjyAlertRule(sjyAlertRule);
        }
    }

    @Override
    public SjyAlertRule getFloodBaseRule() {

        return    sjyAlertRuleMapper.getFloodBaseRule( );

    }

    @Override
    public int insertOrUpdateRiver(SjyAlertRule sjyAlertRule) {
        List<String> list = Arrays.asList(sjyAlertRule.getAlertStcd().split(","));
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("Alert station codes (stcd) cannot be empty.");
        }
        sjyAlertRule.setAlertAdnm("fyhcl");
        SjyAlertRule floodBaseRule = getFloodBaseRule();
        sjyAlertRule.setParentId(Math.toIntExact(floodBaseRule.getRuleId()));
        // 先查询是否存在有对应 stcd 的规则
        sjyAlertRuleMapper.selectSjyAlertRuleByStcd(list).forEach(existingRule -> {
            // 如果存在，更新该规则
            sjyAlertRule.setRuleId(existingRule.getRuleId());
            updateSjyAlertRule(sjyAlertRule);
            list.remove(existingRule.getAlertStcd());
        });
        if(list!=null && !list.isEmpty()) {
            // 如果 list 还有剩余的 stcd，说明这些是新的，需要插入新的规则
            for(String stcd : list) {
                sjyAlertRule.setAlertStcd(stcd);
                insertSjyAlertRule(sjyAlertRule);
            }
        }
        return 1;
    }

    @Override
    public List<SjyAlertRule> selectSjyAlertRuleByRuleType(Long ruleTypeId) {
        // 1. 查询基础的rule和arg数据
        List<SjyAlertRule> rules = sjyAlertRuleMapper.selectSjyAlertRuleByRuleType(ruleTypeId);
        
        // 2. 填充节点和模板关系数据
        fillNodeAndTemplateRelations(rules);
        
        return rules;
    }

    @Override
    public List<SjyAlertRule> selectSimpleRuleList(SjyAlertRule queryRule) {
        return sjyAlertRuleMapper.selectSimpleRuleList(queryRule);
    }

    /**
     * 处理 nodeId 前缀，将带前缀的 nodeId 分解为纯净的 nodeId 和 relationType
     *
     * @param relation 节点关系对象
     */
    private void processNodeIdPrefix(SjyAlertRuleNodeRelation relation) {
        String nodeId = relation.getNodeId();
        if (nodeId != null && nodeId.contains("_")) {
            // 检查是否包含支持的前缀
            if (nodeId.startsWith("g_") || nodeId.startsWith("o_") ||
                nodeId.startsWith("v_") || nodeId.startsWith("p_")) {

                // 提取前缀（去掉下划线）
                String prefix = nodeId.substring(0, nodeId.indexOf("_"));
                // 提取纯净的 nodeId（去掉前缀和下划线）
                String cleanNodeId = nodeId.substring(nodeId.indexOf("_") + 1);

                // 设置纯净的 nodeId
                relation.setNodeId(cleanNodeId);
                // 设置 relationType 为前缀
                relation.setRelationType(prefix);
            }
        }
    }

    /**
     * 重新组装完整的带前缀的 nodeId
     *
     * @param relation 节点关系对象
     */
    private void assembleFullNodeId(SjyAlertRuleNodeRelation relation) {
        String nodeId = relation.getNodeId();
        String relationType = relation.getRelationType();

        if (nodeId != null && relationType != null &&
            (relationType.equals("g") || relationType.equals("o") ||
             relationType.equals("v") || relationType.equals("p"))) {
            // 重新组装完整的 nodeId
            relation.setNodeId(relationType + "_" + nodeId);
        }
    }
    @Override
    public int deleteSjyAlertRuleByRuleIds(Long[] ruleIds) {
        // For full cascading delete, iterate and call deleteSjyAlertRuleByRuleId for each.
        // Or implement a more complex batch delete in SQL if performance is critical for many rules.
        // Current implementation only deletes main rules if not enhanced.
        // For safety and to use the transactional single delete:
        int count = 0;
        if (ruleIds != null) {
            for (Long ruleId : ruleIds) {
                count += deleteSjyAlertRuleByRuleId(ruleId); // This is transactional per rule
            }
        }
        return count;
        // return sjyAlertRuleMapper.deleteSjyAlertRuleByRuleIds(ruleIds); // Original simple delete
    }
    // return sjyAlertRuleMapper.deleteSjyAlertRuleByRuleIds(ruleIds); // Original simple delete
}




