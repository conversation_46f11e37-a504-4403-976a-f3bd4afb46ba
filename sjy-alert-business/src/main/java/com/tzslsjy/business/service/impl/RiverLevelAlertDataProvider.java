package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyPjStMapper;
import com.tzslsjy.business.mapper.StRiverRMapper;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.mapper.SjyAlertStSituationHisMapper;
import com.tzslsjy.business.service.AlertDataProvider;
import com.tzslsjy.business.service.ContinuousAlertService;
import com.tzslsjy.business.service.ISjyAlertStSituationService;
import com.tzslsjy.business.vo.RiverReqVo;
import com.tzslsjy.business.vo.RiverVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 河道水位预警数据提供者
 */
@Component
@Slf4j
public class RiverLevelAlertDataProvider implements AlertDataProvider {

    @Autowired
    private StRiverRMapper stRiverRMapper;
    @Autowired
    private StStbprpBMapper stStbprpBMapper;
    @Autowired
    private SjyPjStMapper sjyPjStMapper;
    @Autowired
    private SjyAlertStSituationHisMapper sjyAlertStSituationHisMapper;
    @Autowired
    private ContinuousAlertService continuousAlertService;

    @Autowired
    private ISjyAlertStSituationService alertStSituationService;
    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("15");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date currentTime) {
        log.debug("开始获取河道水位预警数据，规则类型: {}, 当前时间: {}", ruleTypeCode, currentTime);
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> triggeredRivers = new ArrayList<>();

        Date thisYearStart = DateUtil.beginOfYear(currentTime);
        @SuppressWarnings("unchecked")
        List<SjyRiverLevelAlertParamData> riverLevelParamsList = 
                (List<SjyRiverLevelAlertParamData>) queryParams.get("riverLevelAlertParams");
        @SuppressWarnings("unchecked")
        List<String> stcds = (List<String>) queryParams.get("stcds");

        if (riverLevelParamsList == null || riverLevelParamsList.isEmpty()) {
            log.warn("河道水位预警参数为空");
            result.put("triggeredRiverGroups", Collections.emptyMap());
            result.put("argId", queryParams.get("argId"));
            addStandardFields(ruleTypeCode, result);
            return result;
        }

        if (stcds == null || stcds.isEmpty()) {
            log.warn("测站编码列表为空");
        }
        List<SjyPjSt> sjyPjSts = sjyPjStMapper.getList();
        Map<String, String> pjcdMap = sjyPjSts.stream().collect(Collectors.toMap(
                SjyPjSt::getStcd,
                SjyPjSt::getPjcd
        ));
        // 获取测站基础信息
        RiverReqVo riverReqVo = new RiverReqVo();
        List<RiverVo> stationInfoList = stStbprpBMapper.getRvInfo( riverReqVo);
        Map<String, RiverVo> stationInfoMap = new HashMap<>();
        for (RiverVo station : stationInfoList) {
            stationInfoMap.put(station.getStcd(), station);
        }

        for (SjyRiverLevelAlertParamData currentParam : riverLevelParamsList) {
            Integer val = currentParam.getVal();
            if (val == null || val <= 0) {
                log.warn("阈值未设置或无效，跳过当前参数: {}", currentParam);
                continue;
            }
            BigDecimal threshold = new BigDecimal(val); // 水位阈值

            for (RiverVo stationInfo : stationInfoList) {
                try {
                    stationInfo.setPjcd(pjcdMap.get(stationInfo.getStcd()));
                    String stcd = stationInfo.getStcd();
                    String stnm = (stationInfo != null && stationInfo.getStnm() != null) ? stationInfo.getStnm() : stcd;
                    String addvcdNm = getAddvcdNm(stationInfo);

                    // 获取河道最新水位数据
                    StRiverR latestRiverR = stRiverRMapper.selectLastestStRiverRBySTCD(stcd, thisYearStart, currentTime);
                    BigDecimal wrz = stationInfo.getWrz();
                    if (wrz == null) {
                        log.warn("河道 {} (测站 {}) 的汛限水位未设置，跳过", stationInfo.getStnm(), stationInfo.getStcd());
                        continue;
                    }
                    if (latestRiverR == null || latestRiverR.getZ() == null) {
                        log.warn("未查询到河道 {} (测站 {}) 的实时水位信息", stnm, stcd);
                        continue;
                    }

                    BigDecimal currentZ = latestRiverR.getZ();
                    if (currentZ.compareTo(wrz) > 0) {
                        BigDecimal overWrz = currentZ.subtract(wrz).setScale(2, RoundingMode.HALF_UP);
                        log.info("河道 {} ({}) 超警戒: {} > {}，超 {} 米", stnm, stcd, currentZ, threshold, overWrz);
                        if( overWrz.compareTo(threshold) > 0) {
                            log.info("河道 {} ({}) 超过阈值 {} 米，检查是否需要发送预警", stnm, stcd, threshold);

                            // 检查是否需要发送预警（防止重复发送）
                            Long ruleId = (Long) queryParams.get("ruleId");
                            boolean shouldAlert = shouldSendRiverAlert(stcd, ruleId, currentZ, wrz, currentTime);

                            if (shouldAlert) {
                                log.info("河道 {} ({}) 需要发送预警，添加到触发列表", stnm, stcd);
                                // 添加到触发列表
                                addTriggeredRiver(triggeredRivers, stcd, stnm, addvcdNm, latestRiverR.getTM(), currentZ, threshold, overWrz, stationInfo.getAddvcd9(), pjcdMap);

                                // 更新当前预警状态表
                                updateRiverAlertStatus(stcd, ruleId, currentZ, wrz, latestRiverR.getTM(), stnm, overWrz);
                            } else {
                                log.info("河道 {} ({}) 已在预警状态且非提醒时间，跳过发送", stnm, stcd);
                            }
                        }else {
                            log.info("河道 {} ({}) 超过警戒水位但未超过阈值 {} 米，跳过", stnm, stcd, threshold);
                        }
                    }
                } catch (Exception e) {
                    log.error("处理河道 {} 水位失败: {}", stationInfo.getStnm(), e.getMessage(), e);
                }
            }

            result.put("alertLevel", currentParam.getAlertLevel());
        }

        // 按行政区划分组
        Map<String, List<Map<String, Object>>> groupsByJurisdiction = groupByJurisdiction(triggeredRivers);
        
        // 按工程编码分组
        Map<String, List<Map<String, Object>>> groupsByPjcd = groupByPjcd(triggeredRivers);
        
        result.put("triggeredRivers", triggeredRivers); // 保留原始数据格式以兼容
        result.put("triggeredRiverGroups", groupsByJurisdiction); // 按行政区划分组数据
        result.put("triggeredRiverGroupsByPjcd", groupsByPjcd); // 按工程编码分组数据
        result.put("argId", queryParams.get("argId"));
        
        // 恢复正常状态：将不再超警的测站状态更新为正常
        Long ruleId = (Long) queryParams.get("ruleId");
        if (ruleId != null) {
            try {
                List<String> currentTriggeredStcds = triggeredRivers.stream()
                        .map(river -> (String) river.get("stcd"))
                        .collect(Collectors.toList());
                
                int restoredCount = continuousAlertService.restoreNormalStations(ruleId, currentTriggeredStcds, "河道");
                if (restoredCount > 0) {
                    log.info("河道预警规则 {} 恢复 {} 个测站为正常状态", ruleId, restoredCount);
                }
            } catch (Exception e) {
                log.error("恢复河道测站正常状态失败: {}", e.getMessage(), e);
            }
        }
        
        addStandardFields(ruleTypeCode, result);
        log.info("河道水位预警数据获取完成，共 {} 个测站触发预警", triggeredRivers.size());
        return result;
    }

    /**
     * 检查是否应该发送河道水位预警
     * 防止重复发送，只在以下情况发送：
     * 1. 首次超警（状态变化）
     * 2. 每日8AM提醒（对于仍在预警状态的测站）
     */
    private boolean shouldSendRiverAlert(String stcd, Long ruleId, BigDecimal currentLevel, BigDecimal warningLevel, Date currentTime) {
        if (ruleId == null) {
            return true; // 如果没有规则ID，默认发送
        }

        try {
            // 检查是否为8AM提醒时间
            boolean is8AMReminder = is8AMReminderTime(currentTime);

            // 确定当前预警状态
            Integer newStatus = determineRiverAlertStatus(currentLevel, warningLevel);

            // 获取现有预警情况
            SjyAlertStSituation existing = alertStSituationService.selectByStcdAndRelation(
                    stcd, ruleId.intValue(), 2); // 2=规则实例

            if (existing == null) {
                // 新预警，需要发送
                log.debug("河道测站 {} 首次超警，需要发送预警", stcd);
                return newStatus != null && newStatus > 0;
            }

            if (is8AMReminder && newStatus != null && newStatus > 0) {
                // 8AM提醒时间，对于仍在预警状态的测站发送提醒
                log.debug("河道测站 {} 8AM提醒时间，发送持续预警提醒", stcd);
                return true;
            }

            // 检查状态是否发生变化
            boolean statusChanged = !existing.getStatus().equals(newStatus);
            if (statusChanged && newStatus != null && newStatus > 0) {
                // 状态变化且为预警状态，需要发送
                log.debug("河道测站 {} 预警状态发生变化: {} -> {}，需要发送预警", stcd, existing.getStatus(), newStatus);
                return true;
            }

            // 其他情况不发送
            log.debug("河道测站 {} 预警状态未变化且非提醒时间，跳过发送", stcd);
            return false;

        } catch (Exception e) {
            log.error("检查河道测站 {} 预警发送条件失败: {}", stcd, e.getMessage(), e);
            return true; // 出错时默认发送，避免漏报
        }
    }

    /**
     * 检查是否为8AM提醒时间
     */
    private boolean is8AMReminderTime(Date currentTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentTime);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        return hour == 8;
    }

    /**
     * 确定河道预警状态
     */
    private Integer determineRiverAlertStatus(BigDecimal currentLevel, BigDecimal warningLevel) {
        if (currentLevel == null || warningLevel == null) {
            return 0; // 正常
        }

        if (currentLevel.compareTo(warningLevel) > 0) {
            return 1; // 超警戒
        }

        return 0; // 正常
    }

    /**
     * 更新河道预警状态到数据库
     * 同时更新当前状态表和插入历史记录
     */
    private void updateRiverAlertStatus(String stcd, Long ruleId, BigDecimal currentLevel, BigDecimal warningLevel,
                                       Date alertTime, String stnm, BigDecimal overWrz) {
        if (ruleId == null) {
            return;
        }

        try {
            // 确定预警状态
            Integer newStatus = determineRiverAlertStatus(currentLevel, warningLevel);

            // 更新当前状态表 (sjyAlertStSituation)
            boolean statusChanged = alertStSituationService.updateStationAlertStatus(
                    stcd, ruleId.intValue(), 2, newStatus, "system"); // 2=规则实例

            // 插入历史记录 (sjyAlertStSituationHis) - 只在状态变化时插入
            if (statusChanged && newStatus > 0) {
                insertRiverAlertHistory(stcd, newStatus, alertTime, stnm, overWrz);
                log.info("河道测站 {} 预警状态已更新: 状态={}, 超警戒={}米", stcd, newStatus, overWrz);
            }

        } catch (Exception e) {
            log.error("更新河道测站 {} 预警状态失败: {}", stcd, e.getMessage(), e);
        }
    }

    /**
     * 插入河道预警历史记录
     */
    private void insertRiverAlertHistory(String stcd, Integer status, Date alertTime, String stnm, BigDecimal overWrz) {
        try {
            SjyAlertStSituationHis historyRecord = new SjyAlertStSituationHis();
            historyRecord.setStcd(stcd);
            historyRecord.setCType(2); // 关联类型：2-规则实例
            historyRecord.setStatus(status); // 状态：1-超警戒，2-超保证
            historyRecord.setCId(17); // 河道水位预警类型ID
            historyRecord.setCreateTime(new Date());
            historyRecord.setStartTime(alertTime);
            historyRecord.setEndTime(alertTime);
            historyRecord.setRemark("河道水位预警触发: " + stnm + " 超警戒 " + overWrz + "米");

            sjyAlertStSituationHisMapper.insertSjyAlertStSituationHis(historyRecord);
            log.debug("河道预警历史记录已保存: 测站={}, 状态={}", stcd, status);
        } catch (Exception e) {
            log.error("保存河道预警历史记录失败: 测站={}, 错误={}", stcd, e.getMessage(), e);
        }
    }

    /**
     * 按行政区划分组
     * @param triggeredRivers 触发的河道列表
     * @return 按行政区划分组的结果
     */
    private Map<String, List<Map<String, Object>>> groupByJurisdiction(List<Map<String, Object>> triggeredRivers) {
        Map<String, List<Map<String, Object>>> groups = new HashMap<>();
        
        for (Map<String, Object> river : triggeredRivers) {
            String jurisdiction = (String) river.get("addvcd");
            if (jurisdiction == null || jurisdiction.length() < 6) {
                continue; // 跳过无效的行政区划
            }
            
            // 9位行政区划分组
            String jurisdiction9 = jurisdiction.length() >= 9 ? jurisdiction.substring(0, 9) : jurisdiction;
            String groupKey9 = "9_" + jurisdiction9; // 前缀区分9位和6位
            groups.computeIfAbsent(groupKey9, k -> new ArrayList<>()).add(river);
            
            // 6位行政区划分组
            String jurisdiction6 = jurisdiction.substring(0, 6);
            String groupKey6 = "6_" + jurisdiction6; // 前缀区分9位和6位
            groups.computeIfAbsent(groupKey6, k -> new ArrayList<>()).add(river);
        }
        
        log.info("河道水位预警按行政区划分组完成，共分为 {} 组", groups.size());
        return groups;
    }

    /**
     * 按工程编码分组
     * @param triggeredRivers 触发的河道列表
     * @return 按工程编码分组的结果
     */
    private Map<String, List<Map<String, Object>>> groupByPjcd(List<Map<String, Object>> triggeredRivers) {
        Map<String, List<Map<String, Object>>> groups = new HashMap<>();
        
        for (Map<String, Object> river : triggeredRivers) {
            String pjcd = (String) river.get("pjcd");
            if (pjcd == null || pjcd.trim().isEmpty()) {
                log.warn("测站 {} 的工程编码为空，跳过按工程编码分组", river.get("stcd"));
                continue; // 跳过无效的工程编码
            }
            
            String groupKey = "pjcd_" + pjcd; // 使用pjcd_前缀区分
            groups.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(river);
        }
        
        log.info("河道水位预警按工程编码分组完成，共分为 {} 组", groups.size());
        return groups;
    }

    private void addTriggeredRiver(List<Map<String, Object>> list, String stcd, String stnm, String addvcdNm,
                                   Date tm, BigDecimal z, BigDecimal threshold, BigDecimal overWrz, String addvcd,
                                   Map<String, String> pjcdMap) {
        // 注意：历史记录和状态更新现在在 updateRiverAlertStatus 方法中处理

        // 获取该测站的工程编码
        String pjcd = pjcdMap.get(stcd);

        Map<String, Object> riverData = new HashMap<>();
        riverData.put("stcd", stcd);
        riverData.put("stnm", stnm);
        riverData.put("addvcdNm", addvcdNm);
        riverData.put("tm", DateUtil.format(tm, "yyyy/MM/dd HH:mm:ss"));
        riverData.put("z", z);
        riverData.put("threshold", threshold);
        riverData.put("overWrz", overWrz);
        riverData.put("addvcd", addvcd); // 添加行政区划信息
        riverData.put("pjcd", pjcd); // 添加工程编码信息
        list.add(riverData);
    }
    
    private String getAddvcdNm(RiverVo stationInfo) {
        if (stationInfo == null) return "未知区域";
        
        String addvcd = stationInfo.getAddvcd();
        if (addvcd == null) return "未知区域";
        
        // 根据行政区划编码返回区域名称
        if (addvcd.startsWith("331082001")) return "白水洋镇"; 
        if (addvcd.startsWith("331082")) return "临海市";
        if (addvcd.startsWith("3310")) return "台州市";
        if (addvcd.startsWith("33")) return "浙江省";
        
        return addvcd; // 默认返回编码
    }

    private void addStandardFields(String ruleTypeCode, Map<String, Object> result) {
        result.put("dataType", "15"); 
        result.put("ruleType", ruleTypeCode);
        result.put("queryTime", new Date());
    }
} 