package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.service.AlertRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 测站异常预警规则策略
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Slf4j
@Component
public class StationAbnormalStrategy implements AlertRuleStrategy {

    @Override
    public boolean evaluate(AlertContext context) {
        log.debug("开始评估测站异常预警规则");

        try {
            Map<String, Object> data = context.getData();
            if (data == null) {
                log.warn("测站异常预警数据为空");
                return false;
            }

            // 获取参数结果
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");
            if (argResults == null || argResults.isEmpty()) {
                log.warn("测站异常预警参数结果为空");
                return false;
            }

            // 检查是否有任何异常被触发
            boolean hasAbnormal = false;
            for (Map<String, Object> argResult : argResults) {
                Boolean alertTriggered = (Boolean) argResult.get("alertTriggered");
                if (Boolean.TRUE.equals(alertTriggered)) {
                    hasAbnormal = true;
                    
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> abnormalResults = (List<Map<String, Object>>) argResult.get("abnormalResults");
                    if (abnormalResults != null && !abnormalResults.isEmpty()) {
                        log.info("检测到测站异常，异常数量: {}", abnormalResults.size());
                        
                        // 记录异常详情
                        for (Map<String, Object> abnormal : abnormalResults) {
                            String stcd = (String) abnormal.get("stcd");
                            String type = (String) abnormal.get("abnormalType");
                            String message = (String) abnormal.get("message");
                            log.info("异常详情 - 测站: {}, 类型: {}, 描述: {}", stcd, type, message);
                        }
                    }
                }
            }

            if (hasAbnormal) {
                log.info("测站异常预警规则评估通过，触发预警发送");
                return true;
            } else {
                log.debug("测站异常预警规则评估未通过，无异常情况");
                return false;
            }

        } catch (Exception e) {
            log.error("评估测站异常预警规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getType() {
        return "53"; // 对应测站异常预警类型
    }
}
