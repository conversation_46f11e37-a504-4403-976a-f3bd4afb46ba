package com.tzslsjy.business.service.impl;

import java.util.List;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyAlertRuleVariableMapper;
import com.tzslsjy.business.domain.SjyAlertRuleVariable;
import com.tzslsjy.business.service.ISjyAlertRuleVariableService;

/**
 * 规则标签关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SjyAlertRuleVariableServiceImpl implements ISjyAlertRuleVariableService
{
    @Autowired
    private SjyAlertRuleVariableMapper sjyAlertRuleVariableMapper;

    /**
     * 查询规则标签关联
     *
     * @param ruleVariableId 规则标签关联主键
     * @return 规则标签关联
     */
    @Override
    public SjyAlertRuleVariable selectSjyAlertRuleVariableByRuleVariableId(Long ruleVariableId)
    {
        return sjyAlertRuleVariableMapper.selectSjyAlertRuleVariableByRuleVariableId(ruleVariableId);
    }

    /**
     * 查询规则标签关联列表
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 规则标签关联
     */
    @Override
    public List<SjyAlertRuleVariable> selectSjyAlertRuleVariableList(SjyAlertRuleVariable sjyAlertRuleVariable)
    {
        return sjyAlertRuleVariableMapper.selectSjyAlertRuleVariableList(sjyAlertRuleVariable);
    }

    /**
     * 新增规则标签关联
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 结果
     */
    @Override
    public int insertSjyAlertRuleVariable(SjyAlertRuleVariable sjyAlertRuleVariable)
    {
        sjyAlertRuleVariable.setCreateTime(DateUtils.getNowDate());
        return sjyAlertRuleVariableMapper.insertSjyAlertRuleVariable(sjyAlertRuleVariable);
    }

    /**
     * 修改规则标签关联
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 结果
     */
    @Override
    public int updateSjyAlertRuleVariable(SjyAlertRuleVariable sjyAlertRuleVariable)
    {
        sjyAlertRuleVariable.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertRuleVariableMapper.updateSjyAlertRuleVariable(sjyAlertRuleVariable);
    }

    /**
     * 批量删除规则标签关联
     *
     * @param ruleVariableIds 需要删除的规则标签关联主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertRuleVariableByRuleVariableIds(Long[] ruleVariableIds)
    {
        return sjyAlertRuleVariableMapper.deleteSjyAlertRuleVariableByRuleVariableIds(ruleVariableIds);
    }

    /**
     * 删除规则标签关联信息
     *
     * @param ruleVariableId 规则标签关联主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertRuleVariableByRuleVariableId(Long ruleVariableId)
    {
        return sjyAlertRuleVariableMapper.deleteSjyAlertRuleVariableByRuleVariableId(ruleVariableId);
    }
}
