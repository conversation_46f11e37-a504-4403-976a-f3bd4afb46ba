package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.data.StRsvrR;
import com.baomidou.mybatisplus.extension.service.IService;
import com.tzslsjy.business.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 水库水情Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IStRsvrRService extends IService<StRsvrR> {


    List<ReservoirVo> rsWithRz(ReservoirReqVo vo);


}