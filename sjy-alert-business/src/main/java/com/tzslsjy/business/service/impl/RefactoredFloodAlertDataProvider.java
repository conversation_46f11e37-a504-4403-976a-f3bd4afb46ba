package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.*;
import com.tzslsjy.business.service.AlertDataProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 重构后的山洪预警数据提供者
 * 职责单一：只负责根据解析后的查询参数获取预警数据
 */
@Component("refactoredFloodAlertDataProvider")
@Slf4j
public class RefactoredFloodAlertDataProvider implements AlertDataProvider {

    @Autowired
    private SjyFdStInfoMapper sjyFdStInfoMapper;
    @Autowired
    private SjyFdStObjMapper sjyFdStObjMapper;
    @Autowired
    private SjyFdProneAreaMapper sjyFdProneAreaMapper;
    @Autowired
    private StPptnRMapper stPptnRMapper;
    @Autowired
    private StRiverRMapper stRiverRMapper;

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("1");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date tm) {
        log.debug("开始获取山洪预警数据，规则类型: {}", ruleTypeCode);

        Map<String, Object> result = new HashMap<>();
        
        try {
            // 验证必要参数
            validateRequiredParams(queryParams);

            // 获取山洪预警参数数据
            @SuppressWarnings("unchecked")
            List<SjyAlertFloodParamData> floodParams = (List<SjyAlertFloodParamData>) queryParams.get("floodParams");
            
            if (floodParams == null || floodParams.isEmpty()) {
                log.warn("未找到山洪预警参数数据");
                return result;
            }

            // 获取测站编码
            String stcd = (String) queryParams.get("stcd");
            if (stcd == null || stcd.isEmpty()) {
                log.warn("未指定测站编码");
                return result;
            }

            // 获取监测数据
            Map<String, Object> monitoringData = fetchMonitoringData(queryParams, floodParams, stcd, tm);
            
            // 获取测站数据
            fetchStationData(queryParams, monitoringData, stcd);

            // 获取村庄数据
            fetchVillageData(queryParams, monitoringData);

            result.put("alertData", monitoringData);
            addStandardFields(ruleTypeCode, queryParams, result);

        } catch (Exception e) {
            handleDataFetchException(e, result);
        }

        return result;
    }

    /**
     * 验证必要参数
     */
    private void validateRequiredParams(Map<String, Object> queryParams) {
        if (!queryParams.containsKey("stcd") || queryParams.get("stcd") == null) {
            throw new IllegalArgumentException("缺少必要参数: stcd");
        }
        if (!queryParams.containsKey("floodParams")) {
            throw new IllegalArgumentException("缺少山洪预警参数数据");
        }
    }

    /**
     * 获取监测数据
     */
    private Map<String, Object> fetchMonitoringData(Map<String, Object> queryParams, 
                                                   List<SjyAlertFloodParamData> floodParams,
                                                   String stcd, Date tm) {
        List<Map<String, Object>> rainlist = new ArrayList<>();
        List<Map<String, Object>> waterList = new ArrayList<>();
        Map<String, Object> result = new HashMap<>();
        
        for (SjyAlertFloodParamData paramData : floodParams) {
            // 处理水位数据
            if (paramData.getOverWaterValue() != null) {
                Map<String, Object> waterData = fetchWaterLevelData(stcd, tm, new BigDecimal(paramData.getOverWaterValue()));
                if (waterData.containsKey("status") && (int) waterData.get("status") == 1) {
                    waterList.add(waterData);
                }
            }

            // 处理雨量数据
            if (paramData.getOverRainList() != null && !paramData.getOverRainList().isEmpty()) {
                List<SjyFloodAlertRainParams> rainParams = fetchRainfallData(stcd, tm, paramData.getOverRainList());
                if (rainParams != null && !rainParams.isEmpty()) {
                    for (SjyFloodAlertRainParams rain : rainParams) {
                        Map<String, Object> rainData = convertRainParamsToMap(rain);
                        rainlist.add(rainData);
                    }
                }
            }
            
            result.put("alertLevel", paramData.getAlertLevel());
            result.put("argId", paramData.getArgId());
            result.put("tm", tm);
        }
        
        if (!rainlist.isEmpty()) {
            result.put("rainlist", rainlist);
        }
        if (!waterList.isEmpty()) {
            result.put("waterlist", waterList);
        }
        
        return result;
    }

    /**
     * 获取降雨数据
     */
    private List<SjyFloodAlertRainParams> fetchRainfallData(String stationId, Date endTm, List<SjyFloodRainWarningItem> overRainList) {
        List<SjyFloodAlertRainParams> result = new ArrayList<>();
        Date startTm = DateUtil.offsetDay(endTm, -1);
        
        try {
            for (SjyFloodRainWarningItem item : overRainList) {
                List<StPptnR> rains = stPptnRMapper.selectLastPPtnByTm(stationId, item.getDuration() / 5, startTm, endTm);
                if (rains != null && !rains.isEmpty()) {
                    BigDecimal totalRainfall = rains.stream()
                            .map(StPptnR::getDRP)
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    
                    if (totalRainfall.compareTo(new BigDecimal(item.getThreshold())) > 0) {
                        SjyFloodAlertRainParams rainParam = new SjyFloodAlertRainParams();
                        rainParam.setThreshold(new BigDecimal(item.getThreshold()));
                        rainParam.setDrp(totalRainfall);
                        rainParam.setStcd(stationId);
                        rainParam.setDuration(item.getDuration());
                        rainParam.setStartTm(rains.get(0).getTM());
                        rainParam.setEndTm(rains.get(rains.size() - 1).getTM());
                        result.add(rainParam);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取降雨数据失败: {}", e.getMessage(), e);
        }
        
        return result;
    }

    /**
     * 获取水位数据
     */
    private Map<String, Object> fetchWaterLevelData(String stationId, Date endTm, BigDecimal overWaterValue) {
        Map<String, Object> result = new HashMap<>();
        Date startTm = DateUtil.offsetDay(endTm, -1);
        
        try {
            StRiverR riverR = stRiverRMapper.selectLastestStRiverRBySTCD(stationId, startTm, endTm);
            
            if (riverR != null && riverR.getZ().compareTo(overWaterValue) > 0) {
                result.put("status", 1);
                result.put("tm", riverR.getTM());
                result.put("waterLevel", riverR.getZ());
                result.put("threshold", overWaterValue);
            } else {
                result.put("status", 0);
            }
        } catch (Exception e) {
            log.error("获取水位数据失败: {}", e.getMessage(), e);
            result.put("status", 0);
        }
        
        return result;
    }

    /**
     * 获取测站数据
     */
    private void fetchStationData(Map<String, Object> queryParams, Map<String, Object> monitoringData, String stcd) {
        try {
            SjyFdStObj stationObj = sjyFdStObjMapper.selectOneByStcd(stcd);
            if (stationObj != null) {
                monitoringData.put("stationInfo", stationObj);
                monitoringData.put("stationName", stationObj.getStName());
            }
        } catch (Exception e) {
            log.error("获取测站数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取村庄数据
     */
    private void fetchVillageData(Map<String, Object> queryParams, Map<String, Object> monitoringData) {
        try {
            @SuppressWarnings("unchecked")
            List<String> villageIds = (List<String>) queryParams.get("villageIds");
            if (villageIds != null && !villageIds.isEmpty()) {
                // 这里可以根据需要查询村庄信息
                List<SjyFdProneArea> villages = new ArrayList<>();
                for (String villageId : villageIds) {
                    SjyFdProneArea village = sjyFdProneAreaMapper.selectSjyFdProneAreaByPrevCode(villageId);
                    if (village != null) {
                        villages.add(village);
                    }
                }
                monitoringData.put("villages", villages);
            }
        } catch (Exception e) {
            log.error("获取村庄数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 转换雨量参数为Map
     */
    private Map<String, Object> convertRainParamsToMap(SjyFloodAlertRainParams rain) {
        Map<String, Object> rainData = new HashMap<>();
        rainData.put("startTm", DateUtil.format(rain.getStartTm(), "yyyy-MM-dd HH:mm:ss"));
        rainData.put("endTm", DateUtil.format(rain.getEndTm(), "yyyy-MM-dd HH:mm:ss"));
        rainData.put("duringTm", DateUtil.between(rain.getStartTm(), rain.getEndTm(), DateUnit.MINUTE) + "分钟");
        rainData.put("drp", rain.getDrp());
        rainData.put("threshold", rain.getThreshold());
        rainData.put("duration", rain.getDuration());
        rainData.put("stcd", rain.getStcd());
        return rainData;
    }

    /**
     * 添加标准字段
     */
    private void addStandardFields(String ruleTypeCode, Map<String, Object> queryParams, Map<String, Object> result) {
        result.put("dataType", "1");
        result.put("ruleType", ruleTypeCode);
        result.put("queryTime", new Date());
    }

    /**
     * 处理数据获取异常
     */
    private void handleDataFetchException(Exception e, Map<String, Object> result) {
        log.error("获取山洪预警数据失败: {}", e.getMessage(), e);
        result.put("error", true);
        result.put("errorMessage", e.getMessage());
        throw new RuntimeException("获取山洪预警数据失败", e);
    }
} 