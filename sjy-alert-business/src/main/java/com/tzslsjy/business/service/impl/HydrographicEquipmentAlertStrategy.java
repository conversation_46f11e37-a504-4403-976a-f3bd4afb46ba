package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.service.AlertRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 水文设备预警规则策略
 */
@Slf4j
@Component
public class HydrographicEquipmentAlertStrategy implements AlertRuleStrategy {

    @Override
    public boolean evaluate(AlertContext context) {
        log.debug("开始评估水文设备预警规则");

        try {
            Map<String, Object> data = context.getData();
            if (data == null || !data.containsKey("argResults") || !(data.get("argResults") instanceof List)) {
                log.warn("预警数据格式不正确或缺少argResults");
                return false;
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");

            if (argResults.isEmpty()) {
                log.warn("参数组结果为空，无法评估");
                return false;
            }

            // 只要有一个参数组的数据包含triggeredEquipments且不为空，就认为触发
            for (Map<String, Object> argResult : argResults) {
                if (argResult.containsKey("triggeredEquipments")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> triggeredEquipments = 
                            (List<Map<String, Object>>) argResult.get("triggeredEquipments");
                    if (triggeredEquipments != null && !triggeredEquipments.isEmpty()) {
                        log.info("水文设备预警触发，参数组ID: {}", argResult.get("argId"));
                        return true;
                    }
                }
            }

            log.debug("水文设备预警未触发");
            return false;

        } catch (Exception e) {
            log.error("评估水文设备预警规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getType() {
        return "18"; // 对应水文设备预警类型
    }
} 