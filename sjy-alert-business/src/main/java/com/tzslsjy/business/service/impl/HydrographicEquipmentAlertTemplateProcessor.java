package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 水文设备预警模板处理器
 */
@Component
@Slf4j
public class HydrographicEquipmentAlertTemplateProcessor implements AlertTemplateProcessor {

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate smsTemplate, Map<String, Object> data) {
        log.debug("处理水文设备预警模板，模板ID: {}, 数据: {}", smsTemplate.getTemplateId(), data);
        try {
            // 添加设备预警特定数据，供模板引擎使用
            // triggeredEquipments 列表已经在data中，DefaultAlertTemplateProcessor会处理 {list} 占位符
            // 模板内容示例: "【设备异常】截止${currentTime}，{list}请及时处理。"
            // 模板列表项格式示例: "${equipmentName}(${equipmentCode})已经超过${timeoutHours}小时没有上传数据(${reason})"
            
            // 如果需要，可以在这里进一步处理data，例如添加一些概括性信息
            // data.put("totalTimeoutCount", ((List<?>)data.getOrDefault("triggeredEquipments", new ArrayList<>())).size());

            return defaultTemplateProcessor.processTemplate(context, smsTemplate, data);
        } catch (Exception e) {
            log.error("处理水文设备预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> people) {
        return defaultTemplateProcessor.mergeAlerts(context, alert, people);
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }
} 