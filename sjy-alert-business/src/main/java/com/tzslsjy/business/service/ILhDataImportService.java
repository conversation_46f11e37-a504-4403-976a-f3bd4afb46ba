package com.tzslsjy.business.service;

import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;

/**
 * 临海节点数据导入服务接口
 *
 * <AUTHOR>
 */
public interface ILhDataImportService {

    /**
     * 从上传的JSON文件导入临海节点、人员、岗位及关联数据
     *
     * @param file 上传的JSON文件
     * @throws IOException 文件读取或解析异常
     * @throws IllegalArgumentException 参数错误或数据校验失败
     */
    void importLhJsonData(MultipartFile file) throws IOException, IllegalArgumentException;
}