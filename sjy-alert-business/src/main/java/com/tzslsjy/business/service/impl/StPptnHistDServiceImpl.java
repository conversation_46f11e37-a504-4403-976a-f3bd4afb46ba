package com.tzslsjy.business.service.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.tzslsjy.business.bo.*;
import com.tzslsjy.business.domain.StPptnHistD;
import com.tzslsjy.business.mapper.StPptnHistDMapper;
import com.tzslsjy.business.mapper.StPptnRMapper;
import com.tzslsjy.business.service.IStPptnHistDService;
import com.tzslsjy.business.vo.StPptnHistDVo;
import com.tzslsjy.business.vo.RainInfoHourReqVo;
import com.tzslsjy.business.vo.RainInfoHourVo;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 单站多年日降雨Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
@Slf4j
@Service
public class StPptnHistDServiceImpl extends ServiceImpl<StPptnHistDMapper, StPptnHistD> implements IStPptnHistDService {

    @Resource
    private StPptnRMapper stPptnRMapper;

    @Override
    public StPptnHistDVo queryById(String stcd){
        StPptnHistD db = this.baseMapper.selectById(stcd);
        return BeanUtil.toBean(db, StPptnHistDVo.class);
    }

    @Override
    public List<StPptnHistDVo> queryList(StPptnHistDQueryBo bo) {
        LambdaQueryWrapper<StPptnHistD> lqw = Wrappers.lambdaQuery();
        lqw.eq(StrUtil.isNotBlank(bo.getStcd()), StPptnHistD::getStcd, bo.getStcd());
        lqw.eq(StrUtil.isNotBlank(bo.getAddvcd()), StPptnHistD::getAddvcd, bo.getAddvcd());
        lqw.eq( bo.getType()!=null, StPptnHistD::getType, bo.getType());
        lqw.eq(bo.getTm() != null, StPptnHistD::getTm, bo.getTm());
        lqw.eq(bo.getDrp() != null, StPptnHistD::getDrp, bo.getDrp());
        lqw.eq(StrUtil.isNotBlank(bo.getStnm()), StPptnHistD::getStnm, bo.getStnm());
        return entity2Vo(this.list(lqw));
    }

    /**
     * 实体类转化成视图对象
     *
     * @param collection 实体类集合
     * @return
     */
    private List<StPptnHistDVo> entity2Vo(Collection<StPptnHistD> collection) {
        Date now =DateUtil.date();
        String year = DateUtil.format(now,"yyyy");
        List<StPptnHistDVo> voList = collection.stream()
                .map(any -> BeanUtil.toBean(any, StPptnHistDVo.class))
                .collect(Collectors.toList());
        if (collection instanceof Page) {
            Page<StPptnHistD> page = (Page<StPptnHistD>)collection;
            Page<StPptnHistDVo> pageVo = new Page<>();
            BeanUtil.copyProperties(page,pageVo);


            pageVo.addAll(voList);
            voList = pageVo;
        }
        return voList;
    }

    @Override
    public Boolean insertByAddBo(StPptnHistDAddBo bo) {
        StPptnHistD add = BeanUtil.toBean(bo, StPptnHistD.class);
        validEntityBeforeSave(add);
        return this.save(add);
    }

    @Override
    public Boolean updateByEditBo(StPptnHistDEditBo bo) {
        StPptnHistD update = BeanUtil.toBean(bo, StPptnHistD.class);
        validEntityBeforeSave(update);
        return this.updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(StPptnHistD entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return this.removeByIds(ids);
    }

    @Override
    public void insertOrUpdateBatch(List<StPptnHistD> list) {
        baseMapper.insertOrUpdateBatch(list);
    }
    
    public List<StPptnHistRespVo> selectSumByMonthAndDay(StPptnHistReqVo vo){
        //查找年平均日雨量表
        StPptnHistDo stPptnHistDo = new StPptnHistDo();
        Date startTm = vo.getStartTm();
        Date endTm = vo.getEndTm();
        stPptnHistDo.setStcd(vo.getStcd());
        stPptnHistDo.setType(vo.getType());
        stPptnHistDo.setAddvcd(vo.getAddvcd());

        List<StPptnHistRespVo> rs  =   new ArrayList<>();
        // 如果开始和结束时间在同一年
        if (DateUtil.year(startTm) == DateUtil.year(endTm)) {
            stPptnHistDo.setStartDay(DateUtil.dayOfMonth(startTm));
            stPptnHistDo.setStartMonth(DateUtil.month(startTm)+1);
            stPptnHistDo.setEndDay(DateUtil.dayOfMonth(endTm));
            stPptnHistDo.setEndMonth(DateUtil.month(endTm)+1);
            rs.addAll(baseMapper.selectSumByAllMonthAndDay(stPptnHistDo));
        } else if  ( DateUtil.year(endTm)-DateUtil.year(startTm)==1){
            // 如果开始和结束时间差一年
            // 查询开始年份的剩余月份
            stPptnHistDo.setStartDay(DateUtil.dayOfMonth(startTm));
            stPptnHistDo.setStartMonth(DateUtil.month(startTm)+1);
            stPptnHistDo.setEndDay(31);
            stPptnHistDo.setEndMonth(12);
            rs.addAll(baseMapper.selectSumByAllMonthAndDay(stPptnHistDo));

            // 查询结束年份的前几个月
            stPptnHistDo.setStartDay(1);
            stPptnHistDo.setStartMonth(1);
            stPptnHistDo.setEndDay(DateUtil.dayOfMonth(endTm));
            stPptnHistDo.setEndMonth(DateUtil.month(endTm)+1);
            rs.addAll(baseMapper.selectSumByAllMonthAndDay(stPptnHistDo));
        }else{
            // 如果开始和结束时间差一年以上
            int startYear = DateUtil.year(startTm);
            int endYear = DateUtil.year(endTm);
            // 处理开始年份的剩余部分
            stPptnHistDo.setStartDay(DateUtil.dayOfMonth(startTm));
            stPptnHistDo.setStartMonth(DateUtil.month(startTm)+1);
            stPptnHistDo.setEndDay(31);
            stPptnHistDo.setEndMonth(12);
            rs.addAll(baseMapper.selectSumByAllMonthAndDay(stPptnHistDo));

            // 处理中间的完整年份
            for (int year = startYear + 1; year < endYear; year++) {
                stPptnHistDo.setStartDay(1);
                stPptnHistDo.setStartMonth(1);
                stPptnHistDo.setEndDay(31);
                stPptnHistDo.setEndMonth(12);
                rs.addAll(baseMapper.selectSumByAllMonthAndDay(stPptnHistDo));
            }

            // 处理结束年份的前部分
            stPptnHistDo.setStartDay(1);
            stPptnHistDo.setStartMonth(1);
            stPptnHistDo.setEndDay(DateUtil.dayOfMonth(endTm));
            stPptnHistDo.setEndMonth(DateUtil.month(endTm)+1);
            rs.addAll(baseMapper.selectSumByAllMonthAndDay(stPptnHistDo));

        }
        List<StPptnHistRespVo> rs1 = new ArrayList<>();
        if(vo.getType().equals(1)) {
            //相同addvcd相加
            Map<String, List<StPptnHistRespVo>> collect = rs.stream().collect(Collectors.groupingBy(StPptnHistRespVo::getAddvcd));

            for (Map.Entry<String, List<StPptnHistRespVo>> entry : collect.entrySet()) {
                StPptnHistRespVo stPptnHistRespVo = new StPptnHistRespVo();
                stPptnHistRespVo.setAddvcd(entry.getKey());
                BigDecimal sum = new BigDecimal(0);
                for (StPptnHistRespVo stPptnHistRespVo1 : entry.getValue()) {
                    sum = NumberUtil.add(sum, stPptnHistRespVo1.getSamePeriodSum());
                }
                stPptnHistRespVo.setSamePeriodSum(sum);
                stPptnHistRespVo.setStnm(entry.getValue().get(0).getStnm());
                stPptnHistRespVo.setStcd(entry.getValue().get(0).getStcd());
                rs1.add(stPptnHistRespVo);
            }
        }
        else {
            //相同stcd相加
            Map<String, List<StPptnHistRespVo>> collect = rs.stream().collect(Collectors.groupingBy(StPptnHistRespVo::getStcd));
            for (Map.Entry<String, List<StPptnHistRespVo>> entry : collect.entrySet()) {
                StPptnHistRespVo stPptnHistRespVo = new StPptnHistRespVo();

                BigDecimal sum = new BigDecimal(0);
                for (StPptnHistRespVo stPptnHistRespVo1 : entry.getValue()) {
                    sum = NumberUtil.add(sum, stPptnHistRespVo1.getSamePeriodSum());
                }
                stPptnHistRespVo.setSamePeriodSum(sum);
                stPptnHistRespVo.setAddvcd(entry.getValue().get(0).getAddvcd());
                stPptnHistRespVo.setStnm(entry.getValue().get(0).getStnm());
                stPptnHistRespVo.setStcd(entry.getValue().get(0).getStcd());
                rs1.add(stPptnHistRespVo);
            }
        }
        return rs1;
    }
    
    public List<StPptnHistRespVo> selectSumLeftByMonthAndDay(StPptnHistReqVo vo){
        //查找年平均日雨量表
        StPptnHistDo stPptnHistDo = new StPptnHistDo();
        Date startTm = vo.getStartTm();
        Date endTm = vo.getEndTm();
        stPptnHistDo.setStcd(vo.getStcd());
        stPptnHistDo.setType(vo.getType());
        stPptnHistDo.setAddvcd(vo.getAddvcd());

        List<StPptnHistRespVo> rs  =   new ArrayList<>();
        // 如果开始和结束时间在同一年
        if (DateUtil.year(startTm) == DateUtil.year(endTm)) {
            stPptnHistDo.setStartDay(DateUtil.dayOfMonth(startTm));
            stPptnHistDo.setStartMonth(DateUtil.month(startTm)+1);
            stPptnHistDo.setEndDay(DateUtil.dayOfMonth(endTm));
            stPptnHistDo.setEndMonth(DateUtil.month(endTm)+1);
            rs.addAll(baseMapper.selectSumByMonthAndDay(stPptnHistDo));
        } else if  ( DateUtil.year(endTm)-DateUtil.year(startTm)==1){
            // 如果开始和结束时间差一年
            // 查询开始年份的剩余月份
            stPptnHistDo.setStartDay(DateUtil.dayOfMonth(startTm));
            stPptnHistDo.setStartMonth(DateUtil.month(startTm)+1);
            stPptnHistDo.setEndDay(31);
            stPptnHistDo.setEndMonth(12);
            rs.addAll(baseMapper.selectSumByMonthAndDay(stPptnHistDo));

            // 查询结束年份的前几个月
            stPptnHistDo.setStartDay(1);
            stPptnHistDo.setStartMonth(1);
            stPptnHistDo.setEndDay(DateUtil.dayOfMonth(endTm));
            stPptnHistDo.setEndMonth(DateUtil.month(endTm)+1);
            rs.addAll(baseMapper.selectSumByMonthAndDay(stPptnHistDo));
        }else{
            // 如果开始和结束时间差一年以上
            int startYear = DateUtil.year(startTm);
            int endYear = DateUtil.year(endTm);
            // 处理开始年份的剩余部分
            stPptnHistDo.setStartDay(DateUtil.dayOfMonth(startTm));
            stPptnHistDo.setStartMonth(DateUtil.month(startTm)+1);
            stPptnHistDo.setEndDay(31);
            stPptnHistDo.setEndMonth(12);
            rs.addAll(baseMapper.selectSumByMonthAndDay(stPptnHistDo));

            // 处理中间的完整年份
            for (int year = startYear + 1; year < endYear; year++) {
                stPptnHistDo.setStartDay(1);
                stPptnHistDo.setStartMonth(1);
                stPptnHistDo.setEndDay(31);
                stPptnHistDo.setEndMonth(12);
                rs.addAll(baseMapper.selectSumByMonthAndDay(stPptnHistDo));
            }

            // 处理结束年份的前部分
            stPptnHistDo.setStartDay(1);
            stPptnHistDo.setStartMonth(1);
            stPptnHistDo.setEndDay(DateUtil.dayOfMonth(endTm));
            stPptnHistDo.setEndMonth(DateUtil.month(endTm)+1);
            rs.addAll(baseMapper.selectSumByMonthAndDay(stPptnHistDo));

        }
        return rs;
    }
    
    public List<StPptnHistRainLineRespVo> selectByMonthAndDay(StPptnHistReqVo vo){
        //查找年平均日雨量表
        // 初始化传感器数据对象
        StPptnHistDo stPptnHistDo = new StPptnHistDo();
        stPptnHistDo.setStcd(vo.getStcd());
        stPptnHistDo.setType(vo.getType());
        stPptnHistDo.setAddvcd(vo.getAddvcd());

        Date startTm = vo.getStartTm();
        Date endTm = vo.getEndTm();

        int startDay = DateUtil.dayOfMonth(startTm);
        int startMonth = DateUtil.month(startTm) + 1;
        int endDay = DateUtil.dayOfMonth(endTm);
        int endMonth = DateUtil.month(endTm) + 1;

        List<StPptnHistRainLineRespVo> results = new ArrayList<>();

        // 如果时间跨度不跨年
        // 如果开始和结束时间在同一年
        if (DateUtil.year(startTm) == DateUtil.year(endTm)) {
            stPptnHistDo.setStartDay(startDay);
            stPptnHistDo.setStartMonth(startMonth);
            stPptnHistDo.setEndDay(endDay);
            stPptnHistDo.setEndMonth(endMonth);

            results.addAll(baseMapper.selectByMonthAndDay(stPptnHistDo));
            results.forEach(e->e.setYear(DateUtil.year(startTm)));
        }   else if  ( DateUtil.year(endTm)-DateUtil.year(startTm)>=1){
            // 如果时间跨度跨年, 需要进行两次查询
            // 从开始月份到年末
            stPptnHistDo.setStartDay(startDay);
            stPptnHistDo.setStartMonth(startMonth);
            stPptnHistDo.setEndDay(31);
            stPptnHistDo.setEndMonth(12);
            List<StPptnHistRainLineRespVo> stPptnHistRainLineRespVos = baseMapper.selectByMonthAndDay(stPptnHistDo);
            stPptnHistRainLineRespVos.forEach(e->e.setYear(DateUtil.year(startTm)));
            results.addAll( stPptnHistRainLineRespVos);

            // 处理中间的完整年份
            if  ( DateUtil.year(endTm)-DateUtil.year(startTm)>1) {
                int startYear = DateUtil.year(startTm);
                int endYear = DateUtil.year(endTm);
                for (int year = startYear + 1; year < endYear; year++) {
                    stPptnHistDo.setStartDay(1);
                    stPptnHistDo.setStartMonth(1);
                    stPptnHistDo.setEndDay(31);
                    stPptnHistDo.setEndMonth(12);
                    List<StPptnHistRainLineRespVo> stPptnHistRainLineRespVos1 = baseMapper.selectByMonthAndDay(stPptnHistDo);
                    for(StPptnHistRainLineRespVo e:stPptnHistRainLineRespVos1){
                        e.setYear(year);}
                    results.addAll(stPptnHistRainLineRespVos1 );
                }
            }
            // 从年初到结束月份
            stPptnHistDo.setStartDay(1);
            stPptnHistDo.setStartMonth(1);
            stPptnHistDo.setEndDay(endDay);
            stPptnHistDo.setEndMonth(endMonth);
            List<StPptnHistRainLineRespVo> stPptnHistRainLineRespVos1 = baseMapper.selectByMonthAndDay(stPptnHistDo);
            stPptnHistRainLineRespVos1.forEach(e->e.setYear(DateUtil.year(endTm)));
            results.addAll(stPptnHistRainLineRespVos1 );
        }

        return results.stream().sorted(Comparator.comparing(StPptnHistRainLineRespVo::getYear).thenComparing(StPptnHistRainLineRespVo::getMonth).thenComparing(StPptnHistRainLineRespVo::getDay)).collect(Collectors.toList());
    }
    
    @Override
    public List<StPptnHistRespVo> droughtAnaly(StPptnHistReqVo vo) {
        //查找年平均日雨量表
        if(vo.getEndTm()==null){
            vo.setEndTm(DateUtil.date());
        }
        if(vo.getStartTm()==null){
            vo.setStartTm(DateUtil.beginOfYear(vo.getEndTm()));
        }
        Date startTm =DateUtil.beginOfDay( vo.getStartTm());
        Date endTm = DateUtil.endOfDay(vo.getEndTm());
        List<RainInfoHourVo> sumRain =new ArrayList<>();
        List<StPptnHistRespVo> rs = selectSumByMonthAndDay(vo);
        //查找测站表
        RainInfoHourReqVo infoHourReqVo = new RainInfoHourReqVo();
       // infoHourReqVo.setFlag(1);
        List< RainInfoHourVo> stStbprpBS = stPptnRMapper.loadPage(infoHourReqVo);
        Map<String, RainInfoHourVo> stcdMap = stStbprpBS.stream().collect(Collectors.toMap(RainInfoHourVo::getStcd, Function.identity(), (key1, key2) -> key2));
        Set<String> stcds = stcdMap.keySet();

        //查找实时日雨量表
        RainInfoHourReqVo rainInfoHourReqVo = new RainInfoHourReqVo();
        rainInfoHourReqVo.setStartTm(startTm);
        rainInfoHourReqVo.setEndTm(DateUtil.offsetHour(DateUtil.beginOfDay(DateUtil.offsetDay(endTm,1)),8));
        rainInfoHourReqVo.setStcd(vo.getStcd());
        rainInfoHourReqVo.setStcds(stcds);
        sumRain= stPptnRMapper.getSumMonthRain(rainInfoHourReqVo).stream().filter(e->e.getDrp()!=null).collect(Collectors.toList());
        //如果是今天的时间还需要查小时雨量表并累加到日雨量上
        DateTime now = DateUtil.date();
        if(DateUtil.isSameDay( now,endTm)){
            rainInfoHourReqVo.setStartTm(DateUtil.offsetHour(DateUtil.beginOfDay(now),8));
            rainInfoHourReqVo.setEndTm(DateUtil.offsetHour(now,1));

            //   与sumRain中相同测站编号累加drp值
            List<RainInfoHourVo> finalSumRain = sumRain;
            stPptnRMapper.getSumLeftRain(rainInfoHourReqVo).stream().filter(e->e.getDrp()!=null).forEach(e->{
                finalSumRain.stream().filter(e1->e1.getStcd().equals(e.getStcd())).forEach(e2->{
                    e2.setDrp(NumberUtil.add(e2.getDrp(),e.getDrp()));
                });
            });
            sumRain = finalSumRain;
        }

        if(vo.getType().equals(2)) {
            rs.stream().forEach(stPptnHistRespVo -> {
                if (stcdMap.containsKey(stPptnHistRespVo.getStcd())) {
                    RainInfoHourVo stStbprpB = stcdMap.get(stPptnHistRespVo.getStcd());
                    //stPptnHistRespVo.setStnm(stStbprpB.getStnm());
                    stPptnHistRespVo.setLgtd(stStbprpB.getLgtd());
                    stPptnHistRespVo.setLttd(stStbprpB.getLttd());
                }
            });
        }

        if(vo.getType().equals(1)) {
            sumRain.stream().forEach(stPptnHistRespVo -> {
                if (stcdMap.containsKey(stPptnHistRespVo.getStcd())) {
                    RainInfoHourVo stStbprpB = stcdMap.get(stPptnHistRespVo.getStcd());
                    stPptnHistRespVo.setAddvcd(stStbprpB.getAddvcd());
                }
            });
            sumRain =  sumRain.stream().filter(e->e.getAddvcd()!=null&&!"other".equals(e.getAddvcd())).collect(Collectors.toList());
            //重组数据将相同addvcd的数据平均
            Map<String, List<RainInfoHourVo>> collect = sumRain.stream().collect(Collectors.groupingBy(RainInfoHourVo::getAddvcd));

            List<RainInfoHourVo> rainInfoHourVos = new ArrayList<>();
            for (Map.Entry<String, List<RainInfoHourVo>> entry : collect.entrySet()) {
                RainInfoHourVo rainInfoHourVo = new RainInfoHourVo();
                rainInfoHourVo.setStcd(entry.getKey());
                BigDecimal sum = new BigDecimal(0);
                for (RainInfoHourVo rainInfoHourVo1 : entry.getValue()) {
                    sum = NumberUtil.add(sum, rainInfoHourVo1.getDrp());
                }
                log.debug("addcd:"+rainInfoHourVo.getStcd()+"sum:"+sum+"  size:"+entry.getValue().size());
                rainInfoHourVo.setDrp(NumberUtil.div(sum, new BigDecimal(entry.getValue().size()), 1));
                rainInfoHourVos.add(rainInfoHourVo);
            }

            BigDecimal sumD = sumRain.stream().map(RainInfoHourVo::getDrp).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal parseDe = null;
            if(sumRain.size()>0){
            parseDe = NumberUtil.div(sumD, sumRain.size(), 1, RoundingMode.HALF_UP);
            }else{
                parseDe = BigDecimal.valueOf(0);
            }
            log.debug("sumD:"+sumD+"  size:"+sumRain.size()+"  parseDe:"+parseDe);
            RainInfoHourVo rainInfoHourVo = new RainInfoHourVo();
            rainInfoHourVo.setStcd("3310");
            rainInfoHourVo.setDrp(parseDe);
            rainInfoHourVos.add(rainInfoHourVo);

            sumRain = rainInfoHourVos;
        }
        //赋值
        Map<String, BigDecimal> rainMap = sumRain.stream().collect(Collectors.toMap(RainInfoHourVo::getStcd, RainInfoHourVo::getDrp, (k1, k2) -> k1));

        //查找未来雨量表
        Date sTm = endTm;
        Date eTm =DateUtil.endOfYear(sTm);
        StPptnHistReqVo stPptnHistReqVo = new StPptnHistReqVo();
        stPptnHistReqVo.setStartTm(sTm);
        stPptnHistReqVo.setEndTm(eTm);
        stPptnHistReqVo.setType(vo.getType());
        List<StPptnHistRespVo> stPptnHistRespVos = selectSumLeftByMonthAndDay(stPptnHistReqVo);
        Map<String, StPptnHistRespVo> frMap =new HashMap<>();
        if(vo.getType().equals(1)) {
            frMap = stPptnHistRespVos.stream().collect(Collectors.toMap(StPptnHistRespVo::getAddvcd, Function.identity(), (key1, key2) -> key2));
        }
        else{
            frMap = stPptnHistRespVos.stream().collect(Collectors.toMap(StPptnHistRespVo::getStcd, Function.identity(), (key1, key2) -> key2));
        }

        for(StPptnHistRespVo stPptnHistRespVo:rs){
            if(vo.getType().equals(1)) {
                BigDecimal rain = rainMap.get(stPptnHistRespVo.getAddvcd());
                if(rain!=null){
                    stPptnHistRespVo.setSum(rain);
                    stPptnHistRespVo.setGrowthRate(
                            NumberUtil.mul(
                                    NumberUtil.div(NumberUtil.sub(stPptnHistRespVo.getSum(),stPptnHistRespVo.getSamePeriodSum()),stPptnHistRespVo.getSamePeriodSum(),4),
                                    new BigDecimal(100)).setScale(2,RoundingMode.HALF_UP));
                }
                if(frMap.containsKey(stPptnHistRespVo.getAddvcd())){
                    stPptnHistRespVo.setFrDrp(frMap.get(stPptnHistRespVo.getAddvcd()).getSamePeriodSum());
                }
                else {
                    stPptnHistRespVo.setFrDrp(new BigDecimal(0));
                }
            }
            else {
                BigDecimal rain = rainMap.get(stPptnHistRespVo.getStcd());
                if(rain!=null){
                    stPptnHistRespVo.setSum(rain);
                    stPptnHistRespVo.setGrowthRate(
                            NumberUtil.mul(
                                    NumberUtil.div(NumberUtil.sub(stPptnHistRespVo.getSum(),stPptnHistRespVo.getSamePeriodSum()),stPptnHistRespVo.getSamePeriodSum(),4),
                                    new BigDecimal(100)).setScale(2,BigDecimal.ROUND_HALF_UP));
                }
                if(frMap.containsKey(stPptnHistRespVo.getStcd())){
                    stPptnHistRespVo.setFrDrp(frMap.get(stPptnHistRespVo.getStcd()).getSamePeriodSum());
                }
                else {
                    stPptnHistRespVo.setFrDrp(new BigDecimal(0));
                }
            }
        }

        return rs;
    }

    @Override
    public List<StPptnHistRainLineRespVo> rainLine(StPptnHistReqVo vo) {
        //查找年平均日雨量表
        Date startTm = vo.getStartTm();
        Date endTm = vo.getEndTm();
        Date now  =  DateUtil.date();
        Date today  = DateUtil.beginOfDay(now);
        List<RainInfoHourVo> sumRain =new ArrayList<>();
        List<StPptnHistRainLineRespVo> rs = selectByMonthAndDay(vo);
        //查找实时日雨量表
        RainInfoHourReqVo rainInfoHourReqVo = new RainInfoHourReqVo();
        rainInfoHourReqVo.setStartTm(startTm);
        rainInfoHourReqVo.setEndTm(endTm);
        rainInfoHourReqVo.setStcd(vo.getStcd());
        rainInfoHourReqVo.setAddvcd(vo.getAddvcd());
        sumRain= stPptnRMapper.getDayRains(rainInfoHourReqVo);
        if(DateUtil.compare(today,endTm)<=0){
            RainInfoHourReqVo rainInfoHourReqVos = new RainInfoHourReqVo();
            rainInfoHourReqVos.setStartTm(DateUtil.offsetHour(today,-16));
            rainInfoHourReqVos.setEndTm(DateUtil.offsetHour(today,8));
            rainInfoHourReqVos.setStcd(vo.getStcd());
            rainInfoHourReqVos.setAddvcd(vo.getAddvcd());
            List<RainInfoHourVo> toDayRain = stPptnRMapper.getSumLeftRain(rainInfoHourReqVos);
            toDayRain.forEach(e->e.setTm(today));
            sumRain.addAll(toDayRain );
        }
        //查找测站表
        RainInfoHourReqVo rainInfoHourReqVo1 = new RainInfoHourReqVo();
        rainInfoHourReqVo1.setFlag(1);
        rainInfoHourReqVo1.setAddvcd9(vo.getAddvcd());
        List< RainInfoHourVo> stStbprpBS = stPptnRMapper.loadPage(rainInfoHourReqVo1);
        Map<String, RainInfoHourVo> stcdMap = stStbprpBS.stream().collect(Collectors.toMap(RainInfoHourVo::getStcd, Function.identity(), (key1, key2) -> key2));

        if(vo.getType().equals(1)) {
            sumRain.stream().forEach(stPptnHistRespVo -> {
                if (stcdMap.containsKey(stPptnHistRespVo.getStcd())) {
                    RainInfoHourVo stStbprpB = stcdMap.get(stPptnHistRespVo.getStcd());
                    stPptnHistRespVo.setAddvcd(stStbprpB.getAddvcd());
                }
            });
            //重组数据将相同的（addvcd和时间点）的数据平均
            // 使用一个Map来保存累计的雨量和计数
            Map<String, Double> rainSumMap = new HashMap<>();
            Map<String, Integer> rainCountMap = new HashMap<>();

            // 遍历sumRain列表
            for (RainInfoHourVo rain : sumRain) {
                String key = rain.getAddvcd() + "_" + rain.getTm();
                // 累计雨量
                rainSumMap.put(key, rainSumMap.getOrDefault(key, 0.0) + rain.getDrp().doubleValue());
                // 增加计数
                rainCountMap.put(key, rainCountMap.getOrDefault(key, 0) + 1);

                if(rain.getAddvcd()!=null){
                    String keyAll = "3310_" + rain.getTm();
                    rainSumMap.put(keyAll, rainSumMap.getOrDefault(keyAll, 0.0) + rain.getDrp().doubleValue());
                    rainCountMap.put(keyAll, rainCountMap.getOrDefault(keyAll, 0) + 1);
                }
            }

            // 计算平均值
            Map<String, Double> rainAvgMap = new HashMap<>();
            for (String key : rainSumMap.keySet()) {
                rainAvgMap.put(key, rainSumMap.get(key) / rainCountMap.get(key));
            }
            // 创建一个新的列表来保存计算出的平均值
            List<RainInfoHourVo> averageRainList = new ArrayList<>();

            // 遍历rainAvgMap
            for (Map.Entry<String, Double> entry : rainAvgMap.entrySet()) {
                String key = entry.getKey();
                Double avgRain = entry.getValue();

                // 从键值中提取addvcd和时间点
                String[] keyParts = key.split("_");
                String addvcd = keyParts[0];
                String timePoint = keyParts[1];

                // 创建一个新的RainInfoHourVo对象，并设置addvcd、时间点和平均值
                RainInfoHourVo avgRainVo = new RainInfoHourVo();
                avgRainVo.setStcd(addvcd);
                avgRainVo.setTm(DateUtil.parse(timePoint));
                avgRainVo.setDrp(BigDecimal.valueOf(avgRain).setScale(1, RoundingMode.HALF_UP));
                avgRainVo.setAddvcd(addvcd);
                // 将新创建的RainInfoHourVo对象添加到averageRainList中
                averageRainList.add(avgRainVo);
            }
            sumRain = averageRainList;
        }

        //年平均日雨量表数据与实时日雨量表数据根据时间点合并
        for (StPptnHistRainLineRespVo stPptnHistRespVo : rs) {
            stPptnHistRespVo.setTm(DateUtil.parse(stPptnHistRespVo.getYear()+"-"+(stPptnHistRespVo.getMonth())+"-"+(stPptnHistRespVo.getDay()))
                    );
            for (RainInfoHourVo rainInfoHourVo : sumRain) {
                if( stPptnHistRespVo.getTm().equals(rainInfoHourVo.getTm()) && stPptnHistRespVo.getAddvcd()!=null && stPptnHistRespVo.getAddvcd().equals(rainInfoHourVo.getAddvcd())){
                    stPptnHistRespVo.setTm(DateUtil.offsetHour( stPptnHistRespVo.getTm(),8));
                    stPptnHistRespVo.setDrp(rainInfoHourVo.getDrp());

                }
            }
        }
        return rs;
    }

}