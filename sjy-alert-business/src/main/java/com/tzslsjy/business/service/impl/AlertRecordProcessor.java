package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.mapper.SjyAlertSmsRecordMapper;
import com.tzslsjy.business.service.AlertProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * 预警记录处理器
 * 负责保存预警记录到数据库
 */
@Component
public class AlertRecordProcessor implements AlertProcessor {
    private static final Logger logger = LoggerFactory.getLogger(AlertRecordProcessor.class);

    @Autowired
    private SjyAlertSmsRecordMapper smsRecordMapper;

    // 下一个处理器
    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public void process(AlertContext context) {
        List<SjyAlertSmsRecord> records = context.getRecords();

        if (records != null && !records.isEmpty()) {
            logger.info("开始保存{}条预警记录", records.size());

            try {
                for (SjyAlertSmsRecord record : records) {
                    // 补充记录信息
                    enrichRecord(record, context);

                    // 保存到数据库
                    smsRecordMapper.insertSjyAlertSmsRecord(record);
                }
                logger.info("成功保存{}条预警记录", records.size());
            } catch (Exception e) {
                logger.error("保存预警记录失败: {}", e.getMessage(), e);
            }
        } else {
            logger.debug("没有预警记录需要保存");
        }

        // 继续处理链
        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    /**
     * 补充记录信息
     */
    private void enrichRecord(SjyAlertSmsRecord record, AlertContext context) {
        try {
            // 设置规则ID（如果还没有设置）
            if (record.getRuleId() == null && context.getRule() != null) {
                record.setRuleId(String.valueOf(context.getRule().getRuleId()));
            }

            // 设置发送类型
            if (record.getSendType() == null) {
                record.setSendType("SMS"); // 默认短信
            }

            logger.debug("补充预警记录信息: ruleId={}, personId={}", 
                    record.getRuleId(), record.getPersonId());
                    
        } catch (Exception e) {
            logger.error("补充记录信息时出错: {}", e.getMessage(), e);
        }
    }
}
