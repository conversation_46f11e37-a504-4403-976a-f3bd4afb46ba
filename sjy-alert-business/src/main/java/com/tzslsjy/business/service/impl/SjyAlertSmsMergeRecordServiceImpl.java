package com.tzslsjy.business.service.impl;

import java.util.List;

import com.tzslsjy.business.domain.SjyAlertSmsMergeRecord;
import com.tzslsjy.business.mapper.SjyAlertSmsMergeRecordMapper;
import com.tzslsjy.business.service.ISjyAlertSmsMergeRecordService;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 预警消息合并记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
public class SjyAlertSmsMergeRecordServiceImpl implements ISjyAlertSmsMergeRecordService
{
    @Autowired
    private SjyAlertSmsMergeRecordMapper sjyAlertSmsMergeRecordMapper;

    /**
     * 查询预警消息合并记录
     * 
     * @param smsMergeId 预警消息合并记录主键
     * @return 预警消息合并记录
     */
    @Override
    public SjyAlertSmsMergeRecord selectSjyAlertSmsMergeRecordBySmsMergeId(Long smsMergeId)
    {
        return sjyAlertSmsMergeRecordMapper.selectSjyAlertSmsMergeRecordBySmsMergeId(smsMergeId);
    }

    /**
     * 查询预警消息合并记录列表
     * 
     * @param sjyAlertSmsMergeRecord 预警消息合并记录
     * @return 预警消息合并记录
     */
    @Override
    public List<SjyAlertSmsMergeRecord> selectSjyAlertSmsMergeRecordList(SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord)
    {
        return sjyAlertSmsMergeRecordMapper.selectSjyAlertSmsMergeRecordList(sjyAlertSmsMergeRecord);
    }

    /**
     * 新增预警消息合并记录
     * 
     * @param sjyAlertSmsMergeRecord 预警消息合并记录
     * @return 结果
     */
    @Override
    public int insertSjyAlertSmsMergeRecord(SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord)
    {
        sjyAlertSmsMergeRecord.setCreateTime(DateUtils.getNowDate());
        return sjyAlertSmsMergeRecordMapper.insertSjyAlertSmsMergeRecord(sjyAlertSmsMergeRecord);
    }

    /**
     * 修改预警消息合并记录
     * 
     * @param sjyAlertSmsMergeRecord 预警消息合并记录
     * @return 结果
     */
    @Override
    public int updateSjyAlertSmsMergeRecord(SjyAlertSmsMergeRecord sjyAlertSmsMergeRecord)
    {
        sjyAlertSmsMergeRecord.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertSmsMergeRecordMapper.updateSjyAlertSmsMergeRecord(sjyAlertSmsMergeRecord);
    }

    /**
     * 批量删除预警消息合并记录
     * 
     * @param smsMergeIds 需要删除的预警消息合并记录主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertSmsMergeRecordBySmsMergeIds(Long[] smsMergeIds)
    {
        return sjyAlertSmsMergeRecordMapper.deleteSjyAlertSmsMergeRecordBySmsMergeIds(smsMergeIds);
    }

    /**
     * 删除预警消息合并记录信息
     * 
     * @param smsMergeId 预警消息合并记录主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertSmsMergeRecordBySmsMergeId(Long smsMergeId)
    {
        return sjyAlertSmsMergeRecordMapper.deleteSjyAlertSmsMergeRecordBySmsMergeId(smsMergeId);
    }
}
