package com.tzslsjy.business.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 水库水位预警参数解析器
 */
@Component
@Slf4j
public class ReservoirAlertParameterResolver implements AlertRuleParameterResolver {

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("25"); // 水库水位预警类型

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析水库水位预警规则参数，规则ID: {}", rule.getRuleId());
        List<Map<String, Object>> paramGroups = new ArrayList<>();

        // 水库预警通常针对规则主体配置的测站列表，每个测站有其独立的参数（汛限水位）
        // 或者参数在SjyAlertRuleArg中定义，其中argJson包含汛限水位，argType可能包含测站编码或直接使用规则的stcd

        List<String> globalStcds = new ArrayList<>();
        if (StringUtils.hasText(rule.getAlertStcd())) {
            globalStcds.addAll(Arrays.asList(rule.getAlertStcd().split(",")));
        }

        Map<Long, List<SjyAlertRuleArg>> groupedParams = ruleParams.stream()
                .collect(Collectors.groupingBy(SjyAlertRuleArg::getArgId));

        for (Map.Entry<Long, List<SjyAlertRuleArg>> entry : groupedParams.entrySet()) {
            Long argId = entry.getKey();
            List<SjyAlertRuleArg> argParamsForGroup = entry.getValue();
            Map<String, Object> queryParams = buildQueryParamsForGroup(rule, argParamsForGroup, argId, tm);
            if (!queryParams.isEmpty()) {
                paramGroups.add(queryParams);
            }
        }
        return paramGroups;
    }

    private Map<String, Object> buildQueryParamsForGroup(SjyAlertRule rule, List<SjyAlertRuleArg> argParams, Long argId, Date tm) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ruleId", rule.getRuleId());
        queryParams.put("ruleName", rule.getRuleName());
        queryParams.put("ruleTypeId", rule.getRuleTypeId());
        queryParams.put("argId", argId);
        queryParams.put("tm", tm);



        List<SjyReservoirAlertParamData> reservoirAlertParamData = new ArrayList<>();
        for (SjyAlertRuleArg param : argParams) {
            if (param.getArgId().equals(argId)) { // 确保只处理当前参数组的参数
                try {
                    SjyReservoirAlertJsonData jsonData = JSON.parseObject(param.getArgJson(), SjyReservoirAlertJsonData.class);
                    SjyReservoirAlertParamData paramData = new SjyReservoirAlertParamData();
                    paramData.setArgId(param.getArgId());
                    paramData.setAlertLevel(param.getAlertLevel());
                    if (jsonData != null) {
                        paramData.setVal(jsonData.getVal());
                    }
                    reservoirAlertParamData.add(paramData);
                } catch (Exception e) {
                    log.error("解析水文设备预警参数失败，参数ID: {}, JSON: {}, 错误: {}",
                            param.getArgId(), param.getArgJson(), e.getMessage());
                }
            }
        }
        queryParams.put("reservoirAlertParams", reservoirAlertParamData);
        //
        return queryParams;
    }
} 