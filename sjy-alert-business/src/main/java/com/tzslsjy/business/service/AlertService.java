//package com.tzslsjy.business.service;
//
//import cn.hutool.core.date.DateUtil;
//import com.tzslsjy.business.domain.AlertContext;
//import com.tzslsjy.business.domain.SjyAlertPerson;
//import com.tzslsjy.business.domain.SjyAlertSmsRecord;
//import com.tzslsjy.business.domain.SjyAlertRule;
//import com.tzslsjy.business.domain.SjyAlertRuleType;
//import com.tzslsjy.business.domain.SjyAlertRuleArg;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.core.task.TaskExecutor;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.PostConstruct;
//import java.util.*;
//import java.util.concurrent.ConcurrentHashMap;
//
///**
// * 预警服务核心类
// * 负责组织预警流程和整合各组件
// */
//@Service
//public class AlertService {
//    private static final Logger logger = LoggerFactory.getLogger(AlertService.class);
//
//    @Autowired
//    private AlertStrategyFactory strategyFactory;
//
//    @Autowired
//    private List<AlertProcessor> processors;
//
//    @Autowired
//    private List<AlertNotifier> notifiers;
//
//    @Autowired
//    private List<AlertDataProvider> dataProviders;
//
//    @Autowired
//    private ISjyAlertRuleService alertRuleService;
//
//    @Autowired
//    private ISjyAlertRuleTypeService alertRuleTypeService;
//
//    @Autowired
//    private ISjyAlertRuleArgService alertRuleArgService;
//
//    @Autowired
//    @Qualifier("alertTaskExecutor")
//    private TaskExecutor taskExecutor;  // 异步通知处理的线程池
//
//    @Autowired
//    @Qualifier("floodAlertTemplateProcessor")
//    private AlertTemplateProcessor templateProcessor;
//    AlertProcessor   commonProcessorChain = null;
//    // 规则缓存，提高性能
//    private Map<Long, SjyAlertRule> ruleCache = new ConcurrentHashMap<>();
//
//    // 存储规则类型到数据提供者的映射
//    private Map<String, AlertDataProvider> dataProviderMap = new HashMap<>();
//
//    // 存储策略类型到处理器链的映射
//    private Map<String, AlertProcessor> processorChainMap = new HashMap<>();
//    @PostConstruct
//    public void init() {
//        initDataProviders();
//        initProcessorChain();
//    }
//
//    /**
//     * 定时刷新规则缓存，保证数据时效性
//     */
//    @Scheduled(fixedRate = 300000) // 5分钟刷新一次缓存
//    public void refreshRuleCache() {
//        logger.info("开始刷新规则缓存");
//        ruleCache.clear();
//    }
//
//    /**
//     * 初始化数据提供者映射
//     */
//    private void initDataProviders() {
//        if (dataProviders == null || dataProviders.isEmpty()) {
//            logger.warn("未找到任何数据提供者");
//            return;
//        }
//
//        for (AlertDataProvider provider : dataProviders) {
//            List<String> supportedTypes = provider.getSupportedTypes();
//            if (supportedTypes == null || supportedTypes.isEmpty()) {
//                logger.warn("数据提供者{}未声明支持的规则类型", provider.getClass().getSimpleName());
//                continue;
//            }
//
//            for (String type : supportedTypes) {
//                // 如果有多个提供者支持同一类型，后面的会覆盖前面的
//                // 如果需要更复杂的策略，可以考虑使用优先级机制
//                dataProviderMap.put(type, provider);
//                logger.info("注册类型[{}]的数据提供者: {}",
//                        type, provider.getClass().getSimpleName());
//            }
//        }
//    }
//
//    /**
//     * 初始化处理器链结构
//     */
//    private void initProcessorChain() {
//        if (processors == null || processors.isEmpty()) {
//            logger.warn("未找到任何预警处理器");
//            return;
//        }
//
//        // 对处理器进行分类
//        Map<String, List<AlertProcessor>> typeSpecificProcessors = new HashMap<>();
//        List<AlertProcessor> commonProcessors = new ArrayList<>();
//
//        for (AlertProcessor processor : processors) {
//            if (processor instanceof TypedAlertProcessor) {
//                // 如果是有类型的处理器，按类型分组
//                TypedAlertProcessor typedProcessor = (TypedAlertProcessor) processor;
//                String type = typedProcessor.getSupportedType();
//
//                if (!typeSpecificProcessors.containsKey(type)) {
//                    typeSpecificProcessors.put(type, new ArrayList<>());
//                }
//                typeSpecificProcessors.get(type).add(processor);
//                logger.info("注册类型为{}的处理器: {}", type, processor.getClass().getSimpleName());
//            } else {
//                // 没有指定类型的处理器视为通用处理器
//                commonProcessors.add(processor);
//                logger.info("注册通用处理器: {}", processor.getClass().getSimpleName());
//            }
//        }
//
//        // 构建通用处理器链
//        if (!commonProcessors.isEmpty()) {
//            buildProcessorChain(commonProcessors);
//            commonProcessorChain = commonProcessors.get(0);
//        }
//
//        // 为每种类型构建处理器链
//        for (Map.Entry<String, List<AlertProcessor>> entry : typeSpecificProcessors.entrySet()) {
//            List<AlertProcessor> chainProcessors = new ArrayList<>(entry.getValue());
//
//            // 将通用处理器添加到特定类型处理器链末尾
//            if (commonProcessorChain != null) {
//                // 为了避免影响通用处理器链，我们需要复制一个通用处理器
//                // 这里使用简单方式 - 只添加最后一个类型特定处理器到通用处理器链
//                if (!chainProcessors.isEmpty()) {
//                    chainProcessors.get(chainProcessors.size() - 1)
//                            .setNextProcessor(commonProcessorChain);
//                }
//            }
//
//            if (!chainProcessors.isEmpty()) {
//                buildProcessorChain(chainProcessors);
//                processorChainMap.put(entry.getKey(), chainProcessors.get(0));
//                logger.info("已构建{}类型的处理器链", entry.getKey());
//            }
//        }
//    }
//
//    /**
//     * 构建处理器链辅助方法
//     */
//    private void buildProcessorChain(List<AlertProcessor> chainProcessors) {
//        for (int i = 0; i < chainProcessors.size() - 1; i++) {
//            chainProcessors.get(i).setNextProcessor(chainProcessors.get(i + 1));
//        }
//    }
//
//    /**
//     * 处理预警的主入口方法
//     * @param ruleId 规则ID
//     * @return 处理结果
//     */
//    public boolean processAlert(Long ruleId, Date tm) {
//        try {
//            if(tm==null){
//               tm = DateUtil.date();
//            }
//            //tm转成整5分钟
//            tm = DateUtil.beginOfMinute(DateUtil.offsetMinute(tm,  -(tm.getMinutes() % 5)));
//            // 1. 尝试从缓存加载规则信息
//            SjyAlertRule rule = ruleCache.get(ruleId);
//            if (rule == null) {
//                rule = alertRuleService.selectSjyAlertRuleById(ruleId);
//                if (rule != null && rule.getStatus() == 1) {
//                    ruleCache.put(ruleId, rule);
//                }
//            }
//
//            if (rule == null) {
//                logger.error("未找到ID为{}的预警规则", ruleId);
//                return false;
//            }
//
//            if (rule.getStatus() == null || rule.getStatus() != 1) {
//                logger.warn("规则ID{}已禁用，跳过处理", ruleId);
//                return false;
//            }
//
//            String ruleTypeId = rule.getRuleTypeId();
//
//            // 2. 验证规则类型
//            SjyAlertRuleType ruleType = alertRuleTypeService.selectSjyAlertRuleTypeById(Long.valueOf(ruleTypeId));
//            if (ruleType == null || (ruleType.getStatus() != null && ruleType.getStatus() != 1)) {
//                logger.warn("规则类型{}无效或已禁用", ruleTypeId);
//                return false;
//            }
//
//            // 3. 检查并获取规则策略
//            AlertRuleStrategy strategy = strategyFactory.getStrategy(ruleTypeId);
//            if (strategy == null) {
//                logger.warn("不支持的规则类型: {}", ruleTypeId);
//                return false;
//            }
//
//            // 4. 获取数据提供者
//            AlertDataProvider dataProvider = dataProviderMap.get(ruleTypeId);
//            if (dataProvider == null) {
//                logger.warn("未找到类型{}的数据提供者", ruleTypeId);
//                return false;
//            }
//
//            // 5. 准备数据 - 整合了参数加载、查询参数构建和数据获取
//            Map<String, Object> data;
//            try {
//                logger.debug("开始准备规则{}(类型:{})的预警数据", rule.getRuleName(), ruleTypeId);
//                data = dataProvider.prepareAndFetchData(ruleTypeId, rule,tm);
//                if (data == null || data.isEmpty()) {
//                    logger.warn("规则{}的数据提供者返回了空数据", rule.getRuleName());
//                    return false;
//                }
//            } catch (Exception e) {
//                logger.error("准备规则{}的数据时发生错误: {}", rule.getRuleName(), e.getMessage(), e);
//                return false;
//            }
//
//            // 6. 创建上下文
//            AlertContext context = new AlertContext();
//            context.setData(data);
//            context.setRuleType(ruleTypeId);
//            context.setRule(rule);
//
//            // 从data中获取规则参数
//            @SuppressWarnings("unchecked")
//            List<SjyAlertRuleArg> ruleParams = (List<SjyAlertRuleArg>) data.get("ruleParams");
//            context.setRuleParams(ruleParams);
//
//            // 7. 评估规则
//            logger.debug("开始评估规则: {}", rule.getRuleName());
//            boolean triggered = strategy.evaluate(context);
//            if (!triggered) {
//                logger.debug("规则{}未触发预警", rule.getRuleName());
//                return true; // 规则正常执行但未触发
//            }
//
//            // 8. 根据规则类型获取对应的处理器链
//            processRuleWithChain(ruleTypeId, context);
//
//            return true;
//        } catch (Exception e) {
//            logger.error("处理预警过程中发生错误: {}", e.getMessage(), e);
//            return false;
//        }
//    }
//
//    /**
//     * 使用适当的处理链处理规则
//     */
//    private void processRuleWithChain(String ruleTypeId, AlertContext context) {
//        AlertProcessor chain = processorChainMap.get(ruleTypeId);
//        if (chain != null) {
//            logger.debug("使用{}类型特定的处理器链处理预警", ruleTypeId);
//            chain.process(context);
//        } else if (commonProcessorChain != null) {
//            // 如果没有特定类型的处理器链，使用通用处理器链
//            logger.debug("没有找到{}类型特定的处理器链，使用通用处理链", ruleTypeId);
//            commonProcessorChain.process(context);
//        } else {
//            logger.warn("没有可用的处理器链处理预警类型: {}", ruleTypeId);
//            return;
//        }
//
//        // 发送通知
//        if (context.getRecords() == null) {
//            logger.warn("预警对象为空，无法发送通知");
//            return;
//        }
//        if("1".equals(context.getRule().getSendWay())) {
//            sendNotifications(context.getRecords());
//        }
//    }
//
//    /**
//     * 异步发送通知
//     */
//    private void sendNotifications(List<SjyAlertSmsRecord> alerts) {
//        if (notifiers == null || notifiers.isEmpty()  ) {
//            logger.warn("没有可用的通知器或接收人为空");
//            return;
//        }
//
//        logger.debug("异步发送预警通知给{}个接收人", alerts.size());
//
//        // 异步发送通知
//        taskExecutor.execute(() -> {
//            for (AlertNotifier notifier : notifiers) {
//                try {
//                    notifier.notify(alerts );
//                } catch (Exception e) {
//                    logger.error("通知发送失败: {}", e.getMessage(), e);
//                    // 可以添加重试逻辑或备用通知方式
//                }
//            }
//        });
//    }
//}
