package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 城市内涝水位预警模板处理器
 * 使用特定的模板格式：
 * - list_format: {addvcdNm}的{stnm}超汛限{overWrz}米
 * - template_content: {list}，请你们加强值班值守，及时开闸排涝
 */
@Component
@Slf4j
public class UrbanWaterloggingAlertTemplateProcessor implements AlertTemplateProcessor {

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate smsTemplate, Map<String, Object> data) {
        log.debug("处理城市内涝水位预警模板，模板ID: {}, 数据: {}", smsTemplate.getTemplateId(), data);
        try {
            // data中应包含 triggeredStations 列表
            // DefaultAlertTemplateProcessor 会使用 ListFormat "{addvcdNm}的{stnm}超汛限{overWrz}米" 和 ListSeparator "，" 来格式化列表。
            // 模板内容: "{list}，请你们加强值班值守，及时开闸排涝"

            // 从 triggeredStations 中获取第一个记录的时间作为全局 {tm}
            if (data.containsKey("triggeredStations")) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> triggeredStations = (List<Map<String, Object>>) data.get("triggeredStations");
                if (!CollectionUtils.isEmpty(triggeredStations)) {
                    Map<String, Object> firstStation = triggeredStations.get(0);
                    if (firstStation.containsKey("tm")) {
                        data.put("tm", firstStation.get("tm")); // 将列表中的时间提升为全局时间
                    }
                }
            }

            // 设置城市内涝特定的模板格式
            if (smsTemplate.getListFormat() == null || smsTemplate.getListFormat().isEmpty()) {
                // 如果模板中没有设置listFormat，使用默认的城市内涝格式
                smsTemplate.setListFormat("{addvcdNm}的{stnm}超汛限{overWrz}米");
            }

            if (smsTemplate.getTemplateContent() == null || smsTemplate.getTemplateContent().isEmpty()) {
                // 如果模板中没有设置templateContent，使用默认的城市内涝格式
                smsTemplate.setTemplateContent("{list}，请你们加强值班值守，及时开闸排涝");
            }

            return defaultTemplateProcessor.processTemplate(context, smsTemplate, data);
        } catch (Exception e) {
            log.error("处理城市内涝水位预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public boolean supportsRuleType(String ruleTypeId) {
        return "26".equals(ruleTypeId); // 城市内涝水位预警类型
    }
}
