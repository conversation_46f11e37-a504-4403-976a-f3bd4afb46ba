package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertTemplateRelation;

/**
 * 消息模板关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISjyAlertTemplateRelationService
{
    /**
     * 查询消息模板关联
     *
     * @param id 消息模板关联主键
     * @return 消息模板关联
     */
    public SjyAlertTemplateRelation selectSjyAlertTemplateRelationById(Long id);

    /**
     * 查询消息模板关联列表
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 消息模板关联集合
     */
    public List<SjyAlertTemplateRelation> selectSjyAlertTemplateRelationList(SjyAlertTemplateRelation sjyAlertTemplateRelation);

    /**
     * 新增消息模板关联
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 结果
     */
    public int insertSjyAlertTemplateRelation(SjyAlertTemplateRelation sjyAlertTemplateRelation);

    /**
     * 修改消息模板关联
     *
     * @param sjyAlertTemplateRelation 消息模板关联
     * @return 结果
     */
    public int updateSjyAlertTemplateRelation(SjyAlertTemplateRelation sjyAlertTemplateRelation);

    /**
     * 批量删除消息模板关联
     *
     * @param ids 需要删除的消息模板关联主键集合
     * @return 结果
     */
    public int deleteSjyAlertTemplateRelationByIds(Long[] ids);

    /**
     * 删除消息模板关联信息
     *
     * @param id 消息模板关联主键
     * @return 结果
     */
    public int deleteSjyAlertTemplateRelationById(Long id);
}
