package com.tzslsjy.business.service.impl;

import java.util.List;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyAlertStSituationMapper;
import com.tzslsjy.business.domain.SjyAlertStSituation;
import com.tzslsjy.business.service.ISjyAlertStSituationService;

/**
 * 测站预警情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Service
public class SjyAlertStSituationServiceImpl implements ISjyAlertStSituationService {
    @Autowired
    private SjyAlertStSituationMapper sjyAlertStSituationMapper;

    /**
     * 查询测站预警情况
     *
     * @param id 测站预警情况主键
     * @return 测站预警情况
     */
    @Override
    public SjyAlertStSituation selectSjyAlertStSituationById(Integer id) {
        return sjyAlertStSituationMapper.selectSjyAlertStSituationById(id);
    }

    /**
     * 查询测站预警情况列表
     *
     * @param sjyAlertStSituation 测站预警情况
     * @return 测站预警情况
     */
    @Override
    public List<SjyAlertStSituation> selectSjyAlertStSituationList(SjyAlertStSituation sjyAlertStSituation) {
        return sjyAlertStSituationMapper.selectSjyAlertStSituationList(sjyAlertStSituation);
    }

    /**
     * 新增测站预警情况
     *
     * @param sjyAlertStSituation 测站预警情况
     * @return 结果
     */
    @Override
    public int insertSjyAlertStSituation(SjyAlertStSituation sjyAlertStSituation) {
        sjyAlertStSituation.setCreateTime(DateUtils.getNowDate());
        return sjyAlertStSituationMapper.insertSjyAlertStSituation(sjyAlertStSituation);
    }

    /**
     * 修改测站预警情况
     *
     * @param sjyAlertStSituation 测站预警情况
     * @return 结果
     */
    @Override
    public int updateSjyAlertStSituation(SjyAlertStSituation sjyAlertStSituation) {
        sjyAlertStSituation.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertStSituationMapper.updateSjyAlertStSituation(sjyAlertStSituation);
    }

    /**
     * 批量删除测站预警情况
     *
     * @param ids 需要删除的测站预警情况主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertStSituationByIds(Integer[] ids) {
        return sjyAlertStSituationMapper.deleteSjyAlertStSituationByIds(ids);
    }

    /**
     * 删除测站预警情况信息
     *
     * @param id 测站预警情况主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertStSituationById(Integer id) {
        return sjyAlertStSituationMapper.deleteSjyAlertStSituationById(id);
    }

    /**
     * 根据测站编码、关联ID和关联类型查询预警情况
     *
     * @param stcd 测站编码
     * @param cId 关联ID
     * @param cType 关联类型
     * @return 测站预警情况
     */
    @Override
    public SjyAlertStSituation selectByStcdAndRelation(String stcd, Integer cId, Integer cType) {
        return sjyAlertStSituationMapper.selectByStcdAndRelation(stcd, cId, cType);
    }

    /**
     * 更新或插入测站预警情况
     *
     * @param sjyAlertStSituation 测站预警情况
     * @return 结果
     */
    @Override
    public int insertOrUpdate(SjyAlertStSituation sjyAlertStSituation) {
        if (sjyAlertStSituation.getCreateTime() == null) {
            sjyAlertStSituation.setCreateTime(DateUtils.getNowDate());
        }
        sjyAlertStSituation.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertStSituationMapper.insertOrUpdate(sjyAlertStSituation);
    }

    /**
     * 查询所有仍在预警状态的测站（状态为1或2）
     *
     * @param cId 关联ID
     * @param cType 关联类型
     * @return 预警测站列表
     */
    @Override
    public List<SjyAlertStSituation> selectActiveAlertStations(Integer cId, Integer cType) {
        return sjyAlertStSituationMapper.selectActiveAlertStations(cId, cType);
    }

    /**
     * 更新测站预警状态
     *
     * @param stcd 测站编码
     * @param cId 关联ID（规则ID）
     * @param cType 关联类型（2=规则实例）
     * @param status 新状态（0=正常，1=超警戒，2=超保证）
     * @param createBy 创建者
     * @return 是否状态发生变化
     */
    @Override
    public boolean updateStationAlertStatus(String stcd, Integer cId, Integer cType, Integer status, String createBy) {
        SjyAlertStSituation existing = selectByStcdAndRelation(stcd, cId, cType);

        if (existing != null && existing.getStatus().equals(status)) {
            // 状态没有变化，不需要更新
            return false;
        }

        // 状态发生变化，更新数据库
        SjyAlertStSituation situation = new SjyAlertStSituation();
        situation.setStcd(stcd);
        situation.setCId(cId);
        situation.setCType(cType);
        situation.setStatus(status);
        situation.setCreateBy(createBy);
        situation.setUpdateBy(createBy);
        insertOrUpdate(situation);

        // 如果状态恢复正常，不发送通知
        if (status == 0) {
            return false;
        }

        // 状态变更为预警（1或2），发送通知
        return true;
    }
}