package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;

/**
 * 雨量异常预警处理器
 * 专注于雨量异常预警消息生成和分发
 * 支持规则类型：34
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class RainfallAbnormalProcessor implements AlertProcessor, TypedAlertProcessor {

    @Autowired
    @Qualifier("rainfallAbnormalTemplateProcessor")
    private AlertTemplateProcessor templateProcessor;

    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService;

    @Autowired
    private SjyAlertNodeMapper alertNodeMapper;

    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper;

    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper;

    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public String getSupportedType() {
        return "34"; // 雨量异常预警类型
    }

    @Override
    public void process(AlertContext context) {
        try {
            SjyAlertRule rule = context.getRule();
            Map<String, Object> data = context.getData();

            log.info("开始处理雨量异常预警，规则ID: {}", rule.getRuleId());

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");
            if (argResults == null || argResults.isEmpty()) {
                log.warn("雨量异常预警缺少参数结果，规则ID: {}", rule.getRuleId());
                return;
            }

            List<SjyAlertSmsRecord> allRecords = new ArrayList<>();

            for (Map<String, Object> argResult : argResults) {
                Long argId = (Long) argResult.get("argId");

                @SuppressWarnings("unchecked")
                List<Map<String, Object>> abnormalResults = (List<Map<String, Object>>) argResult.get("abnormalResults");

                if (CollectionUtils.isEmpty(abnormalResults)) {
                    log.debug("参数组 {} 无雨量异常结果", argId);
                    continue;
                }

                // 获取模板
                List<SjyAlertSmsTemplate> templates = alertSmsTemplateMapper.selectOneByRelId(String.valueOf(argId), "2");
                if (CollectionUtils.isEmpty(templates)) {
                    log.warn("未找到规则类型 {} 的短信模板", rule.getRuleTypeId());
                    continue;
                }

                // 按行政区划分组处理
                @SuppressWarnings("unchecked")
                Map<String, List<Map<String, Object>>> abnormalResultGroups =
                        (Map<String, List<Map<String, Object>>>) argResult.get("abnormalResultGroups");

                if (abnormalResultGroups != null && !abnormalResultGroups.isEmpty()) {
                    for (Map.Entry<String, List<Map<String, Object>>> entry : abnormalResultGroups.entrySet()) {
                        String jurisdiction = entry.getKey();
                        List<Map<String, Object>> groupAbnormals = entry.getValue();

                        // 确定行政区划级别
                        String jurisdictionLevel = jurisdiction.length() == 6 ? "6位" : "9位";

                        log.debug("处理{}行政区划 {} 的雨量异常预警，异常数量: {}",
                                jurisdictionLevel, jurisdiction, groupAbnormals.size());

                        for (SjyAlertSmsTemplate template : templates) {
                            Map<String, Object> templateData = buildTemplateDataForGroup(argResult, groupAbnormals, jurisdiction, jurisdictionLevel);

                            SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                            if (alert != null) {
                                // 根据行政区划获取对应的接收人员
                                List<SjyAlertPerson> receivers = getReceiversByJurisdiction(rule, argId, jurisdiction);
                                List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                                List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                                allRecords.addAll(mergedAlerts);
                                log.info("{}位行政区划 {} 生成 {} 条雨量异常预警消息", jurisdictionLevel, jurisdiction, mergedAlerts.size());
                            }
                        }
                    }
                } else {
                    // 如果没有行政区划分组，使用全局处理
                    log.debug("使用全局处理方式处理雨量异常预警");

                    for (SjyAlertSmsTemplate template : templates) {
                        Map<String, Object> templateData = buildTemplateData(argResult, abnormalResults);

                        SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                        if (alert != null) {
                            // 获取全局接收人员
                            List<SjyAlertPerson> receivers = getGlobalReceivers(rule, argId);
                            List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                            List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                            allRecords.addAll(mergedAlerts);
                            log.info("全局处理生成 {} 条雨量异常预警消息", mergedAlerts.size());
                        }
                    }
                }
            }

            // 将生成的消息记录添加到上下文
            if (!allRecords.isEmpty()) {
                // 设置到 AlertContext 的 records 字段，而不是 data 中
                context.setRecords(allRecords);
                log.info("雨量异常预警处理完成，共生成 {} 条消息", allRecords.size());
            }

        } catch (Exception e) {
            log.error("处理雨量异常预警失败: {}", e.getMessage(), e);
        } finally {
            // 继续处理链
            if (nextProcessor != null) {
                nextProcessor.process(context);
            }
        }
    }

    /**
     * 构建分组模板数据
     */
    private Map<String, Object> buildTemplateDataForGroup(Map<String, Object> argResult,
                                                          List<Map<String, Object>> groupAbnormals,
                                                          String jurisdiction, String jurisdictionLevel) {
        Map<String, Object> templateData = new HashMap<>(argResult);
        templateData.put("abnormalResults", groupAbnormals);
        templateData.put("jurisdiction", jurisdiction);
        templateData.put("jurisdictionLevel", jurisdictionLevel);
        templateData.put("abnormalCount", groupAbnormals.size());
        templateData.put("alertType", "雨量异常");

        // 构建异常测站列表
        List<String> abnormalStations = new ArrayList<>();
        for (Map<String, Object> abnormal : groupAbnormals) {
            String stcd = (String) abnormal.get("stcd");
            String stnm = (String) abnormal.get("stnm");
            abnormalStations.add(stnm != null ? stnm : stcd);
        }
        templateData.put("abnormalStations", abnormalStations);

        return templateData;
    }

    /**
     * 构建全局模板数据
     */
    private Map<String, Object> buildTemplateData(Map<String, Object> argResult, List<Map<String, Object>> abnormalResults) {
        Map<String, Object> templateData = new HashMap<>(argResult);
        templateData.put("abnormalResults", abnormalResults);
        templateData.put("abnormalCount", abnormalResults.size());
        templateData.put("alertType", "雨量异常");

        // 构建异常测站列表
        List<String> abnormalStations = new ArrayList<>();
        for (Map<String, Object> abnormal : abnormalResults) {
            String stcd = (String) abnormal.get("stcd");
            String stnm = (String) abnormal.get("stnm");
            abnormalStations.add(stnm != null ? stnm : stcd);
        }
        templateData.put("abnormalStations", abnormalStations);

        return templateData;
    }

    /**
     * 根据行政区划获取接收人员
     */
    private List<SjyAlertPerson> getReceiversByJurisdiction(SjyAlertRule rule, Long argId, String jurisdiction) {
        try {
            // 查询规则节点关联
            List<String> relations = alertRuleNodeRelationMapper.selectNodeIdsByArgIdAndAdcd(String.valueOf(argId), jurisdiction);

            if (CollectionUtils.isEmpty(relations)) {
                log.warn("未找到规则 {} 参数组 {} 的节点关联", rule.getRuleId(), argId);
                return new ArrayList<>();
            }


            // 根据行政区划查询接收人员
            List<SjyAlertPerson> allReceivers = sjyAlertPersonService.selectByMemberIds(
                    relations);


            log.debug("行政区划 {} 找到 {} 个接收人员", jurisdiction, allReceivers.size());
            return allReceivers;
        } catch (Exception e) {
            log.error("获取行政区划 {} 接收人员失败: {}", jurisdiction, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取全局接收人员
     */
    private List<SjyAlertPerson> getGlobalReceivers(SjyAlertRule rule, Long argId) {
        try {
            // 查询规则节点关联
            List<String> relations = alertRuleNodeRelationMapper.selectNodeIdsByArgId(String.valueOf(argId));

            if (CollectionUtils.isEmpty(relations)) {
                log.warn("未找到规则 {} 参数组 {} 的节点关联", rule.getRuleId(), argId);
                return new ArrayList<>();
            }

            List<SjyAlertPerson> allReceivers =   sjyAlertPersonService.selectByMemberIds(relations);

            log.debug("全局找到 {} 个接收人员", allReceivers.size());
            return allReceivers;
        } catch (Exception e) {
            log.error("获取全局接收人员失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }
}
