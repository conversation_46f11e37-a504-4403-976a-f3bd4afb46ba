package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertTemplateRelationMapper;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 山洪预警模板处理器
 * 负责生成和合并山洪预警消息，扩展自默认模板处理器
 */
@Component
public class FloodAlertTemplateProcessor implements AlertTemplateProcessor {
    private static final Logger logger = LoggerFactory.getLogger(FloodAlertTemplateProcessor.class);

    @Resource
    private SjyAlertTemplateRelationMapper sjyAlertTemplateRelationMapper;

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate smsTemplate, Map<String, Object> data) {
        try {
            logger.debug("处理山洪预警模板");

            // 添加山洪预警特定数据
            enrichFloodSpecificData(smsTemplate,data);

            // 委托给默认模板处理器
            return defaultTemplateProcessor.processTemplate(context, smsTemplate, data);
        } catch (Exception e) {
            logger.error("处理山洪预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 添加山洪预警特定数据
     */
    private void enrichFloodSpecificData(SjyAlertSmsTemplate smsTemplate,Map<String, Object> data) {
        // 添加预警类型标记
        data.put("alertTypeText", "山洪");

        // 处理村庄信息
        if (data.containsKey("village") && data.get("village") instanceof SjyFdProneArea) {
            SjyFdProneArea village = (SjyFdProneArea) data.get("village");
            data.put("villageName", village.getNaturalAdnm());
            data.put("villageCode", village.getPrevCode());
        }

        // 处理雨量和水位数据
        if("3".equals(smsTemplate.getType())||"4".equals(smsTemplate.getType())){
            processRainfallData(data);
        }else if ("1".equals(smsTemplate.getType())||"2".equals(smsTemplate.getType())){
            processWaterLevelData(data);
        }

    }

    /**
     * 处理雨量数据
     */
    @SuppressWarnings("unchecked")
    private void processRainfallData(Map<String, Object> data) {
        if (data.containsKey("rainlist")) {
            List<Map<String, Object>> rainlist = (List<Map<String, Object>>) data.get("rainlist");
            data.put("list", rainlist); // 将雨量数据设为列表数据

            // 提取第一条雨量记录的数据作为主要数据
            if (!rainlist.isEmpty()) {
                data.putAll(rainlist.get(0));
            }
        }
    }

    /**
     * 处理水位数据
     */
    @SuppressWarnings("unchecked")
    private void processWaterLevelData(Map<String, Object> data) {
        if (data.containsKey("waterlist")) {
            List<Map<String, Object>> waterList = (List<Map<String, Object>>) data.get("waterlist");

            // 如果雨量列表为空，则使用水位列表作为主列表
            if (!data.containsKey("list") || ((List<?>) data.get("list")).isEmpty()) {
                data.put("list", waterList);
            }

            // 提取第一条水位记录的数据作为主要数据
            if (!waterList.isEmpty()) {
                data.putAll(waterList.get(0));
            }
        }
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> people) {
        // 委托给默认模板处理器
        return defaultTemplateProcessor.mergeAlerts(context, alert, people);
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        // 委托给默认模板处理器
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }
}
