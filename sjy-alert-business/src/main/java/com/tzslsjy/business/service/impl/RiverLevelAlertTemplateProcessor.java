package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.ArrayList;

/**
 * 河道水位预警模板处理器
 */
@Component
@Slf4j
public class RiverLevelAlertTemplateProcessor implements AlertTemplateProcessor {

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate template, Map<String, Object> data) {
        try {
            // 河道水位预警特有的数据处理
            enrichRiverLevelSpecificData(template, data);

            // 使用默认处理器进行模板处理
            return defaultTemplateProcessor.processTemplate(context, template, data);
        } catch (Exception e) {
            log.error("处理河道水位预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeAlerts(context, alert, receivers);
    }

    /**
     * 河道水位预警特有的数据处理
     */
    @SuppressWarnings("unchecked")
    private void enrichRiverLevelSpecificData(SjyAlertSmsTemplate template, Map<String, Object> data) {
        // 添加预警类型标记
        data.put("alertTypeText", "河道水位");

        // 处理河道水位数据
        if (data.containsKey("triggeredRivers")) {
            List<Map<String, Object>> triggeredRivers = (List<Map<String, Object>>) data.get("triggeredRivers");
            
            if (!CollectionUtils.isEmpty(triggeredRivers)) {
                // 设置列表数据供模板使用
                data.put("list", triggeredRivers);
                
                // 处理河道水位列表，生成格式化的列表字符串
                List<String> riverListItems = new ArrayList<>();
                for (Map<String, Object> river : triggeredRivers) {
                    String tm = (String) river.get("tm");
                    String addvcdNm = (String) river.get("addvcdNm");
                    String stnm = (String) river.get("stnm");
                    Object overWrzObj = river.get("overWrz");
                    
                    if (tm != null && addvcdNm != null && stnm != null && overWrzObj != null) {
                        String riverItem = String.format("%s，%s的%s超警戒%s米", tm, addvcdNm, stnm, overWrzObj.toString());
                        riverListItems.add(riverItem);
                    }
                }
                
                // 将列表项连接成字符串
                if (!riverListItems.isEmpty()) {
                    String listStr = String.join("；", riverListItems);
                    data.put("listStr", listStr);
                    log.debug("生成河道水位预警列表: {}", listStr);
                }
                
                // 提取第一条河道记录的数据作为主要数据
                if (!triggeredRivers.isEmpty()) {
                    data.putAll(triggeredRivers.get(0));
                }
            }
        }
        
        log.debug("河道水位预警数据处理完成，数据项数量: {}", data.size());
    }
} 