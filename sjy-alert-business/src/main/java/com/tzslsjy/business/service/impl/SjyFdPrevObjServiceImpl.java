package com.tzslsjy.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyFdPrevObjMapper;
import com.tzslsjy.business.domain.SjyFdPrevObj;
import com.tzslsjy.business.service.ISjyFdPrevObjService;

/**
 * 防治对象名录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class SjyFdPrevObjServiceImpl implements ISjyFdPrevObjService 
{
    @Autowired
    private SjyFdPrevObjMapper sjyFdPrevObjMapper;

    /**
     * 查询防治对象名录
     * 
     * @param prevCode 防治对象名录主键
     * @return 防治对象名录
     */
    @Override
    public SjyFdPrevObj selectSjyFdPrevObjByPrevCode(String prevCode)
    {
        return sjyFdPrevObjMapper.selectSjyFdPrevObjByPrevCode(prevCode);
    }

    /**
     * 查询防治对象名录列表
     * 
     * @param sjyFdPrevObj 防治对象名录
     * @return 防治对象名录
     */
    @Override
    public List<SjyFdPrevObj> selectSjyFdPrevObjList(SjyFdPrevObj sjyFdPrevObj)
    {
        return sjyFdPrevObjMapper.selectSjyFdPrevObjList(sjyFdPrevObj);
    }

    /**
     * 新增防治对象名录
     * 
     * @param sjyFdPrevObj 防治对象名录
     * @return 结果
     */
    @Override
    public int insertSjyFdPrevObj(SjyFdPrevObj sjyFdPrevObj)
    {
        return sjyFdPrevObjMapper.insertSjyFdPrevObj(sjyFdPrevObj);
    }

    /**
     * 修改防治对象名录
     * 
     * @param sjyFdPrevObj 防治对象名录
     * @return 结果
     */
    @Override
    public int updateSjyFdPrevObj(SjyFdPrevObj sjyFdPrevObj)
    {
        return sjyFdPrevObjMapper.updateSjyFdPrevObj(sjyFdPrevObj);
    }

    /**
     * 批量删除防治对象名录
     * 
     * @param prevCodes 需要删除的防治对象名录主键
     * @return 结果
     */
    @Override
    public int deleteSjyFdPrevObjByPrevCodes(String[] prevCodes)
    {
        return sjyFdPrevObjMapper.deleteSjyFdPrevObjByPrevCodes(prevCodes);
    }

    /**
     * 删除防治对象名录信息
     * 
     * @param prevCode 防治对象名录主键
     * @return 结果
     */
    @Override
    public int deleteSjyFdPrevObjByPrevCode(String prevCode)
    {
        return sjyFdPrevObjMapper.deleteSjyFdPrevObjByPrevCode(prevCode);
    }
}
