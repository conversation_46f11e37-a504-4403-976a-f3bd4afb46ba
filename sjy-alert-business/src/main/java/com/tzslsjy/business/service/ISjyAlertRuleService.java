package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertRule;

import javax.validation.Valid;
import java.util.List;

/**
 * 预警规则服务接口
 */
public interface ISjyAlertRuleService {
    /**
     * 根据ID查询预警规则
     */
    SjyAlertRule selectSjyAlertRuleById(Long ruleId);

    /**
     * 查询预警规则列表
     */
    List<SjyAlertRule> selectSjyAlertRuleList(SjyAlertRule rule);

    int updateSjyAlertRule(@Valid SjyAlertRule sjyAlertRule);

    int deleteSjyAlertRuleByRuleIds(Long[] ruleIds);

    int insertSjyAlertRule(@Valid SjyAlertRule sjyAlertRule);

    int deleteSjyAlertRuleByRuleId(Long ruleId);

    /**
     * 新增或更新预警规则
     * (如果 ruleId 为 null, 则新增; 否则, 更新)
     *
     * @param sjyAlertRule 预警规则
     * @return 结果
     */
    int insertOrUpdateSjyAlertRule(@Valid SjyAlertRule sjyAlertRule);


    SjyAlertRule getFloodBaseRule();

    int insertOrUpdateRiver(@Valid SjyAlertRule sjyAlertRule);

    /**
     * 根据规则类型ID查询规则列表
     */
    List<SjyAlertRule> selectSjyAlertRuleByRuleType(Long ruleTypeId);

    List<SjyAlertRule> selectSimpleRuleList(SjyAlertRule queryRule);
}
