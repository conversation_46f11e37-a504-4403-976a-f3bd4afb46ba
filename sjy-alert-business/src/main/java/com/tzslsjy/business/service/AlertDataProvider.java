package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleArg;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 预警数据提供者接口
 * 职责单一：只负责根据解析后的查询参数获取预警数据
 */
public interface AlertDataProvider {
    /**
     * 获取此提供者支持的规则类型列表
     */
    List<String> getSupportedTypes();

    /**
     * 检查是否支持指定的规则类型
     */
    default boolean supportsType(String ruleTypeCode) {
        return getSupportedTypes().contains(ruleTypeCode);
    }

    /**
     * 根据查询参数获取预警数据
     * 
     * @param ruleTypeCode 规则类型编码
     * @param queryParams 已解析的查询参数
     * @param tm 预警时间
     * @return 预警数据结果
     */
    Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date tm);
    
    /**
     * 获取提供者名称（用于日志记录）
     */
    default String getProviderName() {
        return this.getClass().getSimpleName();
    }
}
