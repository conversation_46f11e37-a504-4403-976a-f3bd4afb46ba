package com.tzslsjy.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelAddBo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelEditBo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelQueryBo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelStcdAddBo;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.*;
import com.tzslsjy.business.service.ISjyRiskVillageStcdRelService;
import com.tzslsjy.business.vo.SjyRiskVillageStcdRelVo;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 村庄关联测站编号Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Service
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class SjyRiskVillageStcdRelServiceImpl extends ServiceImpl<SjyRiskVillageStcdRelMapper, SjyRiskVillageStcdRel> implements ISjyRiskVillageStcdRelService {
    private final SjyAlertRuleMapper sjyAlertRuleMapper;
    private final SjyWarnAlongVillageRainMapper sjyWarnAlongVillageRainMapper;
    private final SjyRiskVillageInfoMapper sjyRiskVillageInfoMapper;
    private final SjyAlertRuleArgMapper sjyAlertRuleArgMapper;
  //  @DataSource(DataSourceType.SLAVE)
    @Override
    public SjyRiskVillageStcdRelVo queryById(Long id){
        SjyRiskVillageStcdRel db = this.baseMapper.selectById(id);
        return BeanUtil.toBean(db, SjyRiskVillageStcdRelVo.class);
    }
  //  @DataSource(DataSourceType.SLAVE)
    @Override
    public List<SjyRiskVillageStcdRelVo> queryList(SjyRiskVillageStcdRelQueryBo bo) {
        LambdaQueryWrapper<SjyRiskVillageStcdRel> lqw = Wrappers.lambdaQuery();
        lqw.eq(StrUtil.isNotBlank(bo.getVillageId()), SjyRiskVillageStcdRel::getVillageId, bo.getVillageId());
        lqw.eq(StrUtil.isNotBlank(bo.getStcd()), SjyRiskVillageStcdRel::getStcd, bo.getStcd());
        lqw.eq(StrUtil.isNotBlank(bo.getAdcd()), SjyRiskVillageStcdRel::getAdcd, bo.getAdcd());
        return entity2Vo(this.list(lqw));
    }

    /**
    * 实体类转化成视图对象
    *
    * @param collection 实体类集合
    * @return
    */
    private List<SjyRiskVillageStcdRelVo> entity2Vo(Collection<SjyRiskVillageStcdRel> collection) {
        List<SjyRiskVillageStcdRelVo> voList = collection.stream()
                .map(any -> BeanUtil.toBean(any, SjyRiskVillageStcdRelVo.class))
                .collect(Collectors.toList());
        if (collection instanceof Page) {
            Page<SjyRiskVillageStcdRel> page = (Page<SjyRiskVillageStcdRel>)collection;
            Page<SjyRiskVillageStcdRelVo> pageVo = new Page<>();
            BeanUtil.copyProperties(page,pageVo);
            pageVo.addAll(voList);
            voList = pageVo;
        }
        return voList;
    }
  //  @DataSource(DataSourceType.SLAVE)
    @Override
    public Boolean insertByAddBo(SjyRiskVillageStcdRelAddBo bo) {
        SjyRiskVillageStcdRel add = BeanUtil.toBean(bo, SjyRiskVillageStcdRel.class);
        validEntityBeforeSave(add);
        return this.save(add);
    }
  //  @DataSource(DataSourceType.SLAVE)
    @Override
    public Boolean updateByEditBo(SjyRiskVillageStcdRelEditBo bo) {
        SjyRiskVillageStcdRel update = BeanUtil.toBean(bo, SjyRiskVillageStcdRel.class);
        validEntityBeforeSave(update);
        return this.updateById(update);
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(SjyRiskVillageStcdRel entity){
        //TODO 做一些数据校验,如唯一约束
    }
  //  @DataSource(DataSourceType.SLAVE)
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return this.removeByIds(ids);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
  //  @DataSource(DataSourceType.SLAVE)
    public void insertOrUpdateBatch(List<SjyRiskVillageStcdRel> newRelations, String villageId) {
        if (CollectionUtils.isEmpty(newRelations)) {
            return;
        }
        // 删除旧的关联关系
        LambdaQueryWrapper<SjyRiskVillageStcdRel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SjyRiskVillageStcdRel::getVillageId, villageId);
        this.remove(queryWrapper);

        // 批量插入新的关联关系
        for (SjyRiskVillageStcdRel relation : newRelations) {
            relation.setVillageId(villageId);
        }
        this.saveBatch(newRelations);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
  //  @DataSource(DataSourceType.SLAVE)
    public boolean saveOrUpdateByStcd(SjyRiskVillageStcdRelStcdAddBo bo) {
        LambdaQueryWrapper<SjyRiskVillageStcdRel> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(SjyRiskVillageStcdRel::getStcd, bo.getStcd());
        this.remove(queryWrapper);
        if( bo.getAdcds() == null || bo.getAdcds().isEmpty()) {
            return true; // 如果没有提供行政区划代码，则不进行任何操作
        }else{
            Arrays.stream(bo.getAdcds().split(",")).forEach(adcd -> {
                SjyRiskVillageStcdRel rel = new SjyRiskVillageStcdRel();
                rel.setStcd(bo.getStcd());
                rel.setAdcd(adcd);
                this.save(rel);
            });
        }
        return true;
    }
}