package com.tzslsjy.business.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.tzslsjy.business.domain.StPptnR;
import com.tzslsjy.business.vo.RainInfoHourReqVo;
import com.tzslsjy.business.vo.RainInfoHourVo;

/**
 * 雨量Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface IStPptnRService 
{
    /**
     * 查询雨量
     * 
     * @param STCD 雨量主键
     * @return 雨量
     */
    public StPptnR selectStPptnRBySTCD(String STCD);

    /**
     * 查询雨量列表
     * 
     * @param stPptnR 雨量
     * @return 雨量集合
     */
    public List<StPptnR> selectStPptnRList(StPptnR stPptnR);

    /**
     * 新增雨量
     * 
     * @param stPptnR 雨量
     * @return 结果
     */
    public int insertStPptnR(StPptnR stPptnR);

    /**
     * 修改雨量
     * 
     * @param stPptnR 雨量
     * @return 结果
     */
    public int updateStPptnR(StPptnR stPptnR);

    /**
     * 批量删除雨量
     * 
     * @param STCDs 需要删除的雨量主键集合
     * @return 结果
     */
    public int deleteStPptnRBySTCDs(String[] STCDs);

    /**
     * 删除雨量信息
     * 
     * @param STCD 雨量主键
     * @return 结果
     */
    public int deleteStPptnRBySTCD(String STCD);

    BigDecimal areaRainByAddvcd(String addvcd, Date startTm, Date endTm);

    List<RainInfoHourVo> pageList(RainInfoHourReqVo vo);
}
