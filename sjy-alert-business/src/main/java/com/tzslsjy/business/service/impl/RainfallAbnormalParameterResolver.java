package com.tzslsjy.business.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.domain.SjyStationAbnormalParamData;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 雨量异常预警参数解析器
 * 专注于雨量异常规则参数解析
 * 支持规则类型：34
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class RainfallAbnormalParameterResolver implements AlertRuleParameterResolver {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("34");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析雨量异常预警参数，规则ID: {}, 参数数量: {}", rule.getRuleId(), ruleParams.size());
        
        List<Map<String, Object>> queryParamsList = new ArrayList<>();

        try {
            for (SjyAlertRuleArg ruleParam : ruleParams) {
                Map<String, Object> queryParams = resolveParameterForArg(rule, ruleParam, tm);
                if (queryParams != null) {
                    queryParamsList.add(queryParams);
                }
            }

            log.info("雨量异常预警参数解析完成，规则ID: {}, 生成查询参数组数量: {}", rule.getRuleId(), queryParamsList.size());
            return queryParamsList;

        } catch (Exception e) {
            log.error("解析雨量异常预警参数失败，规则ID: {}, 错误: {}", rule.getRuleId(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析单个参数组
     */
    private Map<String, Object> resolveParameterForArg(SjyAlertRule rule, SjyAlertRuleArg ruleParam, Date tm) {
        try {
            log.debug("解析参数组，argId: {}, argJson: {}", ruleParam.getArgId(), ruleParam.getArgJson());

            // 解析参数JSON
            SjyStationAbnormalParamData params = parseArgJson(ruleParam.getArgJson());
            if (params == null) {
                log.warn("参数组 {} 解析失败", ruleParam.getArgId());
                return null;
            }

            // 验证雨量异常参数
            if (!validateRainfallParams(params)) {
                log.warn("参数组 {} 雨量异常参数验证失败", ruleParam.getArgId());
                return null;
            }

            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("argId", ruleParam.getArgId());
            queryParams.put("ruleId", rule.getRuleId());
            queryParams.put("ruleTypeCode", rule.getRuleTypeId());
            queryParams.put("stationAbnormalParams", params);
            queryParams.put("queryTime", tm);

            log.debug("参数组 {} 解析成功，雨量测站数量: {}", 
                    ruleParam.getArgId(), params.getRainStcdsForDetection().size());

            return queryParams;

        } catch (Exception e) {
            log.error("解析参数组失败，argId: {}, 错误: {}", ruleParam.getArgId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析参数JSON
     * 支持新的标准化格式和旧格式的兼容性
     */
    private SjyStationAbnormalParamData parseArgJson(String argJson) {
        try {
            if (!StringUtils.hasText(argJson)) {
                log.warn("参数JSON为空");
                return null;
            }

            // 先解析为Map
            Map<String, Object> argMap = objectMapper.readValue(argJson, new TypeReference<Map<String, Object>>() {});

            SjyStationAbnormalParamData params = new SjyStationAbnormalParamData();

            // ========== 解析新的标准化格式 ==========
            if (argMap.containsKey("rainfall")) {
                Object rainfallObj = argMap.get("rainfall");
                if (rainfallObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> rainfallMap = (Map<String, Object>) rainfallObj;

                    SjyStationAbnormalParamData.StationAbnormalDetectionParam rainfallParam =
                        new SjyStationAbnormalParamData.StationAbnormalDetectionParam();

                    // 解析时间周期（小时）
                    if (rainfallMap.containsKey("during")) {
                        String duringStr = String.valueOf(rainfallMap.get("during"));
                        if (StringUtils.hasText(duringStr)) {
                            rainfallParam.setDuring(Integer.parseInt(duringStr));
                        }
                    }

                    // 解析阈值（毫米）
                    if (rainfallMap.containsKey("threshold")) {
                        String thresholdStr = String.valueOf(rainfallMap.get("threshold"));
                        if (StringUtils.hasText(thresholdStr)) {
                            rainfallParam.setThreshold(new BigDecimal(thresholdStr));
                        }
                    }

                    params.setRainfall(rainfallParam);
                    log.debug("解析新格式雨量参数: during={}小时, threshold={}毫米",
                            rainfallParam.getDuring(), rainfallParam.getThreshold());
                }
            }

            // ========== 兼容旧格式解析 ==========
            // 解析雨量测站列表（兼容旧版本，新版本中每个规则只针对单个测站）
            if (argMap.containsKey("rainStcds")) {
                Object rainStcdsObj = argMap.get("rainStcds");
                if (rainStcdsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> rainStcds = (List<String>) rainStcdsObj;
                    params.setRainStcds(rainStcds);
                }
            }

            // 解析雨量阈值（兼容旧版本）
            if (argMap.containsKey("overRainVal")) {
                String overRainVal = String.valueOf(argMap.get("overRainVal"));
                if (StringUtils.hasText(overRainVal)) {
                    params.setRainfallThreshold(new BigDecimal(overRainVal));
                }
            }

            // 解析雨量异常检测开关（兼容旧版本）
            if (argMap.containsKey("overRainEnable")) {
                String overRainEnable = String.valueOf(argMap.get("overRainEnable"));
                params.setRainfallDetectionEnabled("1".equals(overRainEnable));
            }

            // 解析全局开关（默认开启）
            params.setGlobalSwitchEnabled(true);

            // 解析检测时间间隔（兼容旧版本，默认5分钟）
            if (argMap.containsKey("detectionInterval")) {
                String detectionInterval = String.valueOf(argMap.get("detectionInterval"));
                if (StringUtils.hasText(detectionInterval)) {
                    params.setDetectionIntervalMinutes(Integer.parseInt(detectionInterval));
                }
            }

            // 兼容旧版本：如果rainStcds为空，尝试使用stcds
            if ((params.getRainStcds() == null || params.getRainStcds().isEmpty()) && argMap.containsKey("stcds")) {
                Object stcdsObj = argMap.get("stcds");
                if (stcdsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> stcds = (List<String>) stcdsObj;
                    params.setStcds(stcds);
                }
            }

            // 日志输出：优先显示新格式参数，如果没有则显示旧格式参数
            if (params.getRainfall() != null) {
                log.debug("参数解析成功（新格式）: 时间周期={}小时, 雨量阈值={}毫米",
                        params.getRainfallDetectionDuringHours(), params.getRainfallDetectionThreshold());
            } else {
                log.debug("参数解析成功（旧格式）: 雨量阈值={}毫米, 雨量检测开关={}, 雨量测站数量={}",
                        params.getRainfallThreshold(), params.getRainfallDetectionEnabled(),
                        params.getRainStcdsForDetection().size());
            }

            return params;

        } catch (Exception e) {
            log.error("解析参数JSON失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证雨量异常参数
     * 支持新的标准化格式和旧格式的兼容性验证
     */
    private boolean validateRainfallParams(SjyStationAbnormalParamData params) {
        // 新版本中通过规则的启用状态控制，不再需要检查检测开关
        // 但为了兼容性，仍然检查旧格式的开关
        if (!params.isRainfallDetectionEnabled()) {
            log.debug("雨量异常检测已禁用（兼容性检查）");
            return false;
        }

        // 检查雨量阈值（使用新的获取方法，自动兼容新旧格式）
        BigDecimal threshold = params.getRainfallDetectionThreshold();
        if (threshold == null || threshold.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("雨量阈值无效: {}", threshold);
            return false;
        }

        // 检查时间周期（使用新的获取方法，自动兼容新旧格式）
        Integer duringHours = params.getRainfallDetectionDuringHours();
        if (duringHours == null || duringHours <= 0) {
            log.warn("检测时间周期无效: {} 小时", duringHours);
            return false;
        }

        // 雨量测站列表可以为空，由数据提供者决定是否查询所有雨量测站
        // 新版本中每个规则只针对单个测站（通过alertStcd字段）
        log.debug("雨量异常参数验证通过: 阈值={}毫米, 时间周期={}小时", threshold, duringHours);
        return true;
    }
}
