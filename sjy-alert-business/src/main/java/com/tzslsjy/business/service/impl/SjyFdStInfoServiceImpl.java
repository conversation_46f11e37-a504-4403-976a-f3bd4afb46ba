package com.tzslsjy.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyFdStInfoMapper;
import com.tzslsjy.business.domain.SjyFdStInfo;
import com.tzslsjy.business.service.ISjyFdStInfoService;

/**
 * 自动监测站基本信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class SjyFdStInfoServiceImpl implements ISjyFdStInfoService 
{
    @Autowired
    private SjyFdStInfoMapper sjyFdStInfoMapper;

    /**
     * 查询自动监测站基本信息
     * 
     * @param stCode 自动监测站基本信息主键
     * @return 自动监测站基本信息
     */
    @Override
    public SjyFdStInfo selectSjyFdStInfoByStCode(String stCode)
    {
        return sjyFdStInfoMapper.selectSjyFdStInfoByStCode(stCode);
    }

    /**
     * 查询自动监测站基本信息列表
     * 
     * @param sjyFdStInfo 自动监测站基本信息
     * @return 自动监测站基本信息
     */
    @Override
    public List<SjyFdStInfo> selectSjyFdStInfoList(SjyFdStInfo sjyFdStInfo)
    {
        return sjyFdStInfoMapper.selectSjyFdStInfoList(sjyFdStInfo);
    }

    /**
     * 新增自动监测站基本信息
     * 
     * @param sjyFdStInfo 自动监测站基本信息
     * @return 结果
     */
    @Override
    public int insertSjyFdStInfo(SjyFdStInfo sjyFdStInfo)
    {
        return sjyFdStInfoMapper.insertSjyFdStInfo(sjyFdStInfo);
    }

    /**
     * 修改自动监测站基本信息
     * 
     * @param sjyFdStInfo 自动监测站基本信息
     * @return 结果
     */
    @Override
    public int updateSjyFdStInfo(SjyFdStInfo sjyFdStInfo)
    {
        return sjyFdStInfoMapper.updateSjyFdStInfo(sjyFdStInfo);
    }

    /**
     * 批量删除自动监测站基本信息
     * 
     * @param stCodes 需要删除的自动监测站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteSjyFdStInfoByStCodes(String[] stCodes)
    {
        return sjyFdStInfoMapper.deleteSjyFdStInfoByStCodes(stCodes);
    }

    /**
     * 删除自动监测站基本信息信息
     * 
     * @param stCode 自动监测站基本信息主键
     * @return 结果
     */
    @Override
    public int deleteSjyFdStInfoByStCode(String stCode)
    {
        return sjyFdStInfoMapper.deleteSjyFdStInfoByStCode(stCode);
    }
}
