package com.tzslsjy.business.service;

import com.tzslsjy.business.vo.StationRuleStatus;
import com.tzslsjy.business.vo.StationWithRuleInfo;

import java.util.List;

/**
 * 测站异常规则服务接口
 * 提供测站异常规则的创建和管理功能
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface StationAbnormalRuleService {

    /**
     * 根据测站ID列表和规则类型创建异常检测规则
     *
     * @param stationIds 测站ID列表
     * @param ruleTypeId 规则类型ID (31-水位异常, 34-雨量异常)
     * @return 创建的规则数量
     */
    int createStationAbnormalRules(List<String> stationIds, String ruleTypeId);

    /**
     * 根据测站ID列表和规则类型启用或禁用异常检测规则
     *
     * @param stationIds 测站ID列表
     * @param ruleTypeId 规则类型ID (31-水位异常, 34-雨量异常)
     * @param enabled 启用标志
     * @return 更新的规则数量
     */
    int toggleStationAbnormalRules(List<String> stationIds, String ruleTypeId, Boolean enabled);

    /**
     * 查询测站异常规则状态
     *
     * @param stationIds 测站ID列表
     * @param ruleTypeId 规则类型ID (31-水位异常, 34-雨量异常)
     * @return 测站规则状态列表
     */
    List<StationRuleStatus> getStationAbnormalRuleStatus(List<String> stationIds, String ruleTypeId);

    /**
     * 批量创建测站异常检测规则（支持水位和雨量）
     *
     * @param stationIds 测站ID列表
     * @param createWaterLevel 是否创建水位异常规则
     * @param createRainfall 是否创建雨量异常规则
     * @return 创建的规则总数量
     */
    int batchCreateStationAbnormalRules(List<String> stationIds, Boolean createWaterLevel, Boolean createRainfall);

    /**
     * 根据测站ID和规则类型删除异常检测规则
     *
     * @param stationIds 测站ID列表
     * @param ruleTypeId 规则类型ID (31-水位异常, 34-雨量异常)
     * @return 删除的规则数量
     */
    int deleteStationAbnormalRules(List<String> stationIds, String ruleTypeId);

    /**
     * 查询绑定了异常检测规则的测站信息列表
     *
     * @param ruleTypeId 规则类型ID (31-水位异常, 34-雨量异常, null-查询所有)
     * @param status 规则状态 (0-禁用, 1-启用, null-查询所有)
     * @param stationName 测站名称关键字
     * @return 测站信息列表
     */
    List<StationWithRuleInfo> getStationsWithAbnormalRules(String ruleTypeId, Integer status, String stationName);
}
