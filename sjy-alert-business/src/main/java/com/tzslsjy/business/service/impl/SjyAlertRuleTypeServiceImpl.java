package com.tzslsjy.business.service.impl;

import java.util.List;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleType;
import com.tzslsjy.business.mapper.SjyAlertRuleMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleTypeMapper;
import com.tzslsjy.business.service.ISjyAlertRuleService;
import com.tzslsjy.business.service.ISjyAlertRuleTypeService;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 规则类型Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
public class SjyAlertRuleTypeServiceImpl implements ISjyAlertRuleTypeService
{
    @Autowired
    private SjyAlertRuleTypeMapper sjyAlertRuleTypeMapper;
    @Autowired
    private SjyAlertRuleMapper sjyAlertRuleMapper;
    @Autowired
    private ISjyAlertRuleService sjyAlertRuleService;
    /**
     * 查询规则类型
     *
     * @param ruleTypeId 规则类型主键
     * @return 规则类型
     */
    @Override
    public SjyAlertRuleType selectSjyAlertRuleTypeByRuleTypeId(Long ruleTypeId)
    {
        return sjyAlertRuleTypeMapper.selectSjyAlertRuleTypeByRuleTypeId(ruleTypeId);
    }

    @Override
    public SjyAlertRuleType selectSjyAlertRuleTypeById(Long ruleTypeId) {
        return sjyAlertRuleTypeMapper.selectSjyAlertRuleTypeByRuleTypeId(ruleTypeId);
    }

    @Override
    public SjyAlertRuleType selectSjyAlertRuleTypeByTypeCode(String typeCode) {
        return sjyAlertRuleTypeMapper.selectSjyAlertRuleTypeByTypeCode(typeCode);
    }

    /**
     * 查询规则类型列表
     *
     * @param sjyAlertRuleType 规则类型
     * @return 规则类型
     */
    @Override
    public List<SjyAlertRuleType> selectSjyAlertRuleTypeList(SjyAlertRuleType sjyAlertRuleType)
    {
        return sjyAlertRuleTypeMapper.selectSjyAlertRuleTypeList(sjyAlertRuleType);
    }

    /**
     * 新增规则类型
     *
     * @param sjyAlertRuleType 规则类型
     * @return 结果
     */
    @Override
    public int insertSjyAlertRuleType(SjyAlertRuleType sjyAlertRuleType)
    {
        sjyAlertRuleType.setCreateTime(DateUtils.getNowDate());
        return sjyAlertRuleTypeMapper.insertSjyAlertRuleType(sjyAlertRuleType);
    }

    /**
     * 修改规则类型
     *
     * @param sjyAlertRuleType 规则类型
     * @return 结果
     */
    @Override
    public int updateSjyAlertRuleType(SjyAlertRuleType sjyAlertRuleType)
    {
        sjyAlertRuleType.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertRuleTypeMapper.updateSjyAlertRuleType(sjyAlertRuleType);
    }

    /**
     * 批量删除规则类型
     *
     * @param ruleTypeIds 需要删除的规则类型主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertRuleTypeByRuleTypeIds(Long[] ruleTypeIds)
    {
        return sjyAlertRuleTypeMapper.deleteSjyAlertRuleTypeByRuleTypeIds(ruleTypeIds);
    }

    /**
     * 删除规则类型信息
     *
     * @param ruleTypeId 规则类型主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertRuleTypeByRuleTypeId(Long ruleTypeId)
    {
        return sjyAlertRuleTypeMapper.deleteSjyAlertRuleTypeByRuleTypeId(ruleTypeId);
    }

    /**
     * 查询规则类型树形列表
     *
     * @param sjyAlertRuleType 规则类型
     * @return 规则类型
     */
    @Override
    public List<SjyAlertRuleType> selectSjyAlertRuleTypeTree(SjyAlertRuleType sjyAlertRuleType)
    {
        List<SjyAlertRuleType> list;
        
        // 如果传入了typeCode或typeName，需要查询该节点及其所有子节点
        if ((sjyAlertRuleType.getTypeCode() != null && !sjyAlertRuleType.getTypeCode().isEmpty()) ||
            (sjyAlertRuleType.getTypeName() != null && !sjyAlertRuleType.getTypeName().isEmpty())) {
            
            list = new java.util.ArrayList<>();
            List<SjyAlertRuleType> matchedNodes = sjyAlertRuleTypeMapper.selectSjyAlertRuleTypeList(sjyAlertRuleType);
            
            // 对每个匹配的节点，递归获取其所有子节点
            for (SjyAlertRuleType matchedNode : matchedNodes) {
                list.add(matchedNode);
                getAllChildrenNodes(matchedNode.getRuleTypeId(), list);
            }
        } else {
            // 如果没有指定typeCode或typeName，查询所有节点
            list = sjyAlertRuleTypeMapper.selectSjyAlertRuleTypeList(sjyAlertRuleType);
        }
        
        // 为每个节点填充规则数据
        for (SjyAlertRuleType alertRuleType : list) {
            List<SjyAlertRule> children = sjyAlertRuleService.selectSjyAlertRuleByRuleType(alertRuleType.getRuleTypeId());
            alertRuleType.setRules(children);
        }
        
        return buildTree(list);
    }

    /**
     * 递归获取所有子节点
     *
     * @param parentId 父节点ID
     * @param resultList 结果列表
     */
    private void getAllChildrenNodes(Long parentId, List<SjyAlertRuleType> resultList) {
        SjyAlertRuleType query = new SjyAlertRuleType();
        query.setParentId(parentId);
        
        List<SjyAlertRuleType> children = sjyAlertRuleTypeMapper.selectSjyAlertRuleTypeList(query);
        for (SjyAlertRuleType child : children) {
            resultList.add(child);
            // 递归获取子节点的子节点
            getAllChildrenNodes(child.getRuleTypeId(), resultList);
        }
    }

    /**
     * 构建树形结构
     *
     * @param list 原始列表数据
     * @return 树形结构列表
     */
    private List<SjyAlertRuleType> buildTree(List<SjyAlertRuleType> list) {
        // 这是一个示例实现，您可能需要根据您的具体需求进行调整
        List<SjyAlertRuleType> treeList = new java.util.ArrayList<>();
        java.util.Map<Long, SjyAlertRuleType> map = new java.util.HashMap<>();
        for (SjyAlertRuleType node : list) {
            map.put(node.getRuleTypeId(), node); // 假设 getRuleTypeId() 是获取ID的方法
        }

        for (SjyAlertRuleType node : list) {
            SjyAlertRuleType parent = map.get(node.getParentId()); // 假设 getParentId() 是获取父ID的方法
            if (parent != null) {
                if (parent.getChildren() == null) { // 假设 getChildren() 和 setChildren() 是处理子节点列表的方法
                    parent.setChildren(new java.util.ArrayList<>());
                }
                parent.getChildren().add(node);
            } else {
                treeList.add(node);
            }
        }
        return treeList;
    }
}
