package com.tzslsjy.business.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.util.StrUtil;
import com.tzslsjy.business.domain.SjyAlertNode;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertPersonNodeRelation;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertPersonMapper;
import com.tzslsjy.business.mapper.SjyAlertPersonNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyVirtualGroupMapper;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 人员Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
public class SjyAlertPersonServiceImpl implements ISjyAlertPersonService
{
    @Autowired
    private SjyAlertPersonMapper sjyAlertPersonMapper;
    @Autowired
    private SjyAlertNodeMapper sjyAlertNodeMapper;
    @Autowired
    private SjyVirtualGroupMapper virtualGroupMapper;
    @Autowired
    private SjyAlertPersonNodeRelationMapper alertPersonNodeRelationMapper;
    /**
     * 查询人员
     * 
     * @param personId 人员主键
     * @return 人员
     */
    @Override
    public SjyAlertPerson selectSjyAlertPersonByPersonId(String personId)
    {
        SjyAlertPerson person = sjyAlertPersonMapper.selectSjyAlertPersonByPersonId(personId);

            // 处理关联的节点信息
            List<SjyAlertNode> alertNodes = person.getAlertNodes();

            person.setDeptNodes(alertNodes.stream().filter(node -> node.getNodeType() == 1).collect(Collectors.toList()));
            // 处理关联的部门信息
            person.setZnNodes(alertNodes.stream().filter(node -> node.getNodeType() == 2).collect(Collectors.toList()));

        return person;
    }

    /**
     * 查询人员列表
     * 
     * @param sjyAlertPerson 人员
     * @return 人员
     */
    @Override
    public List<SjyAlertPerson> selectSjyAlertPersonList(SjyAlertPerson sjyAlertPerson)
    {
        List<SjyAlertPerson> sjyAlertPeople = sjyAlertPersonMapper.selectSjyAlertPersonList(sjyAlertPerson);
        sjyAlertPeople.forEach(person -> {;
            // 处理关联的节点信息
            List<SjyAlertNode> alertNodes = person.getAlertNodes();
            person.setDeptNodes(alertNodes.stream().filter(node -> node.getNodeType() == 1).collect(Collectors.toList()));
            // 处理关联的部门信息
            person.setZnNodes(alertNodes.stream().filter(node -> node.getNodeType() == 2).collect(Collectors.toList()));
        });
        return sjyAlertPeople;
    }

    /**
     * 新增人员
     * 
     * @param sjyAlertPerson 人员
     * @return 结果
     */
    @Override
    public int insertSjyAlertPerson(SjyAlertPerson sjyAlertPerson)
    {
        sjyAlertPerson.setCreateTime(DateUtils.getNowDate());
        return sjyAlertPersonMapper.insertSjyAlertPerson(sjyAlertPerson);
    }

    /**
     * 修改人员
     * 
     * @param sjyAlertPerson 人员
     * @return 结果
     */
    @Override
    public int updateSjyAlertPerson(SjyAlertPerson sjyAlertPerson)
    {
        sjyAlertPerson.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertPersonMapper.updateSjyAlertPerson(sjyAlertPerson);
    }

    /**
     * 批量删除人员
     * 
     * @param personIds 需要删除的人员主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertPersonByPersonIds(String[] personIds)
    {
        return sjyAlertPersonMapper.deleteSjyAlertPersonByPersonIds(personIds);
    }

    /**
     * 删除人员信息
     * 
     * @param personId 人员主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertPersonByPersonId(String personId)
    {
        return sjyAlertPersonMapper.deleteSjyAlertPersonByPersonId(personId);
    }

    @Override
    public List<SjyAlertPerson> selectByMemberIds(List<String> members) {

        List<SjyAlertPerson> rs = new ArrayList<>();

            List<String> orList = new ArrayList<>();
            List<String> ocList = new ArrayList<>();
            List<String> pList = new ArrayList<>();
            List<String> gList = new ArrayList<>();

            for(String member : members){
                if(StrUtil.isNotBlank(member)){
                    if(member.startsWith("o_")){
                        orList.add(member.substring(2));
                    }
                    if(member.startsWith("v_")){
                        ocList.add(member.substring(2));
                    }
                    if(member.startsWith("p_")){
                        pList.add(member.substring(2));
                    }
                    if(member.startsWith("g_")){
                        gList.add(member.substring(2));
                    }
                }
            }
            if(!orList.isEmpty()){
                // 获取祖宗下面的所有节点
                List<String> nodes = new ArrayList<>();
                for(String e:orList){
                    nodes.addAll(sjyAlertNodeMapper.selectNodeIdsByAncestor( e));
                }
                nodes.addAll(orList);
                nodes = nodes.stream().distinct().collect(Collectors.toList());
                List<SjyAlertPerson> sjyAlertPeople = sjyAlertPersonMapper.selectPersonByNodeIds(nodes);
                rs.addAll(sjyAlertPeople);
            }
            if(!ocList.isEmpty()){
                // 获取虚拟分组节点
                List<String> vtList = virtualGroupMapper.selectMembersByIds(ocList);
                //获取人
                for(String vt:vtList){
                    if(StrUtil.isNotBlank(vt)){
                        rs.addAll(selectByMemberIds(Arrays.asList(vt.split(","))));
                    }
                }
            }
            if(!pList.isEmpty()){
                // Assuming pList was List<Long> and now needs to be List<String>
                // The SjyPersonIdReqVo and its 'members' field might need adjustment if it directly produces Longs for person IDs.
                // For now, assuming pList is correctly populated as List<String> or conversion happens before this call.
                List<SjyAlertPerson> sjyAlertPeople = sjyAlertPersonMapper.selectSjyAlertPersonByIds(pList);
                rs.addAll(sjyAlertPeople);
                return sjyAlertPeople;
            }
            if(!gList.isEmpty()){
                // 获取祖宗下面的所有节点
                List<String> nodes = new ArrayList<>();
                for(String e:gList){
                    nodes.addAll(sjyAlertNodeMapper.selectNodeIdsByAncestor( e));
                }
                nodes.addAll(gList);
                nodes = nodes.stream().distinct().collect(Collectors.toList());
                List<SjyAlertPerson> sjyAlertPeople = sjyAlertPersonMapper.selectPersonByNodeIds(nodes);
                rs.addAll(sjyAlertPeople);
            }

        rs = rs.stream().distinct().collect(Collectors.toList());
        return rs;
    }
}
