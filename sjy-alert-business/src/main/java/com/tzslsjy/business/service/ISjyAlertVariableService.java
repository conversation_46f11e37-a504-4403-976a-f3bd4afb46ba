package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertVariable;

/**
 * 预警标签库Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISjyAlertVariableService
{
    /**
     * 查询预警标签库
     *
     * @param variableId 预警标签库主键
     * @return 预警标签库
     */
    public SjyAlertVariable selectSjyAlertVariableByVariableId(Long variableId);

    /**
     * 查询预警标签库列表
     *
     * @param sjyAlertVariable 预警标签库
     * @return 预警标签库集合
     */
    public List<SjyAlertVariable> selectSjyAlertVariableList(SjyAlertVariable sjyAlertVariable);

    /**
     * 新增预警标签库
     *
     * @param sjyAlertVariable 预警标签库
     * @return 结果
     */
    public int insertSjyAlertVariable(SjyAlertVariable sjyAlertVariable);

    /**
     * 修改预警标签库
     *
     * @param sjyAlertVariable 预警标签库
     * @return 结果
     */
    public int updateSjyAlertVariable(SjyAlertVariable sjyAlertVariable);

    /**
     * 批量删除预警标签库
     *
     * @param variableIds 需要删除的预警标签库主键集合
     * @return 结果
     */
    public int deleteSjyAlertVariableByVariableIds(Long[] variableIds);

    /**
     * 删除预警标签库信息
     *
     * @param variableId 预警标签库主键
     * @return 结果
     */
    public int deleteSjyAlertVariableByVariableId(Long variableId);
}
