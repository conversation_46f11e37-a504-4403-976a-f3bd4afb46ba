package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertNodePost;
import com.tzslsjy.business.vo.SjyAlertNodePostVo;
import com.tzslsjy.business.bo.SjyAlertNodePostQueryBo;
import com.tzslsjy.business.bo.SjyAlertNodePostAddBo;
import com.tzslsjy.business.bo.SjyAlertNodePostEditBo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * 节点职位Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface ISjyAlertNodePostService extends IService<SjyAlertNodePost> {
	/**
	 * 查询单个
	 * @return
	 */
	SjyAlertNodePostVo queryById(Long id);

	/**
	 * 查询列表
	 */
	List<SjyAlertNodePostVo> queryList(SjyAlertNodePostQueryBo bo);

	/**
	 * 根据新增业务对象插入节点职位
	 * @param bo 节点职位新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(SjyAlertNodePostAddBo bo);

	/**
	 * 根据编辑业务对象修改节点职位
	 * @param bo 节点职位编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(SjyAlertNodePostEditBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
