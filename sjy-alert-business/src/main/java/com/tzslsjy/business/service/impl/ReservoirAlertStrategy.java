package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.mapper.SjyAlertStSituationMapper;
import com.tzslsjy.business.service.AlertRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;

/**
 * 水库水位预警规则策略
 */
@Slf4j
@Component
public class ReservoirAlertStrategy implements AlertRuleStrategy {
    @Autowired
    SjyAlertStSituationMapper sjyAlertStSituationMapper;
    @Override
    public boolean evaluate(AlertContext context) {
        log.debug("开始评估水库水位预警规则");

        try {
            Map<String, Object> data = context.getData();
            if (data == null || !data.containsKey("argResults") || !(data.get("argResults") instanceof List)) {
                log.warn("水库水位预警数据格式不正确或缺少argResults");
                return false;
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");

            if (CollectionUtils.isEmpty(argResults)) {
                log.warn("参数组结果为空，无法评估水库水位预警");
                return false;
            }

            for (Map<String, Object> argResult : argResults) {
                if (argResult.containsKey("triggeredReservoirs")) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> triggeredReservoirs = 
                            (List<Map<String, Object>>) argResult.get("triggeredReservoirs");
                    if (!CollectionUtils.isEmpty(triggeredReservoirs)) {
                        log.info("水库水位预警触发，参数组ID: {}", argResult.get("argId"));
                        return true;
                    }
                }
            }

            log.debug("水库水位预警未触发");
            return false;

        } catch (Exception e) {
            log.error("评估水库水位预警规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String getType() {
        return "25"; // 对应水库水位预警类型
    }
} 