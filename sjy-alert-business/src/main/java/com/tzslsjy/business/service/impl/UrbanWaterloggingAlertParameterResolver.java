package com.tzslsjy.business.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 城市内涝水位预警参数解析器
 * 特点：
 * 1. 每个监测站点有独立的规则（通过alertStcd字段区分）
 * 2. 无阈值概念 - 当水位超过汛限值时立即触发预警
 * 3. 使用特定的模板格式进行消息格式化
 */
@Component
@Slf4j
public class UrbanWaterloggingAlertParameterResolver implements AlertRuleParameterResolver {

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("26"); // 城市内涝水位预警类型

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析城市内涝水位预警规则参数，规则ID: {}", rule.getRuleId());
        List<Map<String, Object>> paramGroups = new ArrayList<>();

        // 城市内涝预警针对每个测站单独处理，每个测站有其独立的参数（汛限水位）
        // 从规则的alertStcd字段获取测站列表，每个测站单独创建一个参数组
        List<String> stationCodes = new ArrayList<>();
        if (StringUtils.hasText(rule.getAlertStcd())) {
            stationCodes.addAll(Arrays.asList(rule.getAlertStcd().split(",")));
        }

        // 按argId分组处理参数
        Map<Long, List<SjyAlertRuleArg>> groupedParams = ruleParams.stream()
                .collect(Collectors.groupingBy(SjyAlertRuleArg::getArgId));

        // 为每个测站和每个参数组创建独立的查询参数
        for (String stationCode : stationCodes) {
            for (Map.Entry<Long, List<SjyAlertRuleArg>> entry : groupedParams.entrySet()) {
                Long argId = entry.getKey();
                List<SjyAlertRuleArg> argParamsForGroup = entry.getValue();
                Map<String, Object> queryParams = buildQueryParamsForStation(rule, argParamsForGroup, argId, stationCode.trim(), tm);
                if (!queryParams.isEmpty()) {
                    paramGroups.add(queryParams);
                }
            }
        }

        log.debug("城市内涝水位预警规则参数解析完成，共生成{}个参数组", paramGroups.size());
        return paramGroups;
    }

    private Map<String, Object> buildQueryParamsForStation(SjyAlertRule rule, List<SjyAlertRuleArg> argParams,
                                                          Long argId, String stationCode, Date tm) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ruleId", rule.getRuleId());
        queryParams.put("ruleName", rule.getRuleName());
        queryParams.put("ruleTypeId", rule.getRuleTypeId());
        queryParams.put("argId", argId);
        queryParams.put("stationCode", stationCode); // 当前处理的测站编码
        queryParams.put("tm", tm);

        List<SjyUrbanFloodParamData> urbanFloodParamData = new ArrayList<>();
        for (SjyAlertRuleArg param : argParams) {
            if (param.getArgId().equals(argId)) { // 确保只处理当前参数组的参数
                try {
                    SjyUrbanWaterloggingAlertJsonData jsonData = JSON.parseObject(param.getArgJson(), SjyUrbanWaterloggingAlertJsonData.class);
                    if (jsonData != null) {
                        // 只处理与当前测站相关的参数
                        if (stationCode.equals(jsonData.getStcd()) || jsonData.getStcd() == null) {
                            SjyUrbanFloodParamData paramData = new SjyUrbanFloodParamData();
                            paramData.setArgId(param.getArgId());
                            paramData.setAlertLevel(param.getAlertLevel());
                            paramData.setStcd(stationCode);
                            paramData.setWrz(jsonData.getWrz());
                            paramData.setAddvcdNm(jsonData.getAddvcdNm());
                            paramData.setStnm(jsonData.getStnm());
                            urbanFloodParamData.add(paramData);
                        }
                    }
                } catch (Exception e) {
                    log.error("解析城市内涝水位预警参数失败，参数ID: {}, JSON: {}, 错误: {}",
                            param.getArgId(), param.getArgJson(), e.getMessage());
                }
            }
        }

        queryParams.put("urbanFloodParams", urbanFloodParamData);
        return queryParams;
    }
}