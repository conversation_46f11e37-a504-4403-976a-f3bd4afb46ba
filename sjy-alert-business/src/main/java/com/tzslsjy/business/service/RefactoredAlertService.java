package com.tzslsjy.business.service;

import cn.hutool.core.date.DateUtil;
import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleType;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.service.impl.AlertOrchestrator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 重构后的预警服务核心类
 * 使用新的架构：AlertOrchestrator + ParameterResolver + DataProvider
 */
@Service("refactoredAlertService")
@Slf4j
public class RefactoredAlertService {

    @Autowired
    private AlertOrchestrator alertOrchestrator;

    @Autowired
    private List<AlertProcessor> processors;

    @Autowired
    private List<AlertNotifier> notifiers;

    @Autowired
    private ISjyAlertRuleService alertRuleService;

    @Autowired
    private ISjyAlertRuleTypeService alertRuleTypeService;

    @Autowired
    @Qualifier("alertTaskExecutor")
    private TaskExecutor taskExecutor;

    // 规则缓存，提高性能
    private Map<Long, SjyAlertRule> ruleCache = new ConcurrentHashMap<>();

    // 存储策略类型到处理器链的映射
    private Map<String, AlertProcessor> processorChainMap = new HashMap<>();
    private AlertProcessor commonProcessorChain = null;

    @PostConstruct
    public void init() {
        // 初始化协调器的映射关系
        alertOrchestrator.initMappings();
        
        // 初始化处理器链
        initProcessorChain();
        
        log.info("重构后的预警服务初始化完成");
        log.info("支持的规则类型: {}", alertOrchestrator.getSupportedRuleTypes());
    }

    /**
     * 定时刷新规则缓存，保证数据时效性
     */
    @Scheduled(fixedRate = 300000) // 5分钟刷新一次缓存
    public void refreshRuleCache() {
        log.info("开始刷新规则缓存");
        ruleCache.clear();
    }

    /**
     * 处理预警的主入口方法 - 重构版
     * 
     * @param ruleId 规则ID
     * @param tm 预警时间
     * @return 处理结果
     */
    public boolean processAlert(Long ruleId, Date tm) {
        try {
            // 1. 参数预处理
            if (tm == null) {
                tm = DateUtil.date();
            }
            // tm转成整5分钟
            tm = DateUtil.beginOfMinute(DateUtil.offsetMinute(tm, -(tm.getMinutes() % 5)));

            // 2. 获取并验证规则
            SjyAlertRule rule = getAndValidateRule(ruleId);
            if (rule == null) {
                return false;
            }

            // 3. 验证规则类型
            if (!validateRuleType(rule.getRuleTypeId())) {
                return false;
            }

            // 4. 检查新架构是否支持该规则类型
            if (!alertOrchestrator.isRuleTypeFullySupported(rule.getRuleTypeId())) {
                log.warn("规则类型 {} 在新架构中不完全支持，请检查参数解析器、数据提供者和策略配置", rule.getRuleTypeId());
                return false;
            }

            // 5. 使用新架构执行预警规则评估
            log.info("使用新架构处理预警规则: {} (类型: {})", rule.getRuleName(), rule.getRuleTypeId());
            AlertContext context = alertOrchestrator.executeAlert(rule, tm);

            // 6. 检查是否触发预警
            Map<String, Object> data = context.getData();
            if (data == null || !Boolean.TRUE.equals(data.get("overallTriggered"))) {
                log.debug("规则 {} 未触发预警", rule.getRuleName());
                return true; // 规则正常执行但未触发
            }

            // 7. 使用处理器链处理预警结果
            processTriggeredAlert(rule.getRuleTypeId(), context);

            log.info("预警规则 {} 处理完成", rule.getRuleName());
            return true;

        } catch (Exception e) {
            log.error("处理预警规则 {} 时发生错误: {}", ruleId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取并验证规则
     */
    private SjyAlertRule getAndValidateRule(Long ruleId) {
        // 尝试从缓存加载规则信息
        SjyAlertRule rule = ruleCache.get(ruleId);
        if (rule == null) {
            rule = alertRuleService.selectSjyAlertRuleById(ruleId);
            if (rule != null && rule.getStatus() == 1) {
                ruleCache.put(ruleId, rule);
            }
        }

        if (rule == null) {
            log.error("未找到ID为{}的预警规则", ruleId);
            return null;
        }

        if (rule.getStatus() == null || rule.getStatus() != 1) {
            log.warn("规则ID{}已禁用，跳过处理", ruleId);
            return null;
        }

        return rule;
    }

    /**
     * 验证规则类型
     */
    private boolean validateRuleType(String ruleTypeId) {
        SjyAlertRuleType ruleType = alertRuleTypeService.selectSjyAlertRuleTypeById(Long.valueOf(ruleTypeId));
        if (ruleType == null || (ruleType.getStatus() != null && ruleType.getStatus() != 1)) {
            log.warn("规则类型{}无效或已禁用", ruleTypeId);
            return false;
        }
        return true;
    }

    /**
     * 处理被触发的预警
     */
    private void processTriggeredAlert(String ruleTypeId, AlertContext context) {
        log.info("预警已触发，开始处理预警消息和通知");

        // 使用适当的处理器链处理
        AlertProcessor chain = processorChainMap.get(ruleTypeId);
        if (chain != null) {
            log.debug("使用{}类型特定的处理器链处理预警", ruleTypeId);
            chain.process(context);
        } else if (commonProcessorChain != null) {
            log.debug("没有找到{}类型特定的处理器链，使用通用处理链", ruleTypeId);
            commonProcessorChain.process(context);
        } else {
            log.warn("没有可用的处理器链处理预警类型: {}", ruleTypeId);
            return;
        }

        // 发送通知
        sendNotificationsIfNeeded(context);
    }

    /**
     * 根据需要发送通知
     */
    private void sendNotificationsIfNeeded(AlertContext context) {
        if (context.getRecords() == null || context.getRecords().isEmpty()) {
            log.warn("预警记录为空，无法发送通知");
            return;
        }

        SjyAlertRule rule = context.getRule();
        if (rule != null && "1".equals(rule.getSendWay())) {
            sendNotifications(context.getRecords());
        } else {
            log.debug("规则配置为不发送通知或发送方式不为1");
        }
    }

    /**
     * 异步发送通知
     */
    private void sendNotifications(List<SjyAlertSmsRecord> alerts) {
        if (notifiers == null || notifiers.isEmpty()) {
            log.warn("没有可用的通知器");
            return;
        }

        log.info("异步发送预警通知给{}个接收人", alerts.size());

        // 异步发送通知
        taskExecutor.execute(() -> {
            for (AlertNotifier notifier : notifiers) {
                try {
                    int successCount = notifier.notify(alerts);
                    log.info("通知器 {} 成功发送 {}/{} 条通知", 
                            notifier.getNotifierName(), successCount, alerts.size());
                } catch (Exception e) {
                    log.error("通知器 {} 发送失败: {}", 
                            notifier.getNotifierName(), e.getMessage(), e);
                }
            }
        });
    }

    /**
     * 初始化处理器链结构
     */
    private void initProcessorChain() {
        if (processors == null || processors.isEmpty()) {
            log.warn("未找到任何预警处理器");
            return;
        }

        // 对处理器进行分类
        Map<String, List<AlertProcessor>> typeSpecificProcessors = new HashMap<>();
        List<AlertProcessor> commonProcessors = new ArrayList<>();

        for (AlertProcessor processor : processors) {
            if (processor instanceof TypedAlertProcessor) {
                // 如果是有类型的处理器，按类型分组
                TypedAlertProcessor typedProcessor = (TypedAlertProcessor) processor;
                String type = typedProcessor.getSupportedType();

                if (!typeSpecificProcessors.containsKey(type)) {
                    typeSpecificProcessors.put(type, new ArrayList<>());
                }
                typeSpecificProcessors.get(type).add(processor);
                log.info("注册类型为{}的处理器: {}", type, processor.getClass().getSimpleName());
            } else {
                // 没有指定类型的处理器视为通用处理器
                commonProcessors.add(processor);
                log.info("注册通用处理器: {}", processor.getClass().getSimpleName());
            }
        }

        // 构建通用处理器链
        if (!commonProcessors.isEmpty()) {
            buildProcessorChain(commonProcessors);
            commonProcessorChain = commonProcessors.get(0);
        }

        // 为每种类型构建处理器链
        for (Map.Entry<String, List<AlertProcessor>> entry : typeSpecificProcessors.entrySet()) {
            List<AlertProcessor> chainProcessors = new ArrayList<>(entry.getValue());

            // 将通用处理器添加到特定类型处理器链末尾
            if (commonProcessorChain != null && !chainProcessors.isEmpty()) {
                chainProcessors.get(chainProcessors.size() - 1)
                        .setNextProcessor(commonProcessorChain);
            }

            if (!chainProcessors.isEmpty()) {
                buildProcessorChain(chainProcessors);
                processorChainMap.put(entry.getKey(), chainProcessors.get(0));
                log.info("已构建{}类型的处理器链", entry.getKey());
            }
        }
    }

    /**
     * 构建处理器链辅助方法
     */
    private void buildProcessorChain(List<AlertProcessor> chainProcessors) {
        for (int i = 0; i < chainProcessors.size() - 1; i++) {
            chainProcessors.get(i).setNextProcessor(chainProcessors.get(i + 1));
        }
    }

    /**
     * 获取系统状态信息
     */
    public Map<String, Object> getSystemStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("supportedRuleTypes", alertOrchestrator.getSupportedRuleTypes());
        status.put("cachedRulesCount", ruleCache.size());
        status.put("processorChainsCount", processorChainMap.size());
        status.put("notifiersCount", notifiers != null ? notifiers.size() : 0);
        
        // 检查各规则类型的支持情况
        Map<String, Boolean> typeSupport = new HashMap<>();
        for (String type : alertOrchestrator.getSupportedRuleTypes()) {
            typeSupport.put(type, alertOrchestrator.isRuleTypeFullySupported(type));
        }
        status.put("ruleTypeSupport", typeSupport);
        
        return status;
    }

    /**
     * 批量处理预警规则
     */
    public Map<Long, Boolean> processAlerts(List<Long> ruleIds, Date tm) {
        Map<Long, Boolean> results = new HashMap<>();
        
        for (Long ruleId : ruleIds) {
            try {
                boolean result = processAlert(ruleId, tm);
                results.put(ruleId, result);
            } catch (Exception e) {
                log.error("批量处理中，规则 {} 处理失败: {}", ruleId, e.getMessage(), e);
                results.put(ruleId, false);
            }
        }
        
        return results;
    }
} 