package com.tzslsjy.business.service;


import com.tzslsjy.business.bo.*;
import com.tzslsjy.business.domain.StPptnHistD;
import com.tzslsjy.business.vo.StPptnHistDVo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * 单站多年日降雨Service接口
 *
 * <AUTHOR>
 * @date 2022-10-25
 */
public interface IStPptnHistDService extends IService<StPptnHistD> {
	/**
	 * 查询单个
	 * @return
	 */
	StPptnHistDVo queryById(String stcd);

	/**
	 * 查询列表
	 */
	List<StPptnHistDVo> queryList(StPptnHistDQueryBo bo);

	/**
	 * 根据新增业务对象插入单站多年日降雨
	 * @param bo 单站多年日降雨新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(StPptnHistDAddBo bo);

	/**
	 * 根据编辑业务对象修改单站多年日降雨
	 * @param bo 单站多年日降雨编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(StPptnHistDEditBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

	void insertOrUpdateBatch(List<StPptnHistD> list);

    List<StPptnHistRespVo> droughtAnaly(StPptnHistReqVo vo);

    List<StPptnHistRainLineRespVo> rainLine(StPptnHistReqVo bo);

}