package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 水文设备预警处理器
 */
@Component
@Slf4j
public class HydrographicEquipmentAlertProcessor implements AlertProcessor, TypedAlertProcessor {

    @Autowired
    @Qualifier("hydrographicEquipmentAlertTemplateProcessor")
    private AlertTemplateProcessor templateProcessor;
    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService; // 用于获取接收人
    @Autowired
    private SjyAlertNodeMapper alertNodeMapper; // 用于获取接收人
    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper; // 用于获取接收人
    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper; // 用于获取模板

    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public String getSupportedType() {
        return "18"; // 水文设备预警类型
    }

    @Override
    public void process(AlertContext context) {
        log.info("开始处理水文设备预警，规则ID: {}", context.getRule().getRuleId());

        try {
            Map<String, Object> data = context.getData();
            SjyAlertRule rule = context.getRule();

            if (data == null || rule == null || !Boolean.TRUE.equals(data.get("overallTriggered"))) {
                log.debug("水文设备预警未触发或数据不完整，跳过消息生成");
                if (nextProcessor != null) nextProcessor.process(context);
                return;
            }

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");
            if (argResults == null || argResults.isEmpty()) {
                log.warn("参数组结果为空");
                if (nextProcessor != null) nextProcessor.process(context);
                return;
            }

            List<SjyAlertSmsRecord> allRecords = new ArrayList<>();

            for (Map<String, Object> argResult : argResults) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> triggeredEquipments = 
                        (List<Map<String, Object>>) argResult.get("triggeredEquipments");

                if (triggeredEquipments == null || triggeredEquipments.isEmpty()) {
                    continue; //此参数组没有触发的设备
                }

                String argId =  argResult.get("argId").toString();
                List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
                if (templates.isEmpty()) {
                    log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                    continue;
                }

                // 为每个模板生成消息
                for (SjyAlertSmsTemplate template : templates) {
                    // 将触发的设备列表放入当前处理的数据中，供模板引擎使用
                    Map<String, Object> templateData = new HashMap<>(argResult);
                    templateData.put("list", triggeredEquipments); // DefaultAlertTemplateProcessor 使用 "list" 作为列表键
                    templateData.put("tm",triggeredEquipments.get(0).get("currentTime")); // 假设所有设备的时间相同，取第一个设备的时间
                    templateData.put("duringTm",triggeredEquipments.get(0).get("timeoutHours"));
                    SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                    if (alert != null) {
                        // 根据规则或参数组获取接收人
                        List<SjyAlertPerson> receivers = getReceivers(rule, argId);
                        List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                        List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                        allRecords.addAll(mergedAlerts);
                        log.info("参数组 {} 生成 {} 条水文设备预警消息", argId, mergedAlerts.size());
                    }
                }
            }
            context.setRecords(allRecords);
            log.info("总共生成 {} 条水文设备预警消息", allRecords.size());

        } catch (Exception e) {
            log.error("处理水文设备预警失败: {}", e.getMessage(), e);
        }

        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    private List<SjyAlertSmsTemplate> getTemplates(Long ruleId, String argId) {

        List<SjyAlertSmsTemplate> templates = alertSmsTemplateMapper.selectOneByTypeAndRuleId(ruleId, null);
        if (templates == null || templates.isEmpty()) {
            log.warn("未找到规则ID {}，类型7的模板", ruleId);
            // 可以尝试获取一个通用的模板，或者返回空列表
            // templates = alertSmsTemplateMapper.selectOneByTypeAndRuleId(ruleId, "GENERAL_DEVICE_ALERT"); 
            return new ArrayList<>();
        }
        return templates;
    }

    private List<SjyAlertPerson> getReceivers(SjyAlertRule rule, String argId) {
        List<String> nodeIds    = alertRuleNodeRelationMapper.selectNodeIdsByArgId(  argId);
        if( nodeIds == null || nodeIds.isEmpty()) {
            SjyAlertPerson admin = new SjyAlertPerson();
            admin.setName("admin");
            admin.setPhone("15306587076");
            return Collections.singletonList(admin);
        }
        List<SjyAlertPerson> receivers  =sjyAlertPersonService.selectByMemberIds(nodeIds);

        if (receivers.isEmpty()) {
            log.warn("规则ID {} 及其参数组 {} 均未配置接收人或未找到，使用默认管理员", rule.getRuleId(), argId);
            SjyAlertPerson admin = new SjyAlertPerson();
            admin.setName("admin");
            admin.setPhone("15306587076"); 
            return Collections.singletonList(admin);
        }
        // 去重
        return receivers.stream().distinct().collect(Collectors.toList());
    }
} 