package com.tzslsjy.business.service.impl.notifier;

import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.mapper.SjyAlertSmsRecordMapper;
import com.tzslsjy.business.service.AlertNotifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.List;

/**
 * 预警通知抽象基类
 * 提供通用的通知逻辑和状态管理
 */
public abstract class AbstractAlertNotifier implements AlertNotifier {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @Autowired
    protected SjyAlertSmsRecordMapper recordMapper;
    /**
     * 获取此通知器的唯一类型代码
     */
    public abstract String getNotifierType();

    /**
     * 获取此通知器的显示名称
     */
    public abstract String getNotifierName();

    @Override
    public int notify(List<SjyAlertSmsRecord> records) {
        if (records == null || records.isEmpty()) {
            logger.warn("没有需要发送的预警记录");
            return 0;
        }

        logger.info("开始通过{}发送{}条预警", getNotifierName(), records.size());
        int successCount = 0;

        for (SjyAlertSmsRecord record : records) {
            try {
                if (!supports(record)) {
                    logger.debug("预警记录不支持通过{}发送，跳过", getNotifierName());
                    continue;
                }

                // 发送前标记状态
                updateRecordBeforeSend(record);

                // 执行具体的发送逻辑
                boolean success = doSendAlert(record);

                // 发送后更新状态
                updateRecordAfterSend(record, success);

                if (success) {
                    successCount++;
                }
            } catch (Exception e) {
                logger.error("通过{}发送预警失败: {}", getNotifierName(), e.getMessage(), e);
                updateRecordAfterSend(record, false);
            }
        }

        logger.info("{}发送完成，成功: {}/{}", getNotifierName(), successCount, records.size());
        return successCount;
    }

    /**
     * 执行具体的发送逻辑
     * @param record 预警记录
     * @return 是否发送成功
     */
    protected abstract boolean doSendAlert(SjyAlertSmsRecord record);

    /**
     * 发送前更新记录状态
     */
    protected void updateRecordBeforeSend(SjyAlertSmsRecord record) {
        try {
            record.setSendTime(new Date());
            record.setSendType(getNotifierType());
            record.setStatus(1L); // 发送中
            recordMapper.updateSjyAlertSmsRecord(record);
        } catch (Exception e) {
            logger.error("更新预警发送状态失败", e);
        }
    }

    /**
     * 发送后更新记录状态
     */
    protected void updateRecordAfterSend(SjyAlertSmsRecord record, boolean success) {
        try {
            record.setStatus(success ? 2L : 3L); // 2=成功，3=失败
            record.setUpdateTime(new Date());
            recordMapper.updateSjyAlertSmsRecord(record);
        } catch (Exception e) {
            logger.error("更新预警发送结果失败", e);
        }
    }

    @Override
    public boolean supports(SjyAlertSmsRecord record) {
        // 基础实现：检查记录是否有效且未发送
        return record != null &&
               (record.getStatus() == null || record.getStatus() == 0L);
    }
}
