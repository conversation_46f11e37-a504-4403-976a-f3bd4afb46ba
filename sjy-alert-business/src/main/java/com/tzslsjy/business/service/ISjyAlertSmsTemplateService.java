package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;

/**
 * 短信模板Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISjyAlertSmsTemplateService
{
    /**
     * 查询短信模板
     *
     * @param templateId 短信模板主键
     * @return 短信模板
     */
    public SjyAlertSmsTemplate selectSjyAlertSmsTemplateByTemplateId(Long templateId);

    /**
     * 查询短信模板列表
     *
     * @param sjyAlertSmsTemplate 短信模板
     * @return 短信模板集合
     */
    public List<SjyAlertSmsTemplate> selectSjyAlertSmsTemplateList(SjyAlertSmsTemplate sjyAlertSmsTemplate);

    /**
     * 新增短信模板
     *
     * @param sjyAlertSmsTemplate 短信模板
     * @return 结果
     */
    public int insertSjyAlertSmsTemplate(SjyAlertSmsTemplate sjyAlertSmsTemplate);

    /**
     * 修改短信模板
     *
     * @param sjyAlertSmsTemplate 短信模板
     * @return 结果
     */
    public int updateSjyAlertSmsTemplate(SjyAlertSmsTemplate sjyAlertSmsTemplate);

    /**
     * 批量删除短信模板
     *
     * @param templateIds 需要删除的短信模板主键集合
     * @return 结果
     */
    public int deleteSjyAlertSmsTemplateByTemplateIds(Long[] templateIds);

    /**
     * 删除短信模板信息
     *
     * @param templateId 短信模板主键
     * @return 结果
     */
    public int deleteSjyAlertSmsTemplateByTemplateId(Long templateId);
}
