package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.business.domain.vo.NodeTreeVO;

/**
 * 测站基础信息Service接口
 *
 * <AUTHOR>
 * @date 2025-04-18
 */
public interface IStStbprpBService
{
    /**
     * 查询测站基础信息
     *
     * @param STCD 测站基础信息主键
     * @return 测站基础信息
     */
    public StStbprpB selectStStbprpBBySTCD(String STCD);

    /**
     * 查询测站基础信息列表
     *
     * @param stStbprpB 测站基础信息
     * @return 测站基础信息集合
     */
    public List<StStbprpB> selectStStbprpBList(StStbprpB stStbprpB);

    /**
     * 新增测站基础信息
     *
     * @param stStbprpB 测站基础信息
     * @return 结果
     */
    public int insertStStbprpB(StStbprpB stStbprpB);

    /**
     * 修改测站基础信息
     *
     * @param stStbprpB 测站基础信息
     * @return 结果
     */
    public int updateStStbprpB(StStbprpB stStbprpB);

    /**
     * 批量删除测站基础信息
     *
     * @param STCDs 需要删除的测站基础信息主键集合
     * @return 结果
     */
    public int deleteStStbprpBBySTCDs(String[] STCDs);

    /**
     * 删除测站基础信息信息
     *
     * @param STCD 测站基础信息主键
     * @return 结果
     */
    public int deleteStStbprpBBySTCD(String STCD);

    /**
     * 查询测站基础信息列表（包含关联的村庄信息）
     *
     * @param stStbprpB 测站基础信息查询条件
     * @return 包含村庄信息的测站列表
     */
    public List<StStbprpB> selectStStbprpBListWithVillage(StStbprpB stStbprpB);

    List<NodeTreeVO> listWithAdcd(StStbprpB stStbprpB);
}
