package com.tzslsjy.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.StRiverRMapper;
import com.tzslsjy.business.domain.StRiverR;
import com.tzslsjy.business.service.IStRiverRService;

/**
 * 河道水情Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class StRiverRServiceImpl implements IStRiverRService 
{
    @Autowired
    private StRiverRMapper stRiverRMapper;

    /**
     * 查询河道水情
     * 
     * @param STCD 河道水情主键
     * @return 河道水情
     */
    @Override
    public StRiverR selectStRiverRBySTCD(String STCD)
    {
        return stRiverRMapper.selectStRiverRBySTCD(STCD);
    }

    /**
     * 查询河道水情列表
     * 
     * @param stRiverR 河道水情
     * @return 河道水情
     */
    @Override
    public List<StRiverR> selectStRiverRList(StRiverR stRiverR)
    {
        return stRiverRMapper.selectStRiverRList(stRiverR);
    }

    /**
     * 新增河道水情
     * 
     * @param stRiverR 河道水情
     * @return 结果
     */
    @Override
    public int insertStRiverR(StRiverR stRiverR)
    {
        return stRiverRMapper.insertStRiverR(stRiverR);
    }

    /**
     * 修改河道水情
     * 
     * @param stRiverR 河道水情
     * @return 结果
     */
    @Override
    public int updateStRiverR(StRiverR stRiverR)
    {
        return stRiverRMapper.updateStRiverR(stRiverR);
    }

    /**
     * 批量删除河道水情
     * 
     * @param STCDs 需要删除的河道水情主键
     * @return 结果
     */
    @Override
    public int deleteStRiverRBySTCDs(String[] STCDs)
    {
        return stRiverRMapper.deleteStRiverRBySTCDs(STCDs);
    }

    /**
     * 删除河道水情信息
     * 
     * @param STCD 河道水情主键
     * @return 结果
     */
    @Override
    public int deleteStRiverRBySTCD(String STCD)
    {
        return stRiverRMapper.deleteStRiverRBySTCD(STCD);
    }
}
