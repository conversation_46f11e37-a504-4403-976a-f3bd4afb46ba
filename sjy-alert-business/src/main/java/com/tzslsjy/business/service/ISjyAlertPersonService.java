package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertPerson;

import java.util.List;

/**
 * 人员Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface ISjyAlertPersonService 
{
    /**
     * 查询人员
     * 
     * @param personId 人员主键
     * @return 人员
     */
    public SjyAlertPerson selectSjyAlertPersonByPersonId(String personId);

    /**
     * 查询人员列表
     * 
     * @param sjyAlertPerson 人员
     * @return 人员集合
     */
    public List<SjyAlertPerson> selectSjyAlertPersonList(SjyAlertPerson sjyAlertPerson);

    /**
     * 新增人员
     * 
     * @param sjyAlertPerson 人员
     * @return 结果
     */
    public int insertSjyAlertPerson(SjyAlertPerson sjyAlertPerson);

    /**
     * 修改人员
     * 
     * @param sjyAlertPerson 人员
     * @return 结果
     */
    public int updateSjyAlertPerson(SjyAlertPerson sjyAlertPerson);

    /**
     * 批量删除人员
     * 
     * @param personIds 需要删除的人员主键集合
     * @return 结果
     */
    public int deleteSjyAlertPersonByPersonIds(String[] personIds);

    /**
     * 删除人员信息
     * 
     * @param personId 人员主键
     * @return 结果
     */
    public int deleteSjyAlertPersonByPersonId(String personId);

    List<SjyAlertPerson> selectByMemberIds(List<String> nodeIds);
}
