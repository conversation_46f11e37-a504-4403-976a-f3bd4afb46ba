package com.tzslsjy.business.service.impl.notifier;

import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.mapper.SjyAlertPersonMapper;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 短信预警通知实现
 */
@Component
public class SmsAlertNotifier extends AbstractAlertNotifier {

//    @Autowired
//    private ISmsService smsService;

    @Autowired
    private SjyAlertPersonMapper personMapper;

    @Override
    public String getNotifierType() {
        return "sms";
    }

    @Override
    public String getNotifierName() {
        return "短信通知";
    }

    @Override
    protected boolean doSendAlert(SjyAlertSmsRecord record) {
        try {
            // 获取接收人信息
            String personId = record.getPersonId();
            if (personId == null) {
                logger.warn("预警记录未指定接收人ID");
                return false;
            }

            // 查询接收人手机号
            SjyAlertPerson person = personMapper.selectSjyAlertPersonByPersonId(personId);
            if (person == null || person.getPhone() == null || person.getPhone().isEmpty()) {
                logger.warn("接收人{}不存在或未设置手机号", personId);
                return false;
            }

            // 调用短信服务


            // 根据短信服务的返回判断是否成功
            return true;
        } catch (Exception e) {
            logger.error("发送短信失败: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean supports(SjyAlertSmsRecord record) {
        if (!super.supports(record)) {
            return false;
        }

        // 检查是否有接收人ID和内容
        return record.getPersonId() != null &&
               record.getContent() != null &&
               !record.getContent().isEmpty();
    }
}
