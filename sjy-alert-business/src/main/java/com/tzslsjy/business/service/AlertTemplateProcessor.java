package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.*;

import java.util.List;
import java.util.Map;

/**
 * 预警模板处理器接口
 * 负责根据规则参数和数据生成预警消息
 */
public interface AlertTemplateProcessor {

    /**
     * 处理单个参数组的模板
     *
     * @param context 预警上下文
     * @param smsTemplate 模版
     * @param data 该参数组的数据
     * @return 生成的预警消息
     */
    SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate smsTemplate, Map<String, Object> data);

    /**
     * 根据接收人分发预警消息
     *
     * @param context 预警上下文
     * @param alert 需要分发的基础预警消息 
     * @param people 需要接收预警的人员列表
     * @return 分发后的预警消息列表
     */
    List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> people);

    /**
     * 合并多组接收人
     *
     * @param context 预警上下文
     * @param receivers 多组接收人
     * @return 合并后的接收人列表
     */
    List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers);
}
