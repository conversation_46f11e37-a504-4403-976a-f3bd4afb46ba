package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertSmsRecord;

import java.util.List;

/**
 * 预警通知器接口
 * 负责发送预警通知
 */
public interface AlertNotifier {
    
    /**
     * 发送预警通知
     * 
     * @param alerts 要发送的预警记录列表
     * @return 成功发送的通知数量
     */
    int notify(List<SjyAlertSmsRecord> alerts);
    
    /**
     * 获取通知器名称
     * 
     * @return 通知器名称
     */
    String getNotifierName();
    
    /**
     * 获取通知方式类型编码
     * 
     * @return 通知方式类型编码
     */
    String getNotifierType();
    
    /**
     * 检查是否支持指定的预警记录
     * 
     * @param record 预警记录
     * @return 是否支持发送
     */
    default boolean supports(SjyAlertSmsRecord record) {
        return true;
    }
    
    /**
     * 检查此通知器是否可用
     * 
     * @return true如果可用，false如果不可用
     */
    default boolean isAvailable() {
        return true;
    }
}
