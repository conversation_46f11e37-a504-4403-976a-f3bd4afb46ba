package com.tzslsjy.business.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.tzslsjy.business.bo.StPptnHistReqVo;
import com.tzslsjy.business.vo.RainInfoHourReqVo;
import com.tzslsjy.business.vo.RainInfoHourVo;
import com.tzslsjy.business.vo.StPptnHistRespVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.StPptnRMapper;
import com.tzslsjy.business.domain.StPptnR;
import com.tzslsjy.business.service.IStPptnRService;

/**
 * 雨量Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class StPptnRServiceImpl implements IStPptnRService 
{
    @Autowired
    private StPptnRMapper stPptnRMapper;

    /**
     * 查询雨量
     * 
     * @param STCD 雨量主键
     * @return 雨量
     */
    @Override
    public StPptnR selectStPptnRBySTCD(String STCD)
    {
        return stPptnRMapper.selectStPptnRBySTCD(STCD);
    }

    /**
     * 查询雨量列表
     * 
     * @param stPptnR 雨量
     * @return 雨量
     */
    @Override
    public List<StPptnR> selectStPptnRList(StPptnR stPptnR)
    {
        return stPptnRMapper.selectStPptnRList(stPptnR);
    }

    /**
     * 新增雨量
     * 
     * @param stPptnR 雨量
     * @return 结果
     */
    @Override
    public int insertStPptnR(StPptnR stPptnR)
    {
        return stPptnRMapper.insertStPptnR(stPptnR);
    }

    /**
     * 修改雨量
     * 
     * @param stPptnR 雨量
     * @return 结果
     */
    @Override
    public int updateStPptnR(StPptnR stPptnR)
    {
        return stPptnRMapper.updateStPptnR(stPptnR);
    }

    /**
     * 批量删除雨量
     * 
     * @param STCDs 需要删除的雨量主键
     * @return 结果
     */
    @Override
    public int deleteStPptnRBySTCDs(String[] STCDs)
    {
        return stPptnRMapper.deleteStPptnRBySTCDs(STCDs);
    }

    /**
     * 删除雨量信息
     * 
     * @param STCD 雨量主键
     * @return 结果
     */
    @Override
    public int deleteStPptnRBySTCD(String STCD)
    {
        return stPptnRMapper.deleteStPptnRBySTCD(STCD);
    }
    @Override
    public  List< RainInfoHourVo> pageList(RainInfoHourReqVo vo) {
        if(vo.getEndTm()==null){
            vo.setEndTm(DateUtil.offsetHour(DateUtil.beginOfDay(new Date()),8));
        }
        if(vo.getStartTm()==null){
            vo.setStartTm(DateUtil.offsetHour(DateUtil.beginOfDay(new Date()),-24));
        }
        vo.setAddvcd("331082");
        List< RainInfoHourVo> rs = stPptnRMapper.loadPage(vo);
        Map<String, RainInfoHourVo> stcdMap = rs.stream().collect(Collectors.toMap(RainInfoHourVo::getStcd, Function.identity(), (key1, key2) -> key2));
        Set<String> stcds = stcdMap.keySet();
        //查找实时雨量表
        vo.setStcds(stcds);
        List<RainInfoHourVo> rains = stPptnRMapper.getSumLeftRain(vo).stream().filter(e -> e.getDrp() != null).collect(Collectors.toList());
        long stcdSize = rains.stream().map(RainInfoHourVo::getStcd).distinct().count();
        if (stcdSize == 0) {
            return rs;
        }
        Map<String, RainInfoHourVo> collect = rains.stream().collect(Collectors.toMap(RainInfoHourVo::getStcd, Function.identity()));
        //雨量
        rs.forEach(e -> {
            RainInfoHourVo rainInfoHourVo = collect.get(e.getStcd());
            if (rainInfoHourVo != null) {
                e.setDrp(rainInfoHourVo.getDrp());
                e.setZ(rainInfoHourVo.getZ());
                e.setZTm(rainInfoHourVo.getZTm());
                e.setJurisdiction(rainInfoHourVo.getJurisdiction());
                e.setJurisdictionNum(rainInfoHourVo.getJurisdictionNum());
                e.setCity(rainInfoHourVo.getCity());
            }
        });

        return rs;


    }
    @Override
    public BigDecimal areaRainByAddvcd(String addvcd, Date startTm, Date endTm) {
        RainInfoHourReqVo infoHourReqVo = new RainInfoHourReqVo();
        infoHourReqVo.setAddvcd("331082");
        List< RainInfoHourVo> stStbprpBS = stPptnRMapper.loadPage(infoHourReqVo);
        Map<String, RainInfoHourVo> stcdMap = stStbprpBS.stream().collect(Collectors.toMap(RainInfoHourVo::getStcd, Function.identity(), (key1, key2) -> key2));
        Set<String> stcds = stcdMap.keySet();

        //查找实时日雨量表
        RainInfoHourReqVo rainInfoHourReqVo = new RainInfoHourReqVo();
        rainInfoHourReqVo.setStartTm(startTm);
        rainInfoHourReqVo.setEndTm(DateUtil.offsetHour(DateUtil.beginOfDay(DateUtil.offsetDay(endTm,1)),8));
        rainInfoHourReqVo.setStcds(stcds);
        List<RainInfoHourVo> rains = stPptnRMapper.getSumLeftRain(rainInfoHourReqVo).stream().filter(e -> e.getDrp() != null).collect(Collectors.toList());
        long stcdSize = rains.stream().map(RainInfoHourVo::getStcd).distinct().count();
        if (stcdSize == 0) {
            return BigDecimal.ZERO;
        }
        //计算雨量
        BigDecimal totalRain = rains.stream()
                .map(RainInfoHourVo::getDrp)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        //计算平均雨量
       return totalRain.divide(BigDecimal.valueOf(stcdSize), 2, RoundingMode.HALF_UP);


    }


}
