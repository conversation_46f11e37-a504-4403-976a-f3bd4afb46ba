package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsStatsRequest;
import com.tzslsjy.business.vo.SjyAlertStatRespVo;

import javax.validation.Valid;
import java.util.List;

/**
 * 预警消息记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
public interface ISjyAlertSmsRecordService 
{
    /**
     * 查询预警消息记录
     * 
     * @param alertSmsId 预警消息记录主键
     * @return 预警消息记录
     */
    public SjyAlertSmsRecord selectSjyAlertSmsRecordByAlertSmsId(Long alertSmsId);

    /**
     * 查询预警消息记录列表
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 预警消息记录集合
     */
    public List<SjyAlertSmsRecord> selectSjyAlertSmsRecordList(SjyAlertSmsRecord sjyAlertSmsRecord);

    /**
     * 新增预警消息记录
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 结果
     */
    public int insertSjyAlertSmsRecord(SjyAlertSmsRecord sjyAlertSmsRecord);

    /**
     * 修改预警消息记录
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 结果
     */
    public int updateSjyAlertSmsRecord(SjyAlertSmsRecord sjyAlertSmsRecord);

    /**
     * 批量删除预警消息记录
     * 
     * @param alertSmsIds 需要删除的预警消息记录主键集合
     * @return 结果
     */
    public int deleteSjyAlertSmsRecordByAlertSmsIds(Long[] alertSmsIds);

    /**
     * 删除预警消息记录信息
     * 
     * @param alertSmsId 预警消息记录主键
     * @return 结果
     */
    public int deleteSjyAlertSmsRecordByAlertSmsId(Long alertSmsId);

    /**
     * 查询指定时间范围内的SMS记录
     * 
     * @param startTime 开始时间
     * @return SMS记录列表
     */
    public List<SjyAlertSmsRecord> selectSjyAlertSmsRecordsByTimeRange(java.util.Date startTime);

    /**
     * 根据预警记录ID列表和发送方式统计预警消息发送状态
     *
     * @param statsRequest 统计请求参数
     * @return 统计结果列表
     */
    public List<com.tzslsjy.business.domain.vo.SjyAlertSmsStatsVO> getSjyAlertSmsStats(com.tzslsjy.business.domain.SjyAlertSmsStatsRequest statsRequest);

    SjyAlertStatRespVo alertStats(@Valid SjyAlertSmsStatsRequest statsRequest);
}
