package com.tzslsjy.business.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 河道水位预警参数解析器
 */
@Component
@Slf4j
public class RiverLevelAlertParameterResolver implements AlertRuleParameterResolver {

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("15"); // 河道水位预警类型

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析河道水位预警规则参数，规则ID: {}", rule.getRuleId());
        List<Map<String, Object>> paramGroups = new ArrayList<>();

        // 按参数组ID分组
        Map<Long, List<SjyAlertRuleArg>> groupedParams = ruleParams.stream()
                .collect(Collectors.groupingBy(SjyAlertRuleArg::getArgId));

        for (Map.Entry<Long, List<SjyAlertRuleArg>> entry : groupedParams.entrySet()) {
            Long argId = entry.getKey();
            List<SjyAlertRuleArg> argParamsForGroup = entry.getValue();
            Map<String, Object> queryParams = buildQueryParamsForGroup(rule, argParamsForGroup, argId, tm);
            if (!queryParams.isEmpty()) {
                paramGroups.add(queryParams);
            }
        }
        return paramGroups;
    }

    private Map<String, Object> buildQueryParamsForGroup(SjyAlertRule rule, List<SjyAlertRuleArg> argParams, Long argId, Date tm) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ruleId", rule.getRuleId());
        queryParams.put("ruleName", rule.getRuleName());
        queryParams.put("ruleTypeId", rule.getRuleTypeId());
        queryParams.put("argId", argId);
        queryParams.put("tm", tm);

        // 解析河道水位预警参数
        List<SjyRiverLevelAlertParamData> riverLevelAlertParamData = new ArrayList<>();
        for (SjyAlertRuleArg param : argParams) {
            if (param.getArgId().equals(argId)) { // 确保只处理当前参数组的参数
                try {
                    SjyRiverLevelAlertJsonData jsonData = JSON.parseObject(param.getArgJson(), SjyRiverLevelAlertJsonData.class);
                    SjyRiverLevelAlertParamData paramData = new SjyRiverLevelAlertParamData();
                    paramData.setArgId(param.getArgId());
                    paramData.setAlertLevel(param.getAlertLevel());
                    if (jsonData != null) {
                        paramData.setVal(jsonData.getVal());
                    }
                    riverLevelAlertParamData.add(paramData);
                } catch (Exception e) {
                    log.error("解析河道水位预警参数失败，参数ID: {}, JSON: {}, 错误: {}",
                            param.getArgId(), param.getArgJson(), e.getMessage());
                }
            }
        }
        queryParams.put("riverLevelAlertParams", riverLevelAlertParamData);
        
        // 获取测站编码列表
        List<String> stcds = new ArrayList<>();
        if (StringUtils.hasText(rule.getAlertStcd())) {
            stcds.addAll(Arrays.asList(rule.getAlertStcd().split(",")));
        }
        queryParams.put("stcds", stcds);

        return queryParams;
    }
} 