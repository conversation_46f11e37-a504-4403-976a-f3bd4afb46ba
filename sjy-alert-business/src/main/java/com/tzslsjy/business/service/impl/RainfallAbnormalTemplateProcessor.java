package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import cn.hutool.core.date.DateUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 雨量异常预警模板处理器
 * 专注于雨量异常预警模板处理
 * 支持规则类型：34
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component("rainfallAbnormalTemplateProcessor")
@Slf4j
public class RainfallAbnormalTemplateProcessor implements AlertTemplateProcessor {

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate template, Map<String, Object> data) {
        try {
            // 雨量异常预警特有的数据处理
            enrichRainfallAbnormalSpecificData(template, data);

            // 使用默认处理器进行模板处理
            return defaultTemplateProcessor.processTemplate(context, template, data);
        } catch (Exception e) {
            log.error("处理雨量异常预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeAlerts(context, alert, receivers);
    }

    /**
     * 雨量异常预警特有的数据处理
     */
    @SuppressWarnings("unchecked")
    private void enrichRainfallAbnormalSpecificData(SjyAlertSmsTemplate template, Map<String, Object> data) {
        // 添加预警类型标记
        data.put("alertTypeText", "雨量异常");
        
        // 处理时间字段
        addTimeFields(data);
        
        // 处理雨量异常数据
        if (data.containsKey("abnormalResults")) {
            List<Map<String, Object>> abnormalResults = (List<Map<String, Object>>) data.get("abnormalResults");
            
            if (abnormalResults != null && !abnormalResults.isEmpty()) {
                // 转换数据格式以匹配模板要求
                List<Map<String, Object>> listData = convertAbnormalDataForTemplate(abnormalResults);
                
                // 设置为list键，供模板引擎处理{list}占位符
                data.put("list", listData);
                
                // 构建雨量异常描述（保持兼容性）
                StringBuilder abnormalDesc = new StringBuilder();
                for (int i = 0; i < abnormalResults.size(); i++) {
                    Map<String, Object> abnormal = abnormalResults.get(i);
                    String stcd = (String) abnormal.get("stcd");
                    String stnm = (String) abnormal.get("stnm");
                    Object value = abnormal.get("value");
                    
                    abnormalDesc.append(stnm != null ? stnm : stcd);
                    abnormalDesc.append("测站雨量");
                    abnormalDesc.append(value).append("毫米");
                    
                    if (i < abnormalResults.size() - 1) {
                        abnormalDesc.append("，");
                    }
                }
                
                data.put("abnormalDesc", abnormalDesc.toString());
                data.put("abnormalCount", abnormalResults.size());
                
                // 添加第一个异常的详细信息，用于模板中的单个异常显示
                if (!abnormalResults.isEmpty()) {
                    data.put("firstAbnormal", abnormalResults.get(0));
                }
            }
        }
        
        // 添加行政区划信息
        if (data.containsKey("jurisdiction")) {
            String jurisdiction = (String) data.get("jurisdiction");
            data.put("jurisdictionText", jurisdiction);
        }
    }
    
    /**
     * 添加时间字段
     */
    private void addTimeFields(Map<String, Object> data) {
        // 如果数据中已有时间字段，直接使用
        if (data.containsKey("startTm") && data.containsKey("endTm")) {
            return;
        }
        
        // 尝试从异常结果中获取时间信息
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> abnormalResults = (List<Map<String, Object>>) data.get("abnormalResults");
        if (abnormalResults != null && !abnormalResults.isEmpty()) {
            // 获取第一个异常记录的时间
            Map<String, Object> firstAbnormal = abnormalResults.get(0);
            if (firstAbnormal.containsKey("time")) {
                Date abnormalTime = (Date) firstAbnormal.get("time");
                if (abnormalTime != null) {
                    // 假设检测周期为1小时（可以从配置中获取）
                    Date startTime = DateUtil.offsetHour(abnormalTime, -1);
                    data.put("startTm", DateUtil.format(startTime, "MM月dd日HH时"));
                    data.put("endTm", DateUtil.format(abnormalTime, "MM月dd日HH时"));
                    return;
                }
            }
        }
        
        // 如果没有具体时间信息，使用当前时间作为结束时间
        Date currentTime = new Date();
        Date startTime = DateUtil.offsetHour(currentTime, -1); // 默认1小时前
        data.put("startTm", DateUtil.format(startTime, "MM月dd日HH时"));
        data.put("endTm", DateUtil.format(currentTime, "MM月dd日HH时"));
    }
    
    /**
     * 转换异常数据格式以匹配模板要求
     * 模板list_format期望: {stnm}，降雨量为{rainfall}mm
     */
    private List<Map<String, Object>> convertAbnormalDataForTemplate(List<Map<String, Object>> abnormalResults) {
        List<Map<String, Object>> listData = new ArrayList<>();
        
        for (Map<String, Object> abnormal : abnormalResults) {
            Map<String, Object> templateData = new HashMap<>();
            
            // 获取测站名称
            String stnm = (String) abnormal.get("stnm");
            String stcd = (String) abnormal.get("stcd");
            templateData.put("stnm", stnm != null ? stnm : stcd);
            
            // 转换雨量字段名：从value转为rainfall
            Object value = abnormal.get("value");
            templateData.put("rainfall", value);
            
            // 保留其他有用字段
            templateData.put("stcd", stcd);
            templateData.put("threshold", abnormal.get("threshold"));
            templateData.put("time", abnormal.get("time"));
            templateData.put("alertLevel", abnormal.get("alertLevel"));
            
            listData.add(templateData);
        }
        
        return listData;
    }
}
