package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 8时水文预警模板处理器
 */
@Component
@Slf4j
public class HydrologyReportTemplateProcessor implements AlertTemplateProcessor {

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate template, Map<String, Object> data) {
        try {
            // 8时水文预警特有的数据处理
            enrichHydrologyReportSpecificData(template, data);

            // 使用默认处理器进行模板处理
            return defaultTemplateProcessor.processTemplate(context, template, data);
        } catch (Exception e) {
            log.error("处理8时水文预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeAlerts(context, alert, receivers);
    }

    /**
     * 8时水文预警特有的数据处理
     */
    private void enrichHydrologyReportSpecificData(SjyAlertSmsTemplate template, Map<String, Object> data) {
        // 添加预警类型标记
        data.put("alertTypeText", "8时水文汇总");

        // 确保content字段存在
        if (!data.containsKey("content") || data.get("content") == null) {
            data.put("content", "水文汇总报告生成异常，请联系系统管理员。");
            log.warn("8时水文预警缺少content内容，使用默认异常信息");
        }

        // 由于模板内容就是{content}，这里不需要复杂的列表处理
        // content在DataProvider中已经组装完成
        
        log.debug("8时水文预警数据处理完成，content长度: {} 字符", 
                data.get("content").toString().length());
    }
} 