package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.*;
import com.tzslsjy.business.mapper.data.StRsvrRMapper;
import com.tzslsjy.business.mapper.data.StTideRMapper;
import com.tzslsjy.business.mapper.data.StWasRMapper;
import com.tzslsjy.business.service.AlertDataProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 水文设备预警数据提供者
 */
@Component
@Slf4j
public class HydrographicEquipmentDataProvider implements AlertDataProvider {

    @Autowired
    private StStbprpBMapper stStbprpBMapper; // 注入设备信息Mapper
    @Autowired
    private StPptnRMapper stPptnRMapper; // 注入设备信息Mapper
    @Autowired
    private StRiverRMapper stRiverRMapper;
    @Autowired
    private StRsvrRMapper stRsvrRMapper;
    @Autowired
    private StTideRMapper stTideRMapper;
    @Autowired
    private StWasRMapper stWasRMapper;
    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("18");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date currentTime) {
        log.debug("开始获取水文设备预警数据，规则类型: {}, 当前时间: {}", ruleTypeCode, currentTime);
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> triggeredEquipments = new ArrayList<>();

        @SuppressWarnings("unchecked")
        List<String> equipmentCodes = (List<String>) queryParams.get("equipmentCodes");
        @SuppressWarnings("unchecked")
        List<SjyHydrographicEquipmentParamData> equipmentParamsList = 
                (List<SjyHydrographicEquipmentParamData>) queryParams.get("equipmentParams");
        List<StStbprpB> stbprpBList = stStbprpBMapper.selectStStbprpBBySTCDs(equipmentCodes); // 确保设备信息已加载
        List<String> stcds = stbprpBList.stream().map(StStbprpB::getStcd).collect(Collectors.toList());
        
        if (equipmentParamsList == null || equipmentParamsList.isEmpty()) {
            log.warn("未找到设备预警参数");
            result.put("error", "未找到设备预警参数");
            return result;
        }
        
        // 通常每个参数组只对应一个超时阈值
        SjyHydrographicEquipmentParamData equipmentParams = equipmentParamsList.get(0);
        Integer hoursThreshold = equipmentParams.getHoursThreshold();
        if (hoursThreshold == null || hoursThreshold <= 0) {
            log.warn("无效的超时小时数阈值: {}", hoursThreshold);
            result.put("error", "无效的超时小时数");
            return result;
        }
        //获取最近 x 小时有数据的 stcd
        Date lastTongTime = DateUtil.offsetHour(currentTime, -hoursThreshold);
        List<String> existStcds = stPptnRMapper.selectExistStcdsByStcds(stcds, lastTongTime, currentTime);
        existStcds.addAll(stRiverRMapper.selectExistStcdsByStcds(stcds, lastTongTime, currentTime));
        existStcds.addAll(stRsvrRMapper.selectExistStcdsByStcds(stcds, lastTongTime, currentTime));
        existStcds.addAll(stTideRMapper.selectExistStcdsByStcds(stcds, lastTongTime, currentTime));
        existStcds.addAll(stWasRMapper.selectExistStcdsByStcds(stcds, lastTongTime, currentTime));
        
        for (StStbprpB equipment : stbprpBList) {
            String stnm = equipment.getStnm(); // 默认设备名
            String stcd = equipment.getStcd();

            if (!existStcds.contains(stcd)) {
                addTriggeredEquipment(triggeredEquipments, stcd, stnm, currentTime, hoursThreshold, "数据超时");
            }
        }

        result.put("triggeredEquipments", triggeredEquipments);
        result.put("alertLevel", equipmentParams.getAlertLevel());
        result.put("argId", queryParams.get("argId"));
        addStandardFields(ruleTypeCode, result);
        return result;
    }

    private void addTriggeredEquipment(List<Map<String, Object>> list, String code, String name, Date currentTime, int timeoutHours, String reason) {
        Map<String, Object> equipmentData = new HashMap<>();
        equipmentData.put("stcd", code);
        equipmentData.put("stnm", name != null ? name : code);
        equipmentData.put("currentTime", DateUtil.format(currentTime, "MM月dd日HH时"));
        equipmentData.put("timeoutHours", timeoutHours);
        equipmentData.put("reason", reason);
        list.add(equipmentData);
    }

    private void addStandardFields(String ruleTypeCode, Map<String, Object> result) {
        result.put("dataType", "3");
        result.put("ruleType", ruleTypeCode);
        result.put("queryTime", new Date());
    }
} 