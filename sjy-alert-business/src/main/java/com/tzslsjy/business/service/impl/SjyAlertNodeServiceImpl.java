package com.tzslsjy.business.service.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.tzslsjy.business.domain.SjyAlertNode;
import com.tzslsjy.business.domain.SjyAlertNodePost;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertPersonNodeRelation;
import com.tzslsjy.business.domain.vo.NodeTreeVO;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertNodePostMapper;
import com.tzslsjy.business.mapper.SjyAlertPersonMapper;
import com.tzslsjy.business.mapper.SjyAlertPersonNodeRelationMapper;
import com.tzslsjy.business.service.ISjyAlertNodeService;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 节点Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
public class SjyAlertNodeServiceImpl implements ISjyAlertNodeService
{
    @Autowired
    private SjyAlertNodeMapper sjyAlertNodeMapper;

    @Autowired
    private SjyAlertPersonMapper sjyAlertPersonMapper;

    @Autowired
    private SjyAlertNodePostMapper sjyAlertNodePostMapper;
    @Autowired
    private SjyAlertPersonNodeRelationMapper sjyAlertPersonNodeRelationMapper;

    /**
     * 查询节点
     * 
     * @param nodeId 节点主键
     * @return 节点
     */
    @Override
    public SjyAlertNode selectSjyAlertNodeByNodeId(String nodeId)
    {
        return sjyAlertNodeMapper.selectSjyAlertNodeByNodeId(nodeId);
    }

    /**
     * 查询节点列表
     * 
     * @param sjyAlertNode 节点
     * @return 节点
     */
    @Override
    public List<SjyAlertNode> selectSjyAlertNodeList(SjyAlertNode sjyAlertNode)
    {
        return sjyAlertNodeMapper.selectSjyAlertNodeList(sjyAlertNode);
    }

    /**
     * 新增节点
     * 
     * @param sjyAlertNode 节点
     * @return 结果
     */
    @Override
    public int insertSjyAlertNode(SjyAlertNode sjyAlertNode)
    {
        sjyAlertNode.setCreateTime(DateUtils.getNowDate());
        int i = sjyAlertNodeMapper.insertSjyAlertNode(sjyAlertNode);
        // 新增节点时，插入节点与职位的关系
        List<SjyAlertNodePost> nodePosts = sjyAlertNode.getNodePosts();
        if (CollUtil.isNotEmpty(nodePosts)) {
            nodePosts.forEach(nodePost -> {
                if(nodePost.getPostId() == null) {
                    nodePost.setPostId(IdUtil.simpleUUID());
                }
            });
            sjyAlertNodePostMapper.insertBatch(nodePosts);
        }
        return i;
    }

    /**
     * 修改节点
     * 
     * @param sjyAlertNode 节点
     * @return 结果
     */
    @Override
    @Transactional // 确保操作的原子性
    public int updateSjyAlertNode(SjyAlertNode sjyAlertNode) {
        sjyAlertNode.setUpdateTime(DateUtils.getNowDate()); // 更新 SjyAlertNode 自身的时间戳
        int rows = sjyAlertNodeMapper.updateSjyAlertNode(sjyAlertNode);

        if (rows > 0) {
            String nodeId = sjyAlertNode.getNodeId();
            if (nodeId == null) {
                // 通常不应该发生，如果 nodeId 是主键且更新成功
                // 可以考虑抛出异常或记录错误日志
                // 例如: throw new BusinessException("更新节点失败，节点ID为空");
                return rows;
            }

            // 1. 获取数据库中当前节点已有的所有职位关联
            // 假设 SjyAlertNodePostMapper 中有 selectSjyAlertNodePostByNodeId 方法
            List<SjyAlertNodePost> existingPosts = sjyAlertNodePostMapper.selectSjyAlertNodePostByNodeId(nodeId);
            Map<String, SjyAlertNodePost> existingPostsMap = existingPosts.stream()
                    .collect(Collectors.toMap(SjyAlertNodePost::getPostId, post -> post, (oldValue, newValue) -> oldValue));

            // 2. 获取传入的新的职位列表
            List<SjyAlertNodePost> newPosts = sjyAlertNode.getNodePosts();
            if (newPosts == null) {
                newPosts = new ArrayList<>(); // 避免空指针，视为空列表
            }

            List<SjyAlertNodePost> postsToInsert = new ArrayList<>();
            List<SjyAlertNodePost> postsToUpdate = new ArrayList<>();
            List<String> idsToDelete = new ArrayList<>();

            // 3. 区分新增、更新、删除的职位
            // 遍历新传入的职位列表
            for (SjyAlertNodePost newPost : newPosts) {
                newPost.setNodeId(nodeId); // 确保关联到正确的 nodeId
                if (newPost.getPostId() != null && existingPostsMap.containsKey(newPost.getPostId())) {
                    // ID 存在且在旧列表中 -> 更新
                    newPost.setUpdateTime(DateUtils.getNowDate()); // 设置更新时间
                    // 如果需要，也可以在这里设置 updateBy
                    // newPost.setUpdateBy(SecurityUtils.getUsername());
                    postsToUpdate.add(newPost);
                    existingPostsMap.remove(newPost.getPostId()); // 从map中移除，剩下的是需要删除的
                } else {
                    // ID 不存在或在旧列表中不存在 -> 新增
                    newPost.setCreateTime(DateUtils.getNowDate()); // 设置创建时间
                    // 如果业务上新增也需要设置更新时间，可以也加上下面这行 (通常不需要，除非有特殊业务含义)
                    // newPost.setUpdateTime(DateUtils.getNowDate());
                    // 如果需要，也可以在这里设置 createBy
                    // newPost.setCreateBy(SecurityUtils.getUsername());
                    postsToInsert.add(newPost);
                }
            }

            // existingPostsMap 中剩下的是需要删除的旧职位
            if (!existingPostsMap.isEmpty()) {
                idsToDelete.addAll(existingPostsMap.keySet());
            }

            // 4. 执行数据库操作
            if (CollUtil.isNotEmpty(idsToDelete)) {
                // 假设 SjyAlertNodePostMapper 中有 deleteSjyAlertNodePostByIds 方法
                sjyAlertNodePostMapper.deleteSjyAlertNodePostByIds(idsToDelete);
            }
            if (CollUtil.isNotEmpty(postsToInsert)) {
                // 假设 SjyAlertNodePostMapper 中有 insertBatch 方法
                sjyAlertNodePostMapper.insertBatch(postsToInsert);
            }
            if (CollUtil.isNotEmpty(postsToUpdate)) {
                for (SjyAlertNodePost postToUpdate : postsToUpdate) {
                    // 假设 SjyAlertNodePostMapper 中有 updateSjyAlertNodePost 方法
                    sjyAlertNodePostMapper.updateSjyAlertNodePost(postToUpdate);
                }
            }
        }
        return rows;
    }


    /**
     * 批量删除节点
     * 
     * @param nodeIds 需要删除的节点主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertNodeByNodeIds(String[] nodeIds)
    {
        return sjyAlertNodeMapper.deleteSjyAlertNodeByNodeIds(nodeIds);
    }

    /**
     * 删除节点信息
     * 
     * @param nodeId 节点主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertNodeByNodeId(String nodeId)
    {
        return sjyAlertNodeMapper.deleteSjyAlertNodeByNodeId(nodeId);
    }

    @Override
    public List<NodeTreeVO> buildNodeTreeWithPersons(SjyAlertNode sjyAlertNode, Long personId) {
        // 1. 查询所有符合条件的节点
        List<SjyAlertNode> nodes = sjyAlertNodeMapper.selectSjyAlertNodeList(sjyAlertNode);
        if (nodes.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 查询所有人员节点关系
        List<SjyAlertPersonNodeRelation> relations = sjyAlertPersonNodeRelationMapper.selectSjyAlertPersonNodeRelationList(new SjyAlertPersonNodeRelation());

        // 3. 查询所有相关人员信息 (如果需要展示人员详细信息，比如姓名)
        // 为了效率，可以先收集所有在relations中出现的personId，然后一次性查询
        List<String> personIdsFromRelations = relations.stream()
                .map(SjyAlertPersonNodeRelation::getPersonId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, SjyAlertPerson> personMap = new HashMap<>();
        if (!personIdsFromRelations.isEmpty()) {
            List<SjyAlertPerson> persons = sjyAlertPersonMapper.selectSjyAlertPersonByIds(personIdsFromRelations);
            personMap = persons.stream().collect(Collectors.toMap(SjyAlertPerson::getPersonId, p -> p));
        }


        // 4. 将节点列表转换为NodeTreeVO列表，并构建ID到NodeTreeVO的映射
        Map<String, NodeTreeVO> nodeTreeVoMap = new HashMap<>();
        List<NodeTreeVO> rootNodes = new ArrayList<>();

        for (SjyAlertNode node : nodes) {
            NodeTreeVO vo = new NodeTreeVO(node);
            nodeTreeVoMap.put(node.getNodeId(), vo);
        }

        // 5. 构建节点间的父子关系
        for (SjyAlertNode node : nodes) {
            NodeTreeVO currentVo = nodeTreeVoMap.get(node.getNodeId());
            if (node.getParentId() == null   ) { // 假设根节点的parentId为null或0
                rootNodes.add(currentVo);
            } else {
                NodeTreeVO parentVo = nodeTreeVoMap.get(node.getParentId());
                if (parentVo != null) {
                    if (parentVo.getChildren() == null) {
                        parentVo.setChildren(new ArrayList<>());
                    }
                    parentVo.getChildren().add(currentVo);
                } else {
                    // 如果父节点不在当前查询结果中（例如，被sjyAlertNode条件过滤掉了），也将其视为根节点
                    rootNodes.add(currentVo);
                }
            }
        }
        
        // 6. 将人员关联到对应的节点下
        for (SjyAlertPersonNodeRelation relation : relations) {
            NodeTreeVO parentNodeVo = nodeTreeVoMap.get(relation.getNodeId());
            SjyAlertPerson person = personMap.get(relation.getPersonId());
            if (parentNodeVo != null && person != null) {
                NodeTreeVO personVo = new NodeTreeVO(person,relation.getNodeId());
                // 如果需要根据传入的personId判断是否勾选，可以在这里处理
                // 例如: if (personId != null && personId.equals(person.getPersonId())) { personVo.setChecked(true); }
                if (parentNodeVo.getChildren() == null) {
                    parentNodeVo.setChildren(new ArrayList<>());
                }
                parentNodeVo.getChildren().add(personVo);
            }
        }
        // 如果没有父节点的节点也需要作为根节点返回，需要调整rootNodes的逻辑
        // 当前的逻辑是，如果一个节点的父节点ID不为0且在nodeTreeVoMap中找不到，它也会被当作根节点。
        // 如果只想返回顶层节点，需要确保只有 parentId 为 0 或 null 的节点才加入 rootNodes，
        // 并且处理好父节点被过滤掉的情况。

        // 如果只想返回顶层节点，可以过滤一下 rootNodes
        // List<NodeTreeVO> finalRootNodes = rootNodes.stream()
        //        .filter(vo -> nodes.stream().anyMatch(n -> n.getNodeId().equals(vo.getId()) && (n.getParentId() == null || n.getParentId() == 0)))
        //        .collect(Collectors.toList());
        // return finalRootNodes;

        return rootNodes;
    }
}
