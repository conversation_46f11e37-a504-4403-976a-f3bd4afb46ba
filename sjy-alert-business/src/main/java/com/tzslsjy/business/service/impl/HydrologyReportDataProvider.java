package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.tzslsjy.business.bo.StPptnHistReqVo;
import com.tzslsjy.business.bo.StPptnHistRespVo;
import com.tzslsjy.business.domain.SjyHydrologyReportParamData;
import com.tzslsjy.business.domain.StRiverR;
import com.tzslsjy.business.mapper.StRiverRMapper;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.mapper.data.StRsvrRMapper;
import com.tzslsjy.business.mapper.StPptnRMapper;
import com.tzslsjy.business.service.AlertDataProvider;
import com.tzslsjy.business.service.IStPptnHistDService;
import com.tzslsjy.business.service.IStPptnRService;
import com.tzslsjy.business.service.IStRsvrRService;
import com.tzslsjy.business.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 8时水文预警数据提供者
 */
@Component
@Slf4j
public class HydrologyReportDataProvider implements AlertDataProvider {

    @Autowired
    private StRiverRMapper stRiverRMapper;
    @Autowired
    private StStbprpBMapper stStbprpBMapper;
    @Autowired
    private StRsvrRMapper stRsvrRMapper;
    @Autowired
    private IStRsvrRService stRsvrRService;
    @Autowired
    private IStPptnHistDService stPptnHistDService;
    @Autowired
    private IStPptnRService stPptnRService;
    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("13");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date currentTime) {
        log.debug("开始获取8时水文预警数据，规则类型: {}, 当前时间: {}", ruleTypeCode, currentTime);
        Map<String, Object> result = new HashMap<>();

        try {
            // 8时水文预警查询时间固定为当天8时
            Date fixed8AMTime = getFixed8AMTime(currentTime);
            log.info("8时水文预警查询时间固定为: {}", DateUtil.formatDateTime(fixed8AMTime));

            // 组装完整的水文汇总报告内容
            String content = buildHydrologyReport(fixed8AMTime);

            result.put("content", content);
            result.put("argId", queryParams.get("argId"));
            result.put("alertLevel", "1");
            result.put("queryTime", fixed8AMTime); // 记录实际查询时间
            addStandardFields(ruleTypeCode, result);

            log.info("8时水文预警报告生成成功，内容长度: {} 字符", content.length());
            
        } catch (Exception e) {
            log.error("生成8时水文预警报告失败: {}", e.getMessage(), e);
            result.put("content", "系统异常，无法生成水文汇总报告。");
            result.put("argId", queryParams.get("argId"));
            addStandardFields(ruleTypeCode, result);
        }

        return result;
    }

    /**
     * 构建水文汇总报告内容
     * @param fixed8AMTime 固定的8AM查询时间
     */
    private String buildHydrologyReport(Date fixed8AMTime) {
        StringBuilder content = new StringBuilder();

        SimpleDateFormat dateFormat = new SimpleDateFormat("今日H时");
        String timeStr = dateFormat.format(fixed8AMTime);

        content.append("截止").append(timeStr).append("，");

        // 1. 面雨量信息
        String rainfallInfo = buildRainfallInfo(fixed8AMTime);
        content.append(rainfallInfo);

        // 2. 水库水情
        String reservoirInfo = buildReservoirInfo(fixed8AMTime);
        content.append(reservoirInfo);

        // 3. 河道水情
        String riverInfo = buildRiverInfo(fixed8AMTime);
        content.append(riverInfo);

        return content.toString();
    }

    /**
     * 构建面雨量信息
     * @param fixed8AMTime 固定的8AM查询时间
     */
    private String buildRainfallInfo(Date fixed8AMTime) {
        try {
            // 基于固定8AM时间计算24小时前和月初时间
            Date yesterday = DateUtil.offsetDay(fixed8AMTime, -1);
            Date monthStart = DateUtil.beginOfMonth(fixed8AMTime);
            StPptnHistReqVo stPptnHistReqVo = new StPptnHistReqVo();
            stPptnHistReqVo.setStartTm(monthStart);
            stPptnHistReqVo.setEndTm(fixed8AMTime);
            stPptnHistReqVo.setType(1);
            stPptnHistReqVo.setAddvcd("331082");
            List<StPptnHistRespVo> stPptnHistRespVos = stPptnHistDService.droughtAnaly(stPptnHistReqVo);
            if(stPptnHistRespVos==null || stPptnHistRespVos.isEmpty()) {
                log.warn("未查询到面雨量数据");
                return "全市24小时面雨量数据获取异常。";
            }
            StPptnHistRespVo stPptnHistRespVo = stPptnHistRespVos.get(0);

            BigDecimal dailyRainfall =  stPptnRService.areaRainByAddvcd("331082", fixed8AMTime, yesterday) ;
            BigDecimal monthlyRainfall = stPptnHistRespVo.getSum();
            BigDecimal yearlyAverage = stPptnHistRespVo.getSamePeriodSum();
            BigDecimal difference = monthlyRainfall.subtract(yearlyAverage);

            RainInfoHourReqVo vo = new RainInfoHourReqVo();
            vo.setStartTm(DateUtil.offsetHour(fixed8AMTime, -2));
            vo.setEndTm(fixed8AMTime);
            List<RainInfoHourVo> rainInfoHourVos = stPptnRService.pageList(vo);
            BigDecimal thirty = new BigDecimal(30);
            List<RainInfoHourVo> limit = rainInfoHourVos.stream().filter(e -> e.getDrp() != null&&e.getDrp().compareTo(thirty)>0).sorted(Comparator.comparing(RainInfoHourVo::getDrp).reversed()).limit(5).collect(Collectors.toList());
            String str1 =null;
            //较常年平均 小于 0 少，大于 0 多
            if (difference.compareTo(BigDecimal.ZERO) < 0) {
                str1 = String.format("全市24小时面雨量%.1f毫米，本月累计降雨为%.1f毫米,较常年平均少%.1f毫米。",
                        dailyRainfall.doubleValue(),
                        monthlyRainfall.doubleValue(),
                        Math.abs(difference.doubleValue()));
            }else {
                  str1 = String.format("全市24小时面雨量%.1f毫米，本月累计降雨为%.1f毫米,较常年平均多%.1f毫米。",
                        dailyRainfall.doubleValue(),
                        monthlyRainfall.doubleValue(),
                        Math.abs(difference.doubleValue()));
            }
            //。最大降雨站点:祠堂后S站49.5毫米,岭外村站47.0毫米,塘岙水库站46.5毫米,双狮村站45.5毫米,岩头塘水库站45.0毫米,
            if( limit == null || limit.size() == 0) {
                return str1  ;
            }
            String str2 ="最大降雨站点:"+
                    limit.stream().map(e -> e.getStnm() + "站" + e.getDrp() + "毫米").collect(Collectors.joining(","));
            return str1+str2;
                    
        } catch (Exception e) {
            log.error("构建面雨量信息失败: {}", e.getMessage(), e);
            return "全市24小时面雨量数据获取异常。";
        }
    }

    /**
     * 构建水库水情信息
     * @param fixed8AMTime 固定的8AM查询时间
     */
    private String buildReservoirInfo(Date fixed8AMTime) {
        try {
            ReservoirReqVo reservoirReqVo = new ReservoirReqVo();
            reservoirReqVo.setEndTm(fixed8AMTime);
            List<ReservoirVo> reservoirList = stRsvrRService.rsWithRz(reservoirReqVo);
            if (reservoirList == null || reservoirList.isEmpty()) {
                return "【水库水情】:暂无数据;";
            }
            // 过滤出牛头山水库，方溪水库，溪口水库，童燎水库
            List<String> rsList = Arrays.asList("70404100", "30035150", "70407000", "70800100");
           List<ReservoirVo> rpRs = reservoirList.stream()
                    .filter(reservoir -> rsList.contains(reservoir.getStcd()))
                    .collect(Collectors.toList());

            // 过滤出 小型水库
            //小型水库平均蓄水率
            BigDecimal smallSum = BigDecimal.ZERO;
            BigDecimal smallSumN = BigDecimal.ZERO;
            BigDecimal smallRate = BigDecimal.ZERO;
            List<ReservoirVo> smallReservoirs = reservoirList.stream().filter(o ->  o.getLevel()!=null&&(o.getLevel()==1 ||  o.getLevel()==2)).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(smallReservoirs)) {
                smallSum = smallReservoirs.stream().filter(e -> e.getNorRz() != null).map(ReservoirVo::getNorRz).reduce(BigDecimal.ZERO, BigDecimal::add);
                smallSumN = smallReservoirs.stream().filter(e -> e.getNorcp() != null).map(ReservoirVo::getNorcp).reduce(BigDecimal.ZERO, BigDecimal::add);
                smallRate = smallSum.divide(smallSumN, 3, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
            }
            StringBuilder reservoirInfo = new StringBuilder("【水库水情】:");
            List<String> reservoirItems = new ArrayList<>();
            BigDecimal totalCapacity = BigDecimal.ZERO;
            BigDecimal totalStorage = BigDecimal.ZERO;
            
            for (ReservoirVo reservoir : rpRs) {
                if (reservoir.getRz() != null && reservoir.getNormz() != null) {
                    // 计算蓄水率
                    BigDecimal storageRate =reservoir.getNorRate();

                    
                    String item = String.format("%s%.2f米(蓄水率%.2f%%)",
                            reservoir.getStnm() != null ? reservoir.getStnm() : "未知水库",
                            reservoir.getRz().doubleValue(),
                            storageRate!=null ?storageRate.multiply(BigDecimal.valueOf(100)).doubleValue(): 0.0);
                    reservoirItems.add(item);
                    
                    if (reservoir.getTtcp() != null) {
                        totalCapacity = totalCapacity.add(reservoir.getTtcp());
                    }
                }
            }
            
            // 拼接水库信息
            reservoirInfo.append(String.join("、", reservoirItems));
            
            // 添加小型水库汇总
            reservoirInfo.append(String.format(";小型水库总蓄水量%.2f万立方米（蓄水率%.2f%%）;",
                    smallSum.doubleValue(), smallRate.doubleValue()));
            //如果有水库超汛限的话，要显示全市超汛限水库共有几座，其中大中型水库有几座，小一型水库有几座，小二型水库有几座
            List<ReservoirVo> bigWarning = rpRs.stream().filter(e -> e.getRz() != null && e.getRz().compareTo(e.getFsltdz()) > 0).collect(Collectors.toList());
            List<ReservoirVo> small1Warning =reservoirList.stream().filter(e ->  e.getLevel()!=null&&e.getLevel()==1&& e.getRz() != null && e.getRz().compareTo(e.getFsltdz()) > 0).collect(Collectors.toList());
            List<ReservoirVo> small2Warning = smallReservoirs.stream().filter(e ->  e.getLevel()!=null&&e.getLevel()==2&& e.getRz() != null && e.getRz().compareTo(e.getFsltdz()) > 0).collect(Collectors.toList());
            List<ReservoirVo> overWarning = Stream.concat(
                    bigWarning.stream(),
                    Stream.concat(small1Warning.stream(), small2Warning.stream())
            ).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(overWarning)) {
                int largeCount = (int) bigWarning.size();
                int small1Count = (int) small1Warning.size();
                int small2Count = (int) small2Warning.size();
                reservoirInfo.append(String.format("全市超汛限水库共有%d座，其中大中型水库%d座，小一型水库%d座，小二型水库%d座;",
                        overWarning.size(), largeCount, small1Count, small2Count));
            }
            return reservoirInfo.toString();
            
        } catch (Exception e) {
            e.printStackTrace();
            log.error("构建水库水情信息失败: {}", e.getMessage(), e);
            return "【水库水情】:数据获取异常;";
        }
    }


    /**
     * 构建河道水情信息
     * @param fixed8AMTime 固定的8AM查询时间
     */
    private String buildRiverInfo(Date fixed8AMTime) {
        try {
            RiverReqVo riverReqVo = new RiverReqVo();
            List<RiverVo> riverList = stStbprpBMapper.getRvInfo(riverReqVo);
            List<String> rvList = Arrays.asList(
                    "704H8140",  // 义城港
                    "70400800",  // 柏枝岙
                    "70401000",  // 西门1G
                    "70403600",  // *沙段
                    "1565",      // 大田港闸(内)
                    "33539",     // 杜桥山项
                    "70404103",  // 大田桥
                    "70800090",  // 桃渚水利站
                    "33516",     // 上盘水利站
                    "33535"      // 塘渡
            );
            if (riverList == null || riverList.isEmpty()) {
                return "【河道水情】:暂无数据。";
            }
            //  过滤并且按指定顺序排序
            riverList = riverList.stream()
                    .filter(river -> rvList.contains(river.getStcd()))
                    .sorted(Comparator.comparingInt(river -> rvList.indexOf(river.getStcd())))
                    .collect(Collectors.toList());

            StringBuilder riverInfo = new StringBuilder("【河道水情】:");
            List<String> riverItems = new ArrayList<>();
            Date thisYearStart = DateUtil.offsetHour(fixed8AMTime,-1);

            for (RiverVo river : riverList) {
                try {
                    String stcd = river.getStcd();
                    String stnm = river.getStnm() != null ? river.getStnm() : stcd;

                    // 获取最新水位数据（基于固定8AM时间）
                    StRiverR latestRiver = stRiverRMapper.selectLastestStRiverRBySTCD(stcd, thisYearStart, fixed8AMTime);
                    
                    if (latestRiver != null && latestRiver.getZ() != null) {
                        String item = String.format("%s%.2f米", stnm, latestRiver.getZ().doubleValue());
                        riverItems.add(item);
                    }
                      if( latestRiver != null && river.getWrz() != null && latestRiver.getZ() != null) {
                        // 检查是否超警戒水位
                        if (latestRiver.getZ().compareTo(river.getWrz()) > 0) {
                            String item = String.format("(超警戒水位%.1f米)",   latestRiver.getZ().subtract(river.getWrz()).setScale(1,RoundingMode.HALF_UP).doubleValue());
                            riverItems.add(item);
                        }
                    }
                      if( latestRiver != null && river.getGrz() != null && latestRiver.getZ() != null) {
                          // 检查是否超警戒水位
                          if (latestRiver.getZ().compareTo(river.getGrz()) > 0) {
                              String item = String.format("(超保证位%.1f米)",   latestRiver.getZ().subtract(river.getGrz()).setScale(1,RoundingMode.HALF_UP).doubleValue());
                              riverItems.add(item);
                          }
                      }

                } catch (Exception e) {
                    log.warn("处理河道 {} 数据失败: {}", river.getStnm(), e.getMessage());
                }
            }
            
            if (riverItems.isEmpty()) {
                return "【河道水情】:暂无有效数据。";
            }
            //全市超警戒河道水位x 座，具体
            riverInfo.append(String.join("、", riverItems)).append("。");
//            List<RiverVo> collect = riverList.stream().filter(e -> e.getZ().compareTo(e.getWrz()) > 0).collect(Collectors.toList());
//            if (CollectionUtils.isNotEmpty(collect)) {
//                riverInfo.append(String.format("全市超警戒河道水位%d座：", collect.size()));
//                String overRiver = collect.stream()
//                        .map(e -> e.getStnm() + "站" + e.getZ().setScale(2, RoundingMode.HALF_UP) + "米")
//                        .collect(Collectors.joining(","));
//                riverInfo.append(overRiver).append("。");
//            }
            return riverInfo.toString();
            
        } catch (Exception e) {
            log.error("构建河道水情信息失败: {}", e.getMessage(), e);
            return "【河道水情】:数据获取异常。";
        }
    }

    /**
     * 获取固定的8AM时间
     * 8时水文预警查询时间固定为当天8时
     */
    private Date getFixed8AMTime(Date currentTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentTime);

        // 设置为当天8点整
        cal.set(Calendar.HOUR_OF_DAY, 8);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        Date fixed8AM = cal.getTime();
        log.debug("当前时间: {}, 固定8AM时间: {}",
                DateUtil.formatDateTime(currentTime),
                DateUtil.formatDateTime(fixed8AM));

        return fixed8AM;
    }

    private void addStandardFields(String ruleTypeCode, Map<String, Object> result) {
        result.put("dataType", "13");
        result.put("ruleType", ruleTypeCode);
        result.put("reportGenerated", true);
    }
} 