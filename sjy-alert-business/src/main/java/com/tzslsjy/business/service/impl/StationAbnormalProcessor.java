package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 测站异常预警处理器
 *
 * 注意：此类已被拆分为独立的水位异常和雨量异常处理器，请使用：
 * - {@link WaterLevelAbnormalProcessor} 水位异常预警处理器 (规则类型31)
 * - {@link RainfallAbnormalProcessor} 雨量异常预警处理器 (规则类型34)
 *
 * 超时监测功能已被移除。
 *
 * <AUTHOR>
 * @date 2025-07-08
 * @deprecated 请使用独立的水位异常和雨量异常处理器
 */
@Component
@Slf4j
@Deprecated
public class StationAbnormalProcessor implements AlertProcessor, TypedAlertProcessor {

    @Autowired
    @Qualifier("stationAbnormalTemplateProcessor")
    private AlertTemplateProcessor templateProcessor;
    
    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService;
    
    @Autowired
    private SjyAlertNodeMapper alertNodeMapper;
    
    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper;
    
    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper;

    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public String getSupportedType() {
        log.warn("StationAbnormalProcessor已弃用，请使用新的独立处理器：");
        log.warn("- 水位异常预警请使用 WaterLevelAbnormalProcessor (规则类型31)");
        log.warn("- 雨量异常预警请使用 RainfallAbnormalProcessor (规则类型34)");
        return "53"; // 保留原有类型以维持向后兼容性
    }

    @Override
    public void process(AlertContext context) {
        try {
            SjyAlertRule rule = context.getRule();
            Map<String, Object> data = context.getData();

            log.warn("StationAbnormalProcessor已弃用，但仍在处理规则ID: {}，建议迁移到新的独立处理器", rule.getRuleId());
            log.info("开始处理测站异常预警，规则ID: {}", rule.getRuleId());
            
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");
            if (argResults == null || argResults.isEmpty()) {
                log.warn("测站异常预警缺少参数结果，规则ID: {}", rule.getRuleId());
                return;
            }

            List<SjyAlertSmsRecord> allRecords = new ArrayList<>();

            // 处理每个参数组的结果
            for (Map<String, Object> argResult : argResults) {
                Boolean alertTriggered = (Boolean) argResult.get("alertTriggered");
                if (!Boolean.TRUE.equals(alertTriggered)) {
                    continue;
                }

                String argId = argResult.get("argId") != null ? argResult.get("argId").toString() : "default";
                
                // 获取模板
                List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
                if (templates.isEmpty()) {
                    log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                    continue;
                }

                // 处理异常结果
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> abnormalResults = (List<Map<String, Object>>) argResult.get("abnormalResults");
                if (abnormalResults == null || abnormalResults.isEmpty()) {
                    continue;
                }

                // 检查是否有按行政区划分组的数据
                @SuppressWarnings("unchecked")
                Map<String, List<Map<String, Object>>> abnormalResultGroups =
                        (Map<String, List<Map<String, Object>>>) argResult.get("abnormalResultGroups");

                if (abnormalResultGroups != null && !abnormalResultGroups.isEmpty()) {
                    // 为每个行政区划分组生成消息
                    for (Map.Entry<String, List<Map<String, Object>>> groupEntry : abnormalResultGroups.entrySet()) {
                        String groupKey = groupEntry.getKey(); // 格式: "9_331082001" 或 "6_331082"
                        List<Map<String, Object>> groupAbnormals = groupEntry.getValue();

                        if (CollectionUtils.isEmpty(groupAbnormals)) {
                            continue;
                        }

                        String jurisdiction = extractJurisdictionFromGroupKey(groupKey);
                        String jurisdictionLevel = groupKey.substring(0, 1); // "9" 或 "6"

                        log.info("处理{}位行政区划 {} 的测站异常预警，包含 {} 个异常", jurisdictionLevel, jurisdiction, groupAbnormals.size());

                        for (SjyAlertSmsTemplate template : templates) {
                            Map<String, Object> templateData = buildTemplateDataForGroup(argResult, groupAbnormals, jurisdiction, jurisdictionLevel);

                            SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                            if (alert != null) {
                                // 根据行政区划获取对应的接收人员
                                List<SjyAlertPerson> receivers = getReceiversByJurisdiction(rule, argId, jurisdiction);
                                List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                                List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                                allRecords.addAll(mergedAlerts);
                                log.info("{}位行政区划 {} 生成 {} 条测站异常预警消息", jurisdictionLevel, jurisdiction, mergedAlerts.size());
                            }
                        }
                    }
                } else {
                    log.warn("未找到按行政区划分组的异常数据，跳过处理");
                }
            }
            
            context.setRecords(allRecords);
            log.info("总共生成 {} 条测站异常预警消息", allRecords.size());

        } catch (Exception e) {
            log.error("处理测站异常预警失败: {}", e.getMessage(), e);
        }

        // 传递给下一个处理器
        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    /**
     * 为行政区划分组构建模板数据
     */
    private Map<String, Object> buildTemplateDataForGroup(Map<String, Object> argResult,
                                                         List<Map<String, Object>> groupAbnormals,
                                                         String jurisdiction, String jurisdictionLevel) {
        Map<String, Object> templateData = new java.util.HashMap<>(argResult);

        // 按异常类型分组
        Map<String, List<Map<String, Object>>> groupedByType = groupAbnormals.stream()
                .collect(Collectors.groupingBy(result -> (String) result.get("abnormalType")));

        templateData.put("abnormalResults", groupAbnormals);
        templateData.put("groupedAbnormalResults", groupedByType);
        templateData.put("totalAbnormalCount", groupAbnormals.size());
        templateData.put("jurisdiction", jurisdiction);
        templateData.put("jurisdictionLevel", jurisdictionLevel);

        // 构建受影响的测站列表
        List<String> affectedStcds = groupAbnormals.stream()
                .map(result -> (String) result.get("stcd"))
                .distinct()
                .collect(Collectors.toList());
        templateData.put("affectedStcds", affectedStcds);
        templateData.put("affectedStcdCount", affectedStcds.size());

        return templateData;
    }

    /**
     * 从分组键中提取行政区划编码
     */
    private String extractJurisdictionFromGroupKey(String groupKey) {
        // 格式: "9_331082001" 或 "6_331082"
        if (groupKey != null && groupKey.length() > 2) {
            return groupKey.substring(2); // 去掉前缀 "9_" 或 "6_"
        }
        return groupKey;
    }



    /**
     * 获取模板
     */
    private List<SjyAlertSmsTemplate> getTemplates(Long ruleId, String argId) {
        try {
            List<SjyAlertSmsTemplate> templates = alertSmsTemplateMapper.selectOneByRelId(String.valueOf(ruleId), "1");
            if (CollectionUtils.isEmpty(templates)) {
                log.warn("未找到规则ID {}，参数ID {} 的模板", ruleId, argId);
                return new ArrayList<>();
            }
            return templates;
        } catch (Exception e) {
            log.error("获取测站异常预警模板失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 根据行政区划获取接收人员
     */
    private List<SjyAlertPerson> getReceiversByJurisdiction(SjyAlertRule rule, String argId, String adcd) {
        try {
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgIdAndAdcd(argId, adcd);
            if (nodeIds == null || nodeIds.isEmpty()) {
                log.debug("未找到行政区划 {} 的节点ID，使用默认管理员", adcd);
                return getDefaultAdmin();
            }

            List<SjyAlertPerson> receivers = sjyAlertPersonService.selectByMemberIds(nodeIds);
            if (receivers.isEmpty()) {
                log.warn("参数组 {} 的行政区划 {} 未配置接收人或未找到，使用默认管理员", argId, adcd);
                return getDefaultAdmin();
            }

            // 去重
            log.debug("根据行政区划 {} 获取接收人员 {} 个", adcd, receivers.size());
            return receivers.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("根据行政区划 {} 获取接收人员失败: {}", adcd, e.getMessage(), e);
            return getDefaultAdmin();
        }
    }

    /**
     * 获取默认管理员
     */
    private List<SjyAlertPerson> getDefaultAdmin() {
        SjyAlertPerson admin = new SjyAlertPerson();
        admin.setName("admin");
        admin.setPhone("15306587076");
        return Collections.singletonList(admin);
    }

    /**
     * 获取接收人
     */
    private List<SjyAlertPerson> getReceivers(SjyAlertRule rule, String argId, String context) {
        try {
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgId(argId);
            if (nodeIds == null || nodeIds.isEmpty()) {
                // 使用默认管理员
                SjyAlertPerson admin = new SjyAlertPerson();
                admin.setPersonId("1");
                admin.setName("admin");
                admin.setPhone("15306587076");
                return Collections.singletonList(admin);
            }
            
            List<SjyAlertPerson> receivers = sjyAlertPersonService.selectByMemberIds(nodeIds);

            if (receivers.isEmpty()) {
                log.warn("规则ID {} 及其参数组 {} 均未配置接收人或未找到，使用默认管理员", rule.getRuleId(), argId);
                SjyAlertPerson admin = new SjyAlertPerson();
                admin.setName("admin");
                admin.setPhone("15306587076");
                return Collections.singletonList(admin);
            }
            
            // 去重
            return receivers.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取测站异常预警接收人失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }
}
