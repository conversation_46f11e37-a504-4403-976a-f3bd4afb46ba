package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyRiskVillageInfo;
import com.tzslsjy.business.vo.SjyRiskVillageInfoRespVo;
import com.tzslsjy.business.vo.SjyRiskVillageInfoVo;
import com.tzslsjy.business.bo.SjyRiskVillageInfoQueryBo;
import com.tzslsjy.business.bo.SjyRiskVillageInfoAddBo;
import com.tzslsjy.business.bo.SjyRiskVillageInfoEditBo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * 村落基本情况Service接口
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
public interface ISjyRiskVillageInfoService extends IService<SjyRiskVillageInfo> {
	/**
	 * 查询单个
	 * @return
	 */
	SjyRiskVillageInfoVo queryById(String id);

	/**
	 * 查询列表
	 */
	List<SjyRiskVillageInfoVo> queryList(SjyRiskVillageInfoQueryBo bo);

	/**
	 * 根据新增业务对象插入村落基本情况
	 * @param bo 村落基本情况新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(SjyRiskVillageInfoAddBo bo);

	/**
	 * 根据编辑业务对象修改村落基本情况
	 * @param bo 村落基本情况编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(SjyRiskVillageInfoEditBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

	SjyRiskVillageInfoRespVo riskAreaStatistics(SjyRiskVillageInfoQueryBo bo);
}