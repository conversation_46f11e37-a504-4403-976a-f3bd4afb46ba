package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyAlertRuleVariable;

/**
 * 规则标签关联Service接口
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface ISjyAlertRuleVariableService
{
    /**
     * 查询规则标签关联
     *
     * @param ruleVariableId 规则标签关联主键
     * @return 规则标签关联
     */
    public SjyAlertRuleVariable selectSjyAlertRuleVariableByRuleVariableId(Long ruleVariableId);

    /**
     * 查询规则标签关联列表
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 规则标签关联集合
     */
    public List<SjyAlertRuleVariable> selectSjyAlertRuleVariableList(SjyAlertRuleVariable sjyAlertRuleVariable);

    /**
     * 新增规则标签关联
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 结果
     */
    public int insertSjyAlertRuleVariable(SjyAlertRuleVariable sjyAlertRuleVariable);

    /**
     * 修改规则标签关联
     *
     * @param sjyAlertRuleVariable 规则标签关联
     * @return 结果
     */
    public int updateSjyAlertRuleVariable(SjyAlertRuleVariable sjyAlertRuleVariable);

    /**
     * 批量删除规则标签关联
     *
     * @param ruleVariableIds 需要删除的规则标签关联主键集合
     * @return 结果
     */
    public int deleteSjyAlertRuleVariableByRuleVariableIds(Long[] ruleVariableIds);

    /**
     * 删除规则标签关联信息
     *
     * @param ruleVariableId 规则标签关联主键
     * @return 结果
     */
    public int deleteSjyAlertRuleVariableByRuleVariableId(Long ruleVariableId);
}
