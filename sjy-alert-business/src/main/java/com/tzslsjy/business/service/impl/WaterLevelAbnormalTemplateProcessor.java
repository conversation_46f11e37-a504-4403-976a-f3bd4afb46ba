package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 水位异常预警模板处理器
 * 专注于水位异常预警模板处理
 * 支持规则类型：31
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component("waterLevelAbnormalTemplateProcessor")
@Slf4j
public class WaterLevelAbnormalTemplateProcessor implements AlertTemplateProcessor {

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate template, Map<String, Object> data) {
        try {
            // 水位异常预警特有的数据处理
            enrichWaterLevelAbnormalSpecificData(template, data);

            // 使用默认处理器进行模板处理
            return defaultTemplateProcessor.processTemplate(context, template, data);
        } catch (Exception e) {
            log.error("处理水位异常预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> receivers) {
        return defaultTemplateProcessor.mergeAlerts(context, alert, receivers);
    }

    /**
     * 水位异常预警特有的数据处理
     */
    @SuppressWarnings("unchecked")
    private void enrichWaterLevelAbnormalSpecificData(SjyAlertSmsTemplate template, Map<String, Object> data) {
        // 添加预警类型标记
        data.put("alertTypeText", "水位异常");
        
        // 处理水位异常数据
        if (data.containsKey("abnormalResults")) {
            List<Map<String, Object>> abnormalResults = (List<Map<String, Object>>) data.get("abnormalResults");
            
            if (abnormalResults != null && !abnormalResults.isEmpty()) {
                // 转换数据格式以匹配模板要求
                List<Map<String, Object>> listData = convertAbnormalDataForTemplate(abnormalResults, data);
                
                // 设置为list键，供模板引擎处理{list}占位符
                data.put("list", listData);
                
                // 获取第一个异常记录，用于模板中的单个测站显示（保持兼容性）
                Map<String, Object> firstAbnormal = abnormalResults.get(0);
                
                // 设置模板需要的变量
                String stnm = (String) firstAbnormal.get("stnm");
                String stcd = (String) firstAbnormal.get("stcd");
                Object diff = firstAbnormal.get("diff");
                
                // 设置测站名称
                data.put("stnm", stnm != null ? stnm : stcd);
                
                // 设置水位变化差值
                if (diff != null) {
                    data.put("waterDiff", diff.toString());
                } else {
                    data.put("waterDiff", "0");
                }
                
                // 构建水位异常描述（保持兼容性）
                StringBuilder abnormalDesc = new StringBuilder();
                for (int i = 0; i < abnormalResults.size(); i++) {
                    Map<String, Object> abnormal = abnormalResults.get(i);
                    String abnormalStcd = (String) abnormal.get("stcd");
                    String abnormalStnm = (String) abnormal.get("stnm");
                    Object abnormalDiff = abnormal.get("diff");
                    
                    abnormalDesc.append(abnormalStnm != null ? abnormalStnm : abnormalStcd);
                    abnormalDesc.append("测站水位变化");
                    abnormalDesc.append(abnormalDiff).append("米");
                    
                    if (i < abnormalResults.size() - 1) {
                        abnormalDesc.append("，");
                    }
                }
                
                data.put("abnormalDesc", abnormalDesc.toString());
                data.put("abnormalCount", abnormalResults.size());
                data.put("firstAbnormal", firstAbnormal);
            }
        }
        
        // 添加行政区划信息
        if (data.containsKey("jurisdiction")) {
            String jurisdiction = (String) data.get("jurisdiction");
            data.put("jurisdictionText", jurisdiction);
        }
    }
    
    /**
     * 转换异常数据格式以匹配模板要求
     * 模板list_format期望: {stnm}，{duringHour}小时内水位差值起伏{waterDiff}m
     */
    private List<Map<String, Object>> convertAbnormalDataForTemplate(List<Map<String, Object>> abnormalResults, Map<String, Object> templateData) {
        List<Map<String, Object>> listData = new ArrayList<>();
        
        // 获取检测周期，从全局数据中获取
        Object duringHourObj = templateData.get("duringHour");
        String duringHour = duringHourObj != null ? duringHourObj.toString() : "1";
        
        for (Map<String, Object> abnormal : abnormalResults) {
            Map<String, Object> convertedData = new HashMap<>();
            
            // 获取测站名称
            String stnm = (String) abnormal.get("stnm");
            String stcd = (String) abnormal.get("stcd");
            convertedData.put("stnm", stnm != null ? stnm : stcd);
            
            // 添加检测周期
            convertedData.put("duringHour", duringHour);
            
            // 转换水位差值字段名：从diff转为waterDiff
            Object diff = abnormal.get("diff");
            convertedData.put("waterDiff", diff != null ? diff.toString() : "0");
            
            // 保留其他有用字段
            convertedData.put("stcd", stcd);
            convertedData.put("value", abnormal.get("value"));
            convertedData.put("threshold", abnormal.get("threshold"));
            convertedData.put("time", abnormal.get("time"));
            convertedData.put("alertLevel", abnormal.get("alertLevel"));
            
            listData.add(convertedData);
        }
        
        return listData;
    }
}
