package com.tzslsjy.business.service.comm.impl;

import com.tzslsjy.business.domain.comm.BusinessCommCity;
import com.tzslsjy.business.domain.comm.BusinessCommCItyTree;
import com.tzslsjy.business.mapper.comm.BusinessCommCityMapper;

import com.tzslsjy.business.service.comm.ICommCityService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @auther seven
 * @create 2021-05-11 10:55:10
 * @describe 行政区划、城市乡镇服务实现类
 */
@Service("businessCommCityService")
public class CommCityServiceImpl extends ServiceImpl<BusinessCommCityMapper, BusinessCommCity> implements ICommCityService {

        @Override
        public List<BusinessCommCity> pageList(BusinessCommCity vo) {
                //baseMapper.pageList(vo);
                return baseMapper.pageList(vo);
        }

        @Override
        public List<BusinessCommCItyTree> getTree(BusinessCommCity vo) {
                List<BusinessCommCItyTree> rs = baseMapper.getAll(vo);
            rs.addAll(baseMapper.getVillage(vo));
                List<BusinessCommCItyTree> collect = rs.stream().filter(m -> m.getPid() == null).map(
                        (m) -> {
                                m.setChildList(getChildrens(m, rs));
                                return m;
                        }
                ).collect(Collectors.toList());
               // log.debug(collect.toString());
                //System.out.println("-------转json输出结果-------");
               // System.out.println(JSON.toJSON(collect));
                return collect;
        }

    @Override
    public List<BusinessCommCItyTree> getCityTreeWithVillage(BusinessCommCity vo) {

        List<BusinessCommCItyTree> rs = baseMapper.getAllWithVillage(vo);
        List<BusinessCommCItyTree> collect = rs.stream().filter(m -> m.getPid() == null).map(
                (m) -> {
                    m.setChildList(getChildrens(m, rs));
                    return m;
                }
        ).collect(Collectors.toList());
        // log.debug(collect.toString());
        //System.out.println("-------转json输出结果-------");
        // System.out.println(JSON.toJSON(collect));
        return collect;
    }

    /**
         * 递归查询子节点
         * @param root  根节点
         * @param all   所有节点
         * @return 根节点信息
         */
        private List<BusinessCommCItyTree> getChildrens(BusinessCommCItyTree root, List<BusinessCommCItyTree> all) {
                List<BusinessCommCItyTree> children = all.stream().filter(m -> {
                        return Objects.equals(m.getPid(), root.getJurisdictionNum());
                }).map(
                        (m) -> {
                                m.setChildList(getChildrens(m, all));
                                return m;
                        }
                ).collect(Collectors.toList());
                return children;
        }
}