package com.tzslsjy.business.service;

import java.util.List;
import com.tzslsjy.business.domain.SjyFdStObj;

/**
 * 自动监测站名录Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
public interface ISjyFdStObjService 
{
    /**
     * 查询自动监测站名录
     * 
     * @param stCode 自动监测站名录主键
     * @return 自动监测站名录
     */
    public SjyFdStObj selectSjyFdStObjByStCode(String stCode);

    /**
     * 查询自动监测站名录列表
     * 
     * @param sjyFdStObj 自动监测站名录
     * @return 自动监测站名录集合
     */
    public List<SjyFdStObj> selectSjyFdStObjList(SjyFdStObj sjyFdStObj);

    /**
     * 新增自动监测站名录
     * 
     * @param sjyFdStObj 自动监测站名录
     * @return 结果
     */
    public int insertSjyFdStObj(SjyFdStObj sjyFdStObj);

    /**
     * 修改自动监测站名录
     * 
     * @param sjyFdStObj 自动监测站名录
     * @return 结果
     */
    public int updateSjyFdStObj(SjyFdStObj sjyFdStObj);

    /**
     * 批量删除自动监测站名录
     * 
     * @param stCodes 需要删除的自动监测站名录主键集合
     * @return 结果
     */
    public int deleteSjyFdStObjByStCodes(String[] stCodes);

    /**
     * 删除自动监测站名录信息
     * 
     * @param stCode 自动监测站名录主键
     * @return 结果
     */
    public int deleteSjyFdStObjByStCode(String stCode);
}
