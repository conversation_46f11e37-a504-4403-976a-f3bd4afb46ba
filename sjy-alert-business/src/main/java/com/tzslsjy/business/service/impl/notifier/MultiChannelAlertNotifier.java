package com.tzslsjy.business.service.impl.notifier;

import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.service.AlertNotifier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 多渠道预警通知分发器
 * 将预警消息分发到多个通知渠道
 */
@Component
public class MultiChannelAlertNotifier implements AlertNotifier {
    private static final Logger logger = LoggerFactory.getLogger(MultiChannelAlertNotifier.class);
    
    // 注入所有通知器
    @Autowired
    private List<AlertNotifier> notifiers;
    
    // 缓存通知器映射，提高查找效率
    private Map<String, AlertNotifier> notifierMap = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initNotifiers() {
        if (notifiers != null) {
            for (AlertNotifier notifier : notifiers) {
                // 跳过自己，避免循环引用
                if (!(notifier instanceof MultiChannelAlertNotifier)) {
                    notifierMap.put(notifier.getNotifierType(), notifier);
                    logger.info("注册通知渠道: {}", notifier.getNotifierName());
                }
            }
        }
    }

    @Override
    public int notify(List<SjyAlertSmsRecord> records) {
        if (records == null || records.isEmpty()) {
            return 0;
        }
        
        logger.info("开始多渠道发送{}条预警通知", records.size());
        int totalSuccess = 0;
        
        // 按照优先级依次尝试各个通知渠道
        for (SjyAlertSmsRecord record : records) {
            // 首选通知方式
            String preferredChannel = determinePreferredChannel(record);
            boolean sent = sendViaChannel(record, preferredChannel);
            
            // 如果首选方式失败，尝试备用渠道
            if (!sent) {
                for (String backupChannel : getBackupChannels(record, preferredChannel)) {
                    sent = sendViaChannel(record, backupChannel);
                    if (sent) break;
                }
            }
            
            if (sent) {
                totalSuccess++;
            }
        }
        
        logger.info("多渠道通知完成，成功: {}/{}", totalSuccess, records.size());
        return totalSuccess;
    }
    
    /**
     * 通过指定渠道发送单条通知
     */
    private boolean sendViaChannel(SjyAlertSmsRecord record, String channel) {
        AlertNotifier notifier = notifierMap.get(channel);
        if (notifier == null) {
            logger.warn("未找到渠道{}的通知器", channel);
            return false;
        }
        
        try {
            if (notifier.supports(record)) {
                int result = notifier.notify(Collections.singletonList(record));
                return result > 0;
            }
        } catch (Exception e) {
            logger.error("通过{}发送通知失败: {}", channel, e.getMessage(), e);
        }
        
        return false;
    }
    
    /**
     * 根据预警记录确定首选通知渠道
     */
    private String determinePreferredChannel(SjyAlertSmsRecord record) {
        // 如果明确指定了发送类型，优先使用
        if (record.getSendType() != null && !record.getSendType().isEmpty() && 
            notifierMap.containsKey(record.getSendType())) {
            return record.getSendType();
        }
        
        // 根据预警级别选择渠道
        Long level = record.getAlertLevel();
        if (level != null) {
            if (level >= 3) {
                // 最高级别：广播优先
                return "broadcast";
            } else if (level >= 2) {
                // 中高级别：浙政钉优先
                return "zhezhengding";
            }
        }
        
        // 默认使用短信
        return "sms";
    }
    
    /**
     * 获取备用通知渠道
     */
    private String[] getBackupChannels(SjyAlertSmsRecord record, String excludeChannel) {
        // 最高级别的消息尝试所有可用渠道
        if (record.getAlertLevel() != null && record.getAlertLevel() >= 3) {
            return new String[]{
                "zhezhengding", "sms", "fax", "broadcast" 
            };
        }
        
        // 默认备用渠道顺序
        return new String[]{"sms", "zhezhengding"};
    }

    @Override
    public String getNotifierName() {
        return "多渠道通知";
    }

    @Override
    public String getNotifierType() {
        return "multi_channel";
    }
}
