package com.tzslsjy.business.service.impl.notifier;

import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.mapper.SjyAlertPersonMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

/**
 * 传真预警通知实现
 */
@Component
public class FaxAlertNotifier extends AbstractAlertNotifier {



    @Autowired
    private SjyAlertPersonMapper personMapper;

    @Override
    public String getNotifierType() {
        return "fax";
    }

    @Override
    public String getNotifierName() {
        return "传真通知";
    }

    @Override
    protected boolean doSendAlert(SjyAlertSmsRecord record) {
        try {
            // 获取接收人信息
            String personId = record.getPersonId();
            if (personId == null) {
                logger.warn("预警记录未指定接收人ID");
                return false;
            }

            // 查询接收人传真号码

            // 生成传真内容
            ByteArrayInputStream faxContent = generateFaxContent(record);

            // 调用传真服务

            return false;
        } catch (Exception e) {
            logger.error("发送传真失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 生成传真内容
     */
    private ByteArrayInputStream generateFaxContent(SjyAlertSmsRecord record) {
        // 简单实现：创建格式化文本
        String header = "===== 应急预警通知 =====\n\n";
        String time = "发送时间: " + record.getSendTime() + "\n\n";
        String content = record.getContent() + "\n\n";
        String footer = "===== 此信息由水旱灾害预警系统自动生成 =====";

        String faxText = header + time + content + footer;
        return new ByteArrayInputStream(faxText.getBytes(StandardCharsets.UTF_8));
    }

    @Override
    public boolean supports(SjyAlertSmsRecord record) {
        if (!super.supports(record)) {
            return false;
        }

        // 仅支持高级别预警
        Long level = record.getAlertLevel();
        return level != null && level >= 2L;
    }
}
