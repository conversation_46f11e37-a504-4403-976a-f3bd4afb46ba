package com.tzslsjy.business.service.comm;

import com.tzslsjy.business.domain.comm.BusinessCommCity;
import com.tzslsjy.business.domain.comm.BusinessCommCItyTree;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @auther seven
 * @create 2021-05-11 10:55:10
 * @describe 行政区划、城市乡镇服务类
 */
public interface ICommCityService extends IService<BusinessCommCity> {

    List<BusinessCommCity> pageList(BusinessCommCity vo);

    List<BusinessCommCItyTree> getTree(BusinessCommCity vo);

    List<BusinessCommCItyTree> getCityTreeWithVillage(BusinessCommCity vo);
}