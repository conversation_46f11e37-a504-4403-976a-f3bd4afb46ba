package com.tzslsjy.business.service.impl;

import java.util.List;
import java.util.Date;
import java.util.Calendar;

import com.tzslsjy.business.domain.SjyAlertReceiveRecord;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertPerson;
import com.tzslsjy.business.mapper.SjyAlertReceiveRecordMapper;
import com.tzslsjy.business.service.ISjyAlertReceiveRecordService;
import com.tzslsjy.business.service.ISjyAlertSmsRecordService;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 预警接收记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
public class SjyAlertReceiveRecordServiceImpl implements ISjyAlertReceiveRecordService
{
    @Autowired
    private SjyAlertReceiveRecordMapper sjyAlertReceiveRecordMapper;
    
    @Autowired
    private ISjyAlertSmsRecordService sjyAlertSmsRecordService;
    
    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService;

    /**
     * 查询预警接收记录
     * 
     * @param receiveId 预警接收记录主键
     * @return 预警接收记录
     */
    @Override
    public SjyAlertReceiveRecord selectSjyAlertReceiveRecordByReceiveId(Long receiveId)
    {
        return sjyAlertReceiveRecordMapper.selectSjyAlertReceiveRecordByReceiveId(receiveId);
    }

    /**
     * 查询预警接收记录列表
     * 
     * @param sjyAlertReceiveRecord 预警接收记录
     * @return 预警接收记录
     */
    @Override
    public List<SjyAlertReceiveRecord> selectSjyAlertReceiveRecordList(SjyAlertReceiveRecord sjyAlertReceiveRecord)
    {
        return sjyAlertReceiveRecordMapper.selectSjyAlertReceiveRecordList(sjyAlertReceiveRecord);
    }

    /**
     * 新增预警接收记录
     * 
     * @param sjyAlertReceiveRecord 预警接收记录
     * @return 结果
     */
    @Override
    public int insertSjyAlertReceiveRecord(SjyAlertReceiveRecord sjyAlertReceiveRecord)
    {
        sjyAlertReceiveRecord.setCreateTime(DateUtils.getNowDate());
        return sjyAlertReceiveRecordMapper.insertSjyAlertReceiveRecord(sjyAlertReceiveRecord);
    }

    /**
     * 修改预警接收记录
     * 
     * @param sjyAlertReceiveRecord 预警接收记录
     * @return 结果
     */
    @Override
    public int updateSjyAlertReceiveRecord(SjyAlertReceiveRecord sjyAlertReceiveRecord)
    {
        sjyAlertReceiveRecord.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertReceiveRecordMapper.updateSjyAlertReceiveRecord(sjyAlertReceiveRecord);
    }

    /**
     * 批量删除预警接收记录
     * 
     * @param receiveIds 需要删除的预警接收记录主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertReceiveRecordByReceiveIds(Long[] receiveIds)
    {
        return sjyAlertReceiveRecordMapper.deleteSjyAlertReceiveRecordByReceiveIds(receiveIds);
    }

    /**
     * 删除预警接收记录信息
     * 
     * @param receiveId 预警接收记录主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertReceiveRecordByReceiveId(Long receiveId)
    {
        return sjyAlertReceiveRecordMapper.deleteSjyAlertReceiveRecordByReceiveId(receiveId);
    }

    /**
     * 从SMS记录同步数据到接收记录表
     * 
     * @param minutes 最近几分钟
     * @return 同步的记录数
     */
    @Override
    public int syncFromSmsRecord(Integer minutes)
    {
        // 计算查询的开始时间
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MINUTE, -minutes);
        Date startTime = calendar.getTime();

        // 查询最近几分钟的SMS记录
        List<SjyAlertSmsRecord> smsRecords = sjyAlertSmsRecordService.selectSjyAlertSmsRecordsByTimeRange(startTime);

        int syncCount = 0;
        for (SjyAlertSmsRecord smsRecord : smsRecords) {
            // 解析personId - SMS记录中的personId是逗号分隔的字符串
            if (smsRecord.getPersonId() != null && !smsRecord.getPersonId().trim().isEmpty()) {
                String[] personIds = smsRecord.getPersonId().split(",");
                
                for (String personIdStr : personIds) {
                    personIdStr = personIdStr.trim();
                    if (personIdStr.isEmpty()||"null".equals(personIdStr)) {
                        continue;
                    }
                    
                    // 检查是否已存在对应的接收记录
                    SjyAlertReceiveRecord existQuery = new SjyAlertReceiveRecord();
                    existQuery.setSmsId(smsRecord.getAlertSmsId());
                    existQuery.setPersonId(personIdStr);
                    List<SjyAlertReceiveRecord> existRecords = sjyAlertReceiveRecordMapper.selectSjyAlertReceiveRecordList(existQuery);
                    
                    if (existRecords.isEmpty()) {
                        // 为每个personId创建一条接收记录
                        SjyAlertReceiveRecord receiveRecord = new SjyAlertReceiveRecord();
                        receiveRecord.setSmsId(smsRecord.getAlertSmsId());
                        receiveRecord.setPersonId(personIdStr);
                        
                        // 通过personId查询人员信息获取手机号
                        try {
                            SjyAlertPerson person = sjyAlertPersonService.selectSjyAlertPersonByPersonId(personIdStr);
                            if (person != null && person.getPhone() != null) {
                                receiveRecord.setPhone(person.getPhone());
                            }
                        } catch (Exception e) {
                            // 查询人员信息失败时记录日志，但不阻断同步流程
                            // 可以根据需要添加日志记录
                        }
                        
                        // 设置渠道类型，默认为短信
                        receiveRecord.setWayType("1"); // 1-短信
                        receiveRecord.setIzRead(0L); // 0-未读
                        receiveRecord.setStatus(smsRecord.getStatus()); // 使用SMS记录的状态
                        receiveRecord.setSendTime(smsRecord.getSendTime());
                        
                        // 插入接收记录
                        receiveRecord.setCreateTime(DateUtils.getNowDate());
                        int result = sjyAlertReceiveRecordMapper.insertSjyAlertReceiveRecord(receiveRecord);
                        if (result > 0) {
                            syncCount++;
                        }
                    }
                }
            }
        }

        return syncCount;
    }
}
