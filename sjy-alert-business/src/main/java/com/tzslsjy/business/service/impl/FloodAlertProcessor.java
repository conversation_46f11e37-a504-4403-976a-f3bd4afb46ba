package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertRuleArgService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 山洪预警处理器
 * 负责生成预警消息和获取接收人
 */
@Component
public class FloodAlertProcessor implements AlertProcessor, TypedAlertProcessor {
    private static final Logger logger = LoggerFactory.getLogger(FloodAlertProcessor.class);

    @Autowired
    @Qualifier("floodAlertTemplateProcessor") // 指定注入 FloodAlertTemplateProcessor
    private AlertTemplateProcessor templateProcessor;

    @Autowired
    private SjyAlertNodeMapper alertNodeMapper;

    @Autowired
    private ISjyAlertRuleArgService alertRuleArgService;

    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 下一个处理器
    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public void process(AlertContext context) {
        logger.info("开始处理山洪预警");

        try {
            Map<String, Object> data = context.getData();
            SjyAlertRule rule = context.getRule();

            if (data == null || rule == null) {
                logger.warn("预警数据不完整，无法处理");
                if (nextProcessor != null) {
                    nextProcessor.process(context);
                }
                return;
            }

            // 检查是否触发预警
            boolean triggered = (boolean) data.getOrDefault("overallTriggered", false);
            if (!triggered) {
                logger.info("没有触发预警，跳过消息生成");
                if (nextProcessor != null) {
                    nextProcessor.process(context);
                }
                return;
            }

            // 获取触发的参数集结果
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> triggeredResults = (List<Map<String, Object>>) data.get("triggeredResults");
            if (triggeredResults == null || triggeredResults.isEmpty()) {
                logger.warn("没有触发的参数集数据");
                if (nextProcessor != null) {
                    nextProcessor.process(context);
                }
                return;
            }

            // 存储所有生成的预警记录
            List<SjyAlertSmsRecord> allRecords = new ArrayList<>();

            // 为每个触发的参数集生成预警消息
            for (Map<String, Object> alertData : triggeredResults) {
                String argId = (String) alertData.get("argId");
                logger.debug("处理参数集: {}", argId);

                // 获取合适的模板
                List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), rule.getAlertAdnm());
                
                for (SjyAlertSmsTemplate template : templates) {
                    // 根据模板类型选择合适的数据列表
                    if (template.getType().equals("3") || template.getType().equals("4")) {
                        // 雨量模板
                        if (!alertData.containsKey("rainlist")) {
                            continue; // 跳过没有雨量数据的情况
                        }
                        alertData.put("list", alertData.get("rainlist"));
                    } else {
                        // 水位模板
                        if (!alertData.containsKey("waterlist")) {
                            continue; // 跳过没有水位数据的情况
                        }
                        alertData.put("list", alertData.get("waterlist"));
                    }

                    // 生成消息
                    SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, alertData);

                    if (alert != null) {
                        // 获取村庄接收人
                        List<SjyAlertPerson> receivers = getVillageReceivers(rule.getRuleId(), rule.getAlertAdnm(), argId);
                        List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                        List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);

                        // 添加到总结果
                        allRecords.addAll(mergedAlerts);
                        logger.info("参数集{}成功生成预警消息，接收人数量: {}", argId, mergedReceivers.size());
                    }
                }
            }

            // 设置到上下文
            context.setRecords(allRecords);
            logger.info("总共生成{}条预警消息", allRecords.size());

        } catch (Exception e) {
            logger.error("处理山洪预警失败: {}", e.getMessage(), e);
        }

        // 继续处理链
        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    /**
     * 获取适合当前预警级别的模板
     */
    private List<SjyAlertSmsTemplate> getTemplates(Long ruleId, String adnm) {
        try {
            String type="1,2,3,4";
            //根据关联信息选取哪个模板
            if("yhcl".equals(adnm)){
                type="1,2";
            }else {
                type="3,4";
            }
            List<SjyAlertSmsTemplate> sjyAlertSmsTemplates = alertSmsTemplateMapper.selectOneByTypeAndRuleId(ruleId, type);
           return sjyAlertSmsTemplates;
        } catch (Exception e) {
            logger.error("获取预警模板失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取村庄的接收人
     */
    private List<SjyAlertPerson> getVillageReceivers(Long ruleId, String villageId, String argId) {
        List<SjyAlertPerson> receivers = new ArrayList<>();

        try {
            // 查询村庄关联的接收人
              receivers = alertNodeMapper.selectPersonByVillageIdAndArgId( argId);
        } catch (Exception e) {
            logger.error("获取村庄{}的接收人失败: {}", villageId, e.getMessage(), e);
        }

        return receivers;
    }

    @Override
    public String getSupportedType() {
        return "1";
    }
}
