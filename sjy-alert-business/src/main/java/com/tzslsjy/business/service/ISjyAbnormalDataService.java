package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.SjyAbnormalData;
import com.tzslsjy.business.vo.SjyAbnormalDataVo;
import com.tzslsjy.business.bo.SjyAbnormalDataQueryBo;
import com.tzslsjy.business.bo.SjyAbnormalDataAddBo;
import com.tzslsjy.business.bo.SjyAbnormalDataEditBo;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;

import java.util.Collection;
import java.util.Date;
import java.util.List;

/**
 * 测站异常数据Service接口
 *
 * <AUTHOR>
 * @date 2025-07-07
 */
@DataSource(DataSourceType.INFO)
public interface ISjyAbnormalDataService {

    /**
     * 查询单个
     * @param stcd 测站编号
     * @param tm 时间
     * @param type 类型
     * @return
     */
    SjyAbnormalDataVo queryByKey(String stcd, String tm, String type);

    /**
     * 查询列表
     */
    List<SjyAbnormalDataVo> queryList(SjyAbnormalDataQueryBo bo);

    /**
     * 批量查询异常数据
     * @param stcds 测站编号列表
     * @param type 数据类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异常数据列表
     */
    List<SjyAbnormalDataVo> queryBatchByCondition(List<String> stcds,  List<String> type, Date startTime, Date endTime);

    /**
     * 根据新增业务对象插入测站异常数据
     * @param bo 测站异常数据新增业务对象
     * @return
     */
    Boolean insertByAddBo(SjyAbnormalDataAddBo bo);

    /**
     * 根据编辑业务对象修改测站异常数据
     * @param bo 测站异常数据编辑业务对象
     * @return
     */
    Boolean updateByEditBo(SjyAbnormalDataEditBo bo);

    /**
     * 校验并删除数据
     * @param stcds 测站编号集合
     * @param isValid 是否校验,true-删除前校验,false-不校验
     * @return
     */
    Boolean deleteWithValidByStcds(Collection<String> stcds, Boolean isValid);

    /**
     * 根据条件删除数据
     * @param stcd 测站编号
     * @param type 类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    Boolean deleteByCondition(String stcd, String type, String startTime, String endTime);
}
