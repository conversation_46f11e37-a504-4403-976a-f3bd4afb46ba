package com.tzslsjy.business.service.impl;

import java.util.List;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyAlertRuleArgMapper;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.service.ISjyAlertRuleArgService;

/**
 * 预警规则参数Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SjyAlertRuleArgServiceImpl implements ISjyAlertRuleArgService
{
    @Autowired
    private SjyAlertRuleArgMapper sjyAlertRuleArgMapper;

    /**
     * 查询预警规则参数
     *
     * @param argId 预警规则参数主键
     * @return 预警规则参数
     */
    @Override
    public SjyAlertRuleArg selectSjyAlertRuleArgByArgId(Long argId)
    {
        return sjyAlertRuleArgMapper.selectSjyAlertRuleArgByArgId(argId);
    }

    @Override
    public SjyAlertRuleArg selectSjyAlertRuleArgById(Long argId) {
        return null;
    }

    /**
     * 查询预警规则参数列表
     *
     * @param sjyAlertRuleArg 预警规则参数
     * @return 预警规则参数
     */
    @Override
    public List<SjyAlertRuleArg> selectSjyAlertRuleArgList(SjyAlertRuleArg sjyAlertRuleArg)
    {
        return sjyAlertRuleArgMapper.selectSjyAlertRuleArgList(sjyAlertRuleArg);
    }

    /**
     * 新增预警规则参数
     *
     * @param sjyAlertRuleArg 预警规则参数
     * @return 结果
     */
    @Override
    public int insertSjyAlertRuleArg(SjyAlertRuleArg sjyAlertRuleArg)
    {
        sjyAlertRuleArg.setCreateTime(DateUtils.getNowDate());
        return sjyAlertRuleArgMapper.insertSjyAlertRuleArg(sjyAlertRuleArg);
    }

    /**
     * 修改预警规则参数
     *
     * @param sjyAlertRuleArg 预警规则参数
     * @return 结果
     */
    @Override
    public int updateSjyAlertRuleArg(SjyAlertRuleArg sjyAlertRuleArg)
    {
        sjyAlertRuleArg.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertRuleArgMapper.updateSjyAlertRuleArg(sjyAlertRuleArg);
    }

    /**
     * 批量删除预警规则参数
     *
     * @param argIds 需要删除的预警规则参数主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertRuleArgByArgIds(Long[] argIds)
    {
        return sjyAlertRuleArgMapper.deleteSjyAlertRuleArgByArgIds(argIds);
    }

    /**
     * 删除预警规则参数信息
     *
     * @param argId 预警规则参数主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertRuleArgByArgId(Long argId)
    {
        return sjyAlertRuleArgMapper.deleteSjyAlertRuleArgByArgId(argId);
    }
}
