package com.tzslsjy.business.service;

import com.tzslsjy.business.bo.SjyRiskVillageStcdRelStcdAddBo;
import com.tzslsjy.business.domain.SjyRiskVillageStcdRel;
import com.tzslsjy.business.vo.SjyRiskVillageStcdRelVo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelQueryBo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelAddBo;
import com.tzslsjy.business.bo.SjyRiskVillageStcdRelEditBo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;

/**
 * 村庄关联测站编号Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface ISjyRiskVillageStcdRelService extends IService<SjyRiskVillageStcdRel> {
	/**
	 * 查询单个
	 * @return
	 */
	SjyRiskVillageStcdRelVo queryById(Long id);

	/**
	 * 查询列表
	 */
	List<SjyRiskVillageStcdRelVo> queryList(SjyRiskVillageStcdRelQueryBo bo);

	/**
	 * 根据新增业务对象插入村庄关联测站编号
	 * @param bo 村庄关联测站编号新增业务对象
	 * @return
	 */
	Boolean insertByAddBo(SjyRiskVillageStcdRelAddBo bo);

	/**
	 * 根据编辑业务对象修改村庄关联测站编号
	 * @param bo 村庄关联测站编号编辑业务对象
	 * @return
	 */
	Boolean updateByEditBo(SjyRiskVillageStcdRelEditBo bo);

	/**
	 * 校验并删除数据
	 * @param ids 主键集合
	 * @param isValid 是否校验,true-删除前校验,false-不校验
	 * @return
	 */
	Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void insertOrUpdateBatch(List<SjyRiskVillageStcdRel> sts, String id);

	boolean saveOrUpdateByStcd(SjyRiskVillageStcdRelStcdAddBo bo);
}