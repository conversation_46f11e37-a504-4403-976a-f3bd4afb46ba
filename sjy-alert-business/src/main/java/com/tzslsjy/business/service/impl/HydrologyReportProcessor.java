package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 8时水文预警处理器
 */
@Component
@Slf4j
public class HydrologyReportProcessor implements AlertProcessor, TypedAlertProcessor {

    @Autowired
    @Qualifier("hydrologyReportTemplateProcessor")
    private AlertTemplateProcessor templateProcessor;
    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService;
    @Autowired
    private SjyAlertNodeMapper alertNodeMapper;
    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper;
    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper;

    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public String getSupportedType() {
        return "13"; // 8时水文预警类型
    }

    @Override
    public void process(AlertContext context) {
        try {
            SjyAlertRule rule = context.getRule();
            Map<String, Object> data = context.getData();
            
            log.info("开始处理8时水文预警，规则ID: {}", rule.getRuleId());
            data = ( Map<String, Object>) ((List)data.get("argResults")).get(0);
            // 8时水文预警是汇总报告，直接处理content内容
            if (!data.containsKey("content") || data.get("content") == null) {
                log.warn("8时水文预警缺少content内容，规则ID: {}", rule.getRuleId());
                return;
            }

            String argId = data.get("argId") != null ? data.get("argId").toString() : "default";
            List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
            if (templates.isEmpty()) {
                log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                return;
            }

            List<SjyAlertSmsRecord> allRecords = new ArrayList<>();

            for (SjyAlertSmsTemplate template : templates) {
                // 直接使用data作为模板数据
                SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, data);
                if (alert != null) {
                    List<SjyAlertPerson> receivers = getReceivers(rule, argId);
                    List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                    List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                    allRecords.addAll(mergedAlerts);
                    log.info("8时水文预警生成 {} 条消息", mergedAlerts.size());
                }
            }
            
            context.setRecords(allRecords);
            log.info("总共生成 {} 条8时水文预警消息", allRecords.size());

        } catch (Exception e) {
            log.error("处理8时水文预警失败: {}", e.getMessage(), e);
        }

        // 传递给下一个处理器
        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    /**
     * 获取模板
     */
    private List<SjyAlertSmsTemplate> getTemplates(Long ruleId, String argId) {
        try {
            List<SjyAlertSmsTemplate> templates = alertSmsTemplateMapper.selectOneByRelId(String.valueOf(ruleId), "1");
            if (CollectionUtils.isEmpty(templates)) {
                log.warn("未找到规则ID {}，参数ID {} 的模板", ruleId, argId);
                return new ArrayList<>();
            }
            return templates;
        } catch (Exception e) {
            log.error("获取8时水文预警模板失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取接收人
     */
    private List<SjyAlertPerson> getReceivers(SjyAlertRule rule, String argId) {
        try {
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgId(argId);
            if (nodeIds == null || nodeIds.isEmpty()) {
                SjyAlertPerson admin = new SjyAlertPerson();
                admin.setPersonId("1");
                admin.setName("admin");
                admin.setPhone("15306587076");
                return Collections.singletonList(admin);
            }
            List<SjyAlertPerson> receivers = sjyAlertPersonService.selectByMemberIds(nodeIds);

            if (receivers.isEmpty()) {
                log.warn("规则ID {} 及其参数组 {} 均未配置接收人或未找到，使用默认管理员", rule.getRuleId(), argId);
                SjyAlertPerson admin = new SjyAlertPerson();
                admin.setName("admin");
                admin.setPhone("15306587076");
                return Collections.singletonList(admin);
            }
            // 去重
            return receivers.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取8时水文预警接收人失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }
} 