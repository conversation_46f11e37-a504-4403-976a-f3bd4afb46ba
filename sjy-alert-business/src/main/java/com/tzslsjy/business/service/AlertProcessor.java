package com.tzslsjy.business.service;

import com.tzslsjy.business.domain.AlertContext;

/**
 * 预警处理器接口
 * 用于实现责任链模式处理预警结果
 */
public interface AlertProcessor {
    
    /**
     * 处理预警上下文
     * 
     * @param context 预警上下文信息
     */
    void process(AlertContext context);
    
    /**
     * 设置下一个处理器
     * 
     * @param nextProcessor 下一个处理器
     */
    void setNextProcessor(AlertProcessor nextProcessor);
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    default String getProcessorName() {
        return this.getClass().getSimpleName();
    }
}
