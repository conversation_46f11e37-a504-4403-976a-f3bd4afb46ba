package com.tzslsjy.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyFdStObjMapper;
import com.tzslsjy.business.domain.SjyFdStObj;
import com.tzslsjy.business.service.ISjyFdStObjService;

/**
 * 自动监测站名录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-11
 */
@Service
public class SjyFdStObjServiceImpl implements ISjyFdStObjService 
{
    @Autowired
    private SjyFdStObjMapper sjyFdStObjMapper;

    /**
     * 查询自动监测站名录
     * 
     * @param stCode 自动监测站名录主键
     * @return 自动监测站名录
     */
    @Override
    public SjyFdStObj selectSjyFdStObjByStCode(String stCode)
    {
        return sjyFdStObjMapper.selectSjyFdStObjByStCode(stCode);
    }

    /**
     * 查询自动监测站名录列表
     * 
     * @param sjyFdStObj 自动监测站名录
     * @return 自动监测站名录
     */
    @Override
    public List<SjyFdStObj> selectSjyFdStObjList(SjyFdStObj sjyFdStObj)
    {
        return sjyFdStObjMapper.selectSjyFdStObjList(sjyFdStObj);
    }

    /**
     * 新增自动监测站名录
     * 
     * @param sjyFdStObj 自动监测站名录
     * @return 结果
     */
    @Override
    public int insertSjyFdStObj(SjyFdStObj sjyFdStObj)
    {
        return sjyFdStObjMapper.insertSjyFdStObj(sjyFdStObj);
    }

    /**
     * 修改自动监测站名录
     * 
     * @param sjyFdStObj 自动监测站名录
     * @return 结果
     */
    @Override
    public int updateSjyFdStObj(SjyFdStObj sjyFdStObj)
    {
        return sjyFdStObjMapper.updateSjyFdStObj(sjyFdStObj);
    }

    /**
     * 批量删除自动监测站名录
     * 
     * @param stCodes 需要删除的自动监测站名录主键
     * @return 结果
     */
    @Override
    public int deleteSjyFdStObjByStCodes(String[] stCodes)
    {
        return sjyFdStObjMapper.deleteSjyFdStObjByStCodes(stCodes);
    }

    /**
     * 删除自动监测站名录信息
     * 
     * @param stCode 自动监测站名录主键
     * @return 结果
     */
    @Override
    public int deleteSjyFdStObjByStCode(String stCode)
    {
        return sjyFdStObjMapper.deleteSjyFdStObjByStCode(stCode);
    }
}
