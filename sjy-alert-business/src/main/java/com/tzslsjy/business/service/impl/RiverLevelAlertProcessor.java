package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import com.tzslsjy.business.service.ContinuousAlertService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

/**
 * 河道水位预警处理器
 */
@Component
@Slf4j
public class RiverLevelAlertProcessor implements AlertProcessor, TypedAlertProcessor {

    @Autowired
    @Qualifier("riverLevelAlertTemplateProcessor")
    private AlertTemplateProcessor templateProcessor;
    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService;
    @Autowired
    private SjyAlertNodeMapper alertNodeMapper;
    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper;
    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper;

    @Autowired
    private ContinuousAlertService continuousAlertService;

    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public String getSupportedType() {
        return "15"; // 河道水位预警类型
    }

    @Override
    public void process(AlertContext context) {
        try {
            SjyAlertRule rule = context.getRule();
            Map<String, Object> data = context.getData();
            
            log.info("开始处理河道水位预警，规则ID: {}", rule.getRuleId());

            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");
            
            if (CollectionUtils.isEmpty(argResults)) {
                log.warn("河道水位预警参数组结果为空，规则ID: {}", rule.getRuleId());
                return;
            }

            List<SjyAlertSmsRecord> allRecords = new ArrayList<>();

            for (Map<String, Object> argResult : argResults) {
                // 检查是否有按行政区划分组的数据
                @SuppressWarnings("unchecked")
                Map<String, List<Map<String, Object>>> triggeredRiverGroups = 
                        (Map<String, List<Map<String, Object>>>) argResult.get("triggeredRiverGroups");

                if (triggeredRiverGroups != null && !triggeredRiverGroups.isEmpty()) {
                    // 处理按行政区划分组数据
                    String argId = argResult.get("argId").toString();
                    List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
                    if (templates.isEmpty()) {
                        log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                        continue;
                    }

                    // 为每个行政区划分组生成消息
                    for (Map.Entry<String, List<Map<String, Object>>> groupEntry : triggeredRiverGroups.entrySet()) {
                        String groupKey = groupEntry.getKey(); // 格式: "9_331082001" 或 "6_331082"
                        List<Map<String, Object>> groupRivers = groupEntry.getValue();
                        
                        if (CollectionUtils.isEmpty(groupRivers)) {
                            continue;
                        }
                        
                        String jurisdiction = extractJurisdictionFromGroupKey(groupKey);
                        String jurisdictionLevel = groupKey.substring(0, 1); // "9" 或 "6"
                        
                        log.info("处理{}位行政区划 {} 的河道水位预警，包含 {} 个测站", jurisdictionLevel, jurisdiction, groupRivers.size());
                        
                        for (SjyAlertSmsTemplate template : templates) {
                            Map<String, Object> templateData = new HashMap<>(argResult);
                            templateData.put("list", groupRivers);
                            templateData.put("jurisdiction", jurisdiction);
                            templateData.put("jurisdictionLevel", jurisdictionLevel);
                            
                            SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                            if (alert != null) {
                                // 根据行政区划获取对应的接收人员
                                List<SjyAlertPerson> receivers = getReceiversByJurisdiction(rule, argId, jurisdiction);
                                List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                                List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                                allRecords.addAll(mergedAlerts);
                                log.info("{}位行政区划 {} 生成 {} 条河道水位预警消息", jurisdictionLevel, jurisdiction, mergedAlerts.size());
                            }
                        }
                    }
                }

                // 检查是否有按工程编码分组的数据
                @SuppressWarnings("unchecked")
                Map<String, List<Map<String, Object>>> triggeredRiverGroupsByPjcd = 
                        (Map<String, List<Map<String, Object>>>) argResult.get("triggeredRiverGroupsByPjcd");

                if (triggeredRiverGroupsByPjcd != null && !triggeredRiverGroupsByPjcd.isEmpty()) {
                    // 处理按工程编码分组数据
                    String argId = argResult.get("argId").toString();
                    List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
                    if (templates.isEmpty()) {
                        log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                        continue;
                    }

                    // 为每个工程编码分组生成消息
                    for (Map.Entry<String, List<Map<String, Object>>> groupEntry : triggeredRiverGroupsByPjcd.entrySet()) {
                        String groupKey = groupEntry.getKey(); // 格式: "pjcd_工程编码"
                        List<Map<String, Object>> groupRivers = groupEntry.getValue();
                        
                        if (CollectionUtils.isEmpty(groupRivers)) {
                            continue;
                        }
                        
                        String pjcd = extractPjcdFromGroupKey(groupKey);
                        
                        log.info("处理工程编码 {} 的河道水位预警，包含 {} 个测站", pjcd, groupRivers.size());
                        
                        for (SjyAlertSmsTemplate template : templates) {
                            Map<String, Object> templateData = new HashMap<>(argResult);
                            templateData.put("list", groupRivers);
                            templateData.put("pjcd", pjcd);
                            templateData.put("groupType", "pjcd"); // 标识按工程编码分组
                            
                            SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                            if (alert != null) {
                                // 根据工程编码获取对应的接收人员
                                List<SjyAlertPerson> receivers = getReceiversByRscd(rule, argId, pjcd);
                                List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                                List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                                allRecords.addAll(mergedAlerts);
                                log.info("工程编码 {} 生成 {} 条河道水位预警消息", pjcd, mergedAlerts.size());
                            }
                        }
                    }
                }

                // 如果既没有按行政区划分组也没有按工程编码分组，则兼容原有逻辑：处理未分组的数据
                if ((triggeredRiverGroups == null || triggeredRiverGroups.isEmpty()) && 
                    (triggeredRiverGroupsByPjcd == null || triggeredRiverGroupsByPjcd.isEmpty())) {
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> triggeredRivers = 
                            (List<Map<String, Object>>) argResult.get("triggeredRivers");

                    if (CollectionUtils.isEmpty(triggeredRivers)) {
                        continue; 
                    }
                    
                    String argId = argResult.get("argId").toString();
                    List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId(), argId);
                    if (templates.isEmpty()) {
                        log.warn("未找到规则ID {}, 参数ID {} 的可用模板", rule.getRuleId(), argId);
                        continue;
                    }

                    for (SjyAlertSmsTemplate template : templates) {
                        Map<String, Object> templateData = new HashMap<>(argResult);
                        templateData.put("list", triggeredRivers); 
                        
                        SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, templateData);
                        if (alert != null) {
                            List<SjyAlertPerson> receivers = getReceivers(rule, argId);
                            List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                            List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);
                            allRecords.addAll(mergedAlerts);
                            log.info("参数组 {} 生成 {} 条河道水位预警消息（兼容模式）", argId, mergedAlerts.size());
                        }
                    }
                }

                // 执行测站状态更新（针对每个参数组）
                // 注意：因为数据提供阶段已过滤，这里不再依赖状态变化来决定发送
//                @SuppressWarnings("unchecked")
//                List<Map<String, Object>> allTriggeredRivers = getAllTriggeredRivers(argResult);
//                if (!CollectionUtils.isEmpty(allTriggeredRivers)) {
//                    try {
//                        continuousAlertService.processRiverContinuousAlert(rule, allTriggeredRivers);
//                        log.debug("河道预警规则 {} 参数组 {} 已更新 {} 个测站状态",
//                                rule.getRuleId(), argResult.get("argId"), allTriggeredRivers.size());
//                    } catch (Exception e) {
//                        log.error("更新测站状态失败: {}", e.getMessage(), e);
//                    }
//                }
            }
            context.setRecords(allRecords);
            log.info("总共生成 {} 条河道水位预警消息", allRecords.size());

        } catch (Exception e) {
            log.error("处理河道水位预警失败: {}", e.getMessage(), e);
        }

        // 传递给下一个处理器
        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    /**
     * 获取模板
     */
    private List<SjyAlertSmsTemplate> getTemplates(Long ruleId, String argId) {
        try {
            List<SjyAlertSmsTemplate> templates = alertSmsTemplateMapper.selectOneByRelId(argId, "2");
            if (CollectionUtils.isEmpty(templates)) {
                log.warn("未找到规则ID {}，参数ID {}，类型2的模板", ruleId, argId);
                return new ArrayList<>();
            }
            return templates;
        } catch (Exception e) {
            log.error("获取模板失败，规则ID: {}, 参数ID: {}, 错误: {}", ruleId, argId, e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取接收人
     */
    private List<SjyAlertPerson> getReceivers(SjyAlertRule rule, String argId) {
        try {
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgId(argId);
            return getReceiversFromNodeIds(nodeIds, rule.getRuleId(), argId, "参数组 " + argId);
        } catch (Exception e) {
            log.error("获取接收人员失败，规则ID: {}, 参数ID: {}, 错误: {}", rule.getRuleId(), argId, e.getMessage(), e);
            return getDefaultAdmin();
        }
    }

    /**
     * 从分组键中提取行政区划编码
     * @param groupKey 格式如 "9_331082001" 或 "6_331082"
     * @return 行政区划编码
     */
    private String extractJurisdictionFromGroupKey(String groupKey) {
        if (groupKey != null && groupKey.length() > 2) {
            return groupKey.substring(2); // 跳过 "9_" 或 "6_" 前缀
        }
        return "";
    }

    /**
     * 从分组键中提取工程编码
     * @param groupKey 格式如 "pjcd_工程编码"
     * @return 工程编码
     */
    private String extractPjcdFromGroupKey(String groupKey) {
        if (groupKey != null && groupKey.startsWith("pjcd_")) {
            return groupKey.substring(5); // 跳过 "pjcd_" 前缀
        }
        return "";
    }

    /**
     * 根据编码获取接收人员
     * @param rule 预警规则
     * @param argId 参数组ID
     * @return 接收人员列表
     */
    private List<SjyAlertPerson> getReceiversByRscd(SjyAlertRule rule, String argId, String rscd ) {
        try {
            String  nodeId= rscd;
            List<String> nodeIds    = alertRuleNodeRelationMapper.selectNodeIdsByArgIdAndNodeId(  argId,nodeId);
            if( nodeIds == null || nodeIds.isEmpty()) {
                SjyAlertPerson admin = new SjyAlertPerson();
                admin.setName("admin");
                admin.setPhone("15306587076");
                return Collections.singletonList(admin);
            }
            List<SjyAlertPerson> receivers  =sjyAlertPersonService.selectByMemberIds(nodeIds);

            if (receivers.isEmpty()) {
                log.warn("规则ID {} 及其参数组 {} 均未配置接收人或未找到，使用默认管理员", rule.getRuleId(), argId);
                SjyAlertPerson admin = new SjyAlertPerson();
                admin.setName("admin");
                admin.setPhone("15306587076");
                return Collections.singletonList(admin);
            }

            return receivers.stream().distinct().collect(Collectors.toList());

        } catch (Exception e) {
            log.error("根据行政区划获取接收人员失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }
    /**
     * 根据行政区划获取接收人员
     * @param rule 预警规则
     * @param argId 参数组ID
     * @param adcd 行政区划编码
     * @return 接收人员列表
     */
    private List<SjyAlertPerson> getReceiversByJurisdiction(SjyAlertRule rule, String argId, String adcd) {
        try {
            List<String> nodeIds    = alertRuleNodeRelationMapper.selectNodeIdsByArgIdAndAdcd(  argId,adcd);
            if( nodeIds == null || nodeIds.isEmpty()) {
                SjyAlertPerson admin = new SjyAlertPerson();
                admin.setName("admin");
                admin.setPhone("15306587076");
                return Collections.singletonList(admin);
            }
            List<SjyAlertPerson> receivers  =sjyAlertPersonService.selectByMemberIds(nodeIds);

            if (receivers.isEmpty()) {
                log.warn("规则ID {} 及其参数组 {} 均未配置接收人或未找到，使用默认管理员", rule.getRuleId(), argId);
                SjyAlertPerson admin = new SjyAlertPerson();
                admin.setName("admin");
                admin.setPhone("15306587076");
                return Collections.singletonList(admin);
            }
            // 去重
            log.debug("根据行政区划 {} 获取接收人员", adcd);
            return receivers.stream().distinct().collect(Collectors.toList());
//            log.debug("根据行政区划 {} 获取接收人员", adcd);
//            return getReceivers(rule, argId);
        } catch (Exception e) {
            log.error("根据行政区划获取接收人员失败: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取参数组中所有触发的河道（合并所有分组）
     */
    private List<Map<String, Object>> getAllTriggeredRivers(Map<String, Object> argResult) {
        List<Map<String, Object>> allTriggered = new ArrayList<>();
        
        // 获取按行政区划分组的数据
        @SuppressWarnings("unchecked")
        Map<String, List<Map<String, Object>>> triggeredRiverGroups = 
                (Map<String, List<Map<String, Object>>>) argResult.get("triggeredRiverGroups");
        if (triggeredRiverGroups != null) {
            triggeredRiverGroups.values().forEach(allTriggered::addAll);
        }
        
        // 获取按工程编码分组的数据
        @SuppressWarnings("unchecked")
        Map<String, List<Map<String, Object>>> triggeredRiverGroupsByPjcd = 
                (Map<String, List<Map<String, Object>>>) argResult.get("triggeredRiverGroupsByPjcd");
        if (triggeredRiverGroupsByPjcd != null) {
            triggeredRiverGroupsByPjcd.values().forEach(allTriggered::addAll);
        }
        
        // 获取未分组的数据
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> triggeredRivers = 
                (List<Map<String, Object>>) argResult.get("triggeredRivers");
        if (triggeredRivers != null) {
            allTriggered.addAll(triggeredRivers);
        }
        
        // 去重（基于stcd）
        return allTriggered.stream()
                .collect(Collectors.groupingBy(river -> river.get("stcd")))
                .values()
                .stream()
                .map(group -> group.get(0)) // 每个测站只保留一个
                .collect(Collectors.toList());
    }

    /**
     * 通用的从节点ID获取接收人方法
     */
    private List<SjyAlertPerson> getReceiversFromNodeIds(List<String> nodeIds, Long ruleId, String argId, String context) {
        if (nodeIds == null || nodeIds.isEmpty()) {
            log.debug("未找到 {} 的节点ID，使用默认管理员", context);
            return getDefaultAdmin();
        }
        
        try {
            List<SjyAlertPerson> receivers = sjyAlertPersonService.selectByMemberIds(nodeIds);
            if (receivers.isEmpty()) {
                log.warn("规则ID {} 及其参数组 {} 的 {} 未配置接收人或未找到，使用默认管理员", ruleId, argId, context);
                return getDefaultAdmin();
            }
            return receivers.stream().distinct().collect(Collectors.toList());
        } catch (Exception e) {
            log.error("从节点ID获取接收人失败，{}: {}", context, e.getMessage(), e);
            return getDefaultAdmin();
        }
    }

    /**
     * 获取默认管理员
     */
    private List<SjyAlertPerson> getDefaultAdmin() {
        SjyAlertPerson admin = new SjyAlertPerson();
        admin.setName("admin");
        admin.setPhone("15306587076");
        return Collections.singletonList(admin);
    }
} 