package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.service.AlertRuleStrategy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 雨量预警规则策略
 * 负责评估是否触发预警
 */
@Slf4j
@Component
public class RainfallAlertStrategy implements AlertRuleStrategy {

    @Override
    public boolean evaluate(AlertContext context) {
        log.debug("开始评估雨量预警规则");

        try {
            Map<String, Object> data = context.getData();

            if (data == null || data.isEmpty()) {
                log.warn("预警数据为空，无法评估");
                return false;
            }

            // 获取所有参数集结果
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> argResults = (List<Map<String, Object>>) data.get("argResults");

            if (argResults == null || argResults.isEmpty()) {
                log.warn("未找到参数集结果数据");
                return false;
            }

            // 存储每个参数集的评估结果
            List<Map<String, Object>> triggeredResults = new ArrayList<>();

            // 标记是否有任何一组参数触发了预警
            boolean anyTriggered = false;

            // 评估每个参数集的数据
            for (Map<String, Object> argResult : argResults) {
                if (argResult.containsKey("alertData")) {
                    List<Map<String, Object>> alertData = (List<Map<String, Object>>) argResult.get("alertData");

                    // 检查此参数集是否触发预警
                    boolean triggered = isResultTriggered(alertData.get(0));

                    if (triggered) {
                        anyTriggered = true;
                        triggeredResults.add(argResult);
                    }
                }
            }

            // 将评估结果存入上下文，供后续处理使用
            data.put("overallTriggered", anyTriggered);
            data.put("triggeredResults", triggeredResults);

            return anyTriggered;
        } catch (Exception e) {
            log.error("评估雨量预警规则失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 评估单个结果是否触发预警
     */
    private boolean isResultTriggered(Map<String, Object> alertData) {
        // 检查是否有测站数据且有触发
        if (alertData.containsKey("stationResults")) {
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> stationResults = (List<Map<String, Object>>) alertData.get("stationResults");

            // 如果有任何测站触发了预警，则整体触发
            return !stationResults.isEmpty();
        } else if (alertData.containsKey("hasTriggered") && (boolean) alertData.get("hasTriggered")) {
            // 直接检查是否触发标记
            return true;
        } else if (alertData.containsKey("rainfallList") && alertData.get("rainfallList") != null) {
            // 单测站情况：检查雨量列表
            return true;
        }

        return false;
    }

    @Override
    public String getType() {
        return "2"; // 假设2代表雨量规则类型
    }
}
