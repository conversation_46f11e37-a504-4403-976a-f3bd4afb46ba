package com.tzslsjy.business.service.impl;

import java.util.List;
import java.util.ArrayList;

import com.github.pagehelper.PageHelper;
import com.tzslsjy.business.domain.SjyAlertSmsRecord;
import com.tzslsjy.business.domain.SjyAlertSmsStatsRequest;
import com.tzslsjy.business.domain.vo.SjyAlertSmsStatsVO;
import com.tzslsjy.business.mapper.SjyAlertSmsRecordMapper;
import com.tzslsjy.business.service.ISjyAlertSmsRecordService;
import com.tzslsjy.business.vo.SjyAlertStatRespVo;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 预警消息记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-03
 */
@Service
public class SjyAlertSmsRecordServiceImpl implements ISjyAlertSmsRecordService
{
    @Autowired
    private SjyAlertSmsRecordMapper sjyAlertSmsRecordMapper;

    /**
     * 查询预警消息记录
     * 
     * @param alertSmsId 预警消息记录主键
     * @return 预警消息记录
     */
    @Override
    public SjyAlertSmsRecord selectSjyAlertSmsRecordByAlertSmsId(Long alertSmsId)
    {
        return sjyAlertSmsRecordMapper.selectSjyAlertSmsRecordByAlertSmsId(alertSmsId);
    }

    /**
     * 查询预警消息记录列表
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 预警消息记录
     */
    @Override
    public List<SjyAlertSmsRecord> selectSjyAlertSmsRecordList(SjyAlertSmsRecord sjyAlertSmsRecord)
    {
        return sjyAlertSmsRecordMapper.selectSjyAlertSmsRecordList(sjyAlertSmsRecord);
    }

    /**
     * 新增预警消息记录
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 结果
     */
    @Override
    public int insertSjyAlertSmsRecord(SjyAlertSmsRecord sjyAlertSmsRecord)
    {
        sjyAlertSmsRecord.setCreateTime(DateUtils.getNowDate());
        return sjyAlertSmsRecordMapper.insertSjyAlertSmsRecord(sjyAlertSmsRecord);
    }

    /**
     * 修改预警消息记录
     * 
     * @param sjyAlertSmsRecord 预警消息记录
     * @return 结果
     */
    @Override
    public int updateSjyAlertSmsRecord(SjyAlertSmsRecord sjyAlertSmsRecord)
    {
        sjyAlertSmsRecord.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertSmsRecordMapper.updateSjyAlertSmsRecord(sjyAlertSmsRecord);
    }

    /**
     * 批量删除预警消息记录
     * 
     * @param alertSmsIds 需要删除的预警消息记录主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertSmsRecordByAlertSmsIds(Long[] alertSmsIds)
    {
        return sjyAlertSmsRecordMapper.deleteSjyAlertSmsRecordByAlertSmsIds(alertSmsIds);
    }

    /**
     * 查询指定时间范围内的SMS记录
     * 
     * @param startTime 开始时间
     * @return SMS记录列表
     */
    @Override
    public List<SjyAlertSmsRecord> selectSjyAlertSmsRecordsByTimeRange(java.util.Date startTime)
    {
        return sjyAlertSmsRecordMapper.selectSjyAlertSmsRecordsByTimeRange(startTime);
    }

    /**
     * 删除预警消息记录信息
     * 
     * @param alertSmsId 预警消息记录主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertSmsRecordByAlertSmsId(Long alertSmsId)
    {
        return sjyAlertSmsRecordMapper.deleteSjyAlertSmsRecordByAlertSmsId(alertSmsId);
    }

    /**
     * 根据预警记录ID列表和发送方式统计预警消息发送状态
     *
     * @param bo 统计请求参数
     * @return 统计结果列表
     */
    @Override
    public List<SjyAlertSmsStatsVO> getSjyAlertSmsStats(SjyAlertSmsStatsRequest bo)
    {
        // Updated validation logic based on new SjyAlertSmsStatsRequest properties
        // Basic check for null request. More specific checks can be added based on requirements.
        // For example, if sendType is mandatory, add: statsRequest.getSendType() == null || statsRequest.getSendType().isEmpty()
        if (bo == null) {
            return new ArrayList<>();
        }
        if (bo.getPageSize()!=null&&bo.getPageNum()!=null) {
            PageHelper.startPage(bo.getPageNum(), bo.getPageSize());
        }
        // Potentially add validation for sendStartTime and sendEndTime if they are mandatory
        // or if endTime must be after startTime.
        return sjyAlertSmsRecordMapper.selectSjyAlertSmsStats(bo);
    }

    @Override
    public SjyAlertStatRespVo alertStats(SjyAlertSmsStatsRequest statsRequest) {
        SjyAlertStatRespVo  rs = new SjyAlertStatRespVo();
        // 统计几种关键预警
        rs.setRainAlertNum(sjyAlertSmsRecordMapper.alertCount(statsRequest.getSendStartTime(), statsRequest.getSendEndTime(),"2"));
        rs.setRsAlertNum(sjyAlertSmsRecordMapper.alertCount(statsRequest.getSendStartTime(), statsRequest.getSendEndTime(),"25"));
        rs.setRvAlertNum(sjyAlertSmsRecordMapper.alertCount(statsRequest.getSendStartTime(), statsRequest.getSendEndTime(),"15"));
        rs.setFdAlertNum(sjyAlertSmsRecordMapper.alertCount(statsRequest.getSendStartTime(), statsRequest.getSendEndTime(),"1"));
        return rs;
    }
}
