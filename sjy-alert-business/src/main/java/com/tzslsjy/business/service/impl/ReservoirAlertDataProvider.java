package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.domain.data.StRsvrR; // 修正导入路径
import com.tzslsjy.business.mapper.SjyPjStMapper;
import com.tzslsjy.business.mapper.data.StRsvrRMapper;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.mapper.SjyAlertStSituationHisMapper;
import com.tzslsjy.business.service.AlertDataProvider;
import com.tzslsjy.business.service.ContinuousAlertService;
import com.tzslsjy.business.service.ISjyAlertStSituationService;
import com.tzslsjy.business.vo.ReservoirReqVo;
import com.tzslsjy.business.vo.ReservoirVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 水库水位预警数据提供者
 */
@Component
@Slf4j
public class ReservoirAlertDataProvider implements AlertDataProvider {

    @Autowired
    private StRsvrRMapper stRsvrRMapper;
    @Autowired
    private StStbprpBMapper stStbprpBMapper;
    @Autowired
    private SjyPjStMapper sjyPjStMapper;
    @Autowired
    private SjyAlertStSituationHisMapper sjyAlertStSituationHisMapper;
    @Autowired
    private ContinuousAlertService continuousAlertService;

    @Autowired
    private ISjyAlertStSituationService alertStSituationService;
    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("25");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date currentTime) {
        log.debug("开始获取水库水位预警数据，规则类型: {}, 当前时间: {}", ruleTypeCode, currentTime);
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> triggeredReservoirs = new ArrayList<>();

        Date thisYearStart = DateUtil.beginOfYear(currentTime);
        @SuppressWarnings("unchecked")
        List<SjyReservoirAlertParamData> reservoirParamsList = 
                (List<SjyReservoirAlertParamData>) queryParams.get("reservoirAlertParams");
        List<SjyPjSt> sjyPjSts = sjyPjStMapper.getList( );
        Map<String, String> pjcdMap = sjyPjSts.stream().collect(Collectors.toMap(
                SjyPjSt::getStcd,
                SjyPjSt::getPjcd
        ));
        if (  !reservoirParamsList.isEmpty()) {

           for( SjyReservoirAlertParamData currentParam:reservoirParamsList) {
               ReservoirReqVo reservoirReqVo = new ReservoirReqVo();
               reservoirReqVo.setBgmd(DateUtil.format(currentTime, "MMdd"));
               List<ReservoirVo> reservoirVoList = stStbprpBMapper.getRsInfo(reservoirReqVo);
               Integer val = currentParam.getVal();
                if (val == null || val <= 0) {
                     log.warn("阈值未设置或无效，跳过当前参数: {}", currentParam);
                     continue;
                }
               BigDecimal ov =  new BigDecimal(val) ; // 确保阈值是BigDecimal类型
               for (ReservoirVo stationInfo : reservoirVoList) {
                   stationInfo.setPjcd(pjcdMap.get(stationInfo.getStcd()));
                   BigDecimal wrz = stationInfo.getFsltdz();
                   if (wrz == null) {
                       log.warn("水库 {} (测站 {}) 的汛限水位未设置，跳过", stationInfo.getStnm(), stationInfo.getStcd());
                       continue;
                   }
                   String stcd = stationInfo.getStcd();
                   try {

                       String stnm = (stationInfo != null && stationInfo.getStnm() != null) ? stationInfo.getStnm() : stcd;
                       String addvcdNm = (stationInfo != null && stationInfo.getJurisdiction9() != null) ? stationInfo.getJurisdiction9() : "未知区域";

                       StRsvrR latestRsvrR = stRsvrRMapper.selectLatestByStcd(stcd, thisYearStart); // 使用新方法

                       if (latestRsvrR == null || latestRsvrR.getRz() == null) { // 实体方法为getRz
                           log.warn("未查询到水库 {} (测站 {}) 的实时水位信息", stnm, stcd);
                           continue;
                       }

                       BigDecimal currentRz = latestRsvrR.getRz();
                       if (currentRz.compareTo(wrz) > 0) {
                           BigDecimal overWrz = currentRz.subtract(wrz).setScale(2, RoundingMode.HALF_UP);
                           log.info("水库 {} ({}) 超汛限: {} > {}，超 {} 米", stnm, stcd, currentRz, wrz, overWrz);

                           if(overWrz.compareTo(ov)>0) {
                               log.info("水库 {} ({}) 超过阈值 {} 米，检查是否需要发送预警", stnm, stcd, ov);

                               // 检查是否需要发送预警（防止重复发送）
                               Long ruleId = (Long) queryParams.get("ruleId");
                               boolean shouldAlert = shouldSendReservoirAlert(stcd, ruleId, currentRz, wrz, currentTime);

                               if (shouldAlert) {
                                   log.info("水库 {} ({}) 需要发送预警，添加到触发列表", stnm, stcd);
                                   addTriggeredReservoir(triggeredReservoirs, stcd, stnm, addvcdNm, latestRsvrR.getTm(), currentRz, wrz, overWrz, stationInfo.getAddvcd9(), pjcdMap);

                                   // 更新当前预警状态表
                                   updateReservoirAlertStatus(stcd, ruleId, currentRz, wrz, latestRsvrR.getTm(), stnm, overWrz);
                               } else {
                                   log.info("水库 {} ({}) 已在预警状态且非提醒时间，跳过发送", stnm, stcd);
                               }
                           } else {
                               log.info("水库 {} ({}) 超过汛限但未超过阈值 {} 米，跳过", stnm, stcd, ov);
                           }
                       }
                   } catch (Exception e) {
                       log.error("处理水库 {} 水位失败: {}", stcd, e.getMessage(), e);
                   }
               }
           }
        }
        
        // 按行政区划分组
        Map<String, List<Map<String, Object>>> groupsByJurisdiction = groupByJurisdiction(triggeredReservoirs);
        
        // 按工程编码分组
        Map<String, List<Map<String, Object>>> groupsByPjcd = groupByPjcd(triggeredReservoirs);
        
        result.put("triggeredReservoirs", triggeredReservoirs); // 保留原始数据格式以兼容
        result.put("triggeredReservoirGroups", groupsByJurisdiction); // 按行政区划分组数据
        result.put("triggeredReservoirGroupsByPjcd", groupsByPjcd); // 按工程编码分组数据
        result.put("argId", queryParams.get("argId"));
        
        // 恢复正常状态：将不再超警的测站状态更新为正常
        Long ruleId = (Long) queryParams.get("ruleId");
        if (ruleId != null) {
            try {
                List<String> currentTriggeredStcds = triggeredReservoirs.stream()
                        .map(reservoir -> (String) reservoir.get("stcd"))
                        .collect(Collectors.toList());
                
                int restoredCount = continuousAlertService.restoreNormalStations(ruleId, currentTriggeredStcds, "水库");
                if (restoredCount > 0) {
                    log.info("水库预警规则 {} 恢复 {} 个测站为正常状态", ruleId, restoredCount);
                }
            } catch (Exception e) {
                log.error("恢复水库测站正常状态失败: {}", e.getMessage(), e);
            }
        }
        
        addStandardFields(ruleTypeCode, result);
        log.info("水库水位预警数据获取完成，共 {} 个测站触发预警", triggeredReservoirs.size());
        return result;
    }

    /**
     * 检查是否应该发送水库水位预警
     * 防止重复发送，只在以下情况发送：
     * 1. 首次超汛限（状态变化）
     * 2. 每日8AM提醒（对于仍在预警状态的测站）
     */
    private boolean shouldSendReservoirAlert(String stcd, Long ruleId, BigDecimal currentLevel, BigDecimal warningLevel, Date currentTime) {
        if (ruleId == null) {
            return true; // 如果没有规则ID，默认发送
        }

        try {
            // 检查是否为8AM提醒时间
            boolean is8AMReminder = is8AMReminderTime(currentTime);

            // 确定当前预警状态
            Integer newStatus = determineReservoirAlertStatus(currentLevel, warningLevel);

            // 获取现有预警情况
            SjyAlertStSituation existing = alertStSituationService.selectByStcdAndRelation(
                    stcd, ruleId.intValue(), 2); // 2=规则实例

            if (existing == null) {
                // 新预警，需要发送
                log.debug("水库测站 {} 首次超汛限，需要发送预警", stcd);
                return newStatus != null && newStatus > 0;
            }

            if (is8AMReminder && newStatus != null && newStatus > 0) {
                // 8AM提醒时间，对于仍在预警状态的测站发送提醒
                log.debug("水库测站 {} 8AM提醒时间，发送持续预警提醒", stcd);
                return true;
            }

            // 检查状态是否发生变化
            boolean statusChanged = !existing.getStatus().equals(newStatus);
            if (statusChanged && newStatus != null && newStatus > 0) {
                // 状态变化且为预警状态，需要发送
                log.debug("水库测站 {} 预警状态发生变化: {} -> {}，需要发送预警", stcd, existing.getStatus(), newStatus);
                return true;
            }

            // 其他情况不发送
            log.debug("水库测站 {} 预警状态未变化且非提醒时间，跳过发送", stcd);
            return false;

        } catch (Exception e) {
            log.error("检查水库测站 {} 预警发送条件失败: {}", stcd, e.getMessage(), e);
            return true; // 出错时默认发送，避免漏报
        }
    }

    /**
     * 检查是否为8AM提醒时间
     */
    private boolean is8AMReminderTime(Date currentTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(currentTime);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        return hour == 8;
    }

    /**
     * 确定水库预警状态
     */
    private Integer determineReservoirAlertStatus(BigDecimal currentLevel, BigDecimal warningLevel) {
        if (currentLevel == null || warningLevel == null) {
            return 0; // 正常
        }

        if (currentLevel.compareTo(warningLevel) > 0) {
            return 1; // 超汛限
        }

        return 0; // 正常
    }

    /**
     * 更新水库预警状态到数据库
     * 同时更新当前状态表和插入历史记录
     */
    private void updateReservoirAlertStatus(String stcd, Long ruleId, BigDecimal currentLevel, BigDecimal warningLevel,
                                           Date alertTime, String stnm, BigDecimal overWrz) {
        if (ruleId == null) {
            return;
        }

        try {
            // 确定预警状态
            Integer newStatus = determineReservoirAlertStatus(currentLevel, warningLevel);

            // 更新当前状态表 (sjyAlertStSituation)
            boolean statusChanged = alertStSituationService.updateStationAlertStatus(
                    stcd, ruleId.intValue(), 2, newStatus, "system"); // 2=规则实例

            // 插入历史记录 (sjyAlertStSituationHis) - 只在状态变化时插入
            if (statusChanged && newStatus > 0) {
                insertReservoirAlertHistory(stcd, newStatus, alertTime, stnm, overWrz);
                log.info("水库测站 {} 预警状态已更新: 状态={}, 超汛限={}米", stcd, newStatus, overWrz);
            }

        } catch (Exception e) {
            log.error("更新水库测站 {} 预警状态失败: {}", stcd, e.getMessage(), e);
        }
    }

    /**
     * 插入水库预警历史记录
     */
    private void insertReservoirAlertHistory(String stcd, Integer status, Date alertTime, String stnm, BigDecimal overWrz) {
        try {
            SjyAlertStSituationHis historyRecord = new SjyAlertStSituationHis();
            historyRecord.setStcd(stcd);
            historyRecord.setCType(2); // 关联类型：2-规则实例
            historyRecord.setStatus(status); // 状态：1-超汛限，2-超保证
            historyRecord.setCId(18); // 水库水位预警类型ID
            historyRecord.setCreateTime(new Date());
            historyRecord.setStartTime(alertTime);
            historyRecord.setEndTime(alertTime);
            historyRecord.setRemark("水库水位预警触发: " + stnm + " 超汛限 " + overWrz + "米");

            sjyAlertStSituationHisMapper.insertSjyAlertStSituationHis(historyRecord);
            log.debug("水库预警历史记录已保存: 测站={}, 状态={}", stcd, status);
        } catch (Exception e) {
            log.error("保存水库预警历史记录失败: 测站={}, 错误={}", stcd, e.getMessage(), e);
        }
    }

    /**
     * 按行政区划分组
     * @param triggeredReservoirs 触发的水库列表
     * @return 按行政区划分组的结果
     */
    private Map<String, List<Map<String, Object>>> groupByJurisdiction(List<Map<String, Object>> triggeredReservoirs) {
        Map<String, List<Map<String, Object>>> groups = new HashMap<>();
        
        for (Map<String, Object> reservoir : triggeredReservoirs) {
            String jurisdiction = (String) reservoir.get("addvcd");
            if (jurisdiction == null || jurisdiction.length() < 6) {
                continue; // 跳过无效的行政区划
            }
            
            // 9位行政区划分组
            String jurisdiction9 = jurisdiction.length() >= 9 ? jurisdiction.substring(0, 9) : jurisdiction;
            String groupKey9 = "9_" + jurisdiction9; // 前缀区分9位和6位
            groups.computeIfAbsent(groupKey9, k -> new ArrayList<>()).add(reservoir);
            
            // 6位行政区划分组
            String jurisdiction6 = jurisdiction.substring(0, 6);
            String groupKey6 = "6_" + jurisdiction6; // 前缀区分9位和6位
            groups.computeIfAbsent(groupKey6, k -> new ArrayList<>()).add(reservoir);
        }
        
        log.info("水库水位预警按行政区划分组完成，共分为 {} 组", groups.size());
        return groups;
    }

    /**
     * 按工程编码分组
     * @param triggeredReservoirs 触发的水库列表
     * @return 按工程编码分组的结果
     */
    private Map<String, List<Map<String, Object>>> groupByPjcd(List<Map<String, Object>> triggeredReservoirs) {
        Map<String, List<Map<String, Object>>> groups = new HashMap<>();
        
        for (Map<String, Object> reservoir : triggeredReservoirs) {
            String pjcd = (String) reservoir.get("pjcd");
            if (pjcd == null || pjcd.trim().isEmpty()) {
                log.warn("测站 {} 的工程编码为空，跳过按工程编码分组", reservoir.get("stcd"));
                continue; // 跳过无效的工程编码
            }
            
            String groupKey = "pjcd_" + pjcd; // 使用pjcd_前缀区分
            groups.computeIfAbsent(groupKey, k -> new ArrayList<>()).add(reservoir);
        }
        
        log.info("水库水位预警按工程编码分组完成，共分为 {} 组", groups.size());
        return groups;
    }

    private void addTriggeredReservoir(List<Map<String, Object>> list, String stcd, String stnm, String addvcdNm,
                                      Date tm, BigDecimal rz, BigDecimal wrz, BigDecimal overWrz, String addvcd,
                                      Map<String, String> pjcdMap) {
        // 注意：历史记录和状态更新现在在 updateReservoirAlertStatus 方法中处理

        // 获取该测站的工程编码
        String pjcd = pjcdMap.get(stcd);

        Map<String, Object> reservoirData = new HashMap<>();
        reservoirData.put("stcd", stcd);
        reservoirData.put("stnm", stnm);
        reservoirData.put("addvcdNm", addvcdNm);
        reservoirData.put("tm", DateUtil.format(tm, "yyyy/MM/dd HH:mm:ss"));
        reservoirData.put("rz", rz);
        reservoirData.put("wrz", wrz);
        reservoirData.put("overWrz", overWrz);
        reservoirData.put("addvcd", addvcd); // 添加行政区划信息
        reservoirData.put("pjcd", pjcd); // 添加工程编码信息
        list.add(reservoirData);
    }
    
    private String getAdnmByAdcd(String adcd) {
        if (adcd == null) return "";
        if (adcd.startsWith("331082001")) return "白水洋镇"; 
        if (adcd.startsWith("331082")) return "临海市";
        return adcd; 
    }

    private void addStandardFields(String ruleTypeCode, Map<String, Object> result) {
        result.put("dataType", "25"); 
        result.put("ruleType", ruleTypeCode);
        result.put("queryTime", new Date());
    }
} 