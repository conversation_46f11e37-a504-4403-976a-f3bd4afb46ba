package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

/**
 * 雨量预警模板处理器
 * 负责生成和合并雨量预警消息
 */
@Component
public class RainfallAlertTemplateProcessor implements AlertTemplateProcessor {
    private static final Logger logger = LoggerFactory.getLogger(RainfallAlertTemplateProcessor.class);

    @Autowired
    private DefaultAlertTemplateProcessor defaultTemplateProcessor;

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate smsTemplate, Map<String, Object> data) {
        try {
            logger.debug("处理雨量预警模板");

            // 添加雨量预警特定数据
            //enrichRainfallSpecificData(smsTemplate, data);

            // 委托给默认模板处理器
            return defaultTemplateProcessor.processTemplate(context, smsTemplate, data);
        } catch (Exception e) {
            logger.error("处理雨量预警模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 添加雨量预警特定数据
     */
    private void enrichRainfallSpecificData(SjyAlertSmsTemplate smsTemplate, Map<String, Object> data) {
        // 添加预警类型标记
        data.put("alertTypeText", "雨量");
        
        // 添加行政区划信息
        if (data.containsKey("adnm")) {
            data.put("行政区划名称", data.get("adnm"));
        }
        
        // 添加测站信息
        if (data.containsKey("stnm")) {
            data.put("测站名称", data.get("stnm"));
        }
        
        // 处理雨量数据
        processRainfallData(data);
        
        // 生成消息内容
        generateMessageContent(data);
    }

    /**
     * 处理雨量数据
     */
    @SuppressWarnings("unchecked")
    private void processRainfallData(Map<String, Object> data) {
        if (data.containsKey("rainfallList")) {
            List<Map<String, Object>> rainfallList = (List<Map<String, Object>>) data.get("rainfallList");
            data.put("list", rainfallList); // 将雨量数据设为列表数据

            // 提取第一条雨量记录的数据作为主要数据
            if (!rainfallList.isEmpty()) {
                Map<String, Object> firstRainfall = rainfallList.get(0);
                
                // 检查并添加必要数据
                if (firstRainfall.containsKey("adnm")) {
                    data.put("行政区划名称", firstRainfall.get("adnm"));
                }
                
                // 处理测站列表
                if (firstRainfall.containsKey("stationNames")) {
                    List<String> stationNames = (List<String>) firstRainfall.get("stationNames");
                    if (stationNames != null && !stationNames.isEmpty()) {
                        data.put("测站名称列表", String.join("、", stationNames));
                        
                        // 单个测站或最大值测站
                        if (stationNames.size() == 1 || firstRainfall.containsKey("maxStation")) {
                            String stationName = stationNames.size() == 1 ? 
                                    stationNames.get(0) : (String) firstRainfall.get("maxStation");
                            data.put("测站名称", stationName);
                        }
                    }
                }
                
                // 处理降雨量数据
                if (firstRainfall.containsKey("rainfall")) {
                    Object rainfallObj = firstRainfall.get("rainfall");
                    BigDecimal rainfall;
                    if (rainfallObj instanceof BigDecimal) {
                        rainfall = (BigDecimal) rainfallObj;
                    } else if (rainfallObj instanceof Number) {
                        rainfall = BigDecimal.valueOf(((Number) rainfallObj).doubleValue());
                    } else {
                        rainfall = new BigDecimal(rainfallObj.toString());
                    }
                    
                    data.put("降雨量", rainfall.setScale(1, BigDecimal.ROUND_HALF_UP).toString());
                }
                
                // 其他常用数据
                if (firstRainfall.containsKey("startTm")) {
                    data.put("开始时间", firstRainfall.get("startTm"));
                }
                
                if (firstRainfall.containsKey("endTm")) {
                    data.put("结束时间", firstRainfall.get("endTm"));
                }
                
                if (firstRainfall.containsKey("duration")) {
                    data.put("时长", firstRainfall.get("duration") + "小时");
                }
                
                if (firstRainfall.containsKey("stationCount")) {
                    int count = ((Number) firstRainfall.get("stationCount")).intValue();
                    data.put("测站数量", count);
                    if (count > 1) {
                        data.put("多站点提示", "等" + count + "个测站");
                    } else {
                        data.put("多站点提示", "");
                    }
                }
            }
        }
    }
    
    /**
     * 生成消息内容
     */
    private void generateMessageContent(Map<String, Object> data) {
        StringBuilder content = new StringBuilder();
        
        // 获取必要信息
        String adnm = (String) data.getOrDefault("行政区划名称", "");
        String stationInfo;
        
        // 根据测站数量构建不同的描述
        if (data.containsKey("测站数量") && ((Number) data.get("测站数量")).intValue() > 1) {
            stationInfo = (String) data.getOrDefault("测站名称列表", "");
        } else {
            stationInfo = (String) data.getOrDefault("测站名称", "");
        }
        
        String rainfall = (String) data.getOrDefault("降雨量", "");
        
        // 构建内容
        if (!adnm.isEmpty() && !stationInfo.isEmpty() && !rainfall.isEmpty()) {
            content.append(adnm).append("的").append(stationInfo)
                   .append("，降雨量为").append(rainfall).append("mm");
            
            data.put("消息内容", content.toString());
        }
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> people) {
        // 委托给默认模板处理器
        return defaultTemplateProcessor.mergeAlerts(context, alert, people);
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        // 委托给默认模板处理器
        return defaultTemplateProcessor.mergeReceivers(context, receivers);
    }
}
