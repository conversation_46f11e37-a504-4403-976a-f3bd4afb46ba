package com.tzslsjy.business.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.domain.SjyStationAbnormalParamData;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;

/**
 * 水位异常预警参数解析器
 * 专注于水位异常规则参数解析
 * 支持规则类型：31
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Component
@Slf4j
public class WaterLevelAbnormalParameterResolver implements AlertRuleParameterResolver {

    private final ObjectMapper objectMapper = new ObjectMapper();
    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("31");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析水位异常预警参数，规则ID: {}, 参数数量: {}", rule.getRuleId(), ruleParams.size());
        
        List<Map<String, Object>> queryParamsList = new ArrayList<>();

        try {
            for (SjyAlertRuleArg ruleParam : ruleParams) {
                Map<String, Object> queryParams = resolveParameterForArg(rule, ruleParam, tm);
                if (queryParams != null) {
                    queryParamsList.add(queryParams);
                }
            }

            log.info("水位异常预警参数解析完成，规则ID: {}, 生成查询参数组数量: {}", rule.getRuleId(), queryParamsList.size());
            return queryParamsList;

        } catch (Exception e) {
            log.error("解析水位异常预警参数失败，规则ID: {}, 错误: {}", rule.getRuleId(), e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 解析单个参数组
     */
    private Map<String, Object> resolveParameterForArg(SjyAlertRule rule, SjyAlertRuleArg ruleParam, Date tm) {
        try {
            log.debug("解析参数组，argId: {}, argJson: {}", ruleParam.getArgId(), ruleParam.getArgJson());

            // 解析参数JSON
            SjyStationAbnormalParamData params = parseArgJson(ruleParam.getArgJson());
            if (params == null) {
                log.warn("参数组 {} 解析失败", ruleParam.getArgId());
                return null;
            }

            // 验证水位异常参数
            if (!validateWaterLevelParams(params)) {
                log.warn("参数组 {} 水位异常参数验证失败", ruleParam.getArgId());
                return null;
            }

            // 构建查询参数
            Map<String, Object> queryParams = new HashMap<>();
            queryParams.put("argId", ruleParam.getArgId());
            queryParams.put("ruleId", rule.getRuleId());
            queryParams.put("ruleTypeCode", rule.getRuleTypeId());
            queryParams.put("stationAbnormalParams", params);
            queryParams.put("queryTime", tm);

            log.debug("参数组 {} 解析成功，水位测站数量: {}", 
                    ruleParam.getArgId(), params.getWaterStcdsForDetection().size());

            return queryParams;

        } catch (Exception e) {
            log.error("解析参数组失败，argId: {}, 错误: {}", ruleParam.getArgId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析参数JSON
     * 支持新的标准化格式和旧格式的兼容性
     */
    private SjyStationAbnormalParamData parseArgJson(String argJson) {
        try {
            if (!StringUtils.hasText(argJson)) {
                log.warn("参数JSON为空");
                return null;
            }

            // 先解析为Map
            Map<String, Object> argMap = objectMapper.readValue(argJson, new TypeReference<Map<String, Object>>() {});

            SjyStationAbnormalParamData params = new SjyStationAbnormalParamData();

            // ========== 解析新的标准化格式 ==========
            if (argMap.containsKey("waterLevel")) {
                Object waterLevelObj = argMap.get("waterLevel");
                if (waterLevelObj instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> waterLevelMap = (Map<String, Object>) waterLevelObj;

                    SjyStationAbnormalParamData.StationAbnormalDetectionParam waterLevelParam =
                        new SjyStationAbnormalParamData.StationAbnormalDetectionParam();

                    // 解析时间周期（小时）
                    if (waterLevelMap.containsKey("during")) {
                        String duringStr = String.valueOf(waterLevelMap.get("during"));
                        if (StringUtils.hasText(duringStr)) {
                            waterLevelParam.setDuring(Integer.parseInt(duringStr));
                        }
                    }

                    // 解析阈值（米）
                    if (waterLevelMap.containsKey("threshold")) {
                        String thresholdStr = String.valueOf(waterLevelMap.get("threshold"));
                        if (StringUtils.hasText(thresholdStr)) {
                            waterLevelParam.setThreshold(new BigDecimal(thresholdStr));
                        }
                    }

                    params.setWaterLevel(waterLevelParam);
                    log.debug("解析新格式水位参数: during={}小时, threshold={}米",
                            waterLevelParam.getDuring(), waterLevelParam.getThreshold());
                }
            }

            // ========== 兼容旧格式解析 ==========
            // 解析水位测站列表（兼容旧版本，新版本中每个规则只针对单个测站）
            if (argMap.containsKey("waterStcds")) {
                Object waterStcdsObj = argMap.get("waterStcds");
                if (waterStcdsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> waterStcds = (List<String>) waterStcdsObj;
                    params.setWaterStcds(waterStcds);
                }
            }

            // 解析水位变化阈值（兼容旧版本）
            if (argMap.containsKey("overWaterVal")) {
                String overWaterVal = String.valueOf(argMap.get("overWaterVal"));
                if (StringUtils.hasText(overWaterVal)) {
                    params.setWaterLevelThreshold(new BigDecimal(overWaterVal));
                }
            }

            // 解析水位异常检测开关（兼容旧版本）
            if (argMap.containsKey("overWaterEnable")) {
                String overWaterEnable = String.valueOf(argMap.get("overWaterEnable"));
                params.setWaterLevelDetectionEnabled("1".equals(overWaterEnable));
            }

            // 解析全局开关（默认开启）
            params.setGlobalSwitchEnabled(true);

            // 解析检测时间间隔（兼容旧版本，默认5分钟）
            if (argMap.containsKey("detectionInterval")) {
                String detectionInterval = String.valueOf(argMap.get("detectionInterval"));
                if (StringUtils.hasText(detectionInterval)) {
                    params.setDetectionIntervalMinutes(Integer.parseInt(detectionInterval));
                }
            }

            // 兼容旧版本：如果waterStcds为空，尝试使用stcds
            if ((params.getWaterStcds() == null || params.getWaterStcds().isEmpty()) && argMap.containsKey("stcds")) {
                Object stcdsObj = argMap.get("stcds");
                if (stcdsObj instanceof List) {
                    @SuppressWarnings("unchecked")
                    List<String> stcds = (List<String>) stcdsObj;
                    params.setStcds(stcds);
                }
            }

            // 日志输出：优先显示新格式参数，如果没有则显示旧格式参数
            if (params.getWaterLevel() != null) {
                log.debug("参数解析成功（新格式）: 时间周期={}小时, 水位阈值={}米",
                        params.getWaterLevelDetectionDuringHours(), params.getWaterLevelDetectionThreshold());
            } else {
                log.debug("参数解析成功（旧格式）: 水位阈值={}米, 水位检测开关={}, 水位测站数量={}",
                        params.getWaterLevelThreshold(), params.getWaterLevelDetectionEnabled(),
                        params.getWaterStcdsForDetection().size());
            }

            return params;

        } catch (Exception e) {
            log.error("解析参数JSON失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 验证水位异常参数
     * 支持新的标准化格式和旧格式的兼容性验证
     */
    private boolean validateWaterLevelParams(SjyStationAbnormalParamData params) {
        // 新版本中通过规则的启用状态控制，不再需要检查检测开关
        // 但为了兼容性，仍然检查旧格式的开关
        if (!params.isWaterLevelDetectionEnabled()) {
            log.debug("水位异常检测已禁用（兼容性检查）");
            return false;
        }

        // 检查水位阈值（使用新的获取方法，自动兼容新旧格式）
        BigDecimal threshold = params.getWaterLevelDetectionThreshold();
        if (threshold == null || threshold.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("水位变化阈值无效: {}", threshold);
            return false;
        }

        // 检查时间周期（使用新的获取方法，自动兼容新旧格式）
        Integer duringHours = params.getWaterLevelDetectionDuringHours();
        if (duringHours == null || duringHours <= 0) {
            log.warn("检测时间周期无效: {} 小时", duringHours);
            return false;
        }

        // 水位测站列表可以为空，由数据提供者决定是否查询所有水位测站
        // 新版本中每个规则只针对单个测站（通过alertStcd字段）
        log.debug("水位异常参数验证通过: 阈值={}米, 时间周期={}小时", threshold, duringHours);
        return true;
    }
}
