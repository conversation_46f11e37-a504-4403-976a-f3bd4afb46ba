package com.tzslsjy.business.service.impl;

import java.util.List;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyAlertPersonNodeRelationMapper;
import com.tzslsjy.business.domain.SjyAlertPersonNodeRelation;
import com.tzslsjy.business.service.ISjyAlertPersonNodeRelationService;

/**
 * 人员节点关联Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SjyAlertPersonNodeRelationServiceImpl implements ISjyAlertPersonNodeRelationService
{
    @Autowired
    private SjyAlertPersonNodeRelationMapper sjyAlertPersonNodeRelationMapper;

    /**
     * 查询人员节点关联
     *
     * @param relationId 人员节点关联主键
     * @return 人员节点关联
     */
    @Override
    public SjyAlertPersonNodeRelation selectSjyAlertPersonNodeRelationByRelationId(Long relationId)
    {
        return sjyAlertPersonNodeRelationMapper.selectSjyAlertPersonNodeRelationByRelationId(relationId);
    }

    /**
     * 查询人员节点关联列表
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 人员节点关联
     */
    @Override
    public List<SjyAlertPersonNodeRelation> selectSjyAlertPersonNodeRelationList(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation)
    {
        return sjyAlertPersonNodeRelationMapper.selectSjyAlertPersonNodeRelationList(sjyAlertPersonNodeRelation);
    }

    /**
     * 新增人员节点关联
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 结果
     */
    @Override
    public int insertSjyAlertPersonNodeRelation(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation)
    {
        sjyAlertPersonNodeRelation.setCreateTime(DateUtils.getNowDate());
        return sjyAlertPersonNodeRelationMapper.insertSjyAlertPersonNodeRelation(sjyAlertPersonNodeRelation);
    }

    /**
     * 修改人员节点关联
     *
     * @param sjyAlertPersonNodeRelation 人员节点关联
     * @return 结果
     */
    @Override
    public int updateSjyAlertPersonNodeRelation(SjyAlertPersonNodeRelation sjyAlertPersonNodeRelation)
    {
        sjyAlertPersonNodeRelation.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertPersonNodeRelationMapper.updateSjyAlertPersonNodeRelation(sjyAlertPersonNodeRelation);
    }

    /**
     * 批量删除人员节点关联
     *
     * @param relationIds 需要删除的人员节点关联主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertPersonNodeRelationByRelationIds(Long[] relationIds)
    {
        return sjyAlertPersonNodeRelationMapper.deleteSjyAlertPersonNodeRelationByRelationIds(relationIds);
    }

    /**
     * 删除人员节点关联信息
     *
     * @param relationId 人员节点关联主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertPersonNodeRelationByRelationId(Long relationId)
    {
        return sjyAlertPersonNodeRelationMapper.deleteSjyAlertPersonNodeRelationByRelationId(relationId);
    }
}
