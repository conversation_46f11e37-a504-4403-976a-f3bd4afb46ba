package com.tzslsjy.business.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 雨量预警参数解析器
 * 专门负责解析雨量预警相关的规则参数
 */
@Component
@Slf4j
public class RainfallAlertParameterResolver implements AlertRuleParameterResolver {

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("2");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析雨量预警规则参数");

        List<Map<String, Object>> paramGroups = new ArrayList<>();
        
        // 按参数组ID分组
        Map<Long, List<SjyAlertRuleArg>> groupedParams = ruleParams.stream()
                .collect(Collectors.groupingBy(SjyAlertRuleArg::getArgId));

        for (Map.Entry<Long, List<SjyAlertRuleArg>> entry : groupedParams.entrySet()) {
            Long argId = entry.getKey();
            List<SjyAlertRuleArg> argParams = entry.getValue();
            
            Map<String, Object> queryParams = buildQueryParamsForGroup(rule, argParams, argId, tm);
            if (!queryParams.isEmpty()) {
                paramGroups.add(queryParams);
            }
        }

        return paramGroups;
    }

    /**
     * 为特定参数组构建查询参数
     */
    private Map<String, Object> buildQueryParamsForGroup(SjyAlertRule rule, List<SjyAlertRuleArg> argParams, Long argId, Date tm) {
        Map<String, Object> queryParams = new HashMap<>();
        
        // 添加基本参数
        queryParams.put("ruleId", rule.getRuleId());
        queryParams.put("ruleName", rule.getRuleName());
        queryParams.put("ruleTypeId", rule.getRuleTypeId());
        queryParams.put("argId", argId);
        queryParams.put("tm", tm);
        
        // 添加区域信息
        if (rule.getAlertAdnm() != null && !rule.getAlertAdnm().isEmpty()) {
            queryParams.put("adnm", rule.getAlertAdnm());
        }
        
        // 添加测站信息
        addStationInfo(rule, queryParams);
        
        // 解析参数数据
        List<SjyRainfallParamData> paramDataList = parseRainfallParameters(argParams, argId);
        queryParams.put("rainfallParams", paramDataList);
        
        return queryParams;
    }

    /**
     * 添加测站信息
     */
    private void addStationInfo(SjyAlertRule rule, Map<String, Object> queryParams) {
        if (rule.getAlertStcd() != null && !rule.getAlertStcd().isEmpty()) {
            // 指定了测站，使用指定的测站
            queryParams.put("specifiedStations", Arrays.asList(rule.getAlertStcd().split(",")));
        } else {
            // 查询所有雨量测站
            queryParams.put("queryAllStations", true);
        }
    }

    /**
     * 解析雨量预警参数
     */
    private List<SjyRainfallParamData> parseRainfallParameters(List<SjyAlertRuleArg> argParams, Long argId) {
        List<SjyRainfallParamData> paramDataList = new ArrayList<>();

        // 只处理当前参数集的参数
        List<SjyAlertRuleArg> filteredParams = argParams.stream()
                .filter(p -> p.getArgId().equals(argId))
                .collect(Collectors.toList());

        for (SjyAlertRuleArg param : filteredParams) {
            try {
                String argJson = param.getArgJson();
                log.debug("解析雨量预警参数，参数ID: {}, JSON: {}", param.getArgId(), argJson);

                // 现在每个argId对应单个预警参数对象，不再是数组
                SjyRainfallThresholdItem item = JSON.parseObject(argJson, SjyRainfallThresholdItem.class);

                SjyRainfallParamData rainfallParamData = new SjyRainfallParamData();
                if (item != null) {
                    // 将单个阈值项包装成列表，保持与现有处理逻辑的兼容性
                    rainfallParamData.setThresholdItems(Collections.singletonList(item));
                } else {
                    rainfallParamData.setThresholdItems(new ArrayList<>());
                }

                rainfallParamData.setAlertLevel(param.getAlertLevel());
                rainfallParamData.setArgId(param.getArgId());
                paramDataList.add(rainfallParamData);

            } catch (Exception e) {
                log.error("解析雨量预警参数失败，参数ID: {}, JSON: {}, 错误: {}", param.getArgId(), param.getArgJson(), e.getMessage());
            }
        }

        return paramDataList;
    }
} 