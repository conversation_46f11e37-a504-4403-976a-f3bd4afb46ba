package com.tzslsjy.business.service.impl;

import java.util.List;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.domain.SjyAlertSmsTemplate;
import com.tzslsjy.business.service.ISjyAlertSmsTemplateService;

/**
 * 短信模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SjyAlertSmsTemplateServiceImpl implements ISjyAlertSmsTemplateService
{
    @Autowired
    private SjyAlertSmsTemplateMapper sjyAlertSmsTemplateMapper;

    /**
     * 查询短信模板
     *
     * @param templateId 短信模板主键
     * @return 短信模板
     */
    @Override
    public SjyAlertSmsTemplate selectSjyAlertSmsTemplateByTemplateId(Long templateId)
    {
        return sjyAlertSmsTemplateMapper.selectSjyAlertSmsTemplateByTemplateId(templateId);
    }

    /**
     * 查询短信模板列表
     *
     * @param sjyAlertSmsTemplate 短信模板
     * @return 短信模板
     */
    @Override
    public List<SjyAlertSmsTemplate> selectSjyAlertSmsTemplateList(SjyAlertSmsTemplate sjyAlertSmsTemplate)
    {
        return sjyAlertSmsTemplateMapper.selectSjyAlertSmsTemplateList(sjyAlertSmsTemplate);
    }

    /**
     * 新增短信模板
     *
     * @param sjyAlertSmsTemplate 短信模板
     * @return 结果
     */
    @Override
    public int insertSjyAlertSmsTemplate(SjyAlertSmsTemplate sjyAlertSmsTemplate)
    {
        sjyAlertSmsTemplate.setCreateTime(DateUtils.getNowDate());
        return sjyAlertSmsTemplateMapper.insertSjyAlertSmsTemplate(sjyAlertSmsTemplate);
    }

    /**
     * 修改短信模板
     *
     * @param sjyAlertSmsTemplate 短信模板
     * @return 结果
     */
    @Override
    public int updateSjyAlertSmsTemplate(SjyAlertSmsTemplate sjyAlertSmsTemplate)
    {
        sjyAlertSmsTemplate.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertSmsTemplateMapper.updateSjyAlertSmsTemplate(sjyAlertSmsTemplate);
    }

    /**
     * 批量删除短信模板
     *
     * @param templateIds 需要删除的短信模板主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertSmsTemplateByTemplateIds(Long[] templateIds)
    {
        return sjyAlertSmsTemplateMapper.deleteSjyAlertSmsTemplateByTemplateIds(templateIds);
    }

    /**
     * 删除短信模板信息
     *
     * @param templateId 短信模板主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertSmsTemplateByTemplateId(Long templateId)
    {
        return sjyAlertSmsTemplateMapper.deleteSjyAlertSmsTemplateByTemplateId(templateId);
    }
}
