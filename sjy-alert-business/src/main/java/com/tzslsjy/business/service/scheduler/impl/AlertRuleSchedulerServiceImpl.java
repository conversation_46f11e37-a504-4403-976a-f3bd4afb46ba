package com.tzslsjy.business.service.scheduler.impl;

import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.service.ISjyAlertRuleService;
import com.tzslsjy.common.utils.StringUtils;
import com.tzslsjy.business.service.scheduler.AlertRuleSchedulerService;
import com.tzslsjy.business.task.AlertRuleExecutionJob;
import com.tzslsjy.quartz.util.CronUtils;
import lombok.extern.slf4j.Slf4j;
import org.quartz.*;
import org.quartz.impl.matchers.GroupMatcher;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 预警规则动态调度服务实现
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
@Service
@Slf4j
public class AlertRuleSchedulerServiceImpl implements AlertRuleSchedulerService {

    @Autowired
    private Scheduler scheduler;

    @Autowired
    private ISjyAlertRuleService alertRuleService;

    private static final String ALERT_RULE_GROUP = "ALERT_RULE_GROUP";
    private static final String JOB_NAME_PREFIX = "AlertRule_";
    private static final String TRIGGER_NAME_PREFIX = "AlertRuleTrigger_";

    @Override
    public void initializeAllAlertRules() {
        log.info("开始初始化所有预警规则的定时任务");
        
        try {
            clearExistingAlertRuleJobs();
            List<SjyAlertRule> enabledRules = getEnabledAlertRules();
            
            int successCount = 0;
            int failCount = 0;
            
            for (SjyAlertRule rule : enabledRules) {
                try {
                    boolean success = createAlertRuleJob(rule);
                    if (success) {
                        successCount++;
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    failCount++;
                    log.error("创建预警规则任务异常: {} (ID: {})", rule.getRuleName(), rule.getRuleId(), e);
                }
            }
            
            log.info("预警规则定时任务初始化完成，成功: {}, 失败: {}, 总计: {}", 
                    successCount, failCount, enabledRules.size());
                    
        } catch (Exception e) {
            log.error("初始化预警规则定时任务失败", e);
        }
    }

    @Override
    public void refreshAllAlertRules() {
        log.info("开始刷新所有预警规则的定时任务");
        initializeAllAlertRules();
    }

    @Override
    public boolean createAlertRuleJob(SjyAlertRule rule) {
        if (!isRuleValid(rule)) {
            return false;
        }

        try {
            String jobName = JOB_NAME_PREFIX + rule.getRuleId();
            JobKey jobKey = JobKey.jobKey(jobName, ALERT_RULE_GROUP);
            
            if (scheduler.checkExists(jobKey)) {
                scheduler.deleteJob(jobKey);
            }

            JobDetail jobDetail = JobBuilder.newJob(AlertRuleExecutionJob.class)
                    .withIdentity(jobKey)
                    .withDescription("预警规则执行任务: " + rule.getRuleName())
                    .usingJobData("ruleId", rule.getRuleId())
                    .usingJobData("ruleName", rule.getRuleName())
                    .build();

            String[] cronExpressions = parseCronExpressions(rule.getCronExpressions());
            if (cronExpressions.length == 0) {
                return false;
            }

            Set<Trigger> triggers = new HashSet<>();
            for (int i = 0; i < cronExpressions.length; i++) {
                String cronExpression = cronExpressions[i].trim();
                
                if (StringUtils.isEmpty(cronExpression) || !CronUtils.isValid(cronExpression)) {
                    continue;
                }

                String triggerName = TRIGGER_NAME_PREFIX + rule.getRuleId() + "_" + i;
                
                CronTrigger trigger = TriggerBuilder.newTrigger()
                        .withIdentity(triggerName, ALERT_RULE_GROUP)
                        .withSchedule(CronScheduleBuilder.cronSchedule(cronExpression))
                        .startNow()
                        .build();

                triggers.add(trigger);
            }

            if (triggers.isEmpty()) {
                return false;
            }

            scheduler.scheduleJob(jobDetail, triggers, true);
            return true;

        } catch (Exception e) {
            log.error("创建预警规则任务失败: {} (ID: {})", rule.getRuleName(), rule.getRuleId(), e);
            return false;
        }
    }

    @Override
    public boolean updateAlertRuleJob(SjyAlertRule rule) {
        deleteAlertRuleJob(rule.getRuleId());
        return createAlertRuleJob(rule);
    }

    @Override
    public boolean deleteAlertRuleJob(Long ruleId) {
        try {
            String jobName = JOB_NAME_PREFIX + ruleId;
            JobKey jobKey = JobKey.jobKey(jobName, ALERT_RULE_GROUP);
            return scheduler.deleteJob(jobKey);
        } catch (Exception e) {
            log.error("删除预警规则任务异常: {}", ruleId, e);
            return false;
        }
    }

    @Override
    public boolean pauseAlertRuleJob(Long ruleId) {
        try {
            String jobName = JOB_NAME_PREFIX + ruleId;
            JobKey jobKey = JobKey.jobKey(jobName, ALERT_RULE_GROUP);
            scheduler.pauseJob(jobKey);
            return true;
        } catch (Exception e) {
            log.error("暂停预警规则任务异常: {}", ruleId, e);
            return false;
        }
    }

    @Override
    public boolean resumeAlertRuleJob(Long ruleId) {
        try {
            String jobName = JOB_NAME_PREFIX + ruleId;
            JobKey jobKey = JobKey.jobKey(jobName, ALERT_RULE_GROUP);
            scheduler.resumeJob(jobKey);
            return true;
        } catch (Exception e) {
            log.error("恢复预警规则任务异常: {}", ruleId, e);
            return false;
        }
    }

    @Override
    public boolean executeAlertRuleNow(Long ruleId) {
        try {
            String jobName = JOB_NAME_PREFIX + ruleId;
            JobKey jobKey = JobKey.jobKey(jobName, ALERT_RULE_GROUP);
            scheduler.triggerJob(jobKey);
            return true;
        } catch (Exception e) {
            log.error("立即执行预警规则任务异常: {}", ruleId, e);
            return false;
        }
    }

    @Override
    public List<AlertRuleJobStatus> getAllAlertRuleJobStatus() {
        List<AlertRuleJobStatus> statusList = new ArrayList<>();
        try {
            Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.jobGroupEquals(ALERT_RULE_GROUP));
            for (JobKey jobKey : jobKeys) {
                AlertRuleJobStatus status = getJobStatus(jobKey);
                if (status != null) {
                    statusList.add(status);
                }
            }
        } catch (Exception e) {
            log.error("获取所有预警规则任务状态失败", e);
        }
        return statusList;
    }

    @Override
    public boolean isAlertRuleJobExists(Long ruleId) {
        try {
            String jobName = JOB_NAME_PREFIX + ruleId;
            JobKey jobKey = JobKey.jobKey(jobName, ALERT_RULE_GROUP);
            return scheduler.checkExists(jobKey);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public AlertRuleJobStatus getAlertRuleJobStatus(Long ruleId) {
        try {
            String jobName = JOB_NAME_PREFIX + ruleId;
            JobKey jobKey = JobKey.jobKey(jobName, ALERT_RULE_GROUP);
            return getJobStatus(jobKey);
        } catch (Exception e) {
            return null;
        }
    }

    private AlertRuleJobStatus getJobStatus(JobKey jobKey) throws SchedulerException {
        JobDetail jobDetail = scheduler.getJobDetail(jobKey);
        if (jobDetail == null) {
            return null;
        }

        AlertRuleJobStatus status = new AlertRuleJobStatus();
        JobDataMap dataMap = jobDetail.getJobDataMap();
        status.setRuleId(dataMap.getLong("ruleId"));
        status.setRuleName(dataMap.getString("ruleName"));
        status.setExists(true);

        List<? extends Trigger> triggers = scheduler.getTriggersOfJob(jobKey);
        if (!triggers.isEmpty()) {
            Trigger trigger = triggers.get(0);
            if (trigger instanceof CronTrigger) {
                status.setCronExpression(((CronTrigger) trigger).getCronExpression());
            }
            status.setStatus(scheduler.getTriggerState(trigger.getKey()).name());
        }

        return status;
    }

    private void clearExistingAlertRuleJobs() {
        try {
            Set<JobKey> jobKeys = scheduler.getJobKeys(GroupMatcher.jobGroupEquals(ALERT_RULE_GROUP));
            for (JobKey jobKey : jobKeys) {
                scheduler.deleteJob(jobKey);
            }
        } catch (Exception e) {
            log.error("清除现有预警规则任务失败", e);
        }
    }

    private List<SjyAlertRule> getEnabledAlertRules() {
        try {
            SjyAlertRule queryRule = new SjyAlertRule();
            queryRule.setStatus(1);
            List<SjyAlertRule> allRules = alertRuleService.selectSimpleRuleList(queryRule);
            //山洪特殊处理
            Optional<SjyAlertRule> first = allRules.stream().filter(e -> e.getRuleTypeId().equals("1") && e.getParentId() == null).findFirst();
            if(first.isPresent()){
                SjyAlertRule baseRule = first.get();
                allRules.remove(baseRule);
                allRules.stream().filter(e -> "1".equals(e.getRuleTypeId())&&baseRule.getRuleId().equals(e.getParentId()))
                        .forEach(e -> e.setCronExpressions(baseRule.getCronExpressions()));
            }
            return allRules.stream().filter(this::isRuleValid).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("获取启用的预警规则失败", e);
            return new ArrayList<>();
        }
    }

    private boolean isRuleValid(SjyAlertRule rule) {
        return rule != null && rule.getRuleId() != null && 
               rule.getStatus() != null && rule.getStatus() == 1 &&
               !StringUtils.isEmpty(rule.getCronExpressions());
    }

    private String[] parseCronExpressions(String cronExpressions) {
        if (StringUtils.isEmpty(cronExpressions)) {
            return new String[0];
        }
        return cronExpressions.split(",");
    }
}
