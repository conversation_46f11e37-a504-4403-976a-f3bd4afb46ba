package com.tzslsjy.business.service.impl;

import java.util.List;
import com.tzslsjy.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.tzslsjy.business.mapper.SjyAlertVariableMapper;
import com.tzslsjy.business.domain.SjyAlertVariable;
import com.tzslsjy.business.service.ISjyAlertVariableService;

/**
 * 预警标签库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@Service
public class SjyAlertVariableServiceImpl implements ISjyAlertVariableService
{
    @Autowired
    private SjyAlertVariableMapper sjyAlertVariableMapper;

    /**
     * 查询预警标签库
     *
     * @param variableId 预警标签库主键
     * @return 预警标签库
     */
    @Override
    public SjyAlertVariable selectSjyAlertVariableByVariableId(Long variableId)
    {
        return sjyAlertVariableMapper.selectSjyAlertVariableByVariableId(variableId);
    }

    /**
     * 查询预警标签库列表
     *
     * @param sjyAlertVariable 预警标签库
     * @return 预警标签库
     */
    @Override
    public List<SjyAlertVariable> selectSjyAlertVariableList(SjyAlertVariable sjyAlertVariable)
    {
        return sjyAlertVariableMapper.selectSjyAlertVariableList(sjyAlertVariable);
    }

    /**
     * 新增预警标签库
     *
     * @param sjyAlertVariable 预警标签库
     * @return 结果
     */
    @Override
    public int insertSjyAlertVariable(SjyAlertVariable sjyAlertVariable)
    {
        sjyAlertVariable.setCreateTime(DateUtils.getNowDate());
        return sjyAlertVariableMapper.insertSjyAlertVariable(sjyAlertVariable);
    }

    /**
     * 修改预警标签库
     *
     * @param sjyAlertVariable 预警标签库
     * @return 结果
     */
    @Override
    public int updateSjyAlertVariable(SjyAlertVariable sjyAlertVariable)
    {
        sjyAlertVariable.setUpdateTime(DateUtils.getNowDate());
        return sjyAlertVariableMapper.updateSjyAlertVariable(sjyAlertVariable);
    }

    /**
     * 批量删除预警标签库
     *
     * @param variableIds 需要删除的预警标签库主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertVariableByVariableIds(Long[] variableIds)
    {
        return sjyAlertVariableMapper.deleteSjyAlertVariableByVariableIds(variableIds);
    }

    /**
     * 删除预警标签库信息
     *
     * @param variableId 预警标签库主键
     * @return 结果
     */
    @Override
    public int deleteSjyAlertVariableByVariableId(Long variableId)
    {
        return sjyAlertVariableMapper.deleteSjyAlertVariableByVariableId(variableId);
    }
}
