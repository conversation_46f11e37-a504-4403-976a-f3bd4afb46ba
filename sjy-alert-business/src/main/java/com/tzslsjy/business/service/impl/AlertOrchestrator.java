package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.AlertContext;
import com.tzslsjy.business.domain.SjyAlertRule;
import com.tzslsjy.business.domain.SjyAlertRuleArg;
import com.tzslsjy.business.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 预警服务协调器
 * 负责协调参数解析器、数据提供者和策略执行器
 */
@Service
@Slf4j
public class AlertOrchestrator {

    @Autowired
    private List<AlertRuleParameterResolver> parameterResolvers;

    @Autowired
    private List<AlertDataProvider> dataProviders;

    @Autowired
    private AlertStrategyFactory strategyFactory;

    @Autowired
    private ISjyAlertRuleArgService alertRuleArgService;

    // 缓存映射关系
    private Map<String, AlertRuleParameterResolver> resolverMap = new HashMap<>();
    private Map<String, AlertDataProvider> providerMap = new HashMap<>();

    /**
     * 初始化映射关系
     */
    public void initMappings() {
        // 初始化参数解析器映射
        if (parameterResolvers != null) {
            for (AlertRuleParameterResolver resolver : parameterResolvers) {
                for (String type : resolver.getSupportedTypes()) {
                    resolverMap.put(type, resolver);
                    log.info("注册参数解析器 [{}] -> {}", type, resolver.getResolverName());
                }
            }
        }

        // 初始化数据提供者映射
        if (dataProviders != null) {
            for (AlertDataProvider provider : dataProviders) {
                for (String type : provider.getSupportedTypes()) {
                    providerMap.put(type, provider);
                    log.info("注册数据提供者 [{}] -> {}", type, provider.getProviderName());
                }
            }
        }
    }

    /**
     * 执行预警规则评估
     *
     * @param rule 预警规则
     * @param tm   预警时间
     * @return 预警上下文
     */
    public AlertContext executeAlert(SjyAlertRule rule, Date tm) {
        log.info("开始执行预警规则: {}, 规则类型: {}", rule.getRuleName(), rule.getRuleTypeId());

        AlertContext context = new AlertContext();
        context.setRule(rule);

        try {
            // 1. 获取规则参数
            SjyAlertRuleArg queryParam = new SjyAlertRuleArg();
            queryParam.setRelationId(rule.getRuleId());
           // queryParam.setRelationType("2"); // 2表示规则关联
            List<SjyAlertRuleArg> ruleParams = alertRuleArgService.selectSjyAlertRuleArgList(queryParam);
            context.setRuleParams(ruleParams);

            // 2. 解析规则参数
            List<Map<String, Object>> paramGroups = resolveParameters(rule, ruleParams, tm);
            if (paramGroups.isEmpty()) {
                log.warn("规则 {} 没有解析出有效的参数组", rule.getRuleName());
                return context;
            }

            // 3. 获取预警数据
            List<Map<String, Object>> dataResults = fetchAlertData(rule.getRuleTypeId(), paramGroups, tm);

            // 4. 评估预警条件
            boolean triggered = evaluateAlertCondition(rule.getRuleTypeId(), dataResults, context);

            // 5. 设置结果到上下文 - 保留策略设置的数据，避免覆盖triggeredResults
            Map<String, Object> existingData = context.getData();
            if (existingData == null) {
                existingData = new HashMap<>();
            }

            // 添加基础数据，但不覆盖策略已设置的数据
            existingData.put("argResults", dataResults);
            existingData.put("overallTriggered", triggered);
            context.setData(existingData);

            log.info("预警规则 {} 执行完成，触发状态: {}", rule.getRuleName(), triggered);

        } catch (Exception e) {
            log.error("执行预警规则 {} 失败: {}", rule.getRuleName(), e.getMessage(), e);
            // 可以在这里设置错误状态到上下文
        }

        return context;
    }

    /**
     * 解析规则参数
     */
    private List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        String ruleTypeId = rule.getRuleTypeId();
        AlertRuleParameterResolver resolver = resolverMap.get(ruleTypeId);

        if (resolver == null) {
            log.warn("未找到规则类型 {} 的参数解析器", ruleTypeId);
            return Collections.emptyList();
        }

        log.debug("使用参数解析器 {} 解析规则参数", resolver.getResolverName());
        return resolver.resolveParameters(rule, ruleParams, tm);
    }

    /**
     * 获取预警数据
     */
    private List<Map<String, Object>> fetchAlertData(String ruleTypeId, List<Map<String, Object>> paramGroups, Date tm) {
        AlertDataProvider provider = providerMap.get(ruleTypeId);

        if (provider == null) {
            log.warn("未找到规则类型 {} 的数据提供者", ruleTypeId);
            return Collections.emptyList();
        }

        List<Map<String, Object>> results = new ArrayList<>();

        log.debug("使用数据提供者 {} 获取预警数据", provider.getProviderName());

        for (Map<String, Object> paramGroup : paramGroups) {
            try {
                Map<String, Object> data = provider.fetchData(ruleTypeId, paramGroup, tm);
                
                // 将参数组信息添加到结果中
                data.put("paramGroup", paramGroup);
                data.put("argId", paramGroup.get("argId"));
                
                results.add(data);
                
            } catch (Exception e) {
                log.error("获取参数组 {} 的数据失败: {}", paramGroup.get("argId"), e.getMessage(), e);
            }
        }

        return results;
    }

    /**
     * 评估预警条件
     */
    private boolean evaluateAlertCondition(String ruleTypeId, List<Map<String, Object>> dataResults, AlertContext context) {
        AlertRuleStrategy strategy = strategyFactory.getStrategy(ruleTypeId);

        if (strategy == null) {
            log.warn("未找到规则类型 {} 的评估策略", ruleTypeId);
            return false;
        }

        // 设置数据到上下文供策略使用 - 保留现有数据
        Map<String, Object> strategyData = context.getData();
        if (strategyData == null) {
            strategyData = new HashMap<>();
        }
        strategyData.put("argResults", dataResults);
        context.setData(strategyData);

        log.debug("使用策略 {} 评估预警条件", strategy.getClass().getSimpleName());
        return strategy.evaluate(context);
    }

    /**
     * 获取支持的规则类型
     */
    public Set<String> getSupportedRuleTypes() {
        Set<String> supportedTypes = new HashSet<>();
        supportedTypes.addAll(resolverMap.keySet());
        supportedTypes.addAll(providerMap.keySet());
        return supportedTypes;
    }

    /**
     * 检查规则类型是否完全支持（有解析器、数据提供者和策略）
     */
    public boolean isRuleTypeFullySupported(String ruleTypeId) {
        return resolverMap.containsKey(ruleTypeId) 
               && providerMap.containsKey(ruleTypeId)
               && strategyFactory.getStrategy(ruleTypeId) != null;
    }
} 