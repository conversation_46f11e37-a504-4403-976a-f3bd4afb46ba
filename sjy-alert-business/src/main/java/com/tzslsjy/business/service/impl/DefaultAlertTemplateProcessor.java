package com.tzslsjy.business.service.impl;

import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 默认预警模板处理器
 * 提供通用的模板解析和变量替换功能
 */
@Component
public class DefaultAlertTemplateProcessor implements AlertTemplateProcessor {
    private static final Logger logger = LoggerFactory.getLogger(DefaultAlertTemplateProcessor.class);
    
    // 变量模式：${变量名:默认值}
    private static final Pattern VARIABLE_PATTERN = Pattern.compile("\\$\\{([^:}]+)(?::([^}]*))?\\}");
    
    // 内联变量模式：{变量名}
    private static final Pattern INLINE_VARIABLE_PATTERN = Pattern.compile("\\{([^{}]+)\\}");

    @Override
    public SjyAlertSmsRecord processTemplate(AlertContext context, SjyAlertSmsTemplate smsTemplate, Map<String, Object> data) {
        try {
            if (smsTemplate == null || smsTemplate.getTemplateContent() == null) {
                logger.warn("模板内容为空");
                return null;
            }

            // 替换模板中的占位符
            String content = replaceTemplatePlaceholders(smsTemplate, data);

            // 创建预警记录
            SjyAlertSmsRecord record = new SjyAlertSmsRecord();
            record.setRuleId(String.valueOf(context.getRule().getRuleId()));
            record.setContent(content);

            // 设置预警级别（如果有）
            if (data.containsKey("alertLevel")) {
                Object levelObj = data.get("alertLevel");
                if (levelObj instanceof Number) {
                    record.setAlertLevel(((Number) levelObj).longValue());
                }
            }

            return record;
        } catch (Exception e) {
            logger.error("处理模板失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 替换模板中的占位符
     */
    protected String replaceTemplatePlaceholders(SjyAlertSmsTemplate template, Map<String, Object> data) {
        String result = template.getTemplateContent();

        // 添加系统变量
        Map<String, Object> allData = new HashMap<>(data);
        addSystemVariables(allData);

        // 处理普通变量 ${var} 或 ${var:default}
        result = replaceVariables(result, allData);

        // 处理列表变量
        if (template.getListFormat() != null && result.contains("{list}")) {
            String listStr = processListItems(template, allData);
            result = result.replace("{list}", Matcher.quoteReplacement(listStr));
        }

        return result;
    }

    /**
     * 添加系统变量
     */
    protected void addSystemVariables(Map<String, Object> data) {
        // 当前时间
        java.text.SimpleDateFormat fullDateFormat = new java.text.SimpleDateFormat("yyyy年MM月dd日HH时mm分");
        java.text.SimpleDateFormat shortDateFormat = new java.text.SimpleDateFormat("MM月dd日HH:mm");
        Date now = new Date();
        data.put("currentTime", fullDateFormat.format(now));
        data.put("shortTime", shortDateFormat.format(now));
        data.put("now", now);

        // 预警级别文本转换
        processAlertLevelText(data);
    }
    
    /**
     * 处理预警级别文本
     */
    private void processAlertLevelText(Map<String, Object> data) {
        Object levelObj = data.get("alertLevel");
        if (levelObj == null) return;
        
        int level = 0;
        if (levelObj instanceof Number) {
            level = ((Number) levelObj).intValue();
        } else if (levelObj instanceof String) {
            try {
                level = Integer.parseInt(levelObj.toString());
            } catch (NumberFormatException e) {
                // 忽略解析错误
                return;
            }
        }

        String levelText;
        String levelColor;
        switch (level) {
            case 3:
                levelText = "三级";
                levelColor = "红色";
                break;
            case 2:
                levelText = "二级";
                levelColor = "橙色";
                break;
            default:
                levelText = "一级";
                levelColor = "黄色";
                break;
        }
        
        data.put("alertLevelText", levelText);
        data.put("alertLevelColor", levelColor);
        data.put("alertLevelFull", levelColor + "(" + levelText + ")");
    }

    /**
     * 替换模板中的普通变量
     */
    protected String replaceVariables(String template, Map<String, Object> data) {
        StringBuffer result = new StringBuffer();
        Matcher matcher = INLINE_VARIABLE_PATTERN.matcher(template);

        while (matcher.find()) {
            String varName = matcher.group(1).trim();
            if("list".equals(varName)) {
                // 如果是列表变量，跳过处理
                matcher.appendReplacement(result, Matcher.quoteReplacement("{list}"));
                continue;
            }
         //   String defaultValue = matcher.group(2);

            // 从数据中查找变量值
            Object value = getNestedValue(data, varName);

            // 如果值为null且有默认值，则使用默认值
            String replacement = (value != null) ? value.toString() 
                                                : "";

            // 替换匹配的内容
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 处理列表项
     */
    protected String processListItems(SjyAlertSmsTemplate template, Map<String, Object> data) {
        String format = template.getListFormat();
        String separator = template.getListSeparator() != null ? template.getListSeparator() : ", ";
        long maxCount = template.getDisplayNum() != null ? template.getDisplayNum() : 5;
        String omitSymbol = template.getOmitSymbol() != null ? template.getOmitSymbol() : "...";
        
        // 获取列表数据
        Object listObj = data.get("list");
        List<?> list = convertToList(listObj);
        if (list == null || list.isEmpty()) {
            return "";
        }

        // 预处理格式中的外部变量
        String preprocessedFormat = preprocessFormatWithExternalVars(format, data);

        // 格式化列表项
        List<String> formattedItems = new ArrayList<>();
        long itemCount = Math.min(list.size(), maxCount);

        for (int i = 0; i < itemCount; i++) {
            Object item = list.get(i);
            String formattedItem = replaceItemVariables(preprocessedFormat, item);
            formattedItems.add(formattedItem);
        }

        // 添加省略符号
        if (list.size() > maxCount) {
            formattedItems.add(omitSymbol);
        }

        // 使用连接符连接
        return String.join(separator, formattedItems);
    }

    /**
     * 从嵌套的Map中获取值
     */
    protected Object getNestedValue(Map<String, Object> data, String path) {
        if (data == null || path == null) {
            return null;
        }

        // 如果路径中没有点，直接从map获取
        if (!path.contains(".")) {
            return data.get(path);
        }

        // 处理嵌套路径
        String[] parts = path.split("\\.");
        Object current = data;

        for (String part : parts) {
            if (current instanceof Map) {
                current = ((Map<?, ?>) current).get(part);
                if (current == null) {
                    return null;
                }
            } else {
                return null;
            }
        }

        return current;
    }

    /**
     * 预处理格式字符串，替换其中引用的外部变量
     */
    protected String preprocessFormatWithExternalVars(String format, Map<String, Object> data) {
        StringBuffer result = new StringBuffer();
        Matcher matcher = INLINE_VARIABLE_PATTERN.matcher(format);

        while (matcher.find()) {
            String varName = matcher.group(1).trim();
            Object value = getNestedValue(data, varName);
            
            if (value != null) {
                // 是外部变量，替换它
                matcher.appendReplacement(result, Matcher.quoteReplacement(value.toString()));
            } else {
                // 不是外部变量，保留原样（可能是列表项属性引用）
                matcher.appendReplacement(result, matcher.group(0));
            }
        }
        matcher.appendTail(result);

        return result.toString();
    }

    /**
     * 替换列表项中的变量引用
     */
    protected String replaceItemVariables(String format, Object item) {
        if (item == null) {
            return format;
        }

        String result = format;

        if (item instanceof Map) {
            // 如果列表项是Map，替换其中的键
            Map<?, ?> mapItem = (Map<?, ?>) item;
            for (Map.Entry<?, ?> entry : mapItem.entrySet()) {
                String key = entry.getKey().toString();
                Object val = entry.getValue();
                result = result.replace("{" + key + "}", val != null ? val.toString() : "");
            }
        } else if (item instanceof String) {
            // 如果是简单字符串，替换{item}
            result = result.replace("{item}", item.toString());
        } else {
            // 反射获取对象属性
            result = replaceObjectProperties(result, item);
        }

        return result;
    }
    
    /**
     * 使用反射替换对象属性
     */
    private String replaceObjectProperties(String template, Object obj) {
        Matcher matcher = INLINE_VARIABLE_PATTERN.matcher(template);
        StringBuffer sb = new StringBuffer();

        while (matcher.find()) {
            String propertyName = matcher.group(1);
            String propertyValue = "";

            try {
                // 尝试使用Getter方法
                String getterName = "get" + propertyName.substring(0, 1).toUpperCase() + propertyName.substring(1);
                java.lang.reflect.Method getter = obj.getClass().getMethod(getterName);
                Object value = getter.invoke(obj);
                propertyValue = value != null ? value.toString() : "";
            } catch (Exception e) {
                // 如果获取失败，尝试直接访问字段
                try {
                    java.lang.reflect.Field field = obj.getClass().getDeclaredField(propertyName);
                    field.setAccessible(true);
                    Object value = field.get(obj);
                    propertyValue = value != null ? value.toString() : "";
                } catch (Exception ex) {
                    // 忽略访问失败的情况
                }
            }

            matcher.appendReplacement(sb, Matcher.quoteReplacement(propertyValue));
        }
        matcher.appendTail(sb);
        
        return sb.toString();
    }

    /**
     * 将对象转换为List
     */
    protected List<?> convertToList(Object obj) {
        if (obj == null) {
            return Collections.emptyList();
        }

        if (obj instanceof List) {
            return (List<?>) obj;
        } else if (obj instanceof Object[]) {
            return Arrays.asList((Object[]) obj);
        } else if (obj instanceof Map) {
            return new ArrayList<>(((Map<?, ?>) obj).entrySet());
        } else if (obj instanceof Collection) {
            return new ArrayList<>((Collection<?>) obj);
        }

        // 如果是单个对象，包装为单元素列表
        return Collections.singletonList(obj);
    }

    @Override
    public List<SjyAlertSmsRecord> mergeAlerts(AlertContext context, SjyAlertSmsRecord alert, List<SjyAlertPerson> people) {
        if (alert == null || people == null || people.isEmpty()) {
            return Collections.emptyList();
        }

        List<SjyAlertSmsRecord> result = new ArrayList<>();

        SjyAlertSmsRecord personRecord = new SjyAlertSmsRecord();
        personRecord.setRuleId(alert.getRuleId());
        personRecord.setContent(alert.getContent());
        personRecord.setPersonId(people.stream().map(SjyAlertPerson::getPersonId).collect(Collectors.joining(",")));

        personRecord.setAlertLevel(alert.getAlertLevel());
        result.add(personRecord);
        return result;
    }

    @Override
    public List<SjyAlertPerson> mergeReceivers(AlertContext context, List<SjyAlertPerson> receivers) {
        if (receivers == null || receivers.isEmpty()) {
            return Collections.emptyList();
        }

        // 使用电话号码去重
        Map<String, SjyAlertPerson> uniquePersons = new HashMap<>();
        for (SjyAlertPerson person : receivers) {
            String phone = person.getPhone();
            if (phone != null && !phone.isEmpty()) {
                uniquePersons.putIfAbsent(phone, person);
            }
        }

        return new ArrayList<>(uniquePersons.values());
    }
}
