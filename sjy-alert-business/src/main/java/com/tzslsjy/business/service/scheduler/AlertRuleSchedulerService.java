package com.tzslsjy.business.service.scheduler;

import com.tzslsjy.business.domain.SjyAlertRule;
import org.quartz.SchedulerException;

import java.util.List;

/**
 * 预警规则动态调度服务接口
 *
 * <AUTHOR>
 * @date 2025-07-08
 */
public interface AlertRuleSchedulerService {

    /**
     * 初始化所有预警规则的定时任务
     * 从数据库加载所有启用的预警规则并创建定时任务
     */
    void initializeAllAlertRules();

    /**
     * 刷新所有预警规则的定时任务
     * 重新从数据库加载规则并更新定时任务
     */
    void refreshAllAlertRules();

    /**
     * 为单个预警规则创建定时任务
     *
     * @param rule 预警规则
     * @return 是否创建成功
     */
    boolean createAlertRuleJob(SjyAlertRule rule);

    /**
     * 更新预警规则的定时任务
     *
     * @param rule 预警规则
     * @return 是否更新成功
     */
    boolean updateAlertRuleJob(SjyAlertRule rule);

    /**
     * 删除预警规则的定时任务
     *
     * @param ruleId 规则ID
     * @return 是否删除成功
     */
    boolean deleteAlertRuleJob(Long ruleId);

    /**
     * 暂停预警规则的定时任务
     *
     * @param ruleId 规则ID
     * @return 是否暂停成功
     */
    boolean pauseAlertRuleJob(Long ruleId);

    /**
     * 恢复预警规则的定时任务
     *
     * @param ruleId 规则ID
     * @return 是否恢复成功
     */
    boolean resumeAlertRuleJob(Long ruleId);

    /**
     * 立即执行预警规则
     *
     * @param ruleId 规则ID
     * @return 是否执行成功
     */
    boolean executeAlertRuleNow(Long ruleId);

    /**
     * 获取所有预警规则任务的状态
     *
     * @return 任务状态列表
     */
    List<AlertRuleJobStatus> getAllAlertRuleJobStatus();

    /**
     * 检查预警规则任务是否存在
     *
     * @param ruleId 规则ID
     * @return 是否存在
     */
    boolean isAlertRuleJobExists(Long ruleId);

    /**
     * 获取预警规则任务状态
     *
     * @param ruleId 规则ID
     * @return 任务状态
     */
    AlertRuleJobStatus getAlertRuleJobStatus(Long ruleId);

    /**
     * 预警规则任务状态内部类
     */
    class AlertRuleJobStatus {
        private Long ruleId;
        private String ruleName;
        private String cronExpression;
        private String status; // NORMAL, PAUSED, BLOCKED, ERROR, NONE
        private String nextFireTime;
        private String previousFireTime;
        private boolean exists;

        // Getters and Setters
        public Long getRuleId() { return ruleId; }
        public void setRuleId(Long ruleId) { this.ruleId = ruleId; }

        public String getRuleName() { return ruleName; }
        public void setRuleName(String ruleName) { this.ruleName = ruleName; }

        public String getCronExpression() { return cronExpression; }
        public void setCronExpression(String cronExpression) { this.cronExpression = cronExpression; }

        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }

        public String getNextFireTime() { return nextFireTime; }
        public void setNextFireTime(String nextFireTime) { this.nextFireTime = nextFireTime; }

        public String getPreviousFireTime() { return previousFireTime; }
        public void setPreviousFireTime(String previousFireTime) { this.previousFireTime = previousFireTime; }

        public boolean isExists() { return exists; }
        public void setExists(boolean exists) { this.exists = exists; }
    }
}
