package com.tzslsjy.business.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.tzslsjy.business.bo.SjyRiskVillageInfoAddBo;
import com.tzslsjy.business.bo.SjyRiskVillageInfoEditBo;
import com.tzslsjy.business.bo.SjyRiskVillageInfoQueryBo;
import com.tzslsjy.business.domain.SjyRiskVillageInfo;
import com.tzslsjy.business.domain.SjyRiskVillageStcdRel;

import com.tzslsjy.business.domain.StStbprpB;
import com.tzslsjy.business.mapper.SjyRiskVillageInfoMapper;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.service.ISjyRiskVillageInfoService;
import com.tzslsjy.business.service.ISjyRiskVillageStcdRelService;
import com.tzslsjy.business.vo.SjyRiskVillageInfoRespVo;
import com.tzslsjy.business.vo.SjyRiskVillageInfoVo;
import com.tzslsjy.common.annotation.DataSource;
import com.tzslsjy.common.enums.DataSourceType;
import com.tzslsjy.business.mapper.SjyFolderMapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 村落基本情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-07
 */
@Service

public class SjyRiskVillageInfoServiceImpl extends ServiceImpl<SjyRiskVillageInfoMapper, SjyRiskVillageInfo> implements ISjyRiskVillageInfoService {

    @Resource
    private SjyFolderMapper folderMapper;
    @Resource
    private StStbprpBMapper stStbprpBMapper;
    @Resource
    private ISjyRiskVillageStcdRelService stcdRelService;
    @Override
    public SjyRiskVillageInfoVo queryById(String id){
        SjyRiskVillageInfo db = baseMapper.selectWithId(id);
        SjyRiskVillageInfoVo rs = BeanUtil.toBean(db, SjyRiskVillageInfoVo.class);
        rs.setSjyFolders(folderMapper.selectByCIdAndType(rs.getId(),"1"));
        return rs;
    }

    @Override
    public List<SjyRiskVillageInfoVo> queryList(SjyRiskVillageInfoQueryBo bo) {

        List<SjyRiskVillageInfoVo> sjyRiskVillageInfoVos = baseMapper.queryList(bo);
        List<String> stcds = new ArrayList<>();
        sjyRiskVillageInfoVos.forEach(e->{
            stcds.addAll(e.getSts().stream().map(SjyRiskVillageStcdRel::getStcd).filter(Objects::nonNull).collect(Collectors.toList()));
        });
        if(!stcds.isEmpty()) {
            List<StStbprpB> stnmStcd = stStbprpBMapper.getStnmStcd(stcds);
            Map<String, String> stnmStcdMap = stnmStcd.stream().collect(Collectors.toMap(StStbprpB::getStcd, StStbprpB::getStnm));
            sjyRiskVillageInfoVos.forEach(e -> {
                e.getSts().stream().forEach(
                        s -> {
                            s.setStnm(stnmStcdMap.get(s.getStcd()));
                        }
                );
            });
        }
        return sjyRiskVillageInfoVos;
    }

    /**
    * 实体类转化成视图对象
    *
    * @param collection 实体类集合
    * @return
    */
    private List<SjyRiskVillageInfoVo> entity2Vo(Collection<SjyRiskVillageInfo> collection) {
        List<SjyRiskVillageInfoVo> voList = collection.stream()
                .map(any -> BeanUtil.toBean(any, SjyRiskVillageInfoVo.class))
                .collect(Collectors.toList());
        if (collection instanceof Page) {
            Page<SjyRiskVillageInfo> page = (Page<SjyRiskVillageInfo>)collection;
            Page<SjyRiskVillageInfoVo> pageVo = new Page<>();
            BeanUtil.copyProperties(page,pageVo);
            pageVo.addAll(voList);
            voList = pageVo;
        }
        return voList;
    }

    @Override
    @DataSource(DataSourceType.INFO)
    public Boolean insertByAddBo(SjyRiskVillageInfoAddBo bo) {
        SjyRiskVillageInfo add = BeanUtil.toBean(bo, SjyRiskVillageInfo.class);
        validEntityBeforeSave(add);
        boolean save = this.save(add);
        if(bo.getSts() != null && !bo.getSts().isEmpty()){
            bo.getSts().forEach(e->{
                e.setVillageId(add.getId());
            });
            stcdRelService.insertOrUpdateBatch(bo.getSts(),add.getId());
        }
        return save;
    }

    @Override
    @DataSource(DataSourceType.INFO)
    public Boolean updateByEditBo(SjyRiskVillageInfoEditBo bo) {
        SjyRiskVillageInfo update = BeanUtil.toBean(bo, SjyRiskVillageInfo.class);
        validEntityBeforeSave(update);
        boolean b = this.updateById(update);
        if(bo.getSts() != null && !bo.getSts().isEmpty()){
            bo.getSts().forEach(e->{
                e.setVillageId(update.getId());
            });
            stcdRelService.insertOrUpdateBatch(bo.getSts(),update.getId());
        }
        return b;
    }

    /**
     * 保存前的数据校验
     *
     * @param entity 实体类数据
     */
    private void validEntityBeforeSave(SjyRiskVillageInfo entity){
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @DataSource(DataSourceType.INFO)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return this.removeByIds(ids);
    }

    @Override
    public SjyRiskVillageInfoRespVo riskAreaStatistics(SjyRiskVillageInfoQueryBo bo) {
        SjyRiskVillageInfoRespVo rs = new SjyRiskVillageInfoRespVo();
        List<SjyRiskVillageInfo> lists = this.baseMapper.riskAreaStatistics(bo);

        //一般村落
        List<SjyRiskVillageInfo> generalVillageList = lists.stream().filter(o->o.getRiskLevel()!=null && o.getRiskLevel()==1).collect(Collectors.toList());
        List<SjyRiskVillageInfo> importantVillageList = lists.stream().filter(o->o.getRiskLevel()!=null && o.getRiskLevel()!=1).collect(Collectors.toList());

        //高
        List<SjyRiskVillageInfo> highVillageList = lists.stream().filter(o->o.getRiskLevel()!=null && o.getRiskLevel()==2).collect(Collectors.toList());
        //极高
        List<SjyRiskVillageInfo> vereyHighVillageList = lists.stream().filter(o->o.getRiskLevel()!=null && o.getRiskLevel()==4).collect(Collectors.toList());
        //低
        List<SjyRiskVillageInfo> lowVillageList = lists.stream().filter(o->o.getRiskLevel()!=null && o.getRiskLevel()==3).collect(Collectors.toList());
        rs.setGeneralVillageList(generalVillageList);
        rs.setHighVillageList(highVillageList);
        rs.setLowVillageList(lowVillageList);
        rs.setVereyHighVillageList(vereyHighVillageList);
        rs.setImportantVillageList(importantVillageList);
        if(CollectionUtil.isNotEmpty(generalVillageList)){
            rs.setGeneralVillageListCount(generalVillageList.stream().count());
        }
        if(CollectionUtil.isNotEmpty(highVillageList)){
            rs.setHighVillageListCount(highVillageList.stream().count());
        }
        if(CollectionUtil.isNotEmpty(vereyHighVillageList)){
            rs.setVereyHighVillageListCount(vereyHighVillageList.stream().count());
        }
        if(CollectionUtil.isNotEmpty(lowVillageList)){
            rs.setLowVillageListCount(lowVillageList.stream().count());
        }
        if(CollectionUtil.isNotEmpty(importantVillageList)){
            rs.setImportantVillageListCount(importantVillageList.stream().count());
        }
        return rs;
    }
}