package com.tzslsjy.business.service.impl;

import com.alibaba.fastjson2.JSON;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.service.AlertRuleParameterResolver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 水文设备预警参数解析器
 */
@Component
@Slf4j
public class HydrographicEquipmentParameterResolver implements AlertRuleParameterResolver {

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("18"); //   代表水文设备预警

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public List<Map<String, Object>> resolveParameters(SjyAlertRule rule, List<SjyAlertRuleArg> ruleParams, Date tm) {
        log.debug("开始解析水文设备预警规则参数，规则ID: {}", rule.getRuleId());
        List<Map<String, Object>> paramGroups = new ArrayList<>();

        Map<Long, List<SjyAlertRuleArg>> groupedParams = ruleParams.stream()
                .collect(Collectors.groupingBy(SjyAlertRuleArg::getArgId));

        for (Map.Entry<Long, List<SjyAlertRuleArg>> entry : groupedParams.entrySet()) {
            Long argId = entry.getKey();
            List<SjyAlertRuleArg> argParamsForGroup = entry.getValue();
            Map<String, Object> queryParams = buildQueryParamsForGroup(rule, argParamsForGroup, argId, tm);
            if (!queryParams.isEmpty()) {
                paramGroups.add(queryParams);
            }
        }
        return paramGroups;
    }

    private Map<String, Object> buildQueryParamsForGroup(SjyAlertRule rule, List<SjyAlertRuleArg> argParams, Long argId, Date tm) {
        Map<String, Object> queryParams = new HashMap<>();
        queryParams.put("ruleId", rule.getRuleId());
        queryParams.put("ruleName", rule.getRuleName());
        queryParams.put("ruleTypeId", rule.getRuleTypeId());
        queryParams.put("argId", argId);
        queryParams.put("tm", tm);

        // 设备编码通常直接在规则主体中指定
        if (rule.getAlertStcd() != null && !rule.getAlertStcd().isEmpty()) {
            queryParams.put("equipmentCodes", Arrays.asList(rule.getAlertStcd().split(",")));
        }

        List<SjyHydrographicEquipmentParamData> equipmentParamDataList = new ArrayList<>();
        for (SjyAlertRuleArg param : argParams) {
            if (param.getArgId().equals(argId)) { // 确保只处理当前参数组的参数
                try {
                    SjyHydrographicEquipmentJsonData jsonData = JSON.parseObject(param.getArgJson(), SjyHydrographicEquipmentJsonData.class);
                    SjyHydrographicEquipmentParamData paramData = new SjyHydrographicEquipmentParamData();
                    paramData.setArgId(param.getArgId());
                    paramData.setAlertLevel(param.getAlertLevel());
                    if (jsonData != null) {
                        paramData.setHoursThreshold(jsonData.getHoursThreshold());
                    }
                    equipmentParamDataList.add(paramData);
                } catch (Exception e) {
                    log.error("解析水文设备预警参数失败，参数ID: {}, JSON: {}, 错误: {}", 
                            param.getArgId(), param.getArgJson(), e.getMessage());
                }
            }
        }
        queryParams.put("equipmentParams", equipmentParamDataList);
        return queryParams;
    }
} 