package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUtil;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyAlertNodeMapper;
import com.tzslsjy.business.mapper.SjyAlertRuleNodeRelationMapper;
import com.tzslsjy.business.mapper.SjyAlertSmsTemplateMapper;
import com.tzslsjy.business.service.AlertProcessor;
import com.tzslsjy.business.service.AlertTemplateProcessor;
import com.tzslsjy.business.service.ISjyAlertPersonService;
import com.tzslsjy.business.service.TypedAlertProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 重构后的雨量预警处理器
 * 负责生成预警消息和获取接收人
 */
@Component
public class RefactoredRainfallAlertProcessor implements AlertProcessor, TypedAlertProcessor {
    private static final Logger logger = LoggerFactory.getLogger(RefactoredRainfallAlertProcessor.class);

    @Autowired
    @Qualifier("rainfallAlertTemplateProcessor")
    private AlertTemplateProcessor templateProcessor;

    @Autowired
    private SjyAlertNodeMapper alertNodeMapper;

    @Autowired
    private SjyAlertSmsTemplateMapper alertSmsTemplateMapper;

    @Autowired
    private SjyAlertRuleNodeRelationMapper alertRuleNodeRelationMapper;

    @Autowired
    private ISjyAlertPersonService sjyAlertPersonService;

    // 下一个处理器
    private AlertProcessor nextProcessor;

    @Override
    public void setNextProcessor(AlertProcessor nextProcessor) {
        this.nextProcessor = nextProcessor;
    }

    @Override
    public void process(AlertContext context) {
        logger.info("开始处理雨量预警");

        try {
            Map<String, Object> data = context.getData();
            SjyAlertRule rule = context.getRule();

            if (data == null || rule == null) {
                logger.warn("预警数据不完整，无法处理");
                if (nextProcessor != null) {
                    nextProcessor.process(context);
                }
                return;
            }

            // 检查是否触发预警
            boolean triggered = (boolean) data.getOrDefault("overallTriggered", false);
            if (!triggered) {
                logger.info("没有触发预警，跳过消息生成");
                if (nextProcessor != null) {
                    nextProcessor.process(context);
                }
                return;
            }

            // 获取触发的参数集结果
            @SuppressWarnings("unchecked")
            List<Map<String, Object>> triggeredResults = (List<Map<String, Object>>) data.get("triggeredResults");
            if (triggeredResults == null || triggeredResults.isEmpty()) {
                logger.warn("没有触发的参数集数据");
                if (nextProcessor != null) {
                    nextProcessor.process(context);
                }
                return;
            }

            // 存储所有生成的预警记录
            List<SjyAlertSmsRecord> allRecords = new ArrayList<>();

            // 为每个触发的参数集生成预警消息
            for (Map<String, Object> alertData : triggeredResults) {
                String argId =String.valueOf(alertData.get("argId"));
                logger.debug("处理参数集: {}", argId);
                Date endTm=(Date)alertData.get("endTm");
                // 获取适合的模板
                List<SjyAlertSmsTemplate> templates = getTemplates(rule.getRuleId());
                
                // 处理按行政区划合并的雨量数据
                if (alertData.containsKey("adcdRainfallMap")) {
                    @SuppressWarnings("unchecked")
                    Map<String, List<Map<String, Object>>> adcdRainfallMap = 
                            (Map<String, List<Map<String, Object>>>) alertData.get("adcdRainfallMap");

                    // 每个行政区划和时间段分组生成一条预警消息
                    for (Map.Entry<String, List<Map<String, Object>>> entry : adcdRainfallMap.entrySet()) {
                        String groupKey = entry.getKey(); // 格式: "9_331082001_3h" 或 "6_331082_6h"
                        List<Map<String, Object>> adcdRainfall = entry.getValue();

                        if (adcdRainfall == null || adcdRainfall.isEmpty()) {
                            continue;
                        }

                        // 从分组键中提取信息
                        String jurisdiction = extractJurisdictionFromGroupKey(groupKey);
                        String duration = extractDurationFromGroupKey(groupKey);
                        String jurisdictionLevel = groupKey.substring(0, 1); // "9" 或 "6"

                        logger.info("处理{}位行政区划 {} 时间段 {} 的雨量预警，包含 {} 个雨量数据",
                                jurisdictionLevel, jurisdiction, duration, adcdRainfall.size());

                        // 准备生成消息的数据
                        Map<String, Object> adcdData = new HashMap<>(alertData);
                        adcdData.put("rainfallList", adcdRainfall);
                        adcdData.put("list", adcdRainfall);
                        adcdData.put("startTm", DateUtil.format(DateUtil.offsetHour(endTm,-Integer.parseInt(duration.replace("h",""))), "MM月dd日HH时"));
                        adcdData.put("endTm", DateUtil.format(endTm, "dd日HH时"));
                        adcdData.put("adcd", jurisdiction);
                        adcdData.put("groupKey", groupKey);
                        adcdData.put("jurisdictionLevel", jurisdictionLevel);
                        adcdData.put("duration", duration);

                        for (SjyAlertSmsTemplate template : templates) {
                            // 生成消息
                            SjyAlertSmsRecord alert = templateProcessor.processTemplate(context, template, adcdData);

                            if (alert != null) {
                                // 获取行政区划接收人
                                List<SjyAlertPerson> receivers = getReceivers(rule.getRuleId(), argId, jurisdiction);
                                List<SjyAlertPerson> mergedReceivers = templateProcessor.mergeReceivers(context, receivers);
                                List<SjyAlertSmsRecord> mergedAlerts = templateProcessor.mergeAlerts(context, alert, mergedReceivers);

                                // 添加到总结果
                                allRecords.addAll(mergedAlerts);
                                logger.info("{}位行政区划{}时间段{}参数集{}成功生成预警消息，接收人数量: {}",
                                        jurisdictionLevel, jurisdiction, duration, argId, mergedReceivers.size());
                            }
                        }
                    }
                }
            }

            // 设置到上下文
            context.setRecords(allRecords);
            logger.info("总共生成{}条预警消息", allRecords.size());

        } catch (Exception e) {
            logger.error("处理雨量预警失败: {}", e.getMessage(), e);
        }

        // 继续处理链
        if (nextProcessor != null) {
            nextProcessor.process(context);
        }
    }

    /**
     * 获取适合的模板
     */
    private List<SjyAlertSmsTemplate> getTemplates(Long ruleId) {
        try {
            String type = "1"; // 雨量模板类型
            List<SjyAlertSmsTemplate> templates = alertSmsTemplateMapper.selectOneByTypeAndRuleId(ruleId, type);
            return templates != null ? templates : new ArrayList<>();
        } catch (Exception e) {
            logger.error("获取预警模板失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取接收人 - 参考RiverLevelAlertProcessor的getReceiversByJurisdiction实现
     */
    private List<SjyAlertPerson> getReceivers(Long ruleId, String argId, String adcd) {
        return getReceiversByJurisdiction(ruleId, argId, adcd);
    }

    /**
     * 根据行政区划获取接收人员
     * @param ruleId 规则ID
     * @param argId 参数组ID
     * @param adcd 行政区划编码
     * @return 接收人员列表
     */
    private List<SjyAlertPerson> getReceiversByJurisdiction(Long ruleId, String argId, String adcd) {
        try {
            List<String> nodeIds = alertRuleNodeRelationMapper.selectNodeIdsByArgIdAndAdcd(argId, adcd);
            if (nodeIds == null || nodeIds.isEmpty()) {
                logger.debug("未找到行政区划 {} 的节点ID，使用默认管理员", adcd);
                return getDefaultAdmin();
            }

            List<SjyAlertPerson> receivers = sjyAlertPersonService.selectByMemberIds(nodeIds);
            if (receivers.isEmpty()) {
                logger.warn("规则ID {} 及其参数组 {} 的行政区划 {} 未配置接收人或未找到，使用默认管理员", ruleId, argId, adcd);
                return getDefaultAdmin();
            }

            // 去重
            logger.debug("根据行政区划 {} 获取接收人员 {} 个", adcd, receivers.size());
            return receivers.stream().distinct().collect(Collectors.toList());

        } catch (Exception e) {
            logger.error("根据行政区划 {} 获取接收人员失败: {}", adcd, e.getMessage(), e);
            return getDefaultAdmin();
        }
    }

    /**
     * 获取默认管理员
     */
    private List<SjyAlertPerson> getDefaultAdmin() {
        SjyAlertPerson admin = new SjyAlertPerson();
        admin.setName("admin");
        admin.setPhone("15306587076");
        return Collections.singletonList(admin);
    }

    /**
     * 从分组键中提取行政区划编码
     * @param groupKey 格式如 "9_331082001_3h" 或 "6_331082_6h"
     * @return 行政区划编码
     */
    private String extractJurisdictionFromGroupKey(String groupKey) {
        if (groupKey != null && groupKey.length() > 2) {
            // 分割格式: "9_331082001_3h" -> ["9", "331082001", "3h"]
            String[] parts = groupKey.split("_");
            if (parts.length >= 2) {
                return parts[1]; // 返回行政区划编码部分
            }
            // 兼容旧格式 "9_331082001"
            return groupKey.substring(2);
        }
        return "";
    }

    /**
     * 从分组键中提取时间段信息
     * @param groupKey 格式如 "9_331082001_3h" 或 "6_331082_6h"
     * @return 时间段信息，如 "3h"
     */
    private String extractDurationFromGroupKey(String groupKey) {
        if (groupKey != null) {
            String[] parts = groupKey.split("_");
            if (parts.length >= 3) {
                return parts[2]; // 返回时间段部分
            }
        }
        return "";
    }

    @Override
    public String getSupportedType() {
        return "2"; // 2代表雨量规则类型
    }
} 