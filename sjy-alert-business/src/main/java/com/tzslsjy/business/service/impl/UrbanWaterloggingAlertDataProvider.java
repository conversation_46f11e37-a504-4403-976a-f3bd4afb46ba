package com.tzslsjy.business.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.tzslsjy.business.domain.*;
import com.tzslsjy.business.mapper.SjyPjStMapper;
import com.tzslsjy.business.mapper.StRiverRMapper;
import com.tzslsjy.business.mapper.StStbprpBMapper;
import com.tzslsjy.business.mapper.SjyAlertStSituationHisMapper;
import com.tzslsjy.business.service.AlertDataProvider;
import com.tzslsjy.business.service.ContinuousAlertService;
import com.tzslsjy.business.service.ISjyAlertStSituationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 城市内涝水位预警数据提供者
 * 特点：
 * 1. 每个监测站点独立处理
 * 2. 无阈值概念 - 当水位超过汛限值时立即触发预警
 * 3. 使用河道水情数据作为数据源
 */
@Component
@Slf4j
public class UrbanWaterloggingAlertDataProvider implements AlertDataProvider {

    @Autowired
    private StRiverRMapper stRiverRMapper;
    @Autowired
    private StStbprpBMapper stStbprpBMapper;
    @Autowired
    private SjyPjStMapper sjyPjStMapper;
    @Autowired
    private SjyAlertStSituationHisMapper sjyAlertStSituationHisMapper;
    @Autowired
    private ContinuousAlertService continuousAlertService;
    @Autowired
    private ISjyAlertStSituationService alertStSituationService;

    private static final List<String> SUPPORTED_TYPES = Collections.singletonList("26");

    @Override
    public List<String> getSupportedTypes() {
        return SUPPORTED_TYPES;
    }

    @Override
    public Map<String, Object> fetchData(String ruleTypeCode, Map<String, Object> queryParams, Date currentTime) {
        log.debug("开始获取城市内涝水位预警数据，规则类型: {}, 查询参数: {}", ruleTypeCode, queryParams);

        Map<String, Object> result = new HashMap<>();
        result.put("argId", queryParams.get("argId"));
        result.put("ruleId", queryParams.get("ruleId"));

        try {
            String stationCode = (String) queryParams.get("stationCode");
            Date tm = currentTime; // 使用传入的当前时间

            @SuppressWarnings("unchecked")
            List<SjyUrbanFloodParamData> urbanFloodParams =
                    (List<SjyUrbanFloodParamData>) queryParams.get("urbanFloodParams");

            if (CollectionUtils.isEmpty(urbanFloodParams)) {
                log.warn("城市内涝预警参数为空，测站: {}", stationCode);
                result.put("triggeredStations", Collections.emptyList());
                return result;
            }

            List<Map<String, Object>> triggeredStations = new ArrayList<>();

            // 为当前测站获取最新水位数据
            StRiverR latestWaterLevel = getLatestWaterLevel(stationCode, tm);
            if (latestWaterLevel == null) {
                log.warn("未找到测站 {} 的水位数据", stationCode);
                result.put("triggeredStations", Collections.emptyList());
                return result;
            }

            // 检查每个参数是否触发预警
            for (SjyUrbanFloodParamData param : urbanFloodParams) {
                if (isWaterLevelExceedsLimit(latestWaterLevel, param)) {
                    Map<String, Object> triggeredStation = createTriggeredStationData(latestWaterLevel, param);
                    triggeredStations.add(triggeredStation);
                    log.info("城市内涝预警触发，测站: {}, 当前水位: {}, 汛限水位: {}",
                            stationCode, latestWaterLevel.getZ(), param.getWrz());
                }
            }

            result.put("triggeredStations", triggeredStations);
            log.debug("城市内涝水位预警数据获取完成，触发测站数: {}", triggeredStations.size());

        } catch (Exception e) {
            log.error("获取城市内涝水位预警数据时发生错误: {}", e.getMessage(), e);
            result.put("triggeredStations", Collections.emptyList());
        }

        return result;
    }

    /**
     * 获取指定测站的最新水位数据
     */
    private StRiverR getLatestWaterLevel(String stationCode, Date tm) {
        try {
            QueryWrapper<StRiverR> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("STCD", stationCode);
            queryWrapper.le("TM", tm);
            queryWrapper.orderByDesc("TM");
            queryWrapper.last("LIMIT 1");

            return stRiverRMapper.selectOne(queryWrapper);
        } catch (Exception e) {
            log.error("获取测站 {} 水位数据失败: {}", stationCode, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 检查水位是否超过汛限值
     * 城市内涝预警的特点：无阈值概念，当水位超过汛限值时立即触发
     */
    private boolean isWaterLevelExceedsLimit(StRiverR waterLevel, SjyUrbanFloodParamData param) {
        if (waterLevel.getZ() == null || param.getWrz() == null) {
            return false;
        }

        // 直接比较当前水位与汛限水位，超过即触发
        return waterLevel.getZ().compareTo(param.getWrz()) > 0;
    }

    /**
     * 创建触发预警的测站数据
     */
    private Map<String, Object> createTriggeredStationData(StRiverR waterLevel, SjyUrbanFloodParamData param) {
        Map<String, Object> stationData = new HashMap<>();

        stationData.put("stcd", param.getStcd());
        stationData.put("stnm", param.getStnm());
        stationData.put("addvcdNm", param.getAddvcdNm());
        stationData.put("currentWaterLevel", waterLevel.getZ());
        stationData.put("wrz", param.getWrz());

        // 计算超汛限值（当前水位 - 汛限水位）
        BigDecimal overWrz = waterLevel.getZ().subtract(param.getWrz()).setScale(2, RoundingMode.HALF_UP);
        stationData.put("overWrz", overWrz);

        stationData.put("tm", DateUtil.formatDateTime(waterLevel.getTM()));
        stationData.put("alertLevel", param.getAlertLevel());
        stationData.put("argId", param.getArgId());

        return stationData;
    }

    @Override
    public boolean supportsRuleType(String ruleTypeId) {
        return "26".equals(ruleTypeId); // 城市内涝水位预警类型
    }
}