package com.tzslsjy.infra.convert;


import com.tzslsjy.common.pojo.PageResult;
import com.tzslsjy.infra.domain.FileDO;
import com.tzslsjy.infra.vo.FileRespVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface FileConvert {

    FileConvert INSTANCE = Mappers.getMapper(FileConvert.class);

    FileRespVO convert(FileDO bean);

    PageResult<FileRespVO> convertPage(PageResult<FileDO> page);

}
