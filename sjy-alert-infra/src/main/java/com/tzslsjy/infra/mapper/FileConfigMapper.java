package com.tzslsjy.infra.mapper;


import com.tzslsjy.common.pojo.PageResult;
import com.tzslsjy.framework.mybatis.mapper.BaseMapperX;
import com.tzslsjy.framework.mybatis.query.LambdaQueryWrapperX;
import com.tzslsjy.infra.domain.FileConfigDO;
import com.tzslsjy.infra.vo.FileConfigPageReqVO;

import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface FileConfigMapper extends BaseMapperX<FileConfigDO> {

    default PageResult<FileConfigDO> selectPage(FileConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileConfigDO>()
                .likeIfPresent(FileConfigDO::getName, reqVO.getName())
                .eqIfPresent(FileConfigDO::getStorage, reqVO.getStorage())
                .betweenIfPresent(FileConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileConfigDO::getId));
    }

}
