package com.tzslsjy.infra.mapper;


import com.tzslsjy.common.pojo.PageResult;
import com.tzslsjy.framework.mybatis.mapper.BaseMapperX;
import com.tzslsjy.framework.mybatis.query.LambdaQueryWrapperX;
import com.tzslsjy.infra.domain.FileDO;
import com.tzslsjy.infra.vo.FilePageReqVO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 文件操作 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface FileMapper extends BaseMapperX<FileDO> {

    default PageResult<FileDO> selectPage(FilePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<FileDO>()
                .likeIfPresent(FileDO::getPath, reqVO.getPath())
                .likeIfPresent(FileDO::getType, reqVO.getType())
                .betweenIfPresent(FileDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(FileDO::getId));
    }

}
