# 项目相关配置
sjy_alert:
    # 名称
    name: sjy
    # 版本
    version: 3.8.7
    # 版权年份
    copyrightYear: 2024
    # 实例演示开关
    demoEnabled: true
    # 文件路径 示例（ Windows配置D:/sjy/uploadPath，Linux配置 /home/<USER>/uploadPath）
    profile: D:/sjy/uploadPath
    # 获取ip地址开关
    addressEnabled: false
    # 验证码类型 math 数字计算 char 字符验证
    captchaType: math

# 开发环境配置
server:
    # 服务器的HTTP端口，默认为8080
    port: 8080
    servlet:
        # 应用的访问路径
        context-path: /
    tomcat:
        # tomcat的URI编码
        uri-encoding: UTF-8
        # 连接数满后的排队数，默认为100
        accept-count: 1000
        threads:
            # tomcat最大线程数，默认为200
            max: 800
            # Tomcat启动初始化的线程数，默认值10
            min-spare: 100

# 日志配置
logging:
    level:
        com.tzslsjy: debug
        org.springframework: warn
# 定时任务配置
Scheduler:
    # 是否自动启动
    autoStartup: false
# 用户配置
user:
    password:
        # 密码最大错误次数
        maxRetryCount: 5
        # 密码锁定时间（默认10分钟）
        lockTime: 10

# Spring配置
spring:
    # 资源信息
    messages:
        # 国际化资源文件路径
        basename: i18n/messages
    profiles:
        active: druid
    # 文件上传
    servlet:
        multipart:
            # 单个文件大小
            max-file-size:  10MB
            # 设置总上传的文件大小
            max-request-size:  20MB
    # 服务模块
    devtools:
        restart:
            # 热部署开关
            enabled: false
    # 数据源配置

    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        driverClassName: com.mysql.cj.jdbc.Driver
        druid:
            # 主库数据源
            #            master:
            #                url: jdbc:mysql://*************/sjy_template?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8
            #                username: sjy_template
            #                password: sjy@2022=
            master:
                url: jdbc:mysql://*************:8527/sjy_alert?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&autoReconnect=true&failOverReadOnly=false&allowMultiQueries=true
                username: polardbx_root
                password: 123456
            # 从库数据源
            slave:
                # 从数据源开关/默认关闭
                enabled: true
                url: jdbc:mysql://*************:8527/lh_water_basic?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&autoReconnect=true&failOverReadOnly=false&allowMultiQueries=true
                username: polardbx_root
                password: 123456
            info:
                # 从数据源开关/默认关闭
                enabled: true
                url: jdbc:mysql://*************:8527/lh_water_basic_info?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=true&serverTimezone=GMT%2B8&autoReconnect=true&failOverReadOnly=false&allowMultiQueries=true
                username: polardbx_root
                password: 123456
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1 FROM DUAL
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            webStatFilter:
                enabled: true
            statViewServlet:
                enabled: true
                # 设置白名单，不填则允许所有访问
                allow:
                url-pattern: /druid/*
                # 控制台管理用户名和密码
                login-username: sjy
                login-password: sjy@2022=
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
    # redis 配置
    redis:
        # 地址
        host: *************
        # 端口，默认为6379
        port: 6379
        # 数据库索引
        database: 13
        # 密码
        password: redis_Sjy@2022=
        # 连接超时时间
        timeout: 10s
        lettuce:
            pool:
                # 连接池中的最小空闲连接
                min-idle: 0
                # 连接池中的最大空闲连接
                max-idle: 8
                # 连接池的最大数据库连接数
                max-active: 8
                # #连接池最大阻塞等待时间（使用负值表示没有限制）
                max-wait: -1ms
    shardingsphere:
        datasource:
            names: db1
            db1:
                driver-class-name: com.mysql.cj.jdbc.Driver
                type: com.alibaba.druid.pool.DruidDataSource
                url: jdbc:mysql://*************:8527/lh_water_basic_submeter?useUnicode=true&characterEncoding=utf8&zeroDateTimeBehavior=convertToNull&useSSL=false&serverTimezone=GMT%2B8
                username: polardbx_root
                password: 123456
                # 初始连接数
                initialSize: 5
                # 最小连接池数量
                minIdle: 10
                # 最大连接池数量
                maxActive: 20
                # 配置获取连接等待超时的时间
                maxWait: 60000
                # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
                timeBetweenEvictionRunsMillis: 60000
                # 配置一个连接在池中最小生存的时间，单位是毫秒
                minEvictableIdleTimeMillis: 300000
                # 配置一个连接在池中最大生存的时间，单位是毫秒
                maxEvictableIdleTimeMillis: 900000
                # 配置检测连接是否有效
                validationQuery: SELECT 1
                testWhileIdle: true
                testOnBorrow: false
                testOnReturn: false
                webStatFilter:
                    enabled: true
                filter:
                    stat:
                        enabled: true
                        # 慢SQL记录
                        log-slow-sql: true
                        slow-sql-millis: 1000
                        merge-sql: true
                    wall:
                        config:
                            multi-statement-allow: true
        rules:
            # 配置分片规则
            sharding:
                tables:
                    # 配置 maxtemlog 表规则
                    st_river_r:
                        actualDataNodes: db1.st_river_r_$->{2015..2025}
                        tableStrategy:
                            standard:
                                shardingColumn: tm
                                shardingAlgorithmName: sharding_by_year
                        keyGenerateStrategy:
                            column: id
                    st_rsvr_r:
                        actualDataNodes: db1.st_rsvr_r_$->{2015..2025}
                        tableStrategy:
                            standard:
                                shardingColumn: tm
                                shardingAlgorithmName: sharding_by_year
                        keyGenerateStrategy:
                            column: id
                    st_tide_r:
                        actualDataNodes: db1.st_tide_r_$->{2015..2025}
                        tableStrategy:
                            standard:
                                shardingColumn: tm
                                shardingAlgorithmName: sharding_by_year
                        keyGenerateStrategy:
                            column: id
                    st_was_r:
                        actualDataNodes: db1.st_was_r_$->{2015..2025}
                        tableStrategy:
                            standard:
                                shardingColumn: tm
                                shardingAlgorithmName: sharding_by_year
                        keyGenerateStrategy:
                            column: id
                    st_pptn_r:
                        actualDataNodes: db1.st_pptn_r_$->{2015..2025},db1.st_pptn_r
                        tableStrategy:
                            standard:
                                shardingColumn: tm
                                shardingAlgorithmName: sharding_by_year
                    st_pptn_r_h:
                        actualDataNodes: db1.st_pptn_r_h_$->{2015..2025}
                        tableStrategy:
                            standard:
                                shardingColumn: tm
                                shardingAlgorithmName: sharding_by_year
                # 配置分片算法
                sharding-algorithms:
                    auto-mod-4:
                        type: mod
                        props:
                            sharding-count: 1
                    auto-mod-1:
                        type: mod
                        props:
                            sharding-count: 1
                    database-inline:
                        type: INLINE
                        props:
                            algorithm-expression: db$->{tm % 2}
                    sharding_by_year:
                        type: CLASS_BASED #自定义type
                        props:
                            strategy: STANDARD #标准分片类型
                            algorithmClassName: com.tzslsjy.business.config.TmYearShardingAlgorithm
        props:
            sql-show: true
# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30
--- #################### 微信相关配置 ####################
wx: # 参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-mp-spring-boot-starter/README.md 文档
    mp:
        # 公众号配置(必填)
        app-id: wxba4dee94334368d0
        secret: 4cb9854a83b469c46c45475858e8e39f
        # 存储配置，解决 AccessToken 的跨节点的共享
        config-storage:
            type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
            key-prefix: wx # Redis Key 的前缀
            http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
    miniapp: # 小程序配置（必填），参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-miniapp-spring-boot-starter/README.md 文档
        appid: wx35f2d7f75baef0b3
        secret: 037ea19ffc01ead453a8bca6711d876e
        config-storage:
            type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
            key-prefix: wa # Redis Key 的前缀
            http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
justauth:
    enabled: true
    type:
        WECHAT_MP: # 钉钉
            client-id: ${wx.mp.app-id}
            client-secret: ${wx.mp.secret}
            ignore-check-state: true
            ignore-check-redirect-uri: true
        DINGTALK: # 钉钉
            client-id: dingvrnreaje3yqvzhxg
            client-secret: i8E6iZyDvZj51JIb0tYsYfVQYOks9Cq1lgryEjFRqC79P3iJcrxEwT6Qk2QvLrLI
            ignore-check-redirect-uri: true
        WECHAT_ENTERPRISE: # 企业微信
            client-id: wwd411c69a39ad2e54
            client-secret: 1wTb7hYxnpT2TUbIeHGXGo7T0odav1ic10mLdyyATOw
            agent-id: 1000004
            ignore-check-redirect-uri: true
        WECHAT_MINI_APP: # 微信小程序
            client-id: ${wx.miniapp.appid}
            client-secret: ${wx.miniapp.secret}
            ignore-check-redirect-uri: true
            ignore-check-state: true # 微信小程序，不会使用到 state，所以不进行校验
    cache:
        type: REDIS
        prefix: 'social_auth_state:' # 缓存前缀，目前只对 Redis 缓存生效，默认 JUSTAUTH::STATE::
        timeout: 24h # 超时时长，目前只对 Redis 缓存生效，默认 3 分钟
# MyBatis配置
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.sjyAlert.**.domain,com.tzslsjy.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml
# MyBatis Plus 的配置项
mybatis-plus:
    mapper-locations: classpath*:mapper/**/*Mapper.xml
    configuration:
        map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    global-config:
        db-config:
            #id-type: NONE # “智能”模式，基于 IdTypeEnvironmentPostProcessor + 数据源的类型，自动适配成 AUTO、INPUT 模式。
            id-type: AUTO # 自增 ID，适合 MySQL 等直接自增的数据库
            #      id-type: INPUT # 用户输入 ID，适合 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库
            #      id-type: ASSIGN_ID # 分配 ID，默认使用雪花算法。注意，Oracle、PostgreSQL、Kingbase、DB2、H2 数据库时，需要去除实体类上的 @KeySequence 注解
            logic-delete-value: 1 # 逻辑已删除值(默认为 1)
            logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
    #type-aliases-package: ${yudao.info.base-package}.module.*.dal.dataobject
    encryptor:
        password: XDV71a+xqStEA3WH # 加解密的秘钥，可使用 https://www.imaegoo.com/2020/aes-key-generator/ 网站生成

# PageHelper分页插件
pagehelper:
    helperDialect: mysql
    supportMethodsArguments: true
    params: count=countSql
springdoc:
    api-docs:
        enabled: true
        path: /v3/api-docs
    swagger-ui:
        enabled: true
        path: /swagger-ui
sjy:
    swagger:
        title: 标题：台水院系统管理系统_接口文档
        description: 描述：用于管理集团旗下公司的人员信息,具体包括XXX,XXX模块...
        version: 1.0
        url:
        email: <EMAIL>
        license: MIT
        license-url: null
    security:
        permit-all_urls:
            - /**
    sms:
        url: https://*************:28888/sms/submit
        reporturl: https://*************:28888/sms/report
        apId: fxzk
        secretKey: Tzslsjy@2023#
        ecName: 台州市水利局
        sign: VNhjT66F7
        addSerial:
knife4j:
    enable: true
    setting:
        language: zh_cn
# 防止XSS攻击
xss:
    # 过滤开关
    enabled: true
    # 排除链接（多个用逗号分隔）
    excludes: /system/notice
    # 匹配链接
    urlPatterns: /system/*,/monitor/*,/tool/*
