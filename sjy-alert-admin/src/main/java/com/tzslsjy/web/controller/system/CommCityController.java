package com.tzslsjy.web.controller.system;



import com.tzslsjy.business.domain.comm.BusinessCommCItyTree;
import com.tzslsjy.business.domain.comm.BusinessCommCity;
import com.tzslsjy.business.service.comm.ICommCityService;
import com.tzslsjy.common.pojo.CommonResult;


import com.tzslsjy.system.domain.CommCItyTree;
import com.tzslsjy.system.domain.CommCity;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/2/9 15:02
 * @project water-basic-app
 * @company tzslsd
 */

@Tag(name = "城市数据" ,description = "城市数据")
@RestController
@RequestMapping("/CommCity")
public class CommCityController {

    @Resource(name = "businessCommCityService")
    ICommCityService commCityService;

    @PostMapping("/getCitys")
    @Operation(summary = "获取城市")
    public CommonResult<List<BusinessCommCity>> pageList(@RequestBody BusinessCommCity vo) {
        return CommonResult.success(commCityService.pageList(vo));
    }
    @PostMapping("/getCityTree")
    @Operation(summary  = "获取城市树")
    public CommonResult<List<BusinessCommCItyTree>> getCityTree(@RequestBody BusinessCommCity vo) {
        return CommonResult.success(commCityService.getTree(vo));
    }

    @PostMapping("/getCityTreeWithVillage")
    @Operation(summary  = "获取城市树")
    public CommonResult<List<BusinessCommCItyTree>> getCityTreeWithVillage(@RequestBody BusinessCommCity vo) {
        return CommonResult.success(commCityService.getCityTreeWithVillage(vo));
    }

}
