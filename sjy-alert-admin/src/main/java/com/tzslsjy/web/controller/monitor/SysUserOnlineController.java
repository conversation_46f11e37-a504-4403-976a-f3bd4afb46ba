package com.tzslsjy.web.controller.monitor;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.constant.CacheConstants;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.domain.model.LoginUser;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.core.redis.RedisCache;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.common.utils.StringUtils;
import com.tzslsjy.system.domain.SysUserOnline;
import com.tzslsjy.system.service.ISysUserOnlineService;

/**
 * 在线用户监控
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/monitor/online")
public class SysUserOnlineController extends BaseController
{
    @Autowired
    private ISysUserOnlineService userOnlineService;

    @Autowired
    private RedisCache redisCache;

    //@PreAuthorize("@ss.hasPermi('monitor:online:list')")
    @GetMapping("/list")
    public TableDataInfo list(String ipaddr, String userName,Boolean izLock)
    {
        Collection<String> keys = redisCache.keys(CacheConstants.LOGIN_TOKEN_KEY + "*");
        List<SysUserOnline> userOnlineList = new ArrayList<SysUserOnline>();
        Set<String> exitNames = new HashSet<>();
        for (String key : keys) {
            LoginUser user = redisCache.getCacheObject(key);
            if (StringUtils.isNotEmpty(ipaddr) && StringUtils.isNotEmpty(userName)) {
                if (StringUtils.equals(ipaddr, user.getIpaddr()) && StringUtils.contains(user.getUsername() , userName )) {
                    userOnlineList.add(userOnlineService.selectOnlineByInfo(ipaddr, userName, user));
                    exitNames.add(user.getUsername());
                }
            } else if (StringUtils.isNotEmpty(ipaddr)) {
                if (StringUtils.equals(ipaddr, user.getIpaddr())) {
                    userOnlineList.add(userOnlineService.selectOnlineByIpaddr(ipaddr, user) );
                    exitNames.add( user.getUsername());
                }
            } else if (StringUtils.isNotEmpty(userName) && StringUtils.isNotNull(user.getUser())) {
                if (StringUtils.contains( user.getUsername(),userName)) {
                    userOnlineList.add(userOnlineService.selectOnlineByUserName(userName, user));
                    exitNames.add( user.getUsername());
                }
            } else {
                userOnlineList.add(userOnlineService.loginUserToUserOnline(user));
                exitNames.add( user.getUsername());
            }
        }
        userOnlineList.forEach(e->{e.setIzOnline(true);
            e.setIzLock(false);});

        Collection<String> djKeys = redisCache.keys(CacheConstants.PWD_ERR_CNT_KEY + "*");
        for (String djKey : djKeys) {
            Object object = redisCache.getCacheObject(djKey);
            int i = Integer.parseInt(object.toString());
            if (i >= 5) {
                String lockName = djKey.substring(CacheConstants.PWD_ERR_CNT_KEY.length());
                if ((StringUtils.isNotEmpty(userName) && StringUtils.contains(lockName, userName)) ||  (userName==null||userName.isEmpty())) {
                    if (exitNames.contains(lockName)) {
                        userOnlineList.stream().filter(user -> StringUtils.equals(user.getUserName(), lockName)).forEach(user -> user.setIzLock(true));
                    } else {
                        SysUserOnline sysUserOnline = new SysUserOnline();
                        sysUserOnline.setUserName(lockName);
                        sysUserOnline.setIzLock(true);
                        sysUserOnline.setIzOnline(false);
                        userOnlineList.add(sysUserOnline);
                    }
                }
            }

        }
        if(StringUtils.isNotEmpty(ipaddr)){
            userOnlineList = userOnlineList.stream().filter(e->e.getIpaddr().equals(ipaddr)).collect(Collectors.toList());
        }
        if(izLock!=null){
            userOnlineList = userOnlineList.stream().filter(e->e.getIzLock().equals(izLock)).collect(Collectors.toList());
        }
        Collections.reverse(userOnlineList);
        userOnlineList.removeAll(Collections.singleton(null));
        return getDataTable(userOnlineList.stream()
                .sorted(Comparator.comparing(
                        SysUserOnline::getLoginTime,
                        Comparator.nullsLast(Comparator.reverseOrder())
                ))
                .collect(Collectors.toList()));
    }

    /**
     * 强退用户
     */
    //@PreAuthorize("@ss.hasPermi('monitor:online:forceLogout')")
    @Log(title = "在线用户", businessType = BusinessType.FORCE)
    @DeleteMapping("/{tokenId}")
    public AjaxResult forceLogout(@PathVariable String tokenId)
    {
        redisCache.deleteObject(CacheConstants.LOGIN_TOKEN_KEY + tokenId);
        return success();
    }
}
