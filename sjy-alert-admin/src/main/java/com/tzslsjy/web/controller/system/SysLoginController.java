package com.tzslsjy.web.controller.system;

import com.tzslsjy.common.constant.CacheConstants;
import com.tzslsjy.common.constant.Constants;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.domain.entity.SysMenu;
import com.tzslsjy.common.core.domain.entity.SysUser;
import com.tzslsjy.common.core.domain.model.LoginBody;
import com.tzslsjy.common.core.redis.RedisCache;
import com.tzslsjy.common.utils.SecurityUtils;
import com.tzslsjy.framework.web.service.SysLoginService;
import com.tzslsjy.framework.web.service.SysPermissionService;
import com.tzslsjy.system.service.ISysMenuService;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 登录验证
 * 
 * <AUTHOR>
 */
@RestController
public class SysLoginController
{
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private SysPermissionService permissionService;
    private static final int MAX_ATTEMPTS = 5; // 允许的最大失败次数
    /**
     * 登录方法
     * 
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody)
    {
//        String key = "water.basic:system:login.username:" + loginBody.getUsername();
//        if(redisCache.hasKey(key)){
//            Object object = redisCache.getCacheObject(key);
//            int i = Integer.parseInt(object.toString());
//            if (i>=MAX_ATTEMPTS) {
//                return AjaxResult.error("账号密码错误已经超五次，请过五分钟再试！");
//            }
//        }
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
                loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }
    @GetMapping("/clean")
    @Operation(description = "清除登陆5分钟限制")
    public AjaxResult clean(String name) {
        String key = CacheConstants.PWD_ERR_CNT_KEY + name;
        Collection<String> keys = redisCache.keys(key);
        if(keys.size()>0){
            Object object = redisCache.deleteObject(key);
            return AjaxResult.success();
        }
        return AjaxResult.error("用户不正确");
    }
    /**
     * 获取用户信息
     * 
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo()
    {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     * 
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters()
    {
        Long userId = SecurityUtils.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return AjaxResult.success(menuService.buildMenus(menus));
    }
}
