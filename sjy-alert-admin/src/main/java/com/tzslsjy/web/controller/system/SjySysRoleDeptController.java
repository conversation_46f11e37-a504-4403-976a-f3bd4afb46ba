package com.tzslsjy.web.controller.system;



import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.domain.entity.SysDept;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.system.domain.SjySysRoleDept;
import com.tzslsjy.system.domain.bo.SjySysRoleDeptAddBo;
import com.tzslsjy.system.domain.bo.SjySysRoleDeptEditBo;
import com.tzslsjy.system.domain.bo.SjySysRoleDeptQueryBo;
import com.tzslsjy.system.domain.vo.SjySysRoleDeptVo;
import com.tzslsjy.system.service.ISjySysRoleDeptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 设计院角色和部门关联Controller
 * 
 * <AUTHOR>
 * @date 2023-07-31
 */
@Tag(name = "设计院角色和部门关联控制器", description =  "设计院角色和部门关联管理" )
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/sysRoleDept")
public class SjySysRoleDeptController extends BaseController {

    private final ISjySysRoleDeptService iSjySysRoleDeptService;

    /**
     * 查询设计院角色和部门关联列表
     */
    @Operation(summary = "查询设计院角色和部门关联列表")
    ////@PreAuthorize("@ss.hasPermi('business:sys:list')")
    @GetMapping("/list")
    public TableDataInfo<SjySysRoleDeptVo> list(SjySysRoleDeptQueryBo bo) {
        startPage();
        List<SjySysRoleDeptVo> list = iSjySysRoleDeptService.queryList(bo);
        return getDataTable(list);
    }

    /**
     * 导出设计院角色和部门关联列表
     */
     @Operation(summary = "导出设计院角色和部门关联列表")
    ////@PreAuthorize("@ss.hasPermi('business:sys:export')")
    @Log(title = "设计院角色和部门关联", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult<SjySysRoleDeptVo> export(SjySysRoleDeptQueryBo bo) {
        List<SjySysRoleDeptVo> list = iSjySysRoleDeptService.queryList(bo);
        ExcelUtil<SjySysRoleDeptVo> util = new ExcelUtil<SjySysRoleDeptVo>(SjySysRoleDeptVo.class);
        return util.exportExcel(list, "设计院角色和部门关联");
    }

    /**
     * 获取设计院角色和部门关联详细信息
     */
     @Operation(summary = "获取设计院角色和部门关联详细信息")
    ////@PreAuthorize("@ss.hasPermi('business:sys:query')")
    @GetMapping("/{roleId}")
    public AjaxResult<SjySysRoleDeptVo> getInfo(@PathVariable("roleId" ) Long roleId) {
        return AjaxResult.success(iSjySysRoleDeptService.queryById(roleId));
    }

    /**
     * 新增设计院角色和部门关联
     */
     @Operation(summary = "新增设计院角色和部门关联")
    ////@PreAuthorize("@ss.hasPermi('business:sys:add')")
    @Log(title = "设计院角色和部门关联", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SjySysRoleDeptAddBo bo) {
        return toAjax(iSjySysRoleDeptService.insertByAddBo(bo) ? 1 : 0);
    }

    /**
     * 修改设计院角色和部门关联
     */
     @Operation(summary = "修改设计院角色和部门关联")
    ////@PreAuthorize("@ss.hasPermi('business:sys:edit')")
    @Log(title = "设计院角色和部门关联", businessType = BusinessType.UPDATE)
    @PutMapping()
    public AjaxResult edit(@RequestBody SjySysRoleDeptEditBo bo) {
        return toAjax(iSjySysRoleDeptService.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除设计院角色和部门关联
     */
     @Operation(summary = "删除设计院角色和部门关联")
    ////@PreAuthorize("@ss.hasPermi('business:sys:remove')")
    @Log(title = "设计院角色和部门关联" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public AjaxResult remove(@PathVariable Long[] roleIds) {
        return toAjax(iSjySysRoleDeptService.deleteWithValidByIds(Arrays.asList(roleIds), true) ? 1 : 0);
    }





    /**
     * 查询已分配用户角色列表
     */
    ////@PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/authUser/allocatedList")
     @Operation(summary = "查询已分配用户角色列表")
    public TableDataInfo allocatedList(SysDept user) {
        startPage();
        List<SysDept> list = iSjySysRoleDeptService.selectAllocatedList(user);
        return getDataTable(list);
    }

    /**
     * 查询未分配用户角色列表
     */
    ////@PreAuthorize("@ss.hasPermi('system:role:list')")
    @GetMapping("/authUser/unallocatedList")
     @Operation(summary = "查询未分配部门角色列表")
    public TableDataInfo unallocatedList(SysDept user) {
        startPage();
        List<SysDept> list = iSjySysRoleDeptService.selectUnallocatedList(user);
        return getDataTable(list);
    }

    /**
     * 取消授权用户
     */
//    ////@PreAuthorize("@ss.hasPermi('system:role:edit')")
//    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
     @Operation(summary = "取消授权部门")
    public AjaxResult<?> cancelAuthUser(@RequestBody SjySysRoleDept userRole) {
        return toAjax(iSjySysRoleDeptService.deleteAuthDept(userRole));
    }

    /**
     * 批量取消授权用户
     */
//    ////@PreAuthorize("@ss.hasPermi('system:role:edit')")
//    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
     @Operation(summary = "批量取消授权部门")
    public AjaxResult<?> cancelAuthUserAll(Long roleId, Long[] deptIds) {
        return toAjax(iSjySysRoleDeptService.deleteAuthDepts(roleId, deptIds));
    }

    /**
     * 批量选择用户授权
     */
//    ////@PreAuthorize("@ss.hasPermi('system:role:edit')")
//    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
     @Operation(summary = "批量选择授权部门")
    public AjaxResult<?> selectAuthUserAll(Long roleId, Long[] deptIds) {
        return toAjax(iSjySysRoleDeptService.insertAuthDepts(roleId, deptIds));
    }
}
