package com.tzslsjy.web.controller.system;



import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.core.vo.SjySysUserExtVo;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.common.pojo.CommonResult;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.system.domain.vo.SjySysUserExtAddBo;
import com.tzslsjy.system.domain.vo.SjySysUserExtEditBo;
import com.tzslsjy.system.domain.vo.SjySysUserExtQueryBo;
import com.tzslsjy.system.service.ISjySysUserExtService;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 用户多部门关系Controller
 * 
 * <AUTHOR>
 * @date 2022-11-29
 */
@Schema(name = "用户多部门关系控制器")
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/sjySysUserExt")
public class SjySysUserExtController extends BaseController {

    private final ISjySysUserExtService iSjySysUserExtService;

    /**
     * 查询用户多部门关系列表
     */
    @Schema(description ="查询用户多部门关系列表")
    //@PreAuthorize("@ss.hasPermi('erp:sys:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjySysUserExtQueryBo bo) {
        startPage();
        List<SjySysUserExtVo> list = iSjySysUserExtService.queryList(bo);
        return getDataTable(list);
    }
    /**
     * 查询用户多部门关系列表
     */
//    @Schema(description ="查询用户多部门关系列表(含手机号)")
//    //@PreAuthorize("@ss.hasPermi('erp:sys:list')")
//    @GetMapping("/listAll")
//    public TableDataInfo<SjySysUserExtVo> listAll(SjySysUserExtQueryBo bo) {
//        startPage();
//        List<SjySysUserExtVo> list = iSjySysUserExtService.queryList(bo);
//        return getDataTable(list);
//    }
    /**
     * 导出用户多部门关系列表
     */
    @Schema(description ="导出用户多部门关系列表")
    //@PreAuthorize("@ss.hasPermi('erp:sys:export')")
    @Log(title = "用户多部门关系", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult  export(SjySysUserExtQueryBo bo) {
        List<SjySysUserExtVo> list = iSjySysUserExtService.queryList(bo);
        ExcelUtil<SjySysUserExtVo> util = new ExcelUtil<SjySysUserExtVo>(SjySysUserExtVo.class);
        return util.exportExcel(list, "用户多部门关系");
    }

    /**
     * 获取用户多部门关系详细信息
     */
    @Schema(description ="获取用户多部门关系详细信息")
    //@PreAuthorize("@ss.hasPermi('erp:sys:query')")
    @GetMapping("/{userId}")
    public CommonResult<SjySysUserExtVo> getInfo(@PathVariable("userId" ) Long userId) {
        return CommonResult.success(iSjySysUserExtService.queryById(userId));
    }

    /**
     * 新增用户多部门关系
     */
    @Schema(description ="新增用户多部门关系")
    //@PreAuthorize("@ss.hasPermi('erp:sys:add')")
    @Log(title = "用户多部门关系", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SjySysUserExtAddBo bo) {
        return toAjax(iSjySysUserExtService.insertByAddBo(bo) ? 1 : 0);
    }

    /**
     * 修改用户多部门关系
     */
    @Schema(description ="修改用户多部门关系")
    //@PreAuthorize("@ss.hasPermi('erp:sys:edit')")
    @Log(title = "用户多部门关系", businessType = BusinessType.UPDATE)
    @PutMapping()
    public AjaxResult edit(@RequestBody SjySysUserExtEditBo bo) {
        return toAjax(iSjySysUserExtService.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户多部门关系
     */
    @Schema(description ="删除用户多部门关系")
    //@PreAuthorize("@ss.hasPermi('erp:sys:remove')")
    @Log(title = "用户多部门关系" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long[] id) {
        return toAjax(iSjySysUserExtService.deleteWithValidByIds(Arrays.asList(id), true) ? 1 : 0);
    }
}
