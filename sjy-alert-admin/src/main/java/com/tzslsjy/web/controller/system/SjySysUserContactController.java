package com.tzslsjy.web.controller.system;



import com.tzslsjy.common.annotation.Log;
import com.tzslsjy.common.core.controller.BaseController;
import com.tzslsjy.common.core.domain.AjaxResult;
import com.tzslsjy.common.core.page.TableDataInfo;
import com.tzslsjy.common.enums.BusinessType;
import com.tzslsjy.common.pojo.CommonResult;
import com.tzslsjy.common.utils.poi.ExcelUtil;
import com.tzslsjy.system.domain.vo.SjySysUserContactAddBo;
import com.tzslsjy.system.domain.vo.SjySysUserContactEditBo;
import com.tzslsjy.system.domain.vo.SjySysUserContactQueryBo;
import com.tzslsjy.system.domain.vo.SjySysUserContactVo;
import com.tzslsjy.system.service.ISjySysUserContactService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 用户联系方式Controller
 * 
 * <AUTHOR>
 * @date 2022-11-29
 */
@Tag(name = "用户联系方式控制器" )
@RequiredArgsConstructor(onConstructor_ = @Autowired)
@RestController
@RequestMapping("/system/sjySysUserContact")
public class SjySysUserContactController extends BaseController {

    private final ISjySysUserContactService iSjySysUserContactService;

    /**
     * 查询用户联系方式列表
     */
    @Operation(description = "查询用户联系方式列表")
    //@PreAuthorize("@ss.hasPermi('erp:sys:list')")
    @GetMapping("/list")
    public TableDataInfo list(SjySysUserContactQueryBo bo) {
        startPage();
        List<SjySysUserContactVo> list = iSjySysUserContactService.queryList(bo);
        return getDataTable(list);
    }

    /**
     * 导出用户联系方式列表
     */
    @Operation(description ="导出用户联系方式列表")
    //@PreAuthorize("@ss.hasPermi('erp:sys:export')")
    @Log(title = "用户联系方式", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult  export(SjySysUserContactQueryBo bo) {
        List<SjySysUserContactVo> list = iSjySysUserContactService.queryList(bo);
        ExcelUtil<SjySysUserContactVo> util = new ExcelUtil<SjySysUserContactVo>(SjySysUserContactVo.class);
        return util.exportExcel(list, "用户联系方式");
    }

    /**
     * 获取用户联系方式详细信息
     */
    @Operation(description ="获取用户联系方式详细信息")
    //@PreAuthorize("@ss.hasPermi('erp:sys:query')")
    @GetMapping("/{userId}")
    public CommonResult<SjySysUserContactVo> getInfo(@PathVariable("userId" ) Long userId) {
        return CommonResult.success(iSjySysUserContactService.queryById(userId));
    }

    /**
     * 新增用户联系方式
     */
    @Operation(description ="新增用户联系方式")
    //@PreAuthorize("@ss.hasPermi('erp:sys:add')")
    @Log(title = "用户联系方式", businessType = BusinessType.INSERT)
    @PostMapping()
    public AjaxResult add(@RequestBody SjySysUserContactAddBo bo) {
        return toAjax(iSjySysUserContactService.insertByAddBo(bo) ? 1 : 0);
    }

    @Operation(description ="批量新增用户联系方式")
    //@PreAuthorize("@ss.hasPermi('erp:sys:add')")
    @Log(title = "批量新增用户联系方式", businessType = BusinessType.INSERT)
    @PostMapping("/addAll")
    public AjaxResult addAll(@RequestBody List<SjySysUserContactAddBo> bo) {
        bo.forEach(e->iSjySysUserContactService.insertByAddBo(e));
        return AjaxResult.success();
    }

    /**
     * 修改用户联系方式
     */
    @Operation(description ="修改用户联系方式")
    //@PreAuthorize("@ss.hasPermi('erp:sys:edit')")
    @Log(title = "用户联系方式", businessType = BusinessType.UPDATE)
    @PutMapping()
    public AjaxResult edit(@RequestBody SjySysUserContactEditBo bo) {
        return toAjax(iSjySysUserContactService.updateByEditBo(bo) ? 1 : 0);
    }

    /**
     * 删除用户联系方式
     */
    @Operation(description ="删除用户联系方式")
    //@PreAuthorize("@ss.hasPermi('erp:sys:remove')")
    @Log(title = "用户联系方式" , businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(iSjySysUserContactService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
