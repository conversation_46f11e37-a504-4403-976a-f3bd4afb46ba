package com.tzslsjy;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.core.env.Environment;

import java.net.InetAddress;
import java.net.UnknownHostException;

/**
 * 启动程序
 * 
 * <AUTHOR>
 */
@SpringBootApplication(exclude = { DataSourceAutoConfiguration.class })
@ComponentScan(basePackages = {
        "com.tzslsjy",
        "com.tzslsjy"  // 添加这个包路径
})
public class SjyAlertApplication
{
    public static void main(String[] args) throws UnknownHostException
    {
        // System.setProperty("spring.devtools.restart.enabled", "false");
        ConfigurableApplicationContext application = SpringApplication.run(SjyAlertApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("server.servlet.context-path");
        String path = property == null ? "" :  property;
        System.out.println("                                 \n" +
                "                                 \n" +
                "                                 \n" +
                "                                 \n" +
                "                 .--.            \n" +
                "  .--.--.      .--,`|            \n" +
                " /  /    '     |  |.       .--,  \n" +
                "|  :  /`./     '--`_     /_ ./|  \n" +
                "|  :  ;_       ,--,'| , ' , ' :  \n" +
                " \\  \\    `.    |  | '/___/ \\: |  \n" +
                "  `----.   \\   :  | | .  \\  ' |  \n" +
                " /  /`--'  / __|  : '  \\  ;   :  \n" +
                "'--'.     /.'__/\\_: |   \\  \\  ;  \n" +
                "  `--'---' |   :    :    :  \\  \\ \n" +
                "            \\   \\  /      \\  ' ; \n" +
                "             `--`-'        `--`  \n"+
                "Application Boot is running! Access URLs:\n\t" +
                "Local: \t\thttp://localhost:" + port + path + "\n\t" +
                "External: \thttp://" + ip + ":" + port + path + "\n\t");
    }
}
