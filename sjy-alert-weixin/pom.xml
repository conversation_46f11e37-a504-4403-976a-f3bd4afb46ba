<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>sjyAlert</artifactId>
		<groupId>com.tzslsjy</groupId>
		<version>3.8.7</version>
	</parent>

	<artifactId>sjyAlert-weixin</artifactId>

	<name>sjyAlert-weixin</name>
	<description>sjyAlert-weixin</description>
	<properties>
		<java.version>1.8</java.version>
	</properties>
	<dependencies>
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-starter</artifactId>-->
<!--		</dependency>-->
		<!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
		<!-- https://mvnrepository.com/artifact/com.alibaba/fastjson -->
<!--		<dependency>-->
<!--			<groupId>com.alibaba</groupId>-->
<!--			<artifactId>fastjson</artifactId>-->
<!--			<version>2.0.15</version>-->
<!--		</dependency>-->


		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- 核心模块-->
		<dependency>
			<groupId>com.tzslsjy</groupId>
			<artifactId>sjy-spring-boot-start-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
<!--		&lt;!&ndash; 核心模块&ndash;&gt;-->
		<dependency>
			<groupId>com.tzslsjy</groupId>
			<artifactId>sjyAlert-common</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- 第三方微信库-->
		<dependency>
			<groupId>com.github.binarywang</groupId>
			<artifactId>wx-java-miniapp-spring-boot-starter</artifactId>
			<version>4.3.8.B</version>
		</dependency>
		<!-- spring boot 配置所需依赖 -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>
		<!-- 三方云服务相关 -->
		<!-- https://mvnrepository.com/artifact/me.zhyd.oauth/JustAuth -->
		<dependency>
			<groupId>me.zhyd.oauth</groupId>
			<artifactId>JustAuth</artifactId>
			<version>1.16.5</version>
		</dependency>

		<dependency>
			<groupId>com.xkcoding.justauth</groupId>
			<artifactId>justauth-spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>cn.hutool</groupId>
					<artifactId>hutool-core</artifactId>
				</exclusion>
			</exclusions>
			<version>1.4.0</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>${fastjson.version}</version>
		</dependency>
		<dependency>
			<groupId>com.tzslsjy</groupId>
			<artifactId>sjy-spring-boot-start-core</artifactId>
		</dependency>
	</dependencies>

<!--	<build>-->
<!--		<plugins>-->
<!--			<plugin>-->
<!--				<groupId>org.springframework.boot</groupId>-->
<!--				<artifactId>spring-boot-maven-plugin</artifactId>-->
<!--				<configuration>-->
<!--					<excludes>-->
<!--						<exclude>-->
<!--							<groupId>org.projectlombok</groupId>-->
<!--							<artifactId>lombok</artifactId>-->
<!--						</exclude>-->
<!--					</excludes>-->
<!--				</configuration>-->
<!--			</plugin>-->
<!--		</plugins>-->
<!--	</build>-->

</project>
