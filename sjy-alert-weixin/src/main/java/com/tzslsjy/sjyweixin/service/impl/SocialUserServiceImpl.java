package com.tzslsjy.sjyweixin.service.impl;




import com.tzslsjy.sjyweixin.core.YudaoAuthRequestFactory;
import com.tzslsjy.sjyweixin.domain.SocialUserBindDO;
import com.tzslsjy.sjyweixin.domain.SocialUserDO;
import com.tzslsjy.sjyweixin.enums.SocialTypeEnum;
import com.tzslsjy.sjyweixin.json.JsonUtils;
import com.tzslsjy.sjyweixin.mapper.SocialUserBindMapper;
import com.tzslsjy.sjyweixin.mapper.SocialUserMapper;
import com.tzslsjy.sjyweixin.service.SocialUserService;
import com.tzslsjy.sjyweixin.social.dto.SocialUserBindReqDTO;

import com.tzslsjy.sjyweixin.core.enums.ErrorCodeConstants;
import lombok.extern.slf4j.Slf4j;
import me.zhyd.oauth.model.AuthCallback;
import me.zhyd.oauth.model.AuthResponse;
import me.zhyd.oauth.model.AuthUser;
import me.zhyd.oauth.request.AuthRequest;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 社交用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class SocialUserServiceImpl implements SocialUserService {

    @Resource// 由于自定义了 YudaoAuthRequestFactory 无法覆盖默认的 AuthRequestFactory，所以只能注入它
    private YudaoAuthRequestFactory yudaoAuthRequestFactory;

    @Resource
    private SocialUserBindMapper socialUserBindMapper;
    @Resource
    private SocialUserMapper socialUserMapper;

//    @Override
//    public String getAuthorizeUrl(Integer type, String redirectUri) {
//        // 获得对应的 AuthRequest 实现
//        AuthRequest authRequest = yudaoAuthRequestFactory.get(SocialTypeEnum.valueOfType(type).getSource());
//        // 生成跳转地址
//        String authorizeUri = authRequest.authorize(AuthStateUtils.createState());
//        return HttpUtils.replaceUrlQuery(authorizeUri, "redirect_uri", redirectUri);
//    }

    @Override
    public SocialUserDO authSocialUser(Integer type, String code, String state) {
        // 优先从 DB 中获取，因为 code 有且可以使用一次。
        // 在社交登录时，当未绑定 User 时，需要绑定登录，此时需要 code 使用两次
        SocialUserDO socialUser = socialUserMapper.selectByTypeAndCodeAnState(type, code, state);
        if (socialUser != null) {
            return socialUser;
        }

        // 请求获取
        AuthUser authUser = getAuthUser(type, code, state);
        Assert.notNull(authUser, "三方用户不能为空");

        // 保存到 DB 中
        socialUser = socialUserMapper.selectByTypeAndOpenid(type, authUser.getUuid());
        if (socialUser == null) {
            socialUser = new SocialUserDO();
        }

     //   socialUser.setType(type).setCode(code).setState(state)
        socialUser= socialUser.builder().type(type).code(code).state(state) // 需要保存 code + state 字段，保证后续可查询
                .openid(authUser.getUuid()).token(authUser.getToken().getAccessToken()).rawTokenInfo((JsonUtils.toJsonString(authUser.getToken())))
                .nickname(authUser.getNickname()).avatar(authUser.getAvatar()).build();
         log.debug(JsonUtils.toJsonString(authUser));
        if(authUser.getRawUserInfo()!=null)
            socialUser.setRawUserInfo(JsonUtils.toJsonString(authUser.getRawUserInfo()));
        //    System.out.println(authUser.getRawUserInfo());
//        System.out.println(authUser.getRawUserInfo().toJSONString());
        if (socialUser.getId() == null) {
            socialUserMapper.insert(socialUser);
        } else {
            socialUserMapper.updateById(socialUser);
        }
        return socialUser;
    }

//    @Override
//    public List<SocialUserDO> getSocialUserList(Long userId, Integer userType) {
//        // 获得绑定
//        List<SocialUserBindDO> socialUserBinds = socialUserBindMapper.selectListByUserIdAndUserType(userId, userType);
//        if (CollUtil.isEmpty(socialUserBinds)) {
//            return Collections.emptyList();
//        }
//        // 获得社交用户
//        return socialUserMapper.selectBatchIds(convertSet(socialUserBinds, SocialUserBindDO::getSocialUserId));
//    }

    @Override
    @Transactional
    public void bindSocialUser(SocialUserBindReqDTO reqDTO) {
        // 获得社交用户
        SocialUserDO socialUser = authSocialUser(reqDTO.getType(), reqDTO.getCode(), reqDTO.getState());
        Assert.notNull(socialUser, "社交用户不能为空");

        // 社交用户可能之前绑定过别的用户，需要进行解绑
        socialUserBindMapper.deleteByUserTypeAndSocialUserId(reqDTO.getUserType(), socialUser.getId());

        // 用户可能之前已经绑定过该社交类型，需要进行解绑
        socialUserBindMapper.deleteByUserTypeAndUserIdAndSocialType(reqDTO.getUserType(), reqDTO.getUserId(),
                socialUser.getType());

        // 绑定当前登录的社交用户
        SocialUserBindDO socialUserBind = SocialUserBindDO.builder()
                .userId(reqDTO.getUserId()).userType(reqDTO.getUserType())
                .socialUserId(socialUser.getId()).socialType(socialUser.getType()).build();
        socialUserBindMapper.insert(socialUserBind);
    }

    @Override
    public void unbindSocialUser(Long userId, Integer userType, Integer type, String openid) {
        // 获得 openid 对应的 SocialUserDO 社交用户
        SocialUserDO socialUser = socialUserMapper.selectByTypeAndOpenid(type, openid);
        if (socialUser == null) {
            throw new RuntimeException("社交用户未找到");
        }

        // 获得对应的社交绑定关系
        socialUserBindMapper.deleteByUserTypeAndUserIdAndSocialType(userType, userId, socialUser.getType());
    }

    @Override
    public Long getBindUserId(Integer userType, Integer type, String code, String state) {
        // 获得社交用户
        SocialUserDO socialUser = authSocialUser(type, code, state);
        Assert.notNull(socialUser, "社交用户不能为空");

        // 如果未绑定的社交用户，则无法自动登录，进行报错
        SocialUserBindDO socialUserBind = socialUserBindMapper.selectByUserTypeAndSocialUserId(userType,
                socialUser.getId());
        if (socialUserBind == null) {
            throw new RuntimeException(ErrorCodeConstants.AUTH_THIRD_LOGIN_NOT_BIND.getMsg());
        }
        return socialUserBind.getUserId();
    }

    /**
     * 请求社交平台，获得授权的用户
     *
     * @param type 社交平台的类型
     * @param code 授权码
     * @param state 授权 state
     * @return 授权的用户
     */
    private AuthUser getAuthUser(Integer type, String code, String state) {
        AuthRequest authRequest = yudaoAuthRequestFactory.get(SocialTypeEnum.valueOfType(type).getSource());
        AuthCallback authCallback = AuthCallback.builder().code(code).state(state).build();
        AuthResponse<?> authResponse = authRequest.login(authCallback);
        log.info("[getAuthUser][请求社交平台 type({}) request({}) response({})]", type,
                JsonUtils.toJsonString(authCallback), JsonUtils.toJsonString(authResponse));
        if (!authResponse.ok()) {
            throw new RuntimeException( authResponse.getMsg());
        }
        return (AuthUser) authResponse.getData();
    }

}

