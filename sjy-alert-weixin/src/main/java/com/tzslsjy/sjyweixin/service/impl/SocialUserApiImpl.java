package com.tzslsjy.sjyweixin.service.impl;


import com.tzslsjy.sjyweixin.service.SocialUserService;
import com.tzslsjy.sjyweixin.social.SocialUserApi;
import com.tzslsjy.sjyweixin.social.dto.SocialUserBindReqDTO;
import com.tzslsjy.sjyweixin.social.dto.SocialUserUnbindReqDTO;
import me.zhyd.oauth.utils.AuthStateUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;

/**
 * 社交用户的 API 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class SocialUserApiImpl implements SocialUserApi {

    @Resource
    private SocialUserService socialUserService;

//    @Override
//    public String getAuthorizeUrl(Integer type, String redirectUri) {
//        return socialUserService.getAuthorizeUrl(type, redirectUri);
//    }

    @Override
    public void bindSocialUser(SocialUserBindReqDTO reqDTO) {
        reqDTO.setState(   AuthStateUtils.createState()
        );
        socialUserService.bindSocialUser(reqDTO);
    }

    @Override
    public void unbindSocialUser(SocialUserUnbindReqDTO reqDTO) {
        socialUserService.unbindSocialUser(reqDTO.getUserId(), reqDTO.getUserType(),
                reqDTO.getType(), reqDTO.getUnionId());
    }

    @Override
    public Long getBindUserId(Integer userType, Integer type, String code, String state) {
       return socialUserService.getBindUserId(userType, type, code, state);
    }

}
