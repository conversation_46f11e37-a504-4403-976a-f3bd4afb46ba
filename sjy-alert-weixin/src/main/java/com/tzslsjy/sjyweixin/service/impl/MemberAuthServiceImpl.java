package com.tzslsjy.sjyweixin.service.impl;


import cn.hutool.core.bean.BeanUtil;

import com.tzslsjy.common.core.domain.entity.SysUser;
import com.tzslsjy.common.core.domain.model.LoginUser;
import com.tzslsjy.common.exception.ServiceException;

import com.tzslsjy.framework.web.service.SysLoginService;
import com.tzslsjy.framework.web.service.TokenService;
import com.tzslsjy.sjyweixin.enums.UserTypeEnum;
import com.tzslsjy.sjyweixin.service.MemberAuthService;
import com.tzslsjy.sjyweixin.social.SocialUserApi;
import com.tzslsjy.sjyweixin.vo.AppAuthSocialLoginReqVO;

import com.tzslsjy.sjyweixin.core.enums.ErrorCodeConstants;
import com.tzslsjy.system.service.ISysUserService;

import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class MemberAuthServiceImpl implements MemberAuthService {
    @Resource
    private ISysUserService userService;

    @Resource
    private SocialUserApi socialUserApi;

    @Resource
    private SysLoginService loginService;
    @Resource
    private TokenService tokenService;
//    private AuthenticationManager authenticationManager;
//    private RedisCache redisCache;
//
//    private ISysConfigService configService;
//    private Environment env;
//    @Resource
//    private WxMaService wxMaService;
//
//    @Resource
//    private PasswordEncoder passwordEncoder;



    @Override
    public String socialLogin(AppAuthSocialLoginReqVO reqVO) {
        // 使用 code 授权码，进行登录。然后，获得到绑定的用户编号
        Long userId = socialUserApi.getBindUserId(UserTypeEnum.MEMBER.getValue(), reqVO.getType(),
                reqVO.getCode(), reqVO.getState());
        if (userId == null) {
            throw new ServiceException(ErrorCodeConstants.AUTH_THIRD_LOGIN_NOT_BIND.getMsg());
        }

        // 自动登录
        SysUser user = userService.selectUserById(userId);
        if (user == null) {
            throw new ServiceException(ErrorCodeConstants.USER_NOT_EXISTS.getMsg());
        }

        // 生成token
        return createTokenAfterLoginSuccess(user);
        // 记录登录日志

    }
    private String createTokenAfterLoginSuccess(SysUser user) {
        // 插入登陆日志
        loginService.recordLoginInfo(user.getUserId());
        System.out.println(user);
        // 生成token
        LoginUser loginUser = new LoginUser();
        BeanUtil.copyProperties(user,loginUser,true);
        loginUser.setUser(user);
//        System.out.println(loginUser.getUserId());
        System.out.println(loginUser.getUsername());
        String token = tokenService.createToken(loginUser);
        return token;

    }


}

