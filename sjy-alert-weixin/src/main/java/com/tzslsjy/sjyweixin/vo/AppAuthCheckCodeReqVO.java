package com.tzslsjy.sjyweixin.vo;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.checkerframework.checker.units.qual.Length;


import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

// TODO 芋艿：code review 相关逻辑
@Schema(description = "用户 APP - 校验验证码 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AppAuthCheckCodeReqVO {

   @Schema(description = "手机号", example = "15601691234")
    @NotBlank(message = "手机号不能为空")

    private String mobile;

   @Schema(description = "手机验证码", required = true, example = "1024")
    @NotBlank(message = "手机验证码不能为空")
    //@Length(min = 4, max = 6, message = "手机验证码长度为 4-6 位")
    @Pattern(regexp = "^[0-9]+$", message = "手机验证码必须都是数字")
    private String code;

   @Schema(description = "发送场景(对应 SmsSceneEnum 枚举)", example = "1" )
    @NotNull(message = "发送场景不能为空")

    private Integer scene;

}
