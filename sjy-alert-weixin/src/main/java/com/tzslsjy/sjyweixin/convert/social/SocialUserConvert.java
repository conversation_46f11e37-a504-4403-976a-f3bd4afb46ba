package com.tzslsjy.sjyweixin.convert.social;


import com.tzslsjy.sjyweixin.social.dto.SocialUserBindReqDTO;
import com.tzslsjy.sjyweixin.social.dto.SocialUserUnbindReqDTO;
import com.tzslsjy.sjyweixin.vo.AppSocialUserBindReqVO;
import com.tzslsjy.sjyweixin.vo.AppSocialUserUnbindReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SocialUserConvert {

    SocialUserConvert INSTANCE = Mappers.getMapper(SocialUserConvert.class);

    SocialUserBindReqDTO convert(Long userId, Integer userType, AppSocialUserBindReqVO reqVO);

    SocialUserUnbindReqDTO convert(Long userId, Integer userType, AppSocialUserUnbindReqVO reqVO);

}
